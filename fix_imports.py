#!/usr/bin/env python3
"""
批量修復導入路徑的腳本
將舊的 utils.xxx 導入更新為新的模組化結構
"""

import os
import re
import glob

# 定義導入路徑映射
IMPORT_MAPPING = {
    # Database 模組
    'utils.query_helper': 'utils.database.query_helper',
    'utils.query_optimizer': 'utils.database.query_optimizer',
    'utils.db_analyzer': 'utils.database.db_analyzer',
    'utils.db_optimizer': 'utils.database.db_optimizer',
    'utils.db_pool_monitor': 'utils.database.db_pool_monitor',
    'utils.db_session_helper': 'utils.database.db_session_helper',
    'utils.batch_operations': 'utils.database.batch_operations',
    
    # Performance 模組
    'utils.cache_manager': 'utils.performance.cache_manager',
    'utils.advanced_cache': 'utils.performance.advanced_cache',
    'utils.performance_monitor': 'utils.performance.performance_monitor',
    'utils.performance_benchmarking': 'utils.performance.performance_benchmarking',
    'utils.alert_system': 'utils.performance.alert_system',
    'utils.memory_optimizer': 'utils.performance.memory_optimizer',
    
    # Security 模組
    'utils.auth_decorators': 'utils.security.auth_decorators',
    'utils.tenant_decorators': 'utils.security.tenant_decorators',
    'utils.permission_helpers': 'utils.security.permission_helpers',
    'utils.security': 'utils.security.security',
    
    # Logging 模組
    'utils.audit_logger': 'utils.logging.audit_logger',
    'utils.error_monitor': 'utils.logging.error_monitor',
    'utils.system_logger': 'utils.logging.system_logger',
    'utils.log_analyzer': 'utils.logging.log_analyzer',
    
    # Business 模組
    'utils.income_expense_helpers': 'utils.business.income_expense_helpers',
    'utils.reports_helpers': 'utils.business.reports_helpers',
    'utils.report_generator': 'utils.business.report_generator',
    'utils.settings_helpers': 'utils.business.settings_helpers',
    'utils.audit_helper': 'utils.business.audit_helper',
    'utils.bank_helpers': 'utils.business.bank_helpers',
    'utils.backup_manager': 'utils.business.backup_manager',
    
    # Web 模組
    'utils.menu_decorator': 'utils.web.menu_decorator',
    'utils.base_view': 'utils.web.base_view',
    'utils.debug_helpers': 'utils.web.debug_helpers',
    'utils.api_response': 'utils.web.api_response',
    
    # Common 模組
    'utils.helpers': 'utils.common.helpers',
    'utils.error_handler': 'utils.logging.error_handler',
}

def fix_file_imports(file_path):
    """修復單個檔案的導入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替換 from utils.xxx import 形式
        for old_import, new_import in IMPORT_MAPPING.items():
            pattern = rf'\bfrom {re.escape(old_import)} import\b'
            replacement = f'from {new_import} import'
            content = re.sub(pattern, replacement, content)
        
        # 如果有修改，寫回檔案
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f'✅ 修復: {file_path}')
            return True
        
        return False
        
    except Exception as e:
        print(f'❌ 錯誤處理 {file_path}: {e}')
        return False

def main():
    """主函數"""
    print("🔧 開始修復導入路徑...")
    
    # 需要處理的檔案類型
    patterns = [
        'routes/*.py',
        'services/*.py', 
        'models/*.py',
        'utils/**/*.py',
        '*.py'
    ]
    
    total_fixed = 0
    
    for pattern in patterns:
        if '**' in pattern:
            files = glob.glob(pattern, recursive=True)
        else:
            files = glob.glob(pattern)
        for file_path in files:
            if fix_file_imports(file_path):
                total_fixed += 1
    
    print(f"\n✨ 完成！共修復了 {total_fixed} 個檔案的導入路徑。")

if __name__ == '__main__':
    main()