from flask import Flask, render_template, request, g, redirect, flash, jsonify
import sys
import os
import logging
from logging.handlers import RotatingFileHandler
import time
import secrets

# 添加配置和數據路徑
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'data'))

from config.config import Config
from database import init_db

# 直接在這裡定義性能監控功能
from routes.main import main_bp
from routes.income_expense import income_expense_bp
from routes.assets import assets_bp
from routes.payroll import payroll_bp
from routes.service_reward import service_reward_bp
from routes.settings import settings_bp
from routes.accounting_other import accounting_other_bp
from routes.accounting import accounting_bp
from routes.account import account_bp
from routes.monitoring import monitoring_bp
from routes.audit import audit_bp
from routes.performance_test import performance_test_bp
from routes.reports import reports_bp
from routes.share_account import share_account_bp
from routes.bankloan import bankloan_bp
from routes.fund_record import fund_record_bp
from routes.api import api_bp
from routes.transfer import transfer_bp
from routes.admin import admin_bp
from routes.error_monitor import error_monitor_bp
from utils.logging.error_test import error_test_bp
from routes.auth import auth_bp
from routes.permission_admin import permission_admin_bp
from routes.journal_validation import journal_validation_bp
from routes.new_reports import new_reports_bp
from routes.tenant_admin import tenant_admin_bp, tenant_bp
from routes.registration import registration_bp
from routes.security_dashboard import security_dashboard_bp
from routes.password_manager import password_manager_bp
from routes.secure_file_access import secure_file_bp
from routes.api_security_admin import api_security_admin_bp
from routes.new_income_expense import new_income_expense_bp

# 使用統一的性能監控系統
from utils.performance.performance_monitor import performance_monitor
PERFORMANCE_MONITORING_AVAILABLE = True

# 導入所有 Blueprint


def setup_logging(app):
    """設置日誌系統"""
    # 確保日誌目錄存在
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 設置日誌格式
    formatter = logging.Formatter(
        '%(asctime)s %(levelname)s [%(filename)s:%(lineno)d] - %(message)s'
    )
    
    # 應用主日誌
    app_handler = RotatingFileHandler(
        'logs/accounting.log',
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=10
    )
    app_handler.setFormatter(formatter)
    app_handler.setLevel(logging.INFO)
    
    # 錯誤日誌
    error_handler = RotatingFileHandler(
        'logs/error.log',
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=5
    )
    error_handler.setFormatter(formatter)
    error_handler.setLevel(logging.ERROR)
    
    # 訪問日誌
    access_handler = RotatingFileHandler(
        'logs/access.log',
        maxBytes=5 * 1024 * 1024,
        backupCount=10
    )
    access_formatter = logging.Formatter(
        '%(asctime)s - %(message)s'
    )
    access_handler.setFormatter(access_formatter)
    access_handler.setLevel(logging.INFO)
    
    # 配置應用日誌
    app.logger.addHandler(app_handler)
    app.logger.addHandler(error_handler)
    app.logger.setLevel(logging.INFO)
    
    # 創建訪問日誌記錄器
    access_logger = logging.getLogger('access')
    access_logger.addHandler(access_handler)
    access_logger.setLevel(logging.INFO)
    access_logger.propagate = False
    
    # 記錄啟動信息
    app.logger.info('會計系統啟動完成')
    app.logger.info(f'日誌系統初始化完成 - 日誌目錄: {os.path.abspath("logs")}')

# 這些函數已移至 services/account_service.py，保留向後相容性
def get_accounts_data():
    """獲取帳戶資料的通用函數（已棄用，請使用 AccountService.get_accounts_dropdown）"""
    from services.account_service import AccountService
    return AccountService.get_accounts_dropdown()

def get_dropdown_data():
    """獲取所有下拉選單資料的通用函數（已棄用，請使用 AccountService.get_dropdown_data）"""
    from services.account_service import AccountService
    return AccountService.get_dropdown_data()

def _add_security_headers(response, app):
    """添加安全HTTP頭"""
    
    # 檢查是否啟用安全頭
    if not app.config.get('SECURITY_HEADERS_ENABLED', True):
        return response
    
    # 基本安全頭（所有環境）
    security_headers = {
        # 防止MIME類型嗅探攻擊
        'X-Content-Type-Options': 'nosniff',
        
        # 防止點擊劫持攻擊
        'X-Frame-Options': 'DENY',
        
        # 瀏覽器XSS過濾器（雖然現代瀏覽器已內建，但為了兼容性保留）
        'X-XSS-Protection': '1; mode=block',
        
        # 控制Referrer資訊洩露
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        
        # 控制瀏覽器功能權限
        'Permissions-Policy': (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "accelerometer=(), "
            "gyroscope=()"
        ),
    }
    
    # 內容安全策略（根據環境調整）
    if app.config.get('CSP_ALLOW_UNSAFE_INLINE', True):
        # 開發環境：允許內聯腳本和樣式
        csp = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; "
            "img-src 'self' data:; "
            "font-src 'self' cdn.jsdelivr.net; "
            "connect-src 'self'; "
            "frame-ancestors 'none'"
        )
    else:
        # 生產環境：更嚴格的CSP
        csp = (
            "default-src 'self'; "
            "script-src 'self' cdn.jsdelivr.net; "
            "style-src 'self' cdn.jsdelivr.net; "
            "img-src 'self' data:; "
            "font-src 'self' cdn.jsdelivr.net; "
            "connect-src 'self'; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self'; "
            "object-src 'none'"
        )
    
    security_headers['Content-Security-Policy'] = csp
    
    # 生產環境額外的安全頭
    if not app.config.get('DEBUG', False):
        # HSTS - 強制HTTPS連接（僅生產環境）
        hsts_max_age = app.config.get('HSTS_MAX_AGE', 31536000)
        security_headers['Strict-Transport-Security'] = f'max-age={hsts_max_age}; includeSubDomains; preload'
    
    # 添加安全頭到響應
    for header, value in security_headers.items():
        response.headers[header] = value
    
    return response

def create_app():
    """建立 Flask 應用程式"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # 確保密鑰已設置，避免CSRF問題
    if not app.secret_key or app.secret_key == 'dev':
        app.secret_key = app.config.get('SECRET_KEY', secrets.token_hex(32))
    
    # 初始化CSRF保護
    from flask_wtf.csrf import CSRFProtect
    csrf = CSRFProtect(app)
    
    # 初始化 Flask-Login
    from flask_login import LoginManager
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '請先登入系統'
    
    @login_manager.user_loader
    def load_user(user_id):
        """載入用戶物件供 Flask-Login 使用"""
        from database import get_db
        from model import User
        try:
            with get_db() as db:
                user = db.query(User).filter(User.id == int(user_id)).first()
                # 直接返回用戶對象，不再動態添加屬性
                # 屬性已在模型中定義
                return user
        except (ValueError, TypeError):
            return None
    
    # 開發模式下禁用模板緩存
    from utils.web.debug_helpers import is_debug_mode
    if is_debug_mode():
        app.jinja_env.auto_reload = True
        app.config['TEMPLATES_AUTO_RELOAD'] = True
    
    # 設置日誌系統
    setup_logging(app)
    
    # 設置統一的性能監控和日誌記錄
    @app.before_request
    def before_request():
        import uuid
        # 生成請求關聯 ID
        g.correlation_id = str(uuid.uuid4())[:8]  # 使用短 ID 便於追蹤
        g.start_time = time.time()
        g.request_start_time = int(time.time() * 1000)  # 毫秒時間戳
        
        # 會話安全檢查
        from flask import session
        from datetime import datetime, timedelta
        
        # 檢查會話是否過期
        if 'last_activity' in session:
            try:
                last_activity = datetime.fromisoformat(session['last_activity'])
                if datetime.now() - last_activity > timedelta(seconds=app.config.get('PERMANENT_SESSION_LIFETIME', 3600)):
                    session.clear()
                    app.logger.warning(f'[{g.correlation_id}] 會話已過期，已清除')
            except (ValueError, TypeError):
                # 處理無效的時間格式
                session.clear()
        
        # 更新最後活動時間
        session['last_activity'] = datetime.now().isoformat()
        
        app.logger.info(f'[{g.correlation_id}] 請求開始: {request.method} {request.url}')
    
    @app.after_request
    def after_request(response):
        if hasattr(g, 'start_time'):
            response_time = time.time() - g.start_time  # 以秒為單位
            response_time_ms = response_time * 1000  # 轉換為毫秒
            
            # 使用統一的性能監控系統
            performance_monitor.record_request(
                endpoint=request.endpoint or request.path,
                method=request.method,
                response_time=response_time,
                status_code=response.status_code
            )
            
        # 添加安全HTTP頭
        _add_security_headers(response, app)
        
        # 添加安全Cookie設定
        if 'Set-Cookie' in response.headers:
            # 確保會話Cookie有正確的安全屬性
            if app.config.get('SESSION_COOKIE_SECURE'):
                response.headers.add('Set-Cookie', 'Secure')
            if app.config.get('SESSION_COOKIE_HTTPONLY'):
                response.headers.add('Set-Cookie', 'HttpOnly')
            if app.config.get('SESSION_COOKIE_SAMESITE'):
                response.headers.add('Set-Cookie', f'SameSite={app.config["SESSION_COOKIE_SAMESITE"]}')
            
            # 記錄訪問日誌（包含關聯 ID）
            access_logger = logging.getLogger('access')
            correlation_id = getattr(g, 'correlation_id', 'unknown')
            access_logger.info(
                f'[{correlation_id}] {request.remote_addr or "unknown"} - '
                f'{request.method} {request.url} - {response.status_code} - {response_time_ms:.2f}ms'
            )
            
            # 記錄慢請求
            if response_time_ms > 2000:
                app.logger.warning(
                    f'[{correlation_id}] 慢請求: {request.method} {request.url} - {response_time_ms:.2f}ms'
                )
            
            app.logger.info(
                f'[{correlation_id}] 請求完成: {response.status_code} - {response_time_ms:.2f}ms'
            )
            
            # 在響應頭中添加關聯 ID（便於前端追蹤）
            response.headers['X-Correlation-ID'] = correlation_id
        
        return response
    
    # 註冊所有 Blueprint
    app.register_blueprint(main_bp)
    app.register_blueprint(income_expense_bp)
    app.register_blueprint(assets_bp)
    app.register_blueprint(payroll_bp)
    app.register_blueprint(service_reward_bp)
    app.register_blueprint(settings_bp)
    app.register_blueprint(accounting_other_bp)
    app.register_blueprint(accounting_bp, url_prefix='/accounting')
    app.register_blueprint(account_bp)
    app.register_blueprint(monitoring_bp)
    app.register_blueprint(audit_bp)
    app.register_blueprint(performance_test_bp)
    app.register_blueprint(reports_bp)
    app.register_blueprint(share_account_bp)
    app.register_blueprint(bankloan_bp)
    app.register_blueprint(fund_record_bp)
    app.register_blueprint(api_bp)
    app.register_blueprint(transfer_bp)
    app.register_blueprint(admin_bp)
    app.register_blueprint(error_monitor_bp)
    app.register_blueprint(error_test_bp)
    app.register_blueprint(auth_bp)
    app.register_blueprint(permission_admin_bp)
    app.register_blueprint(journal_validation_bp)
    app.register_blueprint(new_reports_bp)
    app.register_blueprint(tenant_admin_bp)
    app.register_blueprint(tenant_bp)
    app.register_blueprint(registration_bp)
    app.register_blueprint(security_dashboard_bp)
    app.register_blueprint(password_manager_bp)
    app.register_blueprint(secure_file_bp)
    app.register_blueprint(api_security_admin_bp)
    app.register_blueprint(new_income_expense_bp)
    
    # 註冊性能監控儀表板
    from routes.performance_dashboard import performance_dashboard_bp
    app.register_blueprint(performance_dashboard_bp)
    
    # 註冊資料庫監控儀表板
    from routes.database_monitor import database_monitor_bp
    app.register_blueprint(database_monitor_bp)
    
    # 初始化資料庫
    init_db()
    
    # 初始化多租戶中間件
    try:
        from services.tenant_service import TenantMiddleware
        TenantMiddleware.init_app(app)
        app.logger.info("多租戶中間件初始化成功")
    except Exception as e:
        app.logger.error(f"多租戶中間件初始化失敗: {str(e)}")
    
    # 初始化連接池監控
    from database import engine
    from utils.database.db_pool_monitor import init_pool_monitor
    init_pool_monitor(engine, start_monitoring=True)
    
    # 初始化快取清理任務
    from utils.performance.cache_manager import setup_cache_cleanup_task, ReferenceDataCache
    setup_cache_cleanup_task()
    
    # 預熱快取（可選）
    try:
        ReferenceDataCache.warm_cache()
    except Exception as e:
        app.logger.warning(f"Failed to warm cache on startup: {str(e)}")
    
    # 初始化第三階段優化功能
    try:
        from utils.performance.advanced_cache import init_cache_warming
        from utils.performance.performance_benchmarking import init_performance_benchmarking
        from utils.performance.memory_optimizer import init_memory_optimization
        from utils.database.query_optimizer import init_query_optimization
        from utils.performance.alert_system import init_alert_system
        
        # 初始化進階快取
        init_cache_warming()
        
        # 初始化性能基準測試
        init_performance_benchmarking()
        
        # 初始化記憶體優化
        init_memory_optimization()
        
        # 初始化查詢優化
        init_query_optimization()
        
        # 初始化告警系統
        init_alert_system()
        
        # 初始化資料庫維護系統
        from utils.db_maintenance import init_database_maintenance
        init_database_maintenance()
        
        app.logger.info("第三階段優化功能已全部啟用")
        
    except Exception as e:
        app.logger.error(f"第三階段優化初始化失敗: {str(e)}")
    
    # 設置全域錯誤處理
    from utils.logging.error_handler import setup_global_error_handlers
    setup_global_error_handlers(app)
    
    # 設置錯誤監控
    from utils.logging.error_monitor import setup_error_monitoring
    setup_error_monitoring(app)
    
    # 設置CSRF錯誤處理
    from flask_wtf.csrf import CSRFError
    @app.errorhandler(CSRFError)
    def handle_csrf_error(e):
        app.logger.warning(f"CSRF錯誤: {str(e)}, URL: {request.url}, 方法: {request.method}")
        
        # 如果是AJAX請求，返回JSON
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({
                'error': 'CSRF驗證失敗，請重新整理頁面後再試。',
                'code': 'CSRF_ERROR'
            }), 400
            
        # 重定向到相同URL的GET請求（用於表單提交）
        if request.method == 'POST':
            flash('安全驗證失敗，請重新提交表單。', 'error')
            return redirect(request.url)
            
        # 一般錯誤頁面
        return render_template('error.html', 
                             error_message='CSRF驗證失敗，這可能是由於表單已過期或安全令牌不匹配。請重新整理頁面後再試。',
                             correlation_id=getattr(g, 'correlation_id', None)), 400
    
    # 設置安全監控
    from utils.security.security_monitor import setup_security_monitoring
    setup_security_monitoring(app)
    
    # API 路由已移至 routes/api.py
    
    # 管理員路由已移至 routes/admin.py
    

    

    
    # 資金轉移路由已移至 routes/transfer.py
    
    # 銀行借款路由已移至 routes/bankloan.py

    # 資金記錄路由已移至 routes/fund_record.py



    # 帳戶明細路由已移至 routes/account.py
    
    return app


if __name__ == '__main__':
    app = create_app()
    # 只在開發環境啟用調試模式
    debug_mode = app.config.get('DEBUG', False)
    # 設置密鑰以確保CSRF令牌正常工作
    if not app.secret_key or app.secret_key == 'dev':
        app.secret_key = app.config.get('SECRET_KEY', secrets.token_hex(32))
    app.run(debug=debug_mode, port=5002, host='127.0.0.1')