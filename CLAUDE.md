# 印錢大師 - 會計系統專案

## 專案概述
這是一個基於 Flask 的完整會計管理系統，提供收支管理、財務報表、薪資管理等功能。一定要回答中文，任何要刪除檔案或者目錄都要問過我，不要自己做決定。

## 技術棧
- **後端**: Flask, SQLAlchemy, PostgreSQL
- **前端**: Bulma CSS, Jinja2 模板
- **認證**: Flask-Login, 自訂權限系統
- **效能**: Redis 快取, 性能監控系統

## 重要檔案說明

### 核心檔案
- `main.py` - 應用程式入口點
- `model.py` - 主要資料模型
- `database.py` - 資料庫連線設定
- `config/config.py` - 應用程式配置

### 前端模板
- `templates/sidebar.html` - 主要導航側邊欄（被 80+ 頁面使用）
- `templates/index.html` - 主頁面模板
- `templates/overview.html` - 總覽儀表板

### 路由模組 (routes/)
- `main.py` - 主要路由
- `income_expense.py` - 收支管理
- `reports.py` - 報表功能
- `auth.py` - 認證系統
- `permission_admin.py` - 權限管理

### 服務層 (services/)
- `balance_sheet_service.py` - 資產負債表服務
- `dashboard_service.py` - 儀表板服務
- `auth_service.py` - 認證服務

## 開發指引

### 啟動專案
```bash
python main.py
```

### 資料庫遷移
```bash
alembic upgrade head
```

### 程式碼檢查
如需執行程式碼檢查，請提供以下指令：
- Lint 檢查: `npm run lint` 或 `ruff`
- 型別檢查: `npm run typecheck`

## 注意事項

### 檔案管理
- **重要**: 所有刪除檔案或目錄的操作都必須先詢問並獲得確認
- 不再使用的檔案應移至 `trash/` 目錄保存，而非直接刪除

### 樣式規範
- 使用 Bulma CSS 框架
- 側邊欄使用深色主題 (#2d3748)
- 避免在檔案中添加表情符號，除非明確要求

### 安全性
- 不要在程式碼中暴露密碼或金鑰
- 使用環境變數管理敏感資訊
- 遵循防禦性安全原則

## 專案結構
```
/accounting/
├── main.py              # 主程式
├── model.py             # 資料模型
├── database.py          # 資料庫設定
├── routes/              # 路由模組
├── services/            # 服務層
├── templates/           # HTML 模板
├── static/              # 靜態檔案
├── utils/               # 工具函式
├── scripts/             # 維護腳本
├── tests/               # 測試檔案
├── docs/                # 文件
└── trash/               # 已廢棄檔案（保留）
```

## 背景程序
系統可能有多個 `python main.py` 背景程序運行，可使用 BashOutput 工具查看其輸出。

## 聯絡資訊
如有問題，請參考 docs/ 目錄下的詳細文件。
