from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, scoped_session
from contextlib import contextmanager
from config.config import Config
import time
import logging
import os

# 設置慢查詢日誌
if not os.path.exists('logs'):
    os.makedirs('logs')

slow_query_logger = logging.getLogger('slow_query')
slow_query_handler = logging.FileHandler('logs/slow_queries.log')
slow_query_formatter = logging.Formatter(
    '%(asctime)s - SLOW QUERY - %(message)s'
)
slow_query_handler.setFormatter(slow_query_formatter)
slow_query_logger.addHandler(slow_query_handler)
slow_query_logger.setLevel(logging.WARNING)

# 主日誌
main_logger = logging.getLogger(__name__)

# 建立資料庫引擎（優化版 - 調整為單進程配置）
engine = create_engine(
    Config.get_database_uri(),
    pool_size=20,          # 優化：減少基本連接數（適合單進程）
    max_overflow=30,       # 優化：減少溢出連接數
    pool_timeout=30,       # 優化：縮短連接等待時間
    pool_recycle=3600,     # 優化：縮短連接回收時間（1小時）
    pool_pre_ping=True,    # 優化：連接驗證，自動處理斷線
    echo=False             # 生產環境關閉 SQL 日誌
)

# 建立 session 工廠
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 建立 scoped session
db_session = scoped_session(SessionLocal)

# 向後相容性別名
Session = SessionLocal

@contextmanager
def get_db():
    """資料庫連線的 context manager"""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()

# 慢查詢監控事件處理器
@event.listens_for(engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    context._query_start_time = time.time()

@event.listens_for(engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - context._query_start_time
    
    # 記錄慢查詢 (超過 200ms)
    if total > 0.2:
        # 清理查詢語句以便記錄
        clean_statement = statement.replace('\n', ' ').replace('\t', ' ')
        while '  ' in clean_statement:
            clean_statement = clean_statement.replace('  ', ' ')
        
        slow_query_logger.warning(
            f"執行時間: {total:.3f}s | 查詢: {clean_statement[:200]}{'...' if len(clean_statement) > 200 else ''}"
        )
        
        # 超過 1 秒的查詢額外記錄到主日誌
        if total > 1.0:
            main_logger.error(f"超慢查詢 ({total:.3f}s): {clean_statement[:100]}...")
    
    # 記錄查詢統計（超過 50ms 記錄到 DEBUG）
    elif total > 0.05:
        main_logger.debug(f"查詢耗時: {total:.3f}s")

def init_db():
    """初始化資料庫"""
    from model import Base
    Base.metadata.create_all(engine)
    main_logger.info("資料庫初始化完成，慢查詢監控已啟用") 