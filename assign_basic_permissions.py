#!/usr/bin/env python3
"""
為一般用戶分配基本權限
讓一般用戶能正常使用系統的基本功能，但不包括管理功能
"""
import sys
import os

# 添加專案路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models.auth_models import Role, Permission
from services.auth_service import RoleService

def assign_basic_permissions_to_user_role():
    """為user角色分配基本權限"""
    print("=== 為一般用戶分配基本權限 ===\n")
    
    with get_db() as db:
        # 獲取user角色
        user_role = db.query(Role).filter(Role.name == 'user').first()
        if not user_role:
            print("錯誤：找不到user角色")
            return
        
        print(f"找到user角色: {user_role.display_name} (ID: {user_role.id})")
        
        # 定義一般用戶應該有的基本權限（不包括刪除和管理權限）
        basic_permissions = [
            # 收支管理 - 基本操作
            ('income_expense', 'view'),
            ('income_expense', 'create'), 
            ('income_expense', 'edit'),
            
            # 會計科目 - 查看
            ('accounting', 'view'),
            
            # 報表 - 查看和匯出
            ('reports', 'view'),
            ('reports', 'export'),
            
            # 資產管理 - 查看和基本操作
            ('asset_management', 'view'),
            ('asset_management', 'create'),
            ('asset_management', 'edit'),
            
            # 薪資報酬 - 查看和基本操作  
            ('payroll', 'view'),
            ('payroll', 'create'),
            ('payroll', 'edit'),
            
            # 勞務報酬 - 查看和基本操作
            ('service_reward', 'view'),
            ('service_reward', 'create'),
            ('service_reward', 'edit'),
            
            # 資金管理 - 基本操作
            ('fund_management', 'view'),
            ('fund_management', 'edit'),
        ]
        
        print(f"準備分配 {len(basic_permissions)} 個基本權限...")
        
        # 查找符合條件的權限
        permission_ids = []
        found_permissions = []
        
        for module, action in basic_permissions:
            permission = db.query(Permission).filter(
                Permission.module == module,
                Permission.action == action
            ).first()
            
            if permission:
                permission_ids.append(permission.id)
                found_permissions.append(f"{module}.{action}")
                print(f"  ✓ 找到權限: {permission.display_name} ({module}.{action})")
            else:
                print(f"  ✗ 未找到權限: {module}.{action}")
        
        print(f"\n找到 {len(permission_ids)} 個有效權限")
        
        if permission_ids:
            # 分配權限給user角色
            RoleService.assign_permissions_to_role(user_role.id, permission_ids)
            print(f"\n✓ 成功為user角色分配了 {len(permission_ids)} 個權限")
            
            print("\n分配的權限列表：")
            for perm in found_permissions:
                print(f"  - {perm}")
        else:
            print("沒有找到任何有效權限")

def verify_user_permissions():
    """驗證一般用戶權限"""
    print("\n=== 驗證一般用戶權限 ===\n")
    
    from model import User
    from services.auth_service import AuthService
    
    with get_db() as db:
        # 找一個一般用戶來測試
        test_user = db.query(User).filter(
            User.is_active == True,
            User.is_tenant_admin == False
        ).first()
        
        if test_user:
            print(f"測試用戶: {test_user.username}")
            
            # 檢查權限
            permissions = AuthService.get_user_permissions(test_user.id)
            print(f"權限數量: {len(permissions)}")
            
            if permissions:
                print("權限列表：")
                for perm in sorted(permissions):
                    print(f"  - {perm}")
            
            # 檢查可存取模組
            modules = AuthService.get_user_modules(test_user.id)
            print(f"\n可存取模組: {modules}")
            
        else:
            print("沒有找到一般用戶")

def main():
    """主要執行函式"""
    try:
        assign_basic_permissions_to_user_role()
        verify_user_permissions()
        
        print("\n>>> 基本權限分配完成！現在一般用戶應該能使用基本功能，但無法使用管理功能。")
        
    except Exception as e:
        print(f"執行過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()