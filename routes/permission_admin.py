from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from services.auth_service import AuthService, RoleService
from services.menu_service import MenuService
from utils.security.auth_decorators import admin_required
from utils.security.permission_helpers import (
    PermissionDataManager, render_admin_template, success_redirect, error_redirect,
    handle_form_submission, api_success, api_error, safe_file_operation
)
from database import get_db
from models.auth_models import Role, Permission
from model import User, Money

permission_admin_bp = Blueprint('permission_admin', __name__, url_prefix='/admin/permissions')

@permission_admin_bp.route('/')
@admin_required
def index():
    """權限管理首頁"""
    overview_data = PermissionDataManager.get_system_overview_data()
    return render_admin_template('admin/permissions/index.html', **overview_data)

@permission_admin_bp.route('/roles')
@admin_required
def roles():
    """角色管理"""
    with get_db() as db:
        # 轉換為字典避免 DetachedInstanceError
        roles_data = []
        for role in db.query(Role).filter(Role.is_active).all():
            roles_data.append({
                'id': role.id,
                'name': role.name,
                'display_name': role.display_name,
                'description': role.description,
                'is_active': role.is_active,
                'created_at': role.created_at,
                'updated_at': role.updated_at
            })
    
    return render_template('admin/permissions/roles.html', roles=roles_data)

@permission_admin_bp.route('/roles/create', methods=['GET', 'POST'])
@admin_required
def create_role():
    """建立角色"""
    if request.method == 'POST':
        name = request.form.get('name')
        display_name = request.form.get('display_name')
        description = request.form.get('description')
        
        def create_role_callback(form_data):
            RoleService.create_role(form_data['name'], form_data['display_name'], form_data.get('description', ''))
        
        form_data = {'name': name, 'display_name': display_name, 'description': description}
        return handle_form_submission(
            form_data, 
            ['name', 'display_name'],
            create_role_callback,
            f'角色 "{display_name}" 建立成功',
            'permission_admin.roles',
            'admin/permissions/create_role.html'
        )
    
    return render_admin_template('admin/permissions/create_role.html')

@permission_admin_bp.route('/roles/<int:role_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_role(role_id):
    """編輯角色"""
    with get_db() as db:
        role = db.query(Role).filter(Role.id == role_id).first()
        if not role:
            flash('角色不存在', 'error')
            return redirect(url_for('permission_admin.roles'))
        
        if request.method == 'POST':
            name = request.form.get('name')
            display_name = request.form.get('display_name')
            description = request.form.get('description')
            is_active = request.form.get('is_active') == 'on'
            
            if not name or not display_name:
                flash('角色名稱和顯示名稱為必填項', 'error')
            else:
                try:
                    # 更新角色資料
                    role.name = name
                    role.display_name = display_name
                    role.description = description
                    role.is_active = is_active
                    db.commit()
                    
                    flash(f'角色 "{display_name}" 更新成功', 'success')
                    return redirect(url_for('permission_admin.roles'))
                except Exception as e:
                    db.rollback()
                    flash(f'更新角色失敗: {str(e)}', 'error')
        
        # 轉換為字典避免 DetachedInstanceError
        role_data = {
            'id': role.id,
            'name': role.name,
            'display_name': role.display_name,
            'description': role.description,
            'is_active': role.is_active,
            'created_at': role.created_at,
            'updated_at': role.updated_at
        }
    
    return render_template('admin/permissions/edit_role.html', role=role_data)

@permission_admin_bp.route('/roles/<int:role_id>/permissions', methods=['GET', 'POST'])
@admin_required
def role_permissions(role_id):
    """角色權限設定"""
    with get_db() as db:
        role = db.query(Role).filter(Role.id == role_id).first()
        if not role:
            flash('角色不存在', 'error')
            return redirect(url_for('permission_admin.roles'))
        
        if request.method == 'POST':
            permission_ids = request.form.getlist('permissions')
            permission_ids = [int(pid) for pid in permission_ids if pid.isdigit()]
            
            try:
                RoleService.assign_permissions_to_role(role_id, permission_ids)
                flash(f'角色 "{role.display_name}" 權限更新成功', 'success')
                return redirect(url_for('permission_admin.roles'))
            except Exception as e:
                flash(f'更新權限失敗: {str(e)}', 'error')
        
        # 轉換為字典避免 DetachedInstanceError
        role_data = {
            'id': role.id,
            'name': role.name,
            'display_name': role.display_name,
            'description': role.description
        }
        
        all_permissions = db.query(Permission).all()
        
        # 獲取角色現有權限ID
        from models.auth_models import role_permissions
        role_permission_results = db.query(role_permissions.c.permission_id).filter(
            role_permissions.c.role_id == role_id
        ).all()
        role_permission_ids = [rp[0] for rp in role_permission_results if rp and len(rp) > 0]
        
        # 按模組分組權限，轉換為字典
        permissions_by_module = {}
        for perm in all_permissions:
            if perm.module not in permissions_by_module:
                permissions_by_module[perm.module] = []
            permissions_by_module[perm.module].append({
                'id': perm.id,
                'name': perm.name,
                'display_name': perm.display_name,
                'module': perm.module,
                'action': perm.action,
                'description': perm.description
            })
    
    return render_template('admin/permissions/role_permissions.html', 
                         role=role_data, 
                         permissions_by_module=permissions_by_module,
                         role_permission_ids=role_permission_ids)

@permission_admin_bp.route('/users/<int:user_id>/roles', methods=['GET', 'POST'])
@admin_required
def user_roles(user_id):
    """用戶角色設定"""
    with get_db() as db:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            flash('用戶不存在', 'error')
            return redirect(url_for('permission_admin.index'))
        
        if request.method == 'POST':
            role_ids = request.form.getlist('roles')
            role_ids = [int(rid) for rid in role_ids if rid.isdigit()]
            
            try:
                RoleService.assign_role_to_user(user_id, role_ids)
                flash(f'用戶 "{user.username}" 角色更新成功', 'success')
                return redirect(url_for('permission_admin.index'))
            except Exception as e:
                flash(f'更新角色失敗: {str(e)}', 'error')
        
        # 轉換為字典避免 DetachedInstanceError
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': user.full_name,
            'is_active': user.is_active
        }
        
        all_roles_data = []
        for role in db.query(Role).filter(Role.is_active).all():
            all_roles_data.append({
                'id': role.id,
                'name': role.name,
                'display_name': role.display_name,
                'description': role.description
            })
        
        # 獲取用戶現有角色ID
        from models.auth_models import user_roles
        user_role_results = db.query(user_roles.c.role_id).filter(
            user_roles.c.user_id == user_id
        ).all()
        user_role_ids = [ur[0] for ur in user_role_results if ur and len(ur) > 0]
    
    return render_template('admin/permissions/user_roles.html', 
                         user=user_data, 
                         all_roles=all_roles_data,
                         user_role_ids=user_role_ids)

@permission_admin_bp.route('/api/user-permissions/<int:user_id>')
@admin_required
def api_user_permissions(user_id):
    """API: 獲取用戶權限"""
    permissions = AuthService.get_user_permissions(user_id)
    modules = AuthService.get_user_modules(user_id)
    
    return api_success({
        'permissions': permissions,
        'modules': modules
    })

@permission_admin_bp.route('/preview-menu/<int:user_id>')
@admin_required
def preview_user_menu(user_id):
    """預覽用戶選單"""
    with get_db() as db:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return error_redirect('用戶不存在', 'permission_admin.index')
        
        # 轉換為字典避免 DetachedInstanceError
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': user.full_name,
            'is_active': user.is_active
        }
    
    user_menu = MenuService.get_user_menu(user_id)
    
    return render_template('admin/permissions/preview_menu.html', 
                         user=user_data, 
                         user_menu=user_menu)

@permission_admin_bp.route('/system-info')
@admin_required
def system_info():
    """系統資訊頁面"""
    import sys
    import platform
    import psutil
    from datetime import datetime
    import os
    
    # 收集系統資訊
    system_data = {
        # 系統基本資訊
        'python_version': sys.version,
        'platform': platform.platform(),
        'architecture': platform.architecture()[0],
        'processor': platform.processor(),
        'hostname': platform.node(),
        
        # 記憶體資訊
        'memory_total': psutil.virtual_memory().total,
        'memory_available': psutil.virtual_memory().available,
        'memory_percent': psutil.virtual_memory().percent,
        
        # 磁碟資訊
        'disk_total': psutil.disk_usage('/').total,
        'disk_used': psutil.disk_usage('/').used,
        'disk_free': psutil.disk_usage('/').free,
        'disk_percent': psutil.disk_usage('/').percent,
        
        # 應用程式資訊
        'app_start_time': datetime.now(),
        'database_path': os.path.abspath('app.db'),
        'database_size': os.path.getsize('app.db') if os.path.exists('app.db') else 0,
    }
    
    # 資料庫統計
    with get_db() as db:
        from models.auth_models import Role, Permission
        
        db_stats = {
            'users_count': db.query(User).count(),
            'active_users': db.query(User).filter(User.is_active).count(),
            'roles_count': db.query(Role).count(),
            'permissions_count': db.query(Permission).count(),
            'money_records': db.query(Money).count(),
        }
    
    return render_template('admin/system_info.html', 
                        system_data=system_data,
                        db_stats=db_stats)

@permission_admin_bp.route('/logs')
@admin_required  
def logs():
    """日誌管理頁面"""
    import os
    import glob
    from datetime import datetime
    
    logs_data = []
    logs_dir = 'logs'
    
    if os.path.exists(logs_dir):
        log_files = glob.glob(os.path.join(logs_dir, '*.log'))
        for log_file in log_files:
            stat = os.stat(log_file)
            logs_data.append({
                'name': os.path.basename(log_file),
                'path': log_file,
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime),
            })
    
    return render_template('admin/logs.html', logs_data=logs_data)

@permission_admin_bp.route('/backup')
@admin_required
def backup():
    """備份管理頁面"""
    import os
    import glob
    from datetime import datetime
    
    backups_data = []
    backups_dir = 'backups'
    
    # 確保備份目錄存在
    if not os.path.exists(backups_dir):
        os.makedirs(backups_dir)
    
    if os.path.exists(backups_dir):
        backup_files = glob.glob(os.path.join(backups_dir, '*.db'))
        for backup_file in backup_files:
            stat = os.stat(backup_file)
            backups_data.append({
                'name': os.path.basename(backup_file),
                'path': backup_file,
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime),
            })
    
    return render_template('admin/backup.html', backups_data=backups_data)

@permission_admin_bp.route('/backup/create', methods=['POST'])
@admin_required
def create_backup():
    """建立備份"""
    import os
    import shutil
    from datetime import datetime
    
    backup_filename = None
    
    def backup_operation():
        nonlocal backup_filename
        # 確保備份目錄存在
        backups_dir = 'backups'
        if not os.path.exists(backups_dir):
            os.makedirs(backups_dir)
        
        # 生成備份檔案名稱
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'app_backup_{timestamp}.db'
        backup_path = os.path.join(backups_dir, backup_filename)
        
        # 複製資料庫檔案
        if os.path.exists('app.db'):
            shutil.copy2('app.db', backup_path)
        else:
            raise FileNotFoundError('找不到資料庫檔案')
    
    return safe_file_operation(
        backup_operation,
        f'備份建立成功: {backup_filename}' if backup_filename else '備份建立成功',
        '備份建立失敗',
        'permission_admin.backup'
    )

@permission_admin_bp.route('/backup/download/<filename>')
@admin_required
def download_backup(filename):
    """下載備份檔案"""
    import os
    from flask import send_file
    
    backups_dir = 'backups'
    file_path = os.path.join(backups_dir, filename)
    
    if os.path.exists(file_path) and filename.endswith('.db'):
        return send_file(file_path, as_attachment=True, download_name=filename)
    else:
        flash('備份檔案不存在', 'error')
        return redirect(url_for('permission_admin.backup'))

@permission_admin_bp.route('/backup/delete/<filename>', methods=['POST'])
@admin_required
def delete_backup(filename):
    """刪除備份檔案"""
    import os
    
    try:
        backups_dir = 'backups'
        file_path = os.path.join(backups_dir, filename)
        
        if os.path.exists(file_path) and filename.endswith('.db'):
            os.remove(file_path)
            flash(f'備份檔案 {filename} 已刪除', 'success')
        else:
            flash('備份檔案不存在', 'error')
    
    except Exception as e:
        flash(f'刪除備份失敗: {str(e)}', 'error')
    
    return redirect(url_for('permission_admin.backup'))

@permission_admin_bp.route('/backup/restore/<filename>', methods=['POST'])
@admin_required
def restore_backup(filename):
    """還原備份"""
    import os
    import shutil
    from datetime import datetime
    
    try:
        backups_dir = 'backups'
        backup_path = os.path.join(backups_dir, filename)
        
        if not os.path.exists(backup_path):
            flash('備份檔案不存在', 'error')
            return redirect(url_for('permission_admin.backup'))
        
        # 先備份當前資料庫
        if os.path.exists('app.db'):
            current_backup = f'app_current_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
            shutil.copy2('app.db', os.path.join(backups_dir, current_backup))
        
        # 還原備份
        shutil.copy2(backup_path, 'app.db')
        flash(f'資料庫已從 {filename} 還原成功', 'success')
        
    except Exception as e:
        flash(f'還原備份失敗: {str(e)}', 'error')
    
    return redirect(url_for('permission_admin.backup'))