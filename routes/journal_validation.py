"""
會計分錄驗證管理路由
提供分錄驗證和檢查功能
"""
from flask import Blueprint, render_template, request, jsonify
from sqlalchemy.orm import sessionmaker
from model import Money, engine
from services.journal_validator import JournalValidator, JournalValidationError
from data.menu_data import menu
from utils.tenant_utils import require_tenant_access, add_tenant_filter, get_current_tenant_id

Session = sessionmaker(bind=engine)
journal_validation_bp = Blueprint('journal_validation', __name__)


@journal_validation_bp.route('/journal_validation')
@require_tenant_access
def journal_validation_page():
    """分錄驗證管理頁面"""
    db = Session()
    try:
        # 獲取所有有 journal_reference 的記錄
        journal_refs = db.query(Money.journal_reference).filter(
            Money.journal_reference.isnot(None)
        ).distinct().all()
        
        validator = JournalValidator(db)
        validation_results = []
        
        for (journal_ref,) in journal_refs:
            try:
                result = validator.validate_journal_entries(journal_ref)
                validation_results.append(result)
            except JournalValidationError as e:
                validation_results.append({
                    'journal_reference': journal_ref,
                    'entry_count': 0,
                    'debit_total': 0,
                    'credit_total': 0,
                    'is_balanced': False,
                    'errors': [str(e)],
                    'warnings': []
                })
        
        # 統計資訊
        total_journals = len(validation_results)
        balanced_journals = sum(1 for r in validation_results if r['is_balanced'])
        error_journals = sum(1 for r in validation_results if r['errors'])
        
        stats = {
            'total_journals': total_journals,
            'balanced_journals': balanced_journals,
            'unbalanced_journals': total_journals - balanced_journals,
            'error_journals': error_journals,
            'balance_rate': (balanced_journals / total_journals * 100) if total_journals > 0 else 0
        }
        
        return render_template('journal_validation.html',
                             sidebar_items=menu,
                             selected='分錄驗證',
                             validation_results=validation_results,
                             stats=stats)
    
    finally:
        db.close()


@journal_validation_bp.route('/api/validate_journal/<journal_reference>')
@require_tenant_access
def validate_single_journal(journal_reference):
    """驗證單一分錄的API"""
    db = Session()
    try:
        validator = JournalValidator(db)
        result = validator.validate_journal_entries(journal_reference)
        return jsonify(result)
    
    except JournalValidationError as e:
        return jsonify({
            'error': str(e),
            'journal_reference': journal_reference
        }), 400
    
    finally:
        db.close()


@journal_validation_bp.route('/api/journal_summary/<journal_reference>')
@require_tenant_access
def get_journal_summary(journal_reference):
    """獲取分錄摘要的API"""
    db = Session()
    try:
        validator = JournalValidator(db)
        summary = validator.get_journal_summary(journal_reference)
        
        if summary is None:
            return jsonify({'error': '找不到指定的分錄'}), 404
        
        return jsonify(summary)
    
    finally:
        db.close()


@journal_validation_bp.route('/api/validate_all_journals')
@require_tenant_access
def validate_all_journals():
    """驗證所有分錄的API"""
    db = Session()
    try:
        # 獲取所有分錄參考號
        journal_refs = db.query(Money.journal_reference).filter(
            Money.journal_reference.isnot(None)
        ).distinct().all()
        
        validator = JournalValidator(db)
        results = []
        
        for (journal_ref,) in journal_refs:
            try:
                result = validator.validate_journal_entries(journal_ref)
                results.append(result)
            except JournalValidationError as e:
                results.append({
                    'journal_reference': journal_ref,
                    'is_balanced': False,
                    'errors': [str(e)]
                })
        
        # 統計
        total = len(results)
        balanced = sum(1 for r in results if r['is_balanced'])
        
        return jsonify({
            'total_journals': total,
            'balanced_journals': balanced,
            'unbalanced_journals': total - balanced,
            'balance_rate': (balanced / total * 100) if total > 0 else 0,
            'results': results
        })
    
    finally:
        db.close()


@journal_validation_bp.route('/journal_detail/<journal_reference>')
@require_tenant_access
def journal_detail(journal_reference):
    """分錄詳細頁面"""
    db = Session()
    try:
        validator = JournalValidator(db)
        
        # 獲取驗證結果
        validation_result = validator.validate_journal_entries(journal_reference)
        
        # 獲取摘要資訊
        summary = validator.get_journal_summary(journal_reference)
        
        # 獲取原始記錄
        entries = db.query(Money).filter(
            Money.journal_reference == journal_reference
        ).order_by(Money.entry_side.desc()).all()  # DEBIT 在前
        
        return render_template('journal_detail.html',
                             sidebar_items=menu,
                             selected='分錄詳細',
                             journal_reference=journal_reference,
                             validation_result=validation_result,
                             summary=summary,
                             entries=entries)
    
    except JournalValidationError as e:
        return render_template('error.html',
                             sidebar_items=menu,
                             selected='錯誤',
                             error_message=f'分錄驗證錯誤：{str(e)}')
    
    finally:
        db.close()
