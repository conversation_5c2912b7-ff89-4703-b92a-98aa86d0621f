from flask import Blueprint, render_template, request, redirect, url_for, flash
from data.menu_data import menu
from model import Account, Department, Project, ServiceReward
from database import get_db
import os
from werkzeug.utils import secure_filename
from datetime import datetime

service_reward_bp = Blueprint('service_reward', __name__)

@service_reward_bp.route('/service_reward_list', methods=['GET'])
def service_reward_list():
    """勞務報酬列表"""
    with get_db() as db:
        # 查詢所有服務報酬記錄，並加入關聯資料
        query_results = db.query(ServiceReward)\
            .outerjoin(Account, ServiceReward.payment_account_id == Account.id)\
            .outerjoin(Department, ServiceReward.department_id == Department.id)\
            .outerjoin(Project, ServiceReward.project_id == Project.id)\
            .add_columns(
                Account.name.label('account_name'),
                Department.name.label('department_name'),
                Project.name.label('project_name')
            )\
            .order_by(ServiceReward.create_date.desc())\
            .all()

        # 將資料轉換為字典格式，避免 DetachedInstanceError
        service_rewards_data = []
        for result in query_results:
            reward = result[0]  # ServiceReward 物件
            account_name = result[1]  # Account.name
            department_name = result[2]  # Department.name
            project_name = result[3]  # Project.name

            reward_data = {
                'id': reward.id,
                'form_type': reward.form_type,
                'create_date': reward.create_date,
                'name': reward.name,
                'email': reward.email,
                'phone': reward.phone,
                'service_content': reward.service_content,
                'declaration_type': reward.declaration_type,
                'business_category': reward.business_category,
                'amount': reward.amount,
                'status': reward.status,
                'owner_passbook': reward.owner_passbook,
                'owner_id_front': reward.owner_id_front,
                'owner_id_back': reward.owner_id_back,
                'created_at': reward.created_at,
                'updated_at': reward.updated_at,
                'account_name': account_name,
                'department_name': department_name,
                'project_name': project_name
            }
            service_rewards_data.append(reward_data)

    main_menu = list(menu.keys())
    selected = '勞務報酬'
    return render_template('service_reward_list.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         service_rewards=service_rewards_data)

@service_reward_bp.route('/add_service_reward', methods=['GET', 'POST'])
def add_service_reward():
    """建立勞報單"""
    if request.method == 'POST':
        try:
            # 獲取表單類型
            form_type = request.form.get('form_type', 'company')
            action = request.form.get('action', 'submit')

            # 基本資料
            create_date = request.form.get('create_date')
            name = request.form.get('name')
            email = request.form.get('email')
            phone = request.form.get('phone')

            # 身份資訊
            identity_type = request.form.get('identity_type')
            no_health_insurance = request.form.get('no_health_insurance')

            # 勞務資訊
            service_content = request.form.get('service_content')
            declaration_type = request.form.get('declaration_type')
            business_category = request.form.get('business_category')
            confirmation_method = request.form.get('confirmation_method')
            service_start_date = request.form.get('service_start_date')
            service_end_date = request.form.get('service_end_date')

            # 付款資訊
            payment_account_id = request.form.get('payment_account')
            payment_method = request.form.get('payment_method')
            amount = request.form.get('amount')
            is_actual_amount = request.form.get('is_actual_amount')

            # 所有人提供資料
            owner_bank_code = request.form.get('owner_bank_code')
            owner_bank_account = request.form.get('owner_bank_account')
            owner_account_name = request.form.get('owner_account_name')

            # 其他資訊
            department_id = request.form.get('department_id')
            project_id = request.form.get('project_id')
            notice = request.form.get('notice')
            note = request.form.get('note')

            # 處理公司填寫特有欄位
            id_number = None
            address = None
            if form_type == 'company':
                id_number = request.form.get('id_number')
                address = request.form.get('address')

            # 處理檔案上傳
            upload_folder = 'static/uploads/service_reward'
            os.makedirs(upload_folder, exist_ok=True)

            uploaded_files = {}
            for file_field in ['owner_passbook', 'owner_id_front', 'owner_id_back']:
                if file_field in request.files:
                    file = request.files[file_field]
                    if file and file.filename:
                        filename = secure_filename(file.filename)
                        file_path = os.path.join(upload_folder, filename)
                        file.save(file_path)
                        uploaded_files[file_field] = filename

            # 儲存資料到資料庫
            with get_db() as db:
                service_reward = ServiceReward(
                    # 基本資料
                    form_type=form_type,
                    create_date=datetime.strptime(create_date, '%Y-%m-%d').date() if create_date else datetime.now().date(),
                    name=name,
                    email=email,
                    phone=phone,

                    # 公司填寫特有欄位
                    id_number=id_number,
                    address=address,

                    # 身份資訊
                    identity_type=identity_type,
                    no_health_insurance=no_health_insurance,

                    # 勞務資訊
                    service_content=service_content,
                    declaration_type=declaration_type,
                    business_category=business_category,
                    confirmation_method=confirmation_method,
                    service_start_date=datetime.strptime(service_start_date, '%Y-%m-%d').date() if service_start_date else None,
                    service_end_date=datetime.strptime(service_end_date, '%Y-%m-%d').date() if service_end_date else None,

                    # 付款資訊
                    payment_account_id=int(payment_account_id) if payment_account_id else None,
                    payment_method=payment_method,
                    amount=int(amount) if amount else 0,
                    is_actual_amount=is_actual_amount == 'on',

                    # 所有人提供資料
                    owner_bank_code=owner_bank_code,
                    owner_bank_account=owner_bank_account,
                    owner_account_name=owner_account_name,

                    # 檔案上傳
                    owner_passbook=uploaded_files.get('owner_passbook'),
                    owner_id_front=uploaded_files.get('owner_id_front'),
                    owner_id_back=uploaded_files.get('owner_id_back'),

                    # 其他資訊
                    department_id=int(department_id) if department_id else None,
                    project_id=int(project_id) if project_id else None,
                    notice=notice,
                    note=note,

                    # 狀態
                    status='draft' if action == 'save_exit' else 'submitted'
                )

                db.add(service_reward)
                db.commit()

                if action == 'save_exit':
                    flash(f'勞務報酬單已暫存（{form_type}填寫）！ID: {service_reward.id}', 'success')
                else:
                    flash(f'勞務報酬單建立完成（{form_type}填寫）！ID: {service_reward.id}', 'success')

            return redirect(url_for('service_reward.service_reward_list'))

        except Exception as e:
            flash(f'處理失敗：{str(e)}', 'error')

    # GET 請求 - 使用通用函數獲取下拉選單資料
    from main import get_dropdown_data
    dropdown_data = get_dropdown_data()
    accounts_data = dropdown_data['accounts']
    departments_data = dropdown_data['departments']
    projects_data = dropdown_data['projects']

    main_menu = list(menu.keys())
    selected = '勞務報酬'
    return render_template('add_service_reward.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         accounts=accounts_data,
                         departments=departments_data,
                         projects=projects_data)


@service_reward_bp.route('/edit_service_reward/<int:reward_id>', methods=['GET', 'POST'])
def edit_service_reward(reward_id):
    """編輯勞務報酬單"""
    with get_db() as db:
        # 查詢要編輯的記錄
        service_reward = db.query(ServiceReward).filter_by(id=reward_id).first()
        if not service_reward:
            flash('找不到指定的勞務報酬記錄！', 'error')
            return redirect(url_for('service_reward.service_reward_list'))

        if request.method == 'POST':
            try:
                # 更新記錄
                service_reward.name = request.form.get('name')
                service_reward.email = request.form.get('email')
                service_reward.phone = request.form.get('phone')
                service_reward.service_content = request.form.get('service_content')
                service_reward.declaration_type = request.form.get('declaration_type')
                service_reward.business_category = request.form.get('business_category')
                service_reward.confirmation_method = request.form.get('confirmation_method')

                # 更新日期欄位
                service_start_date = request.form.get('service_start_date')
                if service_start_date:
                    service_reward.service_start_date = datetime.strptime(service_start_date, '%Y-%m-%d').date()

                service_end_date = request.form.get('service_end_date')
                if service_end_date:
                    service_reward.service_end_date = datetime.strptime(service_end_date, '%Y-%m-%d').date()

                # 更新金額
                amount = request.form.get('amount')
                if amount:
                    service_reward.amount = int(amount)

                # 更新其他欄位
                service_reward.payment_account_id = int(request.form.get('payment_account')) if request.form.get('payment_account') else None
                service_reward.department_id = int(request.form.get('department_id')) if request.form.get('department_id') else None
                service_reward.project_id = int(request.form.get('project_id')) if request.form.get('project_id') else None
                service_reward.notice = request.form.get('notice')
                service_reward.note = request.form.get('note')

                db.commit()
                flash('勞務報酬單更新成功！', 'success')
                return redirect(url_for('service_reward.service_reward_list'))

            except Exception as e:
                flash(f'更新失敗：{str(e)}', 'error')

        # GET 請求 - 載入編輯表單
        # 將資料轉換為字典格式
        reward_data = {
            'id': service_reward.id,
            'form_type': service_reward.form_type,
            'create_date': service_reward.create_date.strftime('%Y-%m-%d') if service_reward.create_date else '',
            'name': service_reward.name or '',
            'email': service_reward.email or '',
            'phone': service_reward.phone or '',
            'id_number': service_reward.id_number or '',
            'address': service_reward.address or '',
            'identity_type': service_reward.identity_type or '',
            'no_health_insurance': service_reward.no_health_insurance or '',
            'service_content': service_reward.service_content or '',
            'declaration_type': service_reward.declaration_type or '',
            'business_category': service_reward.business_category or '',
            'confirmation_method': service_reward.confirmation_method or '',
            'service_start_date': service_reward.service_start_date.strftime('%Y-%m-%d') if service_reward.service_start_date else '',
            'service_end_date': service_reward.service_end_date.strftime('%Y-%m-%d') if service_reward.service_end_date else '',
            'payment_account_id': service_reward.payment_account_id,
            'payment_method': service_reward.payment_method or '',
            'amount': service_reward.amount or 0,
            'is_actual_amount': service_reward.is_actual_amount,
            'owner_bank_code': service_reward.owner_bank_code or '',
            'owner_bank_account': service_reward.owner_bank_account or '',
            'owner_account_name': service_reward.owner_account_name or '',
            'department_id': service_reward.department_id,
            'project_id': service_reward.project_id,
            'notice': service_reward.notice or '',
            'note': service_reward.note or '',
            'status': service_reward.status or 'draft'
        }

    # 載入下拉選單資料 - 使用通用函數
    from main import get_dropdown_data
    dropdown_data = get_dropdown_data()
    accounts_data = dropdown_data['accounts']
    departments_data = dropdown_data['departments']
    projects_data = dropdown_data['projects']

    main_menu = list(menu.keys())
    selected = '勞務報酬'
    return render_template('edit_service_reward.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         reward=reward_data,
                         accounts=accounts_data,
                         departments=departments_data,
                         projects=projects_data)