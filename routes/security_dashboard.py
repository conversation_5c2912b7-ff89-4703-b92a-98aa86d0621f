"""
安全監控儀表板路由
"""
from flask import Blueprint, render_template, jsonify, request
from utils.security.security_monitor import security_monitor
from utils.security.auth_decorators import login_required
from datetime import datetime, timedelta
import json

security_dashboard_bp = Blueprint('security_dashboard', __name__)

@security_dashboard_bp.route('/security-dashboard')
@login_required
def security_dashboard():
    """安全監控儀表板主頁"""
    dashboard_data = security_monitor.get_security_dashboard()
    return render_template('security/dashboard.html', data=dashboard_data)

@security_dashboard_bp.route('/api/security/stats')
@login_required
def security_stats_api():
    """獲取安全統計數據 API"""
    return jsonify(security_monitor.get_security_dashboard())

@security_dashboard_bp.route('/api/security/login-attempts')
@login_required
def login_attempts_api():
    """獲取登入嘗試記錄"""
    # 合併所有 IP 的登入記錄
    all_attempts = []
    for ip, attempts in security_monitor.login_attempts.items():
        for attempt in attempts:
            attempt['ip'] = ip
            all_attempts.append(attempt)
    
    # 按時間排序，最新的在前
    all_attempts.sort(key=lambda x: x['timestamp'], reverse=True)
    
    # 返回最近100條記錄
    return jsonify(all_attempts[:100])

@security_dashboard_bp.route('/api/security/suspicious-activities')
@login_required
def suspicious_activities_api():
    """獲取可疑活動記錄"""
    activities = list(security_monitor.suspicious_activities)
    activities.reverse()  # 最新的在前
    
    # 可以根據參數過濾
    activity_type = request.args.get('type')
    if activity_type:
        activities = [a for a in activities if a['type'] == activity_type]
    
    # 限制返回數量
    limit = min(int(request.args.get('limit', 50)), 200)
    return jsonify(activities[:limit])

@security_dashboard_bp.route('/api/security/blocked-ips')
@login_required
def blocked_ips_api():
    """獲取被封鎖的 IP 列表"""
    # 過濾掉已過期的封鎖
    current_blocks = {}
    current_time = datetime.now()
    
    for ip, block_info in security_monitor.blocked_ips.items():
        unblock_time = datetime.fromisoformat(block_info['unblock_at'])
        if current_time < unblock_time:
            block_info['remaining_hours'] = (unblock_time - current_time).total_seconds() / 3600
            current_blocks[ip] = block_info
    
    return jsonify(current_blocks)

@security_dashboard_bp.route('/api/security/unblock-ip', methods=['POST'])
@login_required
def unblock_ip():
    """手動解除 IP 封鎖"""
    data = request.get_json()
    ip = data.get('ip')
    
    if not ip:
        return jsonify({'error': '缺少 IP 參數'}), 400
    
    if ip in security_monitor.blocked_ips:
        del security_monitor.blocked_ips[ip]
        security_monitor.failed_logins[ip] = 0
        
        # 記錄解封操作
        from flask import session
        user = session.get('username', 'unknown')
        security_monitor.log_sensitive_operation(
            'UNBLOCK_IP',
            user,
            {'ip': ip, 'manual_unblock': True}
        )
        
        return jsonify({'message': f'IP {ip} 已解除封鎖'})
    else:
        return jsonify({'error': 'IP 未在封鎖列表中'}), 404

@security_dashboard_bp.route('/api/security/export-logs', methods=['POST'])
@login_required
def export_security_logs():
    """匯出安全日誌"""
    from flask import session
    user = session.get('username', 'unknown')
    
    # 記錄敏感操作
    security_monitor.log_sensitive_operation(
        'EXPORT_SECURITY_LOGS',
        user,
        {'exported_at': datetime.now().isoformat()}
    )
    
    data = request.get_json()
    log_type = data.get('type', 'all')
    
    logs = {}
    
    if log_type in ['all', 'login_attempts']:
        logs['login_attempts'] = []
        for ip, attempts in security_monitor.login_attempts.items():
            for attempt in attempts:
                attempt_copy = attempt.copy()
                attempt_copy['ip'] = ip
                logs['login_attempts'].append(attempt_copy)
    
    if log_type in ['all', 'suspicious_activities']:
        logs['suspicious_activities'] = list(security_monitor.suspicious_activities)
    
    if log_type in ['all', 'sensitive_operations']:
        logs['sensitive_operations'] = list(security_monitor.sensitive_operations)
    
    if log_type in ['all', 'blocked_ips']:
        logs['blocked_ips'] = security_monitor.blocked_ips
    
    return jsonify({
        'exported_at': datetime.now().isoformat(),
        'exported_by': user,
        'data': logs
    })

@security_dashboard_bp.route('/api/security/risk-assessment')
@login_required
def risk_assessment_api():
    """風險評估 API"""
    now = datetime.now()
    
    # 計算各種風險指標
    risk_factors = {
        'failed_logins_24h': 0,
        'suspicious_activities_24h': 0,
        'blocked_ips_active': len([
            ip for ip, info in security_monitor.blocked_ips.items()
            if datetime.fromisoformat(info['unblock_at']) > now
        ]),
        'unusual_access_patterns': 0,
        'high_risk_operations_24h': 0
    }
    
    # 計算24小時內的活動
    day_ago = now - timedelta(days=1)
    
    # 統計失敗登入
    for attempts in security_monitor.login_attempts.values():
        for attempt in attempts:
            if datetime.fromisoformat(attempt['timestamp']) > day_ago and not attempt['success']:
                risk_factors['failed_logins_24h'] += 1
    
    # 統計可疑活動
    for activity in security_monitor.suspicious_activities:
        if datetime.fromisoformat(activity['timestamp']) > day_ago:
            risk_factors['suspicious_activities_24h'] += 1
    
    # 統計高風險操作
    high_risk_ops = ['DELETE_RECORDS', 'EXPORT_ALL_DATA', 'CHANGE_PERMISSIONS']
    for operation in security_monitor.sensitive_operations:
        if (datetime.fromisoformat(operation['timestamp']) > day_ago and 
            operation['operation'] in high_risk_ops):
            risk_factors['high_risk_operations_24h'] += 1
    
    # 計算總風險分數
    risk_score = security_monitor._calculate_risk_score()
    
    return jsonify({
        'risk_score': risk_score,
        'risk_level': 'HIGH' if risk_score > 70 else 'MEDIUM' if risk_score > 30 else 'LOW',
        'factors': risk_factors,
        'recommendations': _generate_security_recommendations(risk_factors, risk_score)
    })

def _generate_security_recommendations(factors: dict, risk_score: int) -> list:
    """生成安全建議"""
    recommendations = []
    
    if factors['failed_logins_24h'] > 10:
        recommendations.append("建議加強密碼政策，要求更複雜的密碼")
    
    if factors['blocked_ips_active'] > 5:
        recommendations.append("檢查是否遭受協同攻擊，考慮聯繫網路管理員")
    
    if factors['high_risk_operations_24h'] > 5:
        recommendations.append("建議檢查高風險操作的必要性，加強權限控制")
    
    if risk_score > 70:
        recommendations.append("系統風險較高，建議立即審查所有安全日誌")
        recommendations.append("考慮暫時限制非必要的系統功能")
    elif risk_score > 30:
        recommendations.append("建議定期檢查安全日誌，關注異常活動")
    
    if not recommendations:
        recommendations.append("系統安全狀態良好，繼續保持現有安全措施")
    
    return recommendations