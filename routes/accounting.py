from flask import Blueprint, render_template, request, redirect, url_for
from database import get_db
from model import AccountSubject
from sqlalchemy.orm import joinedload
from collections import OrderedDict
from utils.common.helpers import get_template_context
import logging

logger = logging.getLogger(__name__)

accounting_bp = Blueprint('accounting', __name__)

@accounting_bp.route('/subject_manage')
def subject_manage():
    """科目管理頁面"""
    # 固定大分類順序
    category_order = [
        '資產', '負債', '權益', '營業收入', '營業成本',
        '營業費用', '營業外收益及費損', '所得稅'
    ]

    with get_db() as db:
        # 查詢所有科目，預先載入 children
        all_subjects = db.query(AccountSubject).options(
            joinedload(AccountSubject.children)
        ).all()

        # 依 top_category 分組
        category_dict = OrderedDict()
        for cat in category_order:
            category_dict[cat] = []

        for subj in all_subjects:
            cat = subj.top_category or '其他'
            if cat in category_dict and subj.parent_id is None:
                category_dict[cat].append(subj)
        
        # 在 Session 還開啟時，將資料轉換為字典格式
        category_data = {}
        for cat, subjects in category_dict.items():
            category_data[cat] = []
            for subj in subjects:
                subject_data = {
                    'id': subj.id,
                    'name': subj.name,
                    'code': subj.code,
                    'note': subj.note,
                    'top_category': subj.top_category,
                    'children': []
                }
                for child in subj.children:
                    child_data = {
                        'id': child.id,
                        'name': child.name,
                        'code': child.code,
                        'note': child.note,
                        'top_category': child.top_category
                    }
                    subject_data['children'].append(child_data)
                category_data[cat].append(subject_data)
    
    context = get_template_context('會計科目')
    context['category_dict'] = category_data
    return render_template('subject_manage.html', **context)

@accounting_bp.route('/add_subject', methods=['GET', 'POST'])
def add_subject():
    """新增科目"""
    if request.method == 'POST':
        return _handle_add_subject_post()
    else:
        return _handle_add_subject_get()

def _handle_add_subject_post():
    """處理新增科目的 POST 請求"""
    with get_db() as db:
        sub_name = request.form.get('sub_name')
        sub_code = request.form.get('sub_code')
        sub_note = request.form.get('sub_note')
        parent_code = request.form.get('parent_code')
        
        parent = None
        full_code = sub_code
        
        if parent_code:
            parent = db.query(AccountSubject).filter_by(code=parent_code).first()
            if parent:
                full_code = f"{parent.code}{sub_code}"
                top_category = parent.top_category
            else:
                top_category = None
        else:
            top_category = None
            
        new_subject = AccountSubject(
            name=sub_name, 
            code=full_code, 
            note=sub_note, 
            parent_id=parent.id if parent else None, 
            top_category=top_category
        )
        db.add(new_subject)
    
    return redirect(url_for('accounting.subject_manage'))

def _handle_add_subject_get():
    """處理新增科目的 GET 請求"""
    parent_code = request.args.get('parent_code')
    parent_info = None
    next_sub_code = '001'
    
    if parent_code:
        with get_db() as db:
            parent = db.query(AccountSubject).filter_by(code=parent_code).first()
            if parent:
                # 在 Session 還開啟時，將資料轉換為字典格式
                parent_info = {
                    'id': parent.id,
                    'name': parent.name,
                    'code': parent.code,
                    'note': parent.note,
                    'top_category': parent.top_category,
                    'is_expandable': parent.is_expandable
                }
                
                # 計算下一個可用的三碼子科目代碼
                used_codes = set()
                for child in parent.children:
                    try:
                        code_int = int(child.code[-3:])
                        used_codes.add(code_int)
                    except ValueError:
                        pass
                        
                for i in range(1, 1000):
                    code_str = f"{i:03d}"
                    if int(code_str) not in used_codes:
                        next_sub_code = code_str
                        break
    
    context = get_template_context('會計科目')
    context['parent_info'] = parent_info
    context['next_sub_code'] = next_sub_code
    return render_template('add_subject.html', **context)

@accounting_bp.route('/edit_subject', methods=['GET', 'POST'])
def edit_subject():
    """編輯科目"""
    code = request.args.get('code') if request.method == 'GET' else request.form.get('code')
    
    if request.method == 'POST':
        with get_db() as db:
            subject = db.query(AccountSubject).filter_by(code=code).first()
            if not subject:
                return '找不到該科目', 404
                
            subject.name = request.form.get('sub_name')
            subject.note = request.form.get('sub_note')
            return redirect(url_for('accounting.subject_manage'))
    
    # GET 請求 - 需要將資料轉換為字典
    with get_db() as db:
        subject = db.query(AccountSubject).filter_by(code=code).first()
        if not subject:
            return '找不到該科目', 404
        
        # 在 Session 還開啟時，將資料轉換為字典格式
        subject_data = {
            'id': subject.id,
            'name': subject.name,
            'code': subject.code,
            'note': subject.note,
            'top_category': subject.top_category
        }
    
    context = get_template_context('會計科目')
    context['subject'] = subject_data
    return render_template('edit_subject.html', **context)

@accounting_bp.route('/delete_subject', methods=['POST'])
def delete_subject():
    """刪除科目"""
    code = request.form.get('code')
    
    with get_db() as db:
        subject = db.query(AccountSubject).filter_by(code=code).first()
        if subject:
            db.delete(subject)
            logger.info(f"Deleted subject: {subject.name} ({subject.code})")
    
    return redirect(url_for('accounting.subject_manage')) 