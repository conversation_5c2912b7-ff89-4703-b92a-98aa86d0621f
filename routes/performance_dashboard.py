"""
性能監控儀表板路由
提供實時性能指標的 Web 介面
"""

from flask import Blueprint, render_template, jsonify, request
from datetime import datetime, timedelta
import json

performance_dashboard_bp = Blueprint('performance_dashboard', __name__)

@performance_dashboard_bp.route('/performance/dashboard')
def dashboard():
    """性能監控儀表板主頁"""
    return render_template('performance/dashboard.html')

@performance_dashboard_bp.route('/api/performance/overview')
def api_performance_overview():
    """API 性能概覽"""
    try:
        from utils.performance.performance_benchmarking import benchmark_manager
        from utils.database.db_pool_monitor import pool_monitor
        from utils.performance.memory_optimizer import memory_optimizer
        
        hours = request.args.get('hours', 24, type=int)
        
        # API 性能摘要
        api_summary = benchmark_manager.get_api_performance_summary(hours)
        
        # 資料庫性能摘要
        db_summary = benchmark_manager.get_database_performance_summary(hours)
        
        # 系統性能摘要
        system_summary = benchmark_manager.get_system_performance_summary(hours)
        
        # 連接池狀態
        pool_status = pool_monitor.check_pool_health() if pool_monitor else {'error': 'Pool monitor not available'}
        
        # 記憶體狀態
        memory_stats = memory_optimizer.get_memory_stats()
        
        return jsonify({
            'success': True,
            'data': {
                'api_performance': api_summary,
                'database_performance': db_summary,
                'system_performance': system_summary,
                'connection_pool': pool_status,
                'memory_usage': memory_stats,
                'last_updated': datetime.now().isoformat()
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@performance_dashboard_bp.route('/api/performance/metrics')
def api_performance_metrics():
    """獲取詳細性能指標"""
    try:
        from utils.performance.performance_benchmarking import benchmark_manager
        
        metric_type = request.args.get('type', 'all')
        hours = request.args.get('hours', 24, type=int)
        
        metrics = {}
        
        if metric_type in ['all', 'api']:
            metrics['api'] = benchmark_manager.get_api_performance_summary(hours)
        
        if metric_type in ['all', 'database']:
            metrics['database'] = benchmark_manager.get_database_performance_summary(hours)
            
        if metric_type in ['all', 'system']:
            metrics['system'] = benchmark_manager.get_system_performance_summary(hours)
        
        return jsonify({
            'success': True,
            'data': metrics
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@performance_dashboard_bp.route('/api/performance/cache-stats')
def api_cache_stats():
    """獲取快取統計"""
    try:
        from utils.performance.cache_manager import cache_manager
        from utils.performance.advanced_cache import tiered_cache, cache_warmer
        
        # 基本快取統計
        basic_stats = cache_manager.get_stats()
        
        # 分層快取統計
        tiered_stats = tiered_cache.get_stats()
        
        # 熱門快取鍵
        popular_keys = cache_warmer.get_popular_keys(10)
        
        return jsonify({
            'success': True,
            'data': {
                'basic_cache': basic_stats,
                'tiered_cache': tiered_stats,
                'popular_keys': popular_keys,
                'timestamp': datetime.now().isoformat()
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@performance_dashboard_bp.route('/api/performance/query-analysis')
def api_query_analysis():
    """獲取查詢分析報告"""
    try:
        from utils.database.query_optimizer import get_query_performance_report
        
        report = get_query_performance_report()
        
        return jsonify({
            'success': True,
            'data': report
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@performance_dashboard_bp.route('/api/performance/optimization-suggestions')
def api_optimization_suggestions():
    """獲取優化建議"""
    try:
        from utils.database.db_pool_monitor import get_pool_suggestions
        from utils.database.query_optimizer import query_analyzer
        
        suggestions = []
        
        # 連接池優化建議
        pool_suggestions = get_pool_suggestions()
        for suggestion in pool_suggestions:
            suggestions.append({
                'category': 'connection_pool',
                'severity': suggestion.get('severity', 'medium'),
                'message': suggestion.get('message', ''),
                'action': suggestion.get('action', '')
            })
        
        # 查詢優化建議
        if query_analyzer:
            optimization_report = query_analyzer.get_optimization_report()
            for rec in optimization_report.get('recommendations', []):
                suggestions.append({
                    'category': rec.get('category', 'query'),
                    'severity': rec.get('priority', 'medium'),
                    'message': rec.get('description', ''),
                    'action': 'optimize_query'
                })
        
        return jsonify({
            'success': True,
            'data': {
                'suggestions': suggestions,
                'total_count': len(suggestions),
                'by_severity': {
                    'high': len([s for s in suggestions if s['severity'] == 'high']),
                    'medium': len([s for s in suggestions if s['severity'] == 'medium']),
                    'low': len([s for s in suggestions if s['severity'] == 'low'])
                }
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@performance_dashboard_bp.route('/api/performance/alerts')
def api_performance_alerts():
    """獲取性能告警"""
    try:
        from utils.performance.performance_benchmarking import benchmark_manager
        
        # 獲取最近的告警
        alerts = []
        
        # 從基準測試管理器獲取告警
        for alert in benchmark_manager.alerts[-50:]:  # 最近50個告警
            alerts.append({
                'type': alert['type'],
                'severity': alert['severity'],
                'message': alert.get('endpoint') or alert.get('query_type', ''),
                'value': alert.get('response_time') or alert.get('execution_time', 0),
                'timestamp': datetime.fromtimestamp(alert['timestamp']).isoformat()
            })
        
        # 按嚴重程度排序
        alerts.sort(key=lambda x: {'high': 3, 'medium': 2, 'low': 1}[x['severity']], reverse=True)
        
        return jsonify({
            'success': True,
            'data': {
                'alerts': alerts,
                'total_count': len(alerts),
                'by_severity': {
                    'high': len([a for a in alerts if a['severity'] == 'high']),
                    'medium': len([a for a in alerts if a['severity'] == 'medium']),
                    'low': len([a for a in alerts if a['severity'] == 'low'])
                }
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@performance_dashboard_bp.route('/api/performance/export')
def api_export_metrics():
    """導出性能指標"""
    try:
        from utils.performance.performance_benchmarking import benchmark_manager
        
        format_type = request.args.get('format', 'json')
        hours = request.args.get('hours', 24, type=int)
        
        # 導出指標
        exported_data = benchmark_manager.export_metrics(format_type, hours)
        
        if format_type == 'json':
            return jsonify({
                'success': True,
                'data': json.loads(exported_data)
            })
        else:
            return exported_data, 200, {
                'Content-Type': 'text/csv',
                'Content-Disposition': f'attachment; filename=performance_metrics_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            }
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@performance_dashboard_bp.route('/api/performance/clear-cache')
def api_clear_cache():
    """清除快取"""
    try:
        from utils.performance.cache_manager import cache_manager
        from utils.performance.advanced_cache import tiered_cache
        
        cache_type = request.args.get('type', 'all')
        
        if cache_type in ['all', 'basic']:
            cache_manager.invalidate()
        
        if cache_type in ['all', 'tiered']:
            # 清除分層快取（這裡需要根據實際實現調整）
            tiered_cache.l1_cache.clear()
            tiered_cache.l2_cache.clear()
        
        return jsonify({
            'success': True,
            'message': f'Cache cleared: {cache_type}'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@performance_dashboard_bp.route('/api/performance/optimize-memory')
def api_optimize_memory():
    """執行記憶體優化"""
    try:
        from utils.performance.memory_optimizer import memory_optimizer
        
        force = request.args.get('force', 'false').lower() == 'true'
        
        # 執行記憶體優化
        result = memory_optimizer.optimize_memory(force=force)
        
        return jsonify({
            'success': True,
            'data': result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@performance_dashboard_bp.route('/api/performance/realtime-stats')
def api_realtime_stats():
    """即時統計數據（用於圖表）"""
    try:
        from utils.performance.memory_optimizer import memory_optimizer
        from utils.database.db_pool_monitor import pool_monitor
        
        current_time = datetime.now()
        
        # 記憶體使用
        memory_mb = memory_optimizer.get_memory_usage_mb()
        memory_percent = memory_optimizer.get_memory_percent()
        
        # 連接池狀態
        pool_metrics = pool_monitor.get_pool_metrics() if pool_monitor else {}
        
        return jsonify({
            'success': True,
            'data': {
                'timestamp': current_time.isoformat(),
                'memory': {
                    'usage_mb': memory_mb,
                    'usage_percent': memory_percent
                },
                'connection_pool': {
                    'utilization': pool_metrics.get('utilization', 0),
                    'checked_out': pool_metrics.get('checked_out', 0),
                    'size': pool_metrics.get('size', 0)
                }
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500