from flask import Blueprint
from utils.web.menu_decorator import with_menu, render_with_menu_data

accounting_other_bp = Blueprint('accounting_other', __name__)

@accounting_other_bp.route('/voucher_manage')
@with_menu('會計科目')
def voucher_manage():
    """傳票管理"""
    return render_with_menu_data('voucher_manage.html')

@accounting_other_bp.route('/cost_transfer')
@with_menu('會計科目')
def cost_transfer():
    """成本結轉"""
    return render_with_menu_data('cost_transfer.html')

@accounting_other_bp.route('/tax_manage')
@with_menu('會計科目')
def tax_manage():
    """進/銷項稅額管理"""
    return render_with_menu_data('tax_manage.html') 