"""
資金記錄相關路由
"""
from flask import Blueprint, render_template, request, flash, redirect, jsonify
from data.menu_data import menu
from utils.tenant_utils import require_tenant_access, add_tenant_filter, get_current_tenant_id
from model import Money
from database import get_db
from datetime import datetime
from services.account_service import AccountService
from utils.logging.error_handler import handle_database_error, handle_validation_error, ErrorHandler

fund_record_bp = Blueprint('fund_record', __name__, url_prefix='/fund_record')

@fund_record_bp.route('/create', methods=['GET', 'POST'])
@handle_database_error
@handle_validation_error
def create():
    """新增資金紀錄頁面"""
    main_menu = list(menu.keys())
    selected = '資金管理'

    if request.method == 'POST':
        with get_db() as db:
            # 獲取表單資料
            money_type = request.form.get('money_type')
            amount = ErrorHandler.safe_int_convert(request.form.get('amount', 0))
            record_date_str = request.form.get('record_date')

            # 安全地處理可能為 None 的 ID 字段
            account_id = ErrorHandler.safe_int_convert(request.form.get('account_id'))
            subject_code = request.form.get('subject_code')
            payment_identity_id = ErrorHandler.safe_int_convert(request.form.get('payment_identity_id'))
            department_id = ErrorHandler.safe_int_convert(request.form.get('department_id'))
            project_id = ErrorHandler.safe_int_convert(request.form.get('project_id'))
            tags = request.form.get('tags', '')
            note = request.form.get('note', '')

            # 轉換日期
            record_date = datetime.strptime(record_date_str, '%Y-%m-%d').date() if record_date_str else None

            # 創建新的資金記錄
            new_money_record = Money(
                money_type=money_type,
                a_time=record_date,
                name=f"{money_type}記錄",
                total=amount,
                extra_fee=0,
                subject_code=subject_code,
                account_id=account_id if account_id > 0 else None,
                payment_identity_id=payment_identity_id if payment_identity_id > 0 else None,
                department_id=department_id if department_id > 0 else None,
                project_id=project_id if project_id > 0 else None,
                tags=tags,
                note=note,
                is_paper=False,
                is_paid=True,
            )

            db.add(new_money_record)
            db.commit()

            flash('資金記錄新增成功', 'success')
            return redirect('/fund_record/list')

    # GET 請求 - 載入所有需要的資料
    dropdown_data = AccountService.get_dropdown_data()

    return render_template('fund_record_create.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         accounts=dropdown_data['accounts'],
                         payment_identities=dropdown_data['payment_identities'],
                         departments=dropdown_data['departments'],
                         projects=dropdown_data['projects'])

@fund_record_bp.route('/list')
@require_tenant_access
def list_records():
    """資金紀錄列表頁面"""
    main_menu = list(menu.keys())
    selected = '資金管理'

    # 獲取搜尋參數
    search_type = request.args.get('search_type', '')
    search_value = request.args.get('search_value', '')

    with get_db() as db:
        # 基本查詢
        query = db.query(Money).filter(
            Money.subject_code.in_(['2205', '2210', '2910'])  # 資金相關科目
        )

        # 添加搜尋條件
        if search_type and search_value:
            if search_type == 'account':
                query = query.filter(Money.account_id == int(search_value))
            elif search_type == 'money_type':
                query = query.filter(Money.money_type == search_value)

        # 執行查詢並排序
        fund_records = query.order_by(Money.a_time.desc()).all()

        # 轉換為字典格式
        records_data = []
        for record in fund_records:
            records_data.append({
                'id': record.id,
                'money_type': record.money_type,
                'amount': record.total,
                'record_date': record.a_time.strftime('%Y-%m-%d') if record.a_time else None,
                'account_name': record.account.name if record.account else '未指定',
                'subject_code': record.subject_code,
                'note': record.note,
                'tags': record.tags
            })

    return render_template('fund_record_list.html',
                         fund_records=records_data,
                         sidebar_items=main_menu,
                         selected=selected)

@fund_record_bp.route('/edit/<int:record_id>', methods=['GET', 'POST'])
@handle_database_error
@handle_validation_error
def edit(record_id):
    """編輯資金紀錄頁面"""
    main_menu = list(menu.keys())
    selected = '資金管理'

    if request.method == 'POST':
        with get_db() as db:
            money_record = db.query(Money).filter_by(id=record_id).first()
            if not money_record:
                flash('找不到該資金記錄', 'error')
                return redirect('/fund_record/list')

            # 更新資料
            money_type = request.form.get('money_type')
            if money_type is not None:
                money_record.money_type = money_type

            money_record.total = ErrorHandler.safe_int_convert(request.form.get('amount', 0))

            record_date_str = request.form.get('record_date')
            if record_date_str and record_date_str.strip():
                money_record.a_time = datetime.strptime(record_date_str, '%Y-%m-%d').date()

            account_id = ErrorHandler.safe_int_convert(request.form.get('account_id'))
            if account_id > 0:
                money_record.account_id = account_id

            subject_code = request.form.get('subject_code')
            if subject_code is not None:
                money_record.subject_code = subject_code

            payment_identity_id = ErrorHandler.safe_int_convert(request.form.get('payment_identity_id'))
            if payment_identity_id > 0:
                money_record.payment_identity_id = payment_identity_id

            department_id = ErrorHandler.safe_int_convert(request.form.get('department_id'))
            if department_id > 0:
                money_record.department_id = department_id

            project_id = ErrorHandler.safe_int_convert(request.form.get('project_id'))
            if project_id > 0:
                money_record.project_id = project_id

            money_record.tags = request.form.get('tags', '')
            money_record.note = request.form.get('note', '')
            money_record.name = f"{money_type}記錄" if money_type else '未知記錄'

            db.commit()
            flash('資金記錄更新成功', 'success')
            return redirect('/fund_record/list')

    # GET 請求 - 顯示編輯表單
    with get_db() as db:
        money_record = db.query(Money).filter_by(id=record_id).first()
        if not money_record:
            flash('找不到該資金記錄', 'error')
            return redirect('/fund_record/list')

        record_data = {
            'id': money_record.id,
            'money_type': money_record.money_type,
            'amount': money_record.total,
            'record_date': money_record.a_time,
            'account_id': money_record.account_id,
            'subject_code': money_record.subject_code,
            'payment_identity_id': money_record.payment_identity_id,
            'department_id': money_record.department_id,
            'project_id': money_record.project_id,
            'tags': money_record.tags,
            'note': money_record.note
        }

    dropdown_data = AccountService.get_dropdown_data()

    return render_template('fund_record_create.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         edit_mode=True,
                         record_data=record_data,
                         accounts=dropdown_data['accounts'],
                         payment_identities=dropdown_data['payment_identities'],
                         departments=dropdown_data['departments'],
                         projects=dropdown_data['projects'])

@fund_record_bp.route('/delete/<int:record_id>', methods=['DELETE'])
@handle_database_error
def delete(record_id):
    """刪除資金紀錄"""
    with get_db() as db:
        money_record = db.query(Money).filter_by(id=record_id).first()
        if not money_record:
            return jsonify({'success': False, 'message': '找不到該記錄'}), 404
        
        db.delete(money_record)
        db.commit()
        
    return jsonify({'success': True, 'message': '刪除成功'})