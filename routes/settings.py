from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from data.menu_data import menu
from utils.tenant_utils import require_tenant_access, add_tenant_filter, get_current_tenant_id
from database import get_db
from model import CompanyInfo, Account, PaymentIdentity, PaymentIdentityType, Department, Project, SalarySetting, Money, AccountSubject
from datetime import datetime
from utils.business.settings_helpers import (
    render_settings_template, redirect_with_flash, parse_date, 
    get_bank_accounts_data, get_departments_data, get_projects_data,
    get_payment_identities_data, SettingsFormHandler
)

settings_bp = Blueprint('settings', __name__)

@settings_bp.route('/company_setting', methods=['GET'])
@require_tenant_access
def company_setting():
    """公司設定"""
    return render_settings_template('company_setting.html', '薪資報酬')

@settings_bp.route('/salary_setting', methods=['GET', 'POST'])
@require_tenant_access
def salary_setting():
    """薪資設定"""
    accounts_data = get_bank_accounts_data()

    with get_db() as db:

        # 獲取現有的薪資設定
        current_setting = db.query(SalarySetting).first()

        if request.method == 'POST':
            try:
                # 處理表單提交
                payday = int(request.form.get('payday', 10))
                fund_account_id = int(request.form.get('fund_account_id')) if request.form.get('fund_account_id') else None
                days_type = request.form.get('days_type', 'calendar')

                # 驗證資料
                if payday < 1 or payday > 31:
                    flash('發薪日必須在1-31之間！', 'error')
                    return redirect(url_for('settings.salary_setting'))

                if not fund_account_id:
                    flash('請選擇資金帳戶！', 'error')
                    return redirect(url_for('settings.salary_setting'))

                # 檢查帳戶是否存在
                account = db.query(Account).filter_by(id=fund_account_id).first()
                if not account:
                    flash('選擇的帳戶不存在！', 'error')
                    return redirect(url_for('settings.salary_setting'))

                # 儲存或更新設定
                if current_setting:
                    current_setting.payday = payday
                    current_setting.fund_account_id = fund_account_id
                    current_setting.days_type = days_type
                    current_setting.updated_at = datetime.now()
                else:
                    new_setting = SalarySetting(
                        payday=payday,
                        fund_account_id=fund_account_id,
                        days_type=days_type
                    )
                    db.add(new_setting)

                return redirect_with_flash('settings.salary_setting', '薪資設定已儲存！', 'success')

            except ValueError:
                return redirect_with_flash('settings.salary_setting', '輸入資料格式錯誤！', 'error')
            except Exception as e:
                return redirect_with_flash('settings.salary_setting', f'儲存失敗：{str(e)}', 'error')

    return render_settings_template('salary_setting.html', '薪資報酬',
                                   bank_accounts=accounts_data,
                                   current_setting=current_setting)

@settings_bp.route('/basic_info', methods=['GET', 'POST'])
@require_tenant_access
def basic_info():
    """基本資料"""
    
    # 台灣稅徵機關列表
    tax_offices = [
        '財政部北區國稅局', '財政部北區國稅局臺北分局', '財政部北區國稅局板橋分局',
        '財政部北區國稅局桃園分局', '財政部北區國稅局新竹分局', '財政部北區國稅局基隆分局',
        '財政部北區國稅局宜蘭分局', '財政部北區國稅局花蓮分局', '財政部北區國稅局金門分局',
        '財政部北區國稅局馬祖分局', '財政部中區國稅局', '財政部中區國稅局臺中分局',
        '財政部中區國稅局豐原分局', '財政部中區國稅局大屯分局', '財政部中區國稅局沙鹿分局',
        '財政部中區國稅局彰化分局', '財政部中區國稅局員林分局', '財政部中區國稅局南投分局',
        '財政部中區國稅局埔里分局', '財政部中區國稅局竹山分局', '財政部中區國稅局雲林分局',
        '財政部中區國稅局虎尾分局', '財政部中區國稅局北港分局', '財政部南區國稅局',
        '財政部南區國稅局臺南分局', '財政部南區國稅局新化分局', '財政部南區國稅局新營分局',
        '財政部南區國稅局嘉義分局', '財政部南區國稅局民雄分局', '財政部南區國稅局朴子分局',
        '財政部南區國稅局屏東分局', '財政部南區國稅局潮州分局', '財政部南區國稅局東港分局',
        '財政部南區國稅局恆春分局', '財政部南區國稅局臺東分局', '財政部南區國稅局成功分局',
        '財政部南區國稅局關山分局', '財政部南區國稅局澎湖分局', '財政部南區國稅局馬公分局',
        '財政部高雄國稅局', '財政部高雄國稅局三民分局', '財政部高雄國稅局新興分局',
        '財政部高雄國稅局前鎮分局', '財政部高雄國稅局苓雅分局', '財政部高雄國稅局小港分局',
        '財政部高雄國稅局楠梓分局', '財政部高雄國稅局岡山分局', '財政部高雄國稅局鳳山分局',
        '財政部高雄國稅局大寮分局', '財政部高雄國稅局林園分局', '財政部高雄國稅局旗山分局',
        '財政部高雄國稅局美濃分局', '財政部高雄國稅局路竹分局', '財政部高雄國稅局湖內分局',
        '財政部高雄國稅局永安分局', '財政部高雄國稅局彌陀分局', '財政部高雄國稅局梓官分局',
        '財政部高雄國稅局橋頭分局', '財政部高雄國稅局燕巢分局', '財政部高雄國稅局田寮分局',
        '財政部高雄國稅局阿蓮分局', '財政部高雄國稅局茄萣分局', '財政部高雄國稅局桃源分局',
        '財政部高雄國稅局那瑪夏分局', '財政部高雄國稅局茂林分局', '財政部高雄國稅局六龜分局',
        '財政部高雄國稅局甲仙分局', '財政部高雄國稅局杉林分局', '財政部高雄國稅局內門分局'
    ]
    
    company_info = None
    
    if request.method == 'POST':
        with get_db() as db:
            company_info = db.query(CompanyInfo).first()
            
            if company_info:
                # 更新現有資料
                company_info.company_name = request.form.get('company_name', '')
                company_info.company_id = request.form.get('company_id', '')
                company_info.owner_name = request.form.get('owner_name', '')
                company_info.owner_phone = request.form.get('owner_phone', '')
                company_info.email = request.form.get('email', '')
                company_info.tax_office = request.form.get('tax_office', '')
                company_info.address = request.form.get('address', '')
                company_info.contact_name = request.form.get('contact_name', '')
                company_info.contact_phone = request.form.get('contact_phone', '')
                company_info.tax_id = request.form.get('tax_id', '')
            else:
                # 建立新資料
                company_info = CompanyInfo(
                    company_name=request.form.get('company_name', ''),
                    company_id=request.form.get('company_id', ''),
                    owner_name=request.form.get('owner_name', ''),
                    owner_phone=request.form.get('owner_phone', ''),
                    email=request.form.get('email', ''),
                    tax_office=request.form.get('tax_office', ''),
                    address=request.form.get('address', ''),
                    contact_name=request.form.get('contact_name', ''),
                    contact_phone=request.form.get('contact_phone', ''),
                    tax_id=request.form.get('tax_id', '')
                )
                db.add(company_info)
        
        return redirect(url_for('settings.basic_info'))
    
    # GET 請求 - 需要將資料轉換為字典
    with get_db() as db:
        company = db.query(CompanyInfo).first()
        if company:
            company_info = {
                'id': company.id,
                'company_name': company.company_name,
                'company_id': company.company_id,
                'owner_name': company.owner_name,
                'owner_phone': company.owner_phone,
                'email': company.email,
                'tax_office': company.tax_office,
                'address': company.address,
                'contact_name': company.contact_name,
                'contact_phone': company.contact_phone,
                'tax_id': company.tax_id
            }
    
    return render_settings_template('basic_info.html',
                                  company_info=company_info, 
                                  tax_offices=tax_offices)

@settings_bp.route('/account_setting', methods=['GET'])
@require_tenant_access
def account_setting():
    """帳戶設定"""
    tab = request.args.get('tab', '現金')
    return render_settings_template('account_setting.html', tab=tab)

@settings_bp.route('/opening_setting', methods=['GET'])
@require_tenant_access
def opening_setting():
    """開帳設定"""
    main_menu = list(menu.keys())
    selected = '設定'
    def account_to_dict(acc):
        # 預設直接用 subject_code
        full_code = acc.subject_code or ''
        if acc.category == '現金' and acc.subject_code:
            full_code = f'1105{str(acc.subject_code).zfill(3)}'
        elif acc.category == '銀行帳戶' and acc.subject_code:
            full_code = f'1110{str(acc.subject_code).zfill(3)}'
        return {
            'id': acc.id,
            'name': acc.name,
            'subject_code': full_code,  # 使用完整的科目代碼
            'init_amount': acc.init_amount,
            'category': acc.category,
            'full_code': full_code,
        }
    with get_db() as db:
        accounts = db.query(Account).order_by(Account.subject_code.asc()).all()
        accounts = [account_to_dict(acc) for acc in accounts]

        # 計算資本科目的金額（從Money記錄中計算）
        capital_amounts = {}

        # 3110-資本
        capital_3110 = db.query(Money).filter(
            Money.subject_code == '3110',
            Money.money_type == '收入'
        ).all()
        capital_amounts['3110'] = sum(record.total for record in capital_3110) if capital_3110 else 0

        # 3420-資本公積
        capital_3420 = db.query(Money).filter(
            Money.subject_code == '3420',
            Money.money_type == '收入'
        ).all()
        capital_amounts['3420'] = sum(record.total for record in capital_3420) if capital_3420 else 0

        # 3533-累積盈虧
        capital_3533 = db.query(Money).filter(
            Money.subject_code == '3533'
        ).all()
        # 累積盈虧 = 收入 - 支出
        income_3533 = sum(record.total for record in capital_3533 if record.money_type == '收入')
        expense_3533 = sum(record.total for record in capital_3533 if record.money_type == '支出')
        capital_amounts['3533'] = income_3533 - expense_3533

        # 檢查是否已完成開帳（有開帳記錄存在）
        opening_completed = db.query(Money).filter(
            Money.name.like('開帳-%')
        ).first() is not None

    return render_template('opening_setting.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         accounts=accounts,
                         capital_amounts=capital_amounts,
                         opening_completed=opening_completed)

@settings_bp.route('/opening_setting/complete', methods=['POST'])
@require_tenant_access
def opening_setting_complete():
    """完成開帳設定"""
    try:
        from datetime import date

        current_app.logger.info("=== 開帳設定開始 ===")

        # 獲取開帳日期（從表單或使用今天）
        opening_date_str = request.form.get('opening_date')
        current_app.logger.info(f"開帳日期字串: {opening_date_str}")

        if opening_date_str:
            opening_date = datetime.strptime(opening_date_str, '%Y-%m-%d').date()
        else:
            opening_date = date.today()

        current_app.logger.info(f"解析後的開帳日期: {opening_date}")

        with get_db() as db:
            # 獲取所有有期初金額的帳戶
            accounts = db.query(Account).filter(Account.init_amount.isnot(None), Account.init_amount > 0).all()
            current_app.logger.info(f"找到 {len(accounts)} 個有期初金額的帳戶")

            if not accounts:
                flash('沒有找到需要開帳的帳戶資料', 'warning')
                return redirect(url_for('settings.opening_setting'))

            # 檢查是否已經有開帳記錄
            existing_opening = db.query(Money).filter(
                Money.name.like('開帳-%期初餘額'),
                Money.a_time == opening_date
            ).first()

            if existing_opening:
                # 刪除舊的開帳記錄
                old_records = db.query(Money).filter(
                    Money.name.like('開帳-%'),
                    Money.a_time == opening_date
                ).all()

                for record in old_records:
                    db.delete(record)

                current_app.logger.info(f"刪除了 {len(old_records)} 筆舊開帳記錄")

            # 為每個帳戶創建開帳記錄
            total_amount = 0
            record_count = 0
            for account in accounts:
                if account.init_amount and account.init_amount > 0:
                    record_count += 1

                    # 生成唯一的開帳編號
                    opening_number = f"OPEN-{opening_date.strftime('%Y%m%d')}-{record_count:03d}"

                    # 獲取帳戶的完整會計科目代碼
                    account_dict = account_to_dict(account)
                    full_subject_code = account_dict['subject_code']

                    # 創建借方記錄（資產帳戶）
                    debit_record = Money(
                        money_type='支出',  # 借方用支出表示
                        a_time=opening_date,
                        name=f'開帳-{account.name}期初餘額',
                        total=account.init_amount,
                        extra_fee=0,
                        subject_code=full_subject_code,  # 使用完整的會計科目代碼
                        account_id=account.id,
                        note=f'開帳設定 - {account.name}期初金額(借方)',
                        number=opening_number,
                        is_paper=True,
                        tax_type='免稅'
                    )
                    db.add(debit_record)
                    total_amount += account.init_amount

            # 創建貸方記錄（資本科目）
            if total_amount > 0:
                record_count += 1
                capital_number = f"OPEN-{opening_date.strftime('%Y%m%d')}-{record_count:03d}"

                credit_record = Money(
                    money_type='收入',  # 貸方用收入表示
                    a_time=opening_date,
                    name='開帳-資本期初餘額',
                    total=total_amount,
                    extra_fee=0,
                    subject_code='3110',  # 資本科目
                    account_id=None,  # 資本不對應特定帳戶
                    note=f'開帳設定 - 資本期初餘額(貸方)',
                    number=capital_number,
                    is_paper=True,
                    tax_type='免稅'
                )
                db.add(credit_record)

            # 提交所有變更
            db.commit()

            flash(f'開帳設定已完成！共處理 {len(accounts)} 個帳戶，總金額 NT$ {total_amount:,}', 'success')
            return redirect(url_for('settings.opening_setting'))

    except Exception as e:
        flash(f'開帳設定失敗：{str(e)}', 'error')
        return redirect(url_for('settings.opening_setting'))

@settings_bp.route('/department_manage', methods=['GET'])
@require_tenant_access
def department_manage():
    """部門管理"""
    departments_data = get_departments_data()
    
    # 添加父部門資訊
    with get_db() as db:
        for dept_data in departments_data:
            dept = db.query(Department).get(dept_data['id'])
            if dept and dept.parent_id:
                parent = db.query(Department).get(dept.parent_id)
                dept_data['parent_name'] = parent.name if parent else '（無）'
            else:
                dept_data['parent_name'] = '（無）'
            dept_data['note'] = dept.note or '' if dept else ''
    
    return render_settings_template('department_manage.html', departments=departments_data)

@settings_bp.route('/withholding_declare', methods=['GET'])
@require_tenant_access
def withholding_declare():
    """扣繳申報作業"""
    return render_settings_template('withholding_declare.html', '扣繳申報')

@settings_bp.route('/payment_identity_list')
@require_tenant_access
def payment_identity_list():
    page = request.args.get('page', 1, type=int)
    payment_data = get_payment_identities_data(page=page)
    
    with get_db() as db:
        # 查詢所有對象類別
        types = db.query(PaymentIdentityType).all()
        
        # 更新支付身份數據，添加額外欄位
        for identity in payment_data['identities']:
            pi = db.query(PaymentIdentity).get(identity['id'])
            if pi:
                identity.update({
                    'type': pi.identity_type.name if pi.identity_type else '（未分類）',
                    'type_id': pi.type_id,
                    'bank_code': pi.bank_code or '',
                    'bank_account': pi.bank_account or '',
                    'contact': pi.contact or '',
                    'mobile': getattr(pi, 'mobile', ''),
                    'line': getattr(pi, 'line', ''),
                    'note': pi.note or ''
                })
    
    return render_settings_template(
        'payment_identity_list.html',
        identities=payment_data['identities'],
        types=types,
        pagination=payment_data
    )

@settings_bp.route('/payment_identity/edit/<int:pid>', methods=['GET', 'POST'])
@require_tenant_access
def payment_identity_edit(pid):
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        # 查詢所有對象類別
        types = db.query(PaymentIdentityType).all()
        
        identity = db.query(PaymentIdentity).filter_by(id=pid).first()
        if not identity:
            return "找不到該收支對象", 404
        if request.method == 'GET':
            identity_dict = {
                'id': identity.id,
                'type': identity.type,
                'type_id': identity.type_id,
                'name': identity.name,
                'tax_id': identity.tax_id,
                'bank_code': identity.bank_code,
                'bank_account': identity.bank_account,
                'contact': identity.contact,
                'mobile': getattr(identity, 'mobile', ''),
                'line': getattr(identity, 'line', ''),
                'note': identity.note
            }
            
            return render_template(
                'payment_identity_edit.html',
                identity=identity_dict,
                types=types,
                sidebar_items=main_menu,
                selected=selected
            )
        # POST 請求：更新資料並 redirect
        # 處理對象類別
        type_id = request.form.get('type_id')
        if type_id and type_id.isdigit():
            identity.type_id = int(type_id)
        else:
            identity.type_id = None
            
        identity.name = request.form.get('name', '')
        identity.tax_id = request.form.get('tax_id', '')
        identity.bank_code = request.form.get('bank_code', '')
        identity.bank_account = request.form.get('bank_account', '')
        identity.contact = request.form.get('contact', '')
        identity.mobile = request.form.get('mobile', '')
        identity.line = request.form.get('line', '')
        identity.note = request.form.get('note', '')
        db.commit()
        return redirect(url_for('settings.payment_identity_list'))

@settings_bp.route('/payment_identity/add', methods=['GET', 'POST'])
@require_tenant_access
def payment_identity_add():
    main_menu = list(menu.keys())
    selected = '設定'
    
    with get_db() as db:
        # 查詢所有對象類別
        types = db.query(PaymentIdentityType).all()
        
        if request.method == 'POST':
            # 處理對象類別
            type_id = request.form.get('type_id')
            if type_id and type_id.isdigit():
                type_id = int(type_id)
            else:
                type_id = None
                
            new_identity = PaymentIdentity(
                type_id=type_id,
                name=request.form.get('name', ''),
                tax_id=request.form.get('tax_id', ''),
                bank_code=request.form.get('bank_code', ''),
                bank_account=request.form.get('bank_account', ''),
                contact=request.form.get('contact', ''),
                mobile=request.form.get('mobile', ''),
                line=request.form.get('line', ''),
                note=request.form.get('note', '')
            )
            db.add(new_identity)
            db.commit()
            return redirect(url_for('settings.payment_identity_list'))
            
        return render_template(
            'payment_identity_add.html',
            types=types,
            sidebar_items=main_menu,
            selected=selected
        )

@settings_bp.route('/payment_identity/delete/<int:pid>', methods=['DELETE'])
@require_tenant_access
def payment_identity_delete(pid):
    """刪除收支對象"""
    from flask import jsonify
    
    with get_db() as db:
        identity = db.query(PaymentIdentity).filter_by(id=pid).first()
        if not identity:
            return jsonify({'error': '收支對象不存在'}), 404
        
        # 檢查是否被使用中
        from model import Money
        usage_count = db.query(Money).filter_by(payment_identity_id=pid).count()
        if usage_count > 0:
            return jsonify({
                'error': f'無法刪除，此收支對象已被使用 {usage_count} 次',
                'usage_count': usage_count
            }), 400
        
        try:
            db.delete(identity)
            db.commit()
            return jsonify({'message': '刪除成功'}), 200
        except Exception as e:
            db.rollback()
            return jsonify({'error': f'刪除失敗：{str(e)}'}), 500

@settings_bp.route('/department_create', methods=['GET', 'POST'])
@require_tenant_access
def department_create():
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        # 取得所有部門供下拉選單用
        departments = db.query(Department).all()
        if request.method == 'POST':
            new_dept = Department(
                name=request.form.get('name', ''),
                parent_id=request.form.get('parent_id') or None,
                note=request.form.get('note', '')
            )
            db.add(new_dept)
            db.commit()
            return redirect(url_for('settings.department_manage'))
        # GET 請求顯示表單
        return render_template(
            'department_create.html',
            departments=departments,
            sidebar_items=main_menu,
            selected=selected
        )

@settings_bp.route('/project_manage', methods=['GET'])
@require_tenant_access
def project_manage():
    """專案管理"""
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        projects = db.query(Project).all()
        projects = [
            {
                'id': proj.id,
                'name': proj.name,
                'code': proj.code,
                'status': proj.status,
                'department_name': db.query(Department).filter_by(id=proj.department_id).first().name if proj.department_id else '（無）',
                'manager': proj.manager or '（無）',
                'budget': f"{proj.budget:,}" if proj.budget else '（無）',
                'start_date': proj.start_date.strftime('%Y-%m-%d') if proj.start_date else '（無）',
                'end_date': proj.end_date.strftime('%Y-%m-%d') if proj.end_date else '（無）',
                'note': proj.note or ''
            }
            for proj in projects
        ]
    return render_template('project_manage.html', 
                         projects=projects,
                         sidebar_items=main_menu, 
                         selected=selected)

@settings_bp.route('/project_create', methods=['GET', 'POST'])
@require_tenant_access
def project_create():
    """新增專案"""
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        # 取得所有部門供下拉選單用
        departments = db.query(Department).all()
        if request.method == 'POST':
            new_project = Project(
                name=request.form.get('name', ''),
                code=request.form.get('code', ''),
                description=request.form.get('description', ''),
                start_date=parse_date(request.form.get('start_date')),
                end_date=parse_date(request.form.get('end_date')),
                status=request.form.get('status', '進行中'),
                budget=int(request.form.get('budget') or 0),
                department_id=int(request.form.get('department_id')) if request.form.get('department_id') else None,
                manager=request.form.get('manager', ''),
                note=request.form.get('note', '')
            )
            db.add(new_project)
            db.commit()
            return redirect(url_for('settings.project_manage'))
        # GET 請求顯示表單
        return render_template(
            'project_create.html',
            departments=departments,
            sidebar_items=main_menu,
            selected=selected
        )

@settings_bp.route('/project_edit/<int:project_id>', methods=['GET', 'POST'])
@require_tenant_access
def project_edit(project_id):
    """編輯專案"""
    main_menu = list(menu.keys())
    selected = '設定'
    with get_db() as db:
        project = db.query(Project).filter_by(id=project_id).first()
        if not project:
            return redirect(url_for('settings.project_manage'))
        
        departments = db.query(Department).all()
        
        if request.method == 'POST':
            project.name = request.form.get('name', '')
            project.code = request.form.get('code', '')
            project.description = request.form.get('description', '')
            project.start_date = parse_date(request.form.get('start_date'))
            project.end_date = parse_date(request.form.get('end_date'))
            project.status = request.form.get('status', '進行中')
            project.budget = int(request.form.get('budget') or 0)
            project.department_id = int(request.form.get('department_id')) if request.form.get('department_id') else None
            project.manager = request.form.get('manager', '')
            project.note = request.form.get('note', '')
            
            db.commit()
            return redirect(url_for('settings.project_manage'))
        
        return render_template(
            'project_edit.html',
            project=project,
            departments=departments,
            sidebar_items=main_menu,
            selected=selected
        )

@settings_bp.route('/project_delete/<int:project_id>', methods=['POST'])
@require_tenant_access
def project_delete(project_id):
    """刪除專案"""
    with get_db() as db:
        project = db.query(Project).filter_by(id=project_id).first()
        if project:
            db.delete(project)
            db.commit()
    return redirect(url_for('settings.project_manage'))    