"""
報表路由
整合報表生成器，提供各種財務報表
"""
from flask import Blueprint, render_template, request, jsonify
from utils.business.report_generator import ReportGenerator
from utils.database.query_helper import MoneyQueryHelper
from utils.performance.cache_manager import CacheManager
from utils.business.reports_helpers import (
    api_error_handler, get_monthly_params, get_yearly_params,
    get_account_id_param, get_common_filter_params,
    ReportParamsExtractor, api_success, api_error
)
from services.money_service import MoneyService
from datetime import datetime
from model import AccountSubject, PaymentIdentity, Department, engine
from sqlalchemy import func
from sqlalchemy.orm import sessionmaker
from utils.tenant_utils import require_tenant_access, add_tenant_filter, get_current_tenant_id

reports_bp = Blueprint('reports', __name__)
Session = sessionmaker(bind=engine)

@reports_bp.route('/reports')
@require_tenant_access
def reports_dashboard():
    """報表儀表板"""
    return render_template('reports_dashboard.html')

@reports_bp.route('/reports/monthly')
@require_tenant_access
def monthly_report():
    """月度報表"""
    params = get_monthly_params()
    
    # 生成報表
    report_data = ReportGenerator.generate_monthly_report(params['year'], params['month'])
    
    # 取得月度匯總（用於圖表）
    summary = MoneyQueryHelper.get_monthly_summary(params['year'], params['month'])
    
    return render_template('monthly_report.html', 
                         report=report_data, 
                         summary=summary,
                         **params)

@reports_bp.route('/reports/yearly')
@require_tenant_access
def yearly_report():
    """年度報表"""
    params = get_yearly_params()
    
    # 生成年度報表
    report_data = ReportGenerator.generate_yearly_report(params['year'])
    
    return render_template('yearly_report.html', 
                         report=report_data, 
                         **params)

@reports_bp.route('/reports/account_statement')
@require_tenant_access
def account_statement():
    """帳戶對帳單"""
    account_id = request.args.get('account_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # 預設為本月
    if not start_date or not end_date:
        today = datetime.now()
        start_date = today.replace(day=1).strftime('%Y-%m-%d')
        end_date = today.strftime('%Y-%m-%d')
    
    accounts = CacheManager.get_accounts()
    
    statement_data = None
    if account_id:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        statement_data = ReportGenerator.generate_account_statement(account_id, start_dt, end_dt)
    
    return render_template('account_statement.html',
                         accounts=accounts,
                         statement=statement_data,
                         selected_account_id=account_id,
                         start_date=start_date,
                         end_date=end_date)

@reports_bp.route('/reports/cash_flow')
@require_tenant_access
def cash_flow_report():
    """現金流量報表（新版，Transaction表）"""
    from data.menu_data import menu
    from model import Transaction
    from sqlalchemy import extract
    main_menu = list(menu.keys())
    selected = '我的報表'
    year = 2025
    db = Session()
    monthly = {m: {"income": 0, "expense": 0, "opening_balance": 0, "closing_balance": 0} for m in range(1, 13)}
    for m in range(1, 13):
        # 收入
        income = db.query(func.sum(Transaction.total_amount)).filter(
            Transaction.transaction_type == 'income',
            extract('year', Transaction.transaction_date) == year,
            extract('month', Transaction.transaction_date) == m
        ).scalar() or 0
        # 支出
        expense = db.query(func.sum(Transaction.total_amount)).filter(
            Transaction.transaction_type == 'expense',
            extract('year', Transaction.transaction_date) == year,
            extract('month', Transaction.transaction_date) == m
        ).scalar() or 0
        monthly[m]["income"] = income
        monthly[m]["expense"] = expense
        # 期初/期末餘額可根據你需求計算
    # 合計
    total_income = sum(monthly[m]["income"] for m in range(1, 13))
    total_expense = sum(monthly[m]["expense"] for m in range(1, 13))
    cash_flow = {
        "monthly": monthly,
        "summary": {
            "total_income": total_income,
            "total_expense": total_expense,
            "opening_balance": 0,  # 你可以根據需求計算
            "closing_balance": 0   # 你可以根據需求計算
        }
    }
    db.close()
    return render_template('cash_flow_report.html',
                          cash_flow=cash_flow,
                          year=year,
                          sidebar_items=main_menu,
                          selected=selected)

@reports_bp.route('/reports/payment_status')
@require_tenant_access
def payment_status_report():
    """付款狀態報表"""
    report_data = ReportGenerator.generate_payment_status_report()

    return render_template('payment_status_report.html', report=report_data)

@reports_bp.route('/reports/balance_sheet')
@require_tenant_access
def balance_sheet():
    """資產負債表"""
    from data.menu_data import menu
    from services.balance_sheet_service import BalanceSheetService

    # 取得查詢參數
    report_date = request.args.get('report_date')
    if not report_date:
        from datetime import date
        report_date = date.today().strftime('%Y-%m-%d')

    # 生成資產負債表數據
    balance_sheet_data = BalanceSheetService.generate_balance_sheet(report_date)

    main_menu = list(menu.keys())
    selected = '我的報表'

    return render_template('balance_sheet.html',
                         balance_sheet=balance_sheet_data,
                         report_date=report_date,
                         sidebar_items=main_menu,
                         selected=selected)

@reports_bp.route('/reports/income_statement')
@require_tenant_access
def income_statement():
    """年度損益表（月度比較）"""
    from services.new_income_statement_service import NewIncomeStatementService
    from data.menu_data import menu
    from datetime import date
    import calendar
    # 取得年份參數
    year = int(request.args.get('year', date.today().year))
    db = Session()
    try:
        service = NewIncomeStatementService(db)
        monthly_data = []
        for month in range(1, 13):
            start_date = date(year, month, 1)
            last_day = calendar.monthrange(year, month)[1]
            end_date = date(year, month, last_day)
            statement = service.generate_income_statement(start_date, end_date)
            monthly_data.append({
                'month': month,
                'revenue': statement['summary']['total_revenue'],
                'expenses': statement['summary']['total_operating_expenses'],
                'profit': statement['summary']['net_profit'],
            })
        year_totals = {
            'revenue': sum(m['revenue'] for m in monthly_data),
            'expenses': sum(m['expenses'] for m in monthly_data),
            'profit': sum(m['profit'] for m in monthly_data)
        }
    finally:
        db.close()
    main_menu = list(menu.keys())
    selected = '我的報表'
    return render_template('monthly_comparison.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         monthly_data=monthly_data,
                         year_totals=year_totals,
                         year=year)

@reports_bp.route('/reports/category_ledger')
@require_tenant_access
def category_ledger():
    """分類明細賬"""
    from data.menu_data import menu

    # 使用正確的選單資料
    main_menu = list(menu.keys())
    selected = '我的報表'

    return render_template('category_ledger.html',
                        sidebar_items=main_menu,
                        selected=selected)

@reports_bp.route('/reports/account_subject_detail')
@require_tenant_access
def account_subject_detail():
    """會計科目明細"""
    from data.menu_data import menu
    from model import AccountSubject, PaymentIdentity
    from database import get_db
    from sqlalchemy.orm import joinedload
    from collections import OrderedDict

    # 使用正確的選單資料
    main_menu = list(menu.keys())
    selected = '我的報表'

    # 固定大分類順序
    category_order = [
        '資產', '負債', '權益', '營業收入', '營業成本',
        '營業費用', '營業外收益及費損', '所得稅'
    ]

    with get_db() as db:
        # 查詢所有科目，預先載入 children
        all_subjects = db.query(AccountSubject).options(
            joinedload(AccountSubject.children)
        ).all()

        # 查詢所有收支對象
        payment_identities = db.query(PaymentIdentity).filter(
            PaymentIdentity.is_active == True,
            PaymentIdentity.is_deleted == False
        ).order_by(PaymentIdentity.name).all()

        # 查詢所有部門
        departments = db.query(Department).filter(Department.is_deleted == False, Department.is_active == True).order_by(Department.name).all()
        department_data = []
        for dept in departments:
            department_data.append({
                'id': dept.id,
                'name': dept.name,
                'code': dept.code
            })

        # 依 top_category 分組
        category_dict = OrderedDict()
        for cat in category_order:
            category_dict[cat] = []

        for subj in all_subjects:
            cat = subj.top_category or '其他'
            if cat in category_dict and subj.parent_id is None:
                category_dict[cat].append(subj)
        
        # 在 Session 還開啟時，將資料轉換為字典格式
        category_data = {}
        for cat, subjects in category_dict.items():
            category_data[cat] = []
            for subj in subjects:
                subject_data = {
                    'id': subj.id,
                    'code': subj.code,
                    'name': subj.name,
                    'note': subj.note,
                    'is_expandable': subj.is_expandable,
                    'children': []
                }
                
                # 處理子科目
                if subj.children:
                    for child in subj.children:
                        child_data = {
                            'id': child.id,
                            'code': child.code,
                            'name': child.name,
                            'note': child.note,
                            'is_expandable': child.is_expandable
                        }
                        subject_data['children'].append(child_data)
                
                category_data[cat].append(subject_data)

        # 轉換收支對象資料
        payment_identity_data = []
        for identity in payment_identities:
            identity_data = {
                'id': identity.id,
                'name': identity.name,
                'type': identity.type or '其他',
                'tax_id': identity.tax_id or '',
                'contact': identity.contact or ''
            }
            payment_identity_data.append(identity_data)

    return render_template('account_subject_detail.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         category_data=category_data,
                         category_order=category_order,
                         payment_identity_data=payment_identity_data,
                         department_data=department_data)



@reports_bp.route('/reports/project_department_analysis')
@require_tenant_access
def project_department_analysis():
    """專案/部門別分析"""
    from data.menu_data import menu
    from model import Transaction, Department, Project
    from sqlalchemy import extract, func, and_, or_
    from datetime import datetime, date
    import calendar

    # 使用正確的選單資料
    main_menu = list(menu.keys())
    selected = '我的報表'

    # 取得年度參數，預設為今年
    year = int(request.args.get('year', date.today().year))
    
    db = Session()
    try:
        # 查詢所有活躍部門
        departments = db.query(Department).filter(
            Department.is_deleted == False,
            Department.is_active == True
        ).all()

        # 查詢所有專案
        projects = db.query(Project).filter(
            Project.is_deleted == False
        ).all()

        # 計算部門統計資料
        department_data = []
        total_revenue = 0
        total_expense = 0
        active_departments = 0

        for dept in departments:
            # 部門收入
            dept_revenue = db.query(func.sum(Transaction.total_amount)).filter(
                Transaction.transaction_type == 'income',
                Transaction.department_id == dept.id,
                extract('year', Transaction.transaction_date) == year
            ).scalar() or 0

            # 部門支出
            dept_expense = db.query(func.sum(Transaction.total_amount)).filter(
                Transaction.transaction_type == 'expense',
                Transaction.department_id == dept.id,
                extract('year', Transaction.transaction_date) == year
            ).scalar() or 0

            # 如果有收支活動，計為活躍部門
            if dept_revenue > 0 or dept_expense > 0:
                active_departments += 1

            # 計算利潤和利潤率
            dept_profit = dept_revenue - dept_expense
            dept_margin = (dept_profit / dept_revenue * 100) if dept_revenue > 0 else 0

            # 計算預算執行率（這裡使用簡單的邏輯，實際可能需要更複雜的計算）
            budget_rate = min(100, (dept_expense / max(dept_revenue, 1)) * 100) if dept_revenue > 0 else 0

            department_data.append({
                'id': dept.id,
                'name': dept.name,
                'code': dept.code,
                'revenue': dept_revenue,
                'expense': dept_expense,
                'profit': dept_profit,
                'margin': round(dept_margin, 1),
                'budget_rate': round(budget_rate, 1)
            })

            total_revenue += dept_revenue
            total_expense += dept_expense

        # 計算專案統計資料
        project_data = []
        active_projects = 0

        for proj in projects:
            # 專案實際成本（從交易記錄計算）
            actual_cost = db.query(func.sum(Transaction.total_amount)).filter(
                Transaction.transaction_type == 'expense',
                Transaction.project_id == proj.id,
                extract('year', Transaction.transaction_date) == year
            ).scalar() or 0

            # 如果有實際成本或預算，計為活躍專案
            if actual_cost > 0 or (proj.budget and proj.budget > 0):
                active_projects += 1

            # 計算進度（簡單邏輯：基於成本比例和狀態）
            if proj.budget and proj.budget > 0:
                progress = min(100, (actual_cost / proj.budget) * 100)
            else:
                progress = 100 if proj.status == '已完成' else 50 if proj.status == '進行中' else 0

            # 取得負責部門名稱
            dept_name = '未指定'
            if proj.department_id:
                dept = db.query(Department).filter(Department.id == proj.department_id).first()
                if dept:
                    dept_name = dept.name

            project_data.append({
                'id': proj.id,
                'name': proj.name,
                'code': proj.code,
                'department': dept_name,
                'budget': proj.budget or 0,
                'actual_cost': actual_cost,
                'progress': round(progress, 0),
                'status': proj.status or '進行中',
                'start_date': proj.start_date.strftime('%Y-%m-%d') if proj.start_date else '',
                'end_date': proj.end_date.strftime('%Y-%m-%d') if proj.end_date else '',
                'manager': proj.manager or ''
            })

        # 組織分析資料
        analysis_data = {
            'year': year,
            'overview': {
                'active_departments': active_departments,
                'active_projects': active_projects,
                'total_revenue': total_revenue,
                'total_expense': total_expense
            },
            'departments': department_data,
            'projects': project_data
        }

    finally:
        db.close()

    return render_template('project_department_analysis.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         analysis_data=analysis_data,
                         year=year)

@reports_bp.route('/reports/payment_object_analysis')
@require_tenant_access
def payment_object_analysis():
    """收支對象分析"""
    from data.menu_data import menu
    from model import Transaction, PaymentIdentity, PaymentIdentityType
    from sqlalchemy import extract, func, and_, or_, desc
    from datetime import datetime, date

    # 使用正確的選單資料
    main_menu = list(menu.keys())
    selected = '我的報表'

    # 取得年度參數，預設為今年
    year = int(request.args.get('year', date.today().year))
    
    db = Session()
    try:
        # 查詢所有活躍的收支對象
        payment_identities = db.query(PaymentIdentity).filter(
            PaymentIdentity.is_deleted == False,
            PaymentIdentity.is_active == True
        ).all()

        # 計算收支對象統計資料
        object_data = []
        total_income = 0
        total_expense = 0
        income_objects = 0
        expense_objects = 0

        for identity in payment_identities:
            # 計算該對象的收入
            obj_income = db.query(func.sum(Transaction.total_amount)).filter(
                Transaction.transaction_type == 'income',
                Transaction.payment_identity_id == identity.id,
                extract('year', Transaction.transaction_date) == year
            ).scalar() or 0

            # 計算該對象的支出
            obj_expense = db.query(func.sum(Transaction.total_amount)).filter(
                Transaction.transaction_type == 'expense',
                Transaction.payment_identity_id == identity.id,
                extract('year', Transaction.transaction_date) == year
            ).scalar() or 0

            # 計算交易次數
            transaction_count = db.query(func.count(Transaction.id)).filter(
                Transaction.payment_identity_id == identity.id,
                extract('year', Transaction.transaction_date) == year
            ).scalar() or 0

            # 如果有交易記錄，才加入分析
            if obj_income > 0 or obj_expense > 0:
                # 計算淨額
                net_amount = obj_income - obj_expense
                
                # 計算平均交易金額
                avg_amount = (obj_income + obj_expense) / transaction_count if transaction_count > 0 else 0

                # 取得最後交易日期
                last_transaction = db.query(Transaction.transaction_date).filter(
                    Transaction.payment_identity_id == identity.id,
                    extract('year', Transaction.transaction_date) == year
                ).order_by(desc(Transaction.transaction_date)).first()
                
                last_date = last_transaction[0].strftime('%Y-%m-%d') if last_transaction else ''

                # 取得對象類型
                obj_type = identity.type or '未分類'
                if identity.type_id:
                    type_obj = db.query(PaymentIdentityType).filter(PaymentIdentityType.id == identity.type_id).first()
                    if type_obj:
                        obj_type = type_obj.name

                object_data.append({
                    'id': identity.id,
                    'name': identity.name,
                    'type': obj_type,
                    'tax_id': identity.tax_id or '',
                    'income': obj_income,
                    'expense': obj_expense,
                    'net': net_amount,
                    'transactions': transaction_count,
                    'avg_amount': round(avg_amount, 0),
                    'last_date': last_date,
                    'contact': identity.contact or '',
                    'mobile': identity.mobile or '',
                    'note': identity.note or ''
                })

                # 統計總覽數據
                if obj_income > 0:
                    income_objects += 1
                if obj_expense > 0:
                    expense_objects += 1
                
                total_income += obj_income
                total_expense += obj_expense

        # 按淨額排序（絕對值）
        object_data.sort(key=lambda x: abs(x['net']), reverse=True)

        # 組織分析資料
        analysis_data = {
            'year': year,
            'overview': {
                'income_objects': income_objects,
                'expense_objects': expense_objects,
                'total_income': total_income,
                'total_expense': total_expense,
                'total_objects': len(object_data)
            },
            'objects': object_data
        }

    finally:
        db.close()

    return render_template('payment_object_analysis.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         analysis_data=analysis_data,
                         year=year)



@reports_bp.route('/api/reports/monthly_summary')
@api_error_handler
def api_monthly_summary():
    """月度匯總 API"""
    params = get_monthly_params()
    summary = MoneyQueryHelper.get_monthly_summary(params['year'], params['month'])
    return summary

@reports_bp.route('/api/reports/yearly_trend')
@api_error_handler
def api_yearly_trend():
    """年度趨勢 API"""
    params = get_yearly_params()
    trend_data = MoneyQueryHelper.get_yearly_summary(params['year'])
    return trend_data

@reports_bp.route('/api/reports/account_balance')
@api_error_handler
def api_account_balance():
    """帳戶餘額 API"""
    account_id = get_account_id_param(required=True)
    
    balance = MoneyQueryHelper.get_account_balance(account_id)
    
    # 取得帳戶活動摘要
    activity = MoneyService.get_account_activity_summary(account_id, days=30)
    
    return {
        'balance': balance,
        'activity': activity
    }

@reports_bp.route('/api/reports/top_expenses')
@api_error_handler
def api_top_expenses():
    """最大支出 API"""
    params = ReportParamsExtractor.extract_top_expenses_params()
    
    expenses = MoneyQueryHelper.get_top_expenses(
        limit=params['limit'],
        year=params['year'], 
        month=params['month']
    )
    
    # 轉換為 JSON 格式
    expenses_data = []
    for expense in expenses:
        expenses_data.append({
            'id': expense.id,
            'name': expense.name,
            'total': expense.total,
            'a_time': expense.a_time.isoformat() if expense.a_time else None,
            'account_name': expense.account.name if expense.account else None,
            'subject_name': expense.subject.name if expense.subject else None
        })
    
    return expenses_data

@reports_bp.route('/api/reports/cache_stats')
@api_error_handler
def api_cache_stats():
    """快取統計 API"""
    stats = CacheManager.get_cache_stats()
    return stats

@reports_bp.route('/api/reports/refresh_cache', methods=['POST'])
@api_error_handler
def api_refresh_cache():
    """重新整理快取 API"""
    CacheManager.refresh_all()
    return {'message': '快取已重新整理'}

@reports_bp.route('/api/reports/subject_detail')
@api_error_handler
def api_subject_detail():
    """科目明細 API"""
    params = get_common_filter_params()
    
    if not params['subject_code'] and not params['subject_category']:
        raise ValueError('請選擇會計科目或分類')
    
    from model import JournalEntry, Transaction, AccountSubject, PaymentIdentity, Department
    from database import get_db
    from datetime import datetime
    from sqlalchemy import extract
    
    with get_db() as db:
        # 取得要查詢的科目代碼清單
        subject_codes = []
        if params['subject_code']:
            subject_codes = [params['subject_code']]
        elif params['subject_category']:
            # 查詢該分類下所有最底層科目代碼（is_expandable=False）
            subject_codes = [s.code for s in db.query(AccountSubject).filter(
                AccountSubject.top_category == params['subject_category'], 
                AccountSubject.is_expandable == False
            ).all()]
        
        if not subject_codes:
            return []
            
        # 查詢分錄
        query = db.query(
            JournalEntry,
            Transaction,
            AccountSubject,
            PaymentIdentity,
            Department
        ).join(
            Transaction, JournalEntry.transaction_id == Transaction.id
        ).join(
            AccountSubject, JournalEntry.subject_code == AccountSubject.code
        ).outerjoin(
            PaymentIdentity, Transaction.payment_identity_id == PaymentIdentity.id
        ).outerjoin(
            Department, Transaction.department_id == Department.id
        ).filter(
            JournalEntry.subject_code.in_(subject_codes)
        )
        
        # 其他條件
        if params['payment_identity_id']:
            query = query.filter(Transaction.payment_identity_id == params['payment_identity_id'])
        if params['year']:
            query = query.filter(extract('year', Transaction.transaction_date) == int(params['year']))
        if params['month']:
            query = query.filter(extract('month', Transaction.transaction_date) == int(params['month']))
        if params['department_id']:
            query = query.filter(Transaction.department_id == params['department_id'])
        
        query = query.order_by(Transaction.transaction_date.desc())
        entries = query.all()
        
        # 計算餘額
        balance = 0
        result_data = []
        for entry in entries:
            journal_entry, transaction, account_subject, payment_identity, department = entry
            if journal_entry.debit_amount > 0:
                balance += journal_entry.debit_amount
            else:
                balance -= journal_entry.credit_amount
            result_data.append({
                'date': transaction.transaction_date.strftime('%Y-%m-%d') if transaction.transaction_date else '',
                'subject_code': journal_entry.subject_code,
                'subject_name': account_subject.name if account_subject else '',
                'description': journal_entry.description or transaction.description or '',
                'debit_amount': journal_entry.debit_amount or 0,
                'credit_amount': journal_entry.credit_amount or 0,
                'balance': balance,
                'payment_identity': payment_identity.name if payment_identity else '',
                'department': department.name if department else ''
            })
        
        return result_data