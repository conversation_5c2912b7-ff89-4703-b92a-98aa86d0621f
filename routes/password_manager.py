"""
密碼管理路由
提供密碼變更、強度檢查等功能
"""
from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, session
from database import get_db
from model import User
from werkzeug.security import generate_password_hash, check_password_hash
from utils.security.password_policy import validate_password_strength, password_policy
from utils.security.auth_decorators import login_required
from utils.security.security_monitor import security_monitor
from datetime import datetime

password_manager_bp = Blueprint('password_manager', __name__, url_prefix='/password')

@password_manager_bp.route('/change', methods=['GET', 'POST'])
@login_required
def change_password():
    """密碼變更頁面"""
    if request.method == 'POST':
        try:
            user_id = session.get('user_id')
            current_password = request.form.get('current_password', '').strip()
            new_password = request.form.get('new_password', '').strip()
            confirm_password = request.form.get('confirm_password', '').strip()
            
            errors = []
            
            # 基本驗證
            if not current_password:
                errors.append('請輸入當前密碼')
            
            if not new_password:
                errors.append('請輸入新密碼')
            
            if new_password != confirm_password:
                errors.append('新密碼確認不匹配')
            
            if errors:
                for error in errors:
                    flash(error, 'error')
                return render_template('password/change.html')
            
            # 驗證當前用戶和密碼
            with get_db() as db:
                user = db.query(User).filter(User.id == user_id).first()
                if not user or not check_password_hash(user.password_hash, current_password):
                    flash('當前密碼錯誤', 'error')
                    
                    # 記錄可疑活動
                    security_monitor.log_sensitive_operation(
                        'FAILED_PASSWORD_CHANGE',
                        session.get('username', 'unknown'),
                        {
                            'reason': 'incorrect_current_password',
                            'ip': request.remote_addr
                        }
                    )
                    return render_template('password/change.html')
                
                # 檢查密碼更改間隔
                if user.updated_at and not password_policy.can_change_password(user.updated_at):
                    flash('密碼更改過於頻繁，請稍後再試', 'error')
                    return render_template('password/change.html')
                
                # 驗證新密碼強度
                password_result = validate_password_strength(new_password, user.username, user_id)
                if not password_result['is_valid']:
                    for error in password_result['errors']:
                        flash(error, 'error')
                    
                    # 顯示密碼強度信息
                    flash(f'密碼強度: {password_result["strength"]} ({password_result["score"]}/100)', 'info')
                    return render_template('password/change.html', 
                                         password_recommendations=password_result['recommendations'])
                
                # 更新密碼
                new_password_hash = generate_password_hash(new_password)
                user.password_hash = new_password_hash
                user.updated_at = datetime.now()
                user.updated_by = user.username
                
                # 添加到密碼歷史
                password_policy.add_password_to_history(user_id, new_password_hash)
                
                db.commit()
                
                # 記錄安全操作
                security_monitor.log_sensitive_operation(
                    'PASSWORD_CHANGED',
                    user.username,
                    {
                        'password_strength': password_result['strength'],
                        'password_score': password_result['score'],
                        'ip': request.remote_addr
                    }
                )
                
                flash('密碼更改成功！', 'success')
                return redirect(url_for('main.index'))
                
        except Exception as e:
            flash(f'密碼更改失敗: {str(e)}', 'error')
    
    return render_template('password/change.html')

@password_manager_bp.route('/api/check-strength', methods=['POST'])
@login_required
def check_password_strength():
    """檢查密碼強度 API"""
    try:
        data = request.get_json()
        password = data.get('password', '')
        username = data.get('username', session.get('username', ''))
        user_id = session.get('user_id')
        
        if not password:
            return jsonify({
                'error': '密碼不能為空'
            }), 400
        
        result = validate_password_strength(password, username, user_id)
        
        return jsonify({
            'is_valid': result['is_valid'],
            'score': result['score'],
            'strength': result['strength'],
            'errors': result['errors'],
            'recommendations': result['recommendations']
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@password_manager_bp.route('/api/password-requirements')
@login_required
def get_password_requirements():
    """獲取密碼要求"""
    return jsonify({
        'min_length': password_policy.min_length,
        'max_length': password_policy.max_length,
        'require_uppercase': password_policy.require_uppercase,
        'require_lowercase': password_policy.require_lowercase,
        'require_digits': password_policy.require_digits,
        'require_special': password_policy.require_special,
        'special_chars': password_policy.special_chars,
        'max_history_count': password_policy.max_history_count,
        'password_expiry_days': password_policy.password_expiry_days
    })

@password_manager_bp.route('/status')
@login_required
def password_status():
    """密碼狀態頁面"""
    try:
        user_id = session.get('user_id')
        with get_db() as db:
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                flash('用戶不存在', 'error')
                return redirect(url_for('auth.login'))
            
            # 計算密碼狀態
            last_change = user.updated_at or user.created_at
            days_until_expiry = password_policy.days_until_expiry(last_change)
            is_expired = password_policy.is_password_expired(last_change)
            can_change = password_policy.can_change_password(last_change)
            
            status_info = {
                'last_change': last_change.strftime('%Y-%m-%d %H:%M:%S') if last_change else '未知',
                'days_until_expiry': days_until_expiry,
                'is_expired': is_expired,
                'can_change': can_change,
                'password_expiry_days': password_policy.password_expiry_days
            }
            
            return render_template('password/status.html', 
                                 user=user, 
                                 status=status_info)
    
    except Exception as e:
        flash(f'獲取密碼狀態失敗: {str(e)}', 'error')
        return redirect(url_for('main.index'))

@password_manager_bp.route('/force-change')
@login_required
def force_password_change():
    """強制密碼變更（管理員功能）"""
    try:
        # 檢查是否為管理員
        if not session.get('is_admin', False):
            flash('權限不足', 'error')
            return redirect(url_for('main.index'))
        
        target_user_id = request.args.get('user_id', type=int)
        if not target_user_id:
            flash('缺少用戶 ID', 'error')
            return redirect(url_for('admin.user_list'))
        
        with get_db() as db:
            user = db.query(User).filter(User.id == target_user_id).first()
            if not user:
                flash('用戶不存在', 'error')
                return redirect(url_for('admin.user_list'))
            
            # 設置密碼為過期狀態（將更新時間設為很久以前）
            from datetime import datetime, timedelta
            user.updated_at = datetime.now() - timedelta(days=password_policy.password_expiry_days + 1)
            db.commit()
            
            # 記錄管理操作
            security_monitor.log_sensitive_operation(
                'FORCE_PASSWORD_EXPIRY',
                session.get('username', 'admin'),
                {
                    'target_user': user.username,
                    'target_user_id': target_user_id,
                    'ip': request.remote_addr
                }
            )
            
            flash(f'已強制用戶 {user.username} 的密碼過期', 'success')
            return redirect(url_for('admin.user_list'))
    
    except Exception as e:
        flash(f'操作失敗: {str(e)}', 'error')
        return redirect(url_for('admin.user_list'))