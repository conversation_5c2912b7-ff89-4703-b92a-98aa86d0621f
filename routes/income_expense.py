from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, session
from data.menu_data import menu
from model import Account, PaymentIdentity, Money, AccountSubject, Transaction
from model import PaymentIdentityType
from utils.business.audit_helper import <PERSON><PERSON><PERSON>elper, get_current_user
from utils.business.income_expense_helpers import parse_date, parse_date_range, apply_transaction_filters, extract_filter_params, log_query_info, get_order_clause
from services.money_service import MoneyService
from services.journal_validator import JournalValidator, JournalValidationError
from utils.security.file_upload_security import secure_file_upload
from utils.tenant_utils import require_tenant_access, safe_get_by_id, add_tenant_filter, get_current_tenant_id
from datetime import datetime
import os

# 導入基礎路由功能
from routes.base_route import BaseRoute, with_form_data, handle_errors, audit_trail, RouteHelper
from database import get_db, Session

income_expense_bp = Blueprint('income_expense', __name__)

@income_expense_bp.route('/income_record', methods=['GET', 'POST'])
@with_form_data
@handle_errors()
@audit_trail('income_record')
@require_tenant_access
def income_record():
    """收入紀錄頁面（優化版）"""
    from flask import g
    
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    
    # 表單數據已經通過 @with_form_data 注入到 g.form_data
    form_data = g.form_data
    accounts = form_data['accounts']
    departments = form_data['departments']
    projects = form_data['projects']
    identities_by_type = form_data['identities_by_type']
    company_id = form_data['company_id']

    # 檢查是否為編輯模式
    edit_id = request.args.get('edit_id')
    edit_record = None
    if edit_id:
        try:
            with get_db() as db:
                # 使用租戶隔離的安全查詢
                query = db.query(Money).filter_by(id=edit_id, money_type='收入', entry_side='CREDIT')
                query = add_tenant_filter(query, Money)
                edit_record = query.first()
                if not edit_record:
                    flash('找不到該收入記錄或無權限存取', 'error')
                    return redirect('/income_list')
        except Exception as e:
            flash(f'載入記錄失敗：{str(e)}', 'error')
            return redirect('/income_list')

    if request.method == 'POST':
        form = request.form
        
        # 檢查是否為編輯模式
        edit_id = form.get('edit_id')
        if edit_id:
            # 編輯模式
            try:
                edit_record = db.query(Money).filter_by(id=edit_id, money_type='收入', entry_side='CREDIT').first()
                if not edit_record:
                    flash('找不到該收入記錄', 'error')
                    return redirect('/income_list')
                
                # 更新記錄
                edit_record.a_time = parse_date(form.get('a_time'))
                edit_record.name = form.get('name')
                edit_record.total = int(form.get('total') or 0)
                edit_record.extra_fee = int(form.get('extra_fee') or 0)
                edit_record.subject_code = form.get('subject_code')
                edit_record.account_id = form.get('account_id')
                edit_record.payment_identity_id = form.get('payment_identity_id')
                edit_record.note = form.get('note')
                edit_record.department_id = form.get('department_id')
                edit_record.project_id = form.get('project_id')
                edit_record.tags = form.get('tag')
                
                db.commit()
                flash('收入記錄更新成功', 'success')
                return redirect('/income_list')
                
            except Exception as e:
                db.rollback()
                flash(f'更新失敗：{str(e)}', 'error')
                return render_template('income_record.html',
                                    sidebar_items=main_menu,
                                    selected=selected,
                                    accounts=accounts,
                                    departments=departments,
                                    projects=projects,
                                    identities_by_type=identities_by_type,
                                    company_id=company_id,
                                    edit_record=edit_record,
                                    edit_mode=True)
        
        # 新增模式
        invoice_number = form.get('invoice_number')
        paper_status = form.get('paper_status')  # 憑證狀態 radio
        is_paper = form.get('is_paper')         # 收據類憑證 checkbox
        # 只有「有憑證」且「收據類憑證」沒打勾時才檢查
        if paper_status == 'has' and not is_paper and invoice_number:
            exists = db.query(Money).filter(Money.number == invoice_number).first()
            if exists:
                db.close()
                return render_template(
                    'income_record.html',
                    sidebar_items=main_menu,
                    selected=selected,
                    accounts=accounts,
                    departments=departments,
                    projects=projects,
                    identities_by_type=identities_by_type,
                    company_id=company_id,
                    error='發票號碼已存在，請重新輸入'
                )
        # 生成分錄參考號
        import uuid
        journal_ref = f"JE-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8]}"

        # 計算金額
        revenue_amount = int(form.get('total') or 0)  # 用戶輸入的是未稅金額
        tax_amount = int(form.get('tax') or 0)        # 稅額
        total_amount = revenue_amount + tax_amount    # 含稅總額 = 未稅金額 + 稅額

        # 創建貸方記錄（收入科目）- 記錄未稅金額
        credit_entry = Money(
            a_time = parse_date(form.get('a_time')),
            name = form.get('name'),
            total = revenue_amount,  # 未稅金額
            tax = 0,  # 收入科目不記錄稅額
            extra_fee = int(form.get('extra_fee') or 0),
            subject_code = form.get('subject_code'),
            account_id = form.get('account_id'),
            payment_identity_id = form.get('payment_identity_id'),
            money_type = '收入',
            is_paper = bool(form.get('is_paper')),
            number = invoice_number,
            tax_type = form.get('tax_type'),
            buyer_tax_id = form.get('buyer_tax_id'),
            seller_tax_id = form.get('seller_tax_id'),
            date = form.get('invoice_date'),
            is_paid = form.get('is_paid') == '1',
            should_paid_date = parse_date(form.get('should_paid_date')),
            paid_date = parse_date(form.get('paid_date')),
            note = form.get('note'),
            department_id = form.get('department_id'),
            project_id = form.get('project_id'),
            tags = form.get('tag'),
            image_path = None,
            entry_side = 'CREDIT',  # 收入是貸方
            journal_reference = journal_ref
        )

        # 設定審計欄位
        AuditHelper.set_create_audit(credit_entry, get_current_user())
        db.add(credit_entry)

        # 創建銷項稅額分錄（如果有稅額）
        if tax_amount > 0:
            output_tax_entry = Money(
                a_time = parse_date(form.get('a_time')),
                name = f'{form.get("name")} - 銷項稅額',
                total = tax_amount,  # 稅額
                tax = 0,
                extra_fee = 0,
                subject_code = '2290',  # 銷項稅額科目
                account_id = form.get('account_id'),
                payment_identity_id = form.get('payment_identity_id'),
                money_type = '收入',
                is_paper = bool(form.get('is_paper')),
                number = invoice_number,
                tax_type = form.get('tax_type'),
                buyer_tax_id = form.get('buyer_tax_id'),
                seller_tax_id = form.get('seller_tax_id'),
                date = form.get('invoice_date'),
                is_paid = form.get('is_paid') == '1',
                should_paid_date = parse_date(form.get('should_paid_date')),
                paid_date = parse_date(form.get('paid_date')),
                note = f'{form.get("note")} - 銷項稅額',
                department_id = form.get('department_id'),
                project_id = form.get('project_id'),
                tags = form.get('tag'),
                image_path = None,
                entry_side = 'CREDIT',  # 銷項稅額是貸方
                journal_reference = journal_ref
            )
            AuditHelper.set_create_audit(output_tax_entry, get_current_user())
            db.add(output_tax_entry)

        # 創建對應的借方記錄（銀行存款增加）
        account_id = form.get('account_id')
        if account_id:
            account = db.query(Account).filter_by(id=account_id).first()
            if account and account.subject_code:
                # 獲取帳戶的完整會計科目代碼
                if account.category == '銀行帳戶' and account.subject_code:
                    full_subject_code = f'1110{str(account.subject_code).zfill(3)}'
                elif account.category == '現金' and account.subject_code:
                    full_subject_code = f'1105{str(account.subject_code).zfill(3)}'
                else:
                    full_subject_code = account.subject_code

                debit_entry = Money(
                    a_time = parse_date(form.get('a_time')),
                    name = f'{form.get("name")} - 收款',
                    total = total_amount,  # 含稅總額
                    tax = 0,
                    extra_fee = 0,
                    subject_code = full_subject_code,
                    account_id = account_id,
                    payment_identity_id = form.get('payment_identity_id'),
                    money_type = '收入',
                    is_paper = bool(form.get('is_paper')),
                    number = invoice_number,
                    tax_type = form.get('tax_type'),
                    buyer_tax_id = form.get('buyer_tax_id'),
                    seller_tax_id = form.get('seller_tax_id'),
                    date = form.get('invoice_date'),
                    is_paid = form.get('is_paid') == '1',
                    should_paid_date = parse_date(form.get('should_paid_date')),
                    paid_date = parse_date(form.get('paid_date')),
                    note = f'{form.get("note")} - 自動產生的借方分錄',
                    department_id = form.get('department_id'),
                    project_id = form.get('project_id'),
                    tags = form.get('tag'),
                    image_path = None,
                    entry_side = 'DEBIT',  # 銀行存款增加是借方
                    journal_reference = journal_ref
                )

                # 設定審計欄位
                AuditHelper.set_create_audit(debit_entry, get_current_user())
                db.add(debit_entry)

        # 驗證複式記帳完整性
        try:
            validator = JournalValidator(db)
            entries_to_validate = [credit_entry]
            if 'debit_entry' in locals():
                entries_to_validate.append(debit_entry)
            if 'output_tax_entry' in locals():
                entries_to_validate.append(output_tax_entry)

            validator.validate_before_commit(entries_to_validate)
            db.commit()

            # 提交後再次驗證
            validation_result = validator.validate_journal_entries(journal_ref)
            if not validation_result['is_balanced']:
                current_app.logger.warning(f"分錄 {journal_ref} 借貸不平衡，驗證結果：{validation_result}")

        except JournalValidationError as e:
            db.rollback()
            return render_template('income_record.html',
                                sidebar_items=menu,
                                selected='收入記錄',
                                accounts=db.query(Account).all(),
                                departments=[],
                                projects=[],
                                identities_by_type={},
                                company_id=None,
                                error=f'分錄驗證失敗：{str(e)}'
                            )
        # 安全檔案上傳處理
        file = request.files.get('other_file')
        current_app.logger.info(f'收到檔案上傳: {file.filename if file else "無"}')
        if file and file.filename:
            # 使用安全的檔案上傳系統
            user_id = session.get('user_id')
            upload_result = secure_file_upload(file, user_id)
            
            if upload_result['is_valid'] and upload_result.get('success'):
                # 更新主要記錄的 image_path (儲存相對路徑)
                credit_entry.image_path = upload_result['relative_path']
                current_app.logger.info(f'安全上傳成功: {upload_result["safe_filename"]}')
                
                # 顯示警告訊息（如果有的話）
                for warning in upload_result.get('warnings', []):
                    flash(f'檔案上傳警告: {warning}', 'warning')
                
                db.commit()
            else:
                # 上傳失敗，顯示錯誤訊息
                for error in upload_result.get('errors', ['檔案上傳失敗']):
                    flash(f'檔案上傳錯誤: {error}', 'error')
                current_app.logger.warning(f'檔案上傳失敗: {upload_result.get("errors", [])}')
                
                # 不中斷流程，記錄仍然保存，只是沒有附件
        return redirect(url_for('income_expense.income_record'))

    return render_template('income_record.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        accounts=accounts,
                        departments=departments,
                        projects=projects,
                        identities_by_type=identities_by_type,
                        company_id=company_id,
                        edit_record=edit_record,
                        edit_mode=bool(edit_record))

@income_expense_bp.route('/expense_record', methods=['GET', 'POST'])
def expense_record():
    """支出紀錄頁面"""
    # 使用優化的查詢服務，一次性獲取所有表單資料
    from services.optimized_query_service import OptimizedQueryService

    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'

    # 優化：一次性獲取所有表單資料
    form_data = OptimizedQueryService.get_form_data_optimized()
    accounts = form_data['accounts']
    departments = form_data['departments']
    projects = form_data['projects']
    identities_by_type = form_data['identities_by_type']
    company_id = form_data['company_id']


    if request.method == 'POST':
        form = request.form
        invoice_number = form.get('invoice_number')
        paper_status = form.get('paper_status')  # 憑證狀態 radio
        is_paper = form.get('is_paper')         # 收據類憑證 checkbox
        # 只有「有憑證」且「收據類憑證」沒打勾時才檢查
        if paper_status == 'has' and not is_paper and invoice_number:
            exists = db.query(Money).filter(Money.number == invoice_number).first()
            if exists:
                db.close()
                return render_template(
                    'expense_record.html',
                    sidebar_items=main_menu,
                    selected=selected,
                    accounts=accounts,
                    departments=departments,
                    projects=projects,
                    identities_by_type=identities_by_type,
                    company_id=company_id,
                    error='發票號碼已存在，請重新輸入'
                )
        # 生成分錄參考號
        import uuid
        journal_ref = f"JE-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8]}"

        # 計算金額
        expense_amount = int(form.get('total') or 0)  # 用戶輸入的是未稅金額
        tax_amount = int(form.get('tax') or 0)        # 稅額
        total_amount = expense_amount + tax_amount    # 含稅總額 = 未稅金額 + 稅額

        # 創建借方記錄（費用/成本科目）- 記錄未稅金額
        debit_entry = Money(
            a_time = parse_date(form.get('a_time')),
            name = form.get('name'),
            total = expense_amount,  # 未稅金額
            tax = 0,  # 費用科目不記錄稅額
            extra_fee = int(form.get('extra_fee') or 0),
            subject_code = form.get('subject_code'),
            account_id = form.get('account_id'),
            payment_identity_id = form.get('payment_identity_id'),
            money_type = '支出',
            is_paper = bool(form.get('is_paper')),
            number = invoice_number,
            tax_type = form.get('tax_type'),
            buyer_tax_id = form.get('buyer_tax_id'),
            seller_tax_id = form.get('seller_tax_id'),
            date = form.get('invoice_date'),
            is_paid = form.get('is_paid') == '1',
            should_paid_date = parse_date(form.get('should_paid_date')),
            paid_date = parse_date(form.get('paid_date')),
            note = form.get('note'),
            department_id = form.get('department_id'),
            project_id = form.get('project_id'),
            tags = form.get('tag'),
            image_path = None,
            entry_side = 'DEBIT',  # 費用/成本是借方
            journal_reference = journal_ref
        )

        # 設定審計欄位
        AuditHelper.set_create_audit(debit_entry, get_current_user())
        db.add(debit_entry)

        # 創建進項稅額分錄（如果有稅額）
        if tax_amount > 0:
            input_tax_entry = Money(
                a_time = parse_date(form.get('a_time')),
                name = f'{form.get("name")} - 進項稅額',
                total = tax_amount,  # 稅額
                tax = 0,
                extra_fee = 0,
                subject_code = '1290',  # 進項稅額科目
                account_id = form.get('account_id'),
                payment_identity_id = form.get('payment_identity_id'),
                money_type = '支出',
                is_paper = bool(form.get('is_paper')),
                number = invoice_number,
                tax_type = form.get('tax_type'),
                buyer_tax_id = form.get('buyer_tax_id'),
                seller_tax_id = form.get('seller_tax_id'),
                date = form.get('invoice_date'),
                is_paid = form.get('is_paid') == '1',
                should_paid_date = parse_date(form.get('should_paid_date')),
                paid_date = parse_date(form.get('paid_date')),
                note = f'{form.get("note")} - 進項稅額',
                department_id = form.get('department_id'),
                project_id = form.get('project_id'),
                tags = form.get('tag'),
                image_path = None,
                entry_side = 'DEBIT',  # 進項稅額是借方
                journal_reference = journal_ref
            )
            AuditHelper.set_create_audit(input_tax_entry, get_current_user())
            db.add(input_tax_entry)

        # 創建對應的貸方記錄（銀行存款減少）
        account_id = form.get('account_id')
        if account_id:
            account = db.query(Account).filter_by(id=account_id).first()
            if account and account.subject_code:
                # 獲取帳戶的完整會計科目代碼
                if account.category == '銀行帳戶' and account.subject_code:
                    full_subject_code = f'1110{str(account.subject_code).zfill(3)}'
                elif account.category == '現金' and account.subject_code:
                    full_subject_code = f'1105{str(account.subject_code).zfill(3)}'
                else:
                    full_subject_code = account.subject_code

                credit_entry = Money(
                    a_time = parse_date(form.get('a_time')),
                    name = f'{form.get("name")} - 付款',
                    total = total_amount,  # 含稅總額
                    tax = 0,
                    extra_fee = 0,
                    subject_code = full_subject_code,
                    account_id = account_id,
                    payment_identity_id = form.get('payment_identity_id'),
                    money_type = '支出',
                    is_paper = bool(form.get('is_paper')),
                    number = invoice_number,
                    tax_type = form.get('tax_type'),
                    buyer_tax_id = form.get('buyer_tax_id'),
                    seller_tax_id = form.get('seller_tax_id'),
                    date = form.get('invoice_date'),
                    is_paid = form.get('is_paid') == '1',
                    should_paid_date = parse_date(form.get('should_paid_date')),
                    paid_date = parse_date(form.get('paid_date')),
                    note = f'{form.get("note")} - 自動產生的貸方分錄',
                    department_id = form.get('department_id'),
                    project_id = form.get('project_id'),
                    tags = form.get('tag'),
                    image_path = None,
                    entry_side = 'CREDIT',  # 銀行存款減少是貸方
                    journal_reference = journal_ref
                )

                # 設定審計欄位
                AuditHelper.set_create_audit(credit_entry, get_current_user())
                db.add(credit_entry)

        # 驗證複式記帳完整性
        try:
            validator = JournalValidator(db)
            entries_to_validate = [debit_entry]
            if 'credit_entry' in locals():
                entries_to_validate.append(credit_entry)
            if 'input_tax_entry' in locals():
                entries_to_validate.append(input_tax_entry)

            validator.validate_before_commit(entries_to_validate)
            db.commit()

            # 提交後再次驗證
            validation_result = validator.validate_journal_entries(journal_ref)
            if not validation_result['is_balanced']:
                # 如果驗證失敗，記錄錯誤但不回滾（因為已提交）
                current_app.logger.warning(f"分錄 {journal_ref} 借貸不平衡，驗證結果：{validation_result}")

        except JournalValidationError as e:
            db.rollback()
            return render_template('expense_record.html',
                                sidebar_items=menu,
                                selected='支出記錄',
                                accounts=db.query(Account).all(),
                                departments=[],
                                projects=[],
                                identities_by_type={},
                                company_id=None,
                                error=f'分錄驗證失敗：{str(e)}'
                            )
        # 檔案上傳處理
        file = request.files.get('other_file')
        current_app.logger.info(f'收到檔案上傳: {file.filename if file else "無"}')
        if file and file.filename:
            # 使用安全的檔案上傳系統
            user_id = session.get('user_id')
            upload_result = secure_file_upload(file, user_id)
            
            if upload_result['is_valid'] and upload_result.get('success'):
                # 更新主要記錄的 image_path (儲存相對路徑)
                debit_entry.image_path = upload_result['relative_path']
                current_app.logger.info(f'安全上傳成功: {upload_result["safe_filename"]}')
                
                # 顯示警告訊息（如果有的話）
                for warning in upload_result.get('warnings', []):
                    flash(f'檔案上傳警告: {warning}', 'warning')
                
                db.commit()
            else:
                # 上傳失敗，顯示錯誤訊息
                for error in upload_result.get('errors', ['檔案上傳失敗']):
                    flash(f'檔案上傳錯誤: {error}', 'error')
                current_app.logger.warning(f'檔案上傳失敗: {upload_result.get("errors", [])}')
                
                # 不中斷流程，記錄仍然保存，只是沒有附件
        return redirect(url_for('income_expense.expense_record'))

    return render_template('expense_record.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        accounts=accounts,
                        departments=departments,
                        projects=projects,
                        identities_by_type=identities_by_type,
                        company_id=company_id)

@income_expense_bp.route('/income_list')
def income_list():
    """收入列表頁面"""
    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    
    # 取得查詢參數
    date_type = request.args.get('date_type', 'a_time')
    date_start = request.args.get('date_start')
    date_end = request.args.get('date_end')
    hide_paid = request.args.get('hide_paid')

    # 構建查詢條件
    query = db.query(Money).filter(Money.money_type == '收入')
    
    # 只顯示主要交易記錄（貸方分錄，收入科目）
    query = query.filter(Money.entry_side == 'CREDIT')
    
    # 排除稅額分錄（銷項稅額科目）
    query = query.filter(Money.subject_code != '2290')
    
    if date_start and date_end and date_type in ['a_time', 'should_paid_date', 'paid_date', 'invoice_date']:
        if date_type == 'invoice_date':
            query = query.filter(Money.date >= date_start, Money.date <= date_end)
        else:
            query = query.filter(getattr(Money, date_type) >= date_start, getattr(Money, date_type) <= date_end)
    if hide_paid:
        query = query.filter(Money.is_paid == False)
    # 使用 JOIN 避免 N+1 查詢
    income_records = query.outerjoin(AccountSubject, Money.subject_code == AccountSubject.code)\
                          .outerjoin(Account, Money.account_id == Account.id)\
                          .outerjoin(PaymentIdentity, Money.payment_identity_id == PaymentIdentity.id)\
                          .add_columns(
                              AccountSubject.name.label('subject_name'),
                              Account.name.label('account_name'),
                              PaymentIdentity.name.label('payment_identity_name')
                          )\
                          .order_by(Money.a_time.desc()).all()
    
    # 準備顯示資料
    income_data = []
    for record, subject_name, account_name, payment_identity_name in income_records:
        subject_name = subject_name or ''
        account_name = account_name or ''
        payment_identity_name = payment_identity_name or ''
        a_time_str = record.a_time.strftime('%Y-%m-%d') if record.a_time else ''
        should_paid_date_str = record.should_paid_date.strftime('%Y-%m-%d') if record.should_paid_date else ''
        paid_date_str = record.paid_date.strftime('%Y-%m-%d') if record.paid_date else ''
        invoice_date_str = record.date if record.date else ''
        total_str = f"{record.total:,}" if record.total else '0'
        payment_status = '已收付款' if record.is_paid else '未收付款'
        income_data.append({
            'id': record.id,
            'a_time': a_time_str,
            'total': total_str,
            'subject_name': subject_name,
            'name': record.name or '',
            'payment_identity_name': payment_identity_name,
            'account_name': account_name,
            'payment_status': payment_status,
            'should_paid_date': should_paid_date_str,
            'paid_date': paid_date_str,
            'invoice_number': record.number or '',
            'invoice_date': invoice_date_str,
            'note': record.note or ''
        })
    db.close()
    return render_template('income_list.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        income_records=income_data)

@income_expense_bp.route('/expense_list')
def expense_list():
    """支出列表頁面"""
    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    
    # 取得查詢參數
    date_type = request.args.get('date_type', 'a_time')
    date_start = request.args.get('date_start')
    date_end = request.args.get('date_end')
    hide_paid = request.args.get('hide_paid')

    # 構建查詢條件
    query = db.query(Money).filter(Money.money_type == '支出')
    
    # 只顯示主要交易記錄（借方分錄，費用/成本科目）
    query = query.filter(Money.entry_side == 'DEBIT')
    
    # 排除稅額分錄（進項稅額科目）
    query = query.filter(Money.subject_code != '1290')
    
    if date_start and date_end and date_type in ['a_time', 'should_paid_date', 'paid_date', 'invoice_date']:
        if date_type == 'invoice_date':
            query = query.filter(Money.date >= date_start, Money.date <= date_end)
        else:
            query = query.filter(getattr(Money, date_type) >= date_start, getattr(Money, date_type) <= date_end)
    if hide_paid:
        query = query.filter(Money.is_paid == False)
    # 使用 JOIN 避免 N+1 查詢
    expense_records = query.outerjoin(AccountSubject, Money.subject_code == AccountSubject.code)\
                           .outerjoin(Account, Money.account_id == Account.id)\
                           .outerjoin(PaymentIdentity, Money.payment_identity_id == PaymentIdentity.id)\
                           .add_columns(
                               AccountSubject.name.label('subject_name'),
                               Account.name.label('account_name'),
                               PaymentIdentity.name.label('payment_identity_name')
                           )\
                           .order_by(Money.a_time.desc()).all()
    
    # 準備顯示資料
    expense_data = []
    for record, subject_name, account_name, payment_identity_name in expense_records:
        subject_name = subject_name or ''
        account_name = account_name or ''
        payment_identity_name = payment_identity_name or ''
        a_time_str = record.a_time.strftime('%Y-%m-%d') if record.a_time else ''
        should_paid_date_str = record.should_paid_date.strftime('%Y-%m-%d') if record.should_paid_date else ''
        paid_date_str = record.paid_date.strftime('%Y-%m-%d') if record.paid_date else ''
        invoice_date_str = record.date if record.date else ''
        total_str = f"{record.total:,}" if record.total else '0'
        payment_status = '已收付款' if record.is_paid else '未收付款'
        expense_data.append({
            'id': record.id,
            'a_time': a_time_str,
            'total': total_str,
            'subject_name': subject_name,
            'name': record.name or '',
            'payment_identity_name': payment_identity_name,
            'account_name': account_name,
            'payment_status': payment_status,
            'should_paid_date': should_paid_date_str,
            'paid_date': paid_date_str,
            'invoice_number': record.number or '',
            'invoice_date': invoice_date_str,
            'note': record.note or ''
        })
    db.close()
    return render_template('expense_list.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        expense_records=expense_data)

@income_expense_bp.route('/transaction_details')
def transaction_details():
    """交易明細頁面"""
    # 獲取查詢參數
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    payment_daterange = request.args.get('payment_daterange')
    voucher_daterange = request.args.get('voucher_daterange')
    expected_daterange = request.args.get('expected_daterange')
    payment_identity = request.args.get('payment_identity')
    project = request.args.get('project')
    account = request.args.get('account')
    voucher_type = request.args.get('voucher_type')
    attachment = request.args.get('attachment')
    subject_code = request.args.get('subject_code')
    transaction_status = request.args.get('transaction_status')
    real_start_date = request.args.get('real_start_date')
    real_end_date = request.args.get('real_end_date')
    note = request.args.get('note')
    target = request.args.get('target')
    keyword = request.args.get('keyword')
    payment_identity_type = request.args.get('payment_identity_type')
    amount_min = request.args.get('amount_min')
    amount_max = request.args.get('amount_max')
    order_by = request.args.get('order_by', 'a_time_desc')  # 新增排序參數
    # 解析 daterangepicker 的日期區間格式 "YYYY-MM-DD ~ YYYY-MM-DD"
    
    # 處理收付日期區間
    if payment_daterange:
        # 將 ~ 替換為 to 以符合 parse_date_range 的格式
        formatted_daterange = payment_daterange.replace(' ~ ', ' to ')
        start_date_obj, end_date_obj = parse_date_range(formatted_daterange)
        if start_date_obj and end_date_obj:
            start_date = start_date_obj.strftime('%Y-%m-%d')
            end_date = end_date_obj.strftime('%Y-%m-%d')
    
    # 處理憑證日期區間
    if voucher_daterange:
        # 將 ~ 替換為 to 以符合 parse_date_range 的格式
        formatted_daterange = voucher_daterange.replace(' ~ ', ' to ')
        start_date_obj, end_date_obj = parse_date_range(formatted_daterange)
        if start_date_obj and end_date_obj:
            real_start_date = start_date_obj.strftime('%Y-%m-%d')
            real_end_date = end_date_obj.strftime('%Y-%m-%d')
    
    # 處理預計日期區間
    if expected_daterange:
        # 將 ~ 替換為 to 以符合 parse_date_range 的格式
        formatted_daterange = expected_daterange.replace(' ~ ', ' to ')
        start_date_obj, end_date_obj = parse_date_range(formatted_daterange)
        if start_date_obj and end_date_obj:
            expected_start_date = start_date_obj.strftime('%Y-%m-%d')
            expected_end_date = end_date_obj.strftime('%Y-%m-%d')
        else:
            expected_start_date = None
            expected_end_date = None
    else:
        expected_start_date = None
        expected_end_date = None
    
    # 獲取菜單數據
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    
    # 執行查詢
    transactions = []
    db = Session()
    try:
        # 構建查詢條件
        query = db.query(Transaction)
        
        # 過濾掉開帳交易（描述以「開帳-」開頭的交易）
        query = query.filter(~Transaction.description.like('開帳-%'))
        
        # 除錯資訊
        
        # 檢查資料庫中的總記錄數
        total_count = query.count()
        
        # 提取篩選參數並應用篩選
        filters = {
            'start_date': start_date, 'end_date': end_date,
            'payment_identity': payment_identity, 'project': project, 'account': account,
            'voucher_type': voucher_type, 'attachment': attachment,
            'subject_code': subject_code, 'transaction_status': transaction_status,
            'real_start_date': real_start_date, 'real_end_date': real_end_date,
            'note': note, 'target': target, 'keyword': keyword,
            'payment_identity_type': payment_identity_type,
            'amount_min': amount_min, 'amount_max': amount_max,
            'expected_start_date': expected_start_date, 'expected_end_date': expected_end_date
        }
        
        # 檢查是否有任何篩選條件
        if any(filters.values()):
            query = apply_transaction_filters(query, filters)
            filtered_count = query.count()
            
            # 應用排序並獲取所有結果
            order_clause = get_order_clause(order_by)
            transactions = query.order_by(order_clause).all()
        else:
            # 未篩選時限制返回筆數
            order_clause = get_order_clause(order_by)
            transactions = query.order_by(order_clause).limit(50).all()
        
        
    finally:
        db.close()
    
    # 計算統計資料
    total_count = len(transactions)
    income_amount = sum(t.total_amount for t in transactions if t.transaction_type == 'income' and t.total_amount)
    expense_amount = sum(t.total_amount for t in transactions if t.transaction_type == 'expense' and t.total_amount)
    
    # 獲取下拉選單選項
    db = Session()
    try:
        accounts = db.query(Account).filter(Account.is_deleted == False).all()
        payment_identities = db.query(PaymentIdentity).all()
        subjects = db.query(AccountSubject).all()
        payment_identity_types = db.query(PaymentIdentityType).all()
    finally:
        db.close()
    
    return render_template('transaction_details.html',
                        sidebar_items=main_menu,
                        selected=selected,
                        transactions=transactions,
                        accounts=accounts,
                        payment_identities=payment_identities,
                        subjects=subjects,
                        payment_identity_types=payment_identity_types,
                        total_count=total_count,
                        income_amount=income_amount,
                        expense_amount=expense_amount)

@income_expense_bp.route('/ac_delay_list')
def ac_delay_list():
    """應收應付逾期列表"""
    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    from datetime import date
    today = date.today()
    # 使用優化的查詢方法取得逾期記錄
    overdue_records = MoneyService.get_overdue_payments()
    
    # 取得即將到期的記錄（7天內）
    # upcoming_records = MoneyService.get_upcoming_payments(days=7)  # 暫時註解，未使用
    
    # 準備顯示資料 - 已在 MoneyService.get_overdue_payments() 中使用 JOIN 優化
    overdue_data = []
    for record in overdue_records:
        # 由於已經使用 JOIN 查詢，這些關聯屬性已經預載入，不會觸發額外查詢
        subject_name = record.subject.name if record.subject else ''
        account_name = record.account.name if record.account else ''
        payment_identity_name = record.payment_identity.name if record.payment_identity else ''
        a_time_str = record.a_time.strftime('%Y-%m-%d') if record.a_time else ''
        should_paid_date_str = record.should_paid_date.strftime('%Y-%m-%d') if record.should_paid_date else ''
        paid_date_str = record.paid_date.strftime('%Y-%m-%d') if record.paid_date else ''
        invoice_date_str = record.date if record.date else ''
        total_str = f"{record.total:,}" if record.total else '0'
        payment_status = '已收付款' if record.is_paid else '未收付款'
        
        # 計算逾期天數
        overdue_days = (today - record.should_paid_date.date()).days if record.should_paid_date else 0
        
        overdue_data.append({
            'id': record.id,
            'a_time': a_time_str,
            'total': total_str,
            'subject_name': subject_name,
            'name': record.name or '',
            'payment_identity_name': payment_identity_name,
            'account_name': account_name,
            'payment_status': payment_status,
            'should_paid_date': should_paid_date_str,
            'paid_date': paid_date_str,
            'invoice_date': invoice_date_str,
            'note': record.note or '',
            'money_type': record.money_type,
            'overdue_days': overdue_days
        })
    
    db.close()
    return render_template('ac_delay_list.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        overdue_records=overdue_data) 

@income_expense_bp.route('/journal_entries/<int:money_id>')
def journal_entries(money_id):
    """顯示某一交易的所有分錄（只讀）"""
    db = Session()
    try:
        # 先找到這筆交易
        main_entry = db.query(Money).filter(Money.id == money_id).first()
        if not main_entry:
            return '找不到該交易', 404
        # 以 journal_reference 查詢所有同分錄的明細
        journal_ref = main_entry.journal_reference
        entries = db.query(Money).filter(Money.journal_reference == journal_ref).order_by(Money.entry_side.desc()).all()
        # 取得科目名稱
        subjects = {s.code: s.name for s in db.query(AccountSubject).all()}
    finally:
        db.close()
    return render_template('journal_entries.html', entries=entries, main_entry=main_entry, subjects=subjects) 