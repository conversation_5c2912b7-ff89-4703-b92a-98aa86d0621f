"""
資料庫監控儀表板路由
提供資料庫性能監控的 Web 介面和 API
"""

from flask import Blueprint, render_template, jsonify, request
from datetime import datetime
import json

database_monitor_bp = Blueprint('database_monitor', __name__)

@database_monitor_bp.route('/database/monitor')
def database_monitor_dashboard():
    """資料庫監控儀表板主頁"""
    return render_template('database/monitor.html')

@database_monitor_bp.route('/api/database/health')
def api_database_health():
    """API 資料庫健康狀況"""
    try:
        from utils.database.db_analyzer import get_database_health_summary
        
        health_summary = get_database_health_summary()
        
        return jsonify({
            'success': True,
            'data': health_summary
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_monitor_bp.route('/api/database/analysis')
def api_database_analysis():
    """API 完整資料庫分析"""
    try:
        from utils.database.db_analyzer import db_analyzer
        
        # 執行各項分析
        size_analysis = db_analyzer.analyze_database_size()
        index_analysis = db_analyzer.analyze_indexes()
        integrity_analysis = db_analyzer.check_data_integrity()
        performance_analysis = db_analyzer.analyze_performance_bottlenecks()
        
        return jsonify({
            'success': True,
            'data': {
                'size_analysis': size_analysis,
                'index_analysis': index_analysis,
                'integrity_analysis': integrity_analysis,
                'performance_analysis': performance_analysis,
                'last_updated': datetime.now().isoformat()
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_monitor_bp.route('/api/database/optimization-plan')
def api_optimization_plan():
    """API 獲取優化計畫"""
    try:
        from utils.database.db_optimizer import get_optimization_plan
        
        plan = get_optimization_plan()
        
        return jsonify({
            'success': True,
            'data': plan
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_monitor_bp.route('/api/database/execute-optimization', methods=['POST'])
def api_execute_optimization():
    """API 執行資料庫優化"""
    try:
        from utils.database.db_optimizer import optimize_database
        
        dry_run = request.json.get('dry_run', True)
        
        result = optimize_database(dry_run=dry_run)
        
        return jsonify({
            'success': True,
            'data': result
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_monitor_bp.route('/api/database/table-stats')
def api_table_stats():
    """API 獲取表統計信息"""
    try:
        from utils.database.db_analyzer import db_analyzer
        
        size_analysis = db_analyzer.analyze_database_size()
        
        table_stats = []
        if 'table_sizes' in size_analysis:
            for table, count in size_analysis['table_sizes'].items():
                table_stats.append({
                    'table_name': table,
                    'record_count': count,
                    'percentage': round((count / size_analysis['total_records']) * 100, 2) if size_analysis['total_records'] > 0 else 0
                })
        
        # 按記錄數排序
        table_stats.sort(key=lambda x: x['record_count'], reverse=True)
        
        return jsonify({
            'success': True,
            'data': {
                'table_stats': table_stats,
                'total_tables': len(table_stats),
                'total_records': size_analysis.get('total_records', 0),
                'database_size_mb': size_analysis.get('database_file_size_mb', 0)
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_monitor_bp.route('/api/database/query-performance')
def api_query_performance():
    """API 獲取查詢性能數據"""
    try:
        from utils.database.query_optimizer import query_analyzer
        
        if not query_analyzer:
            return jsonify({
                'success': False,
                'message': 'Query analyzer not available'
            })
        
        hours = request.args.get('hours', 24, type=int)
        
        # 獲取性能摘要
        performance_summary = query_analyzer.get_performance_summary(hours)
        optimization_report = query_analyzer.get_optimization_report()
        
        # 獲取最近的慢查詢
        recent_slow_queries = []
        for query_info in list(query_analyzer.slow_queries)[-10:]:  # 最近10個
            recent_slow_queries.append({
                'query': query_info['query'][:200] + '...' if len(query_info['query']) > 200 else query_info['query'],
                'execution_time': query_info['execution_time'],
                'query_type': query_info.get('query_type', 'unknown'),
                'timestamp': datetime.fromtimestamp(query_info['timestamp']).isoformat()
            })
        
        return jsonify({
            'success': True,
            'data': {
                'performance_summary': performance_summary,
                'optimization_report': optimization_report,
                'recent_slow_queries': recent_slow_queries,
                'query_history_size': len(query_analyzer.query_history)
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@database_monitor_bp.route('/api/database/maintenance-tasks')
def api_maintenance_tasks():
    """API 獲取維護任務狀態"""
    try:
        # 這裡可以實現維護任務的狀態追蹤
        # 目前返回模擬數據
        tasks = [
            {
                'name': '索引重建',
                'status': 'completed',
                'last_run': datetime.now().isoformat(),
                'next_run': None,
                'frequency': 'monthly'
            },
            {
                'name': '統計更新',
                'status': 'completed',
                'last_run': datetime.now().isoformat(),
                'next_run': None,
                'frequency': 'weekly'
            },
            {
                'name': 'VACUUM',
                'status': 'completed',
                'last_run': datetime.now().isoformat(),
                'next_run': None,
                'frequency': 'monthly'
            },
            {
                'name': '舊會話清理',
                'status': 'pending',
                'last_run': None,
                'next_run': None,
                'frequency': 'monthly'
            }
        ]
        
        return jsonify({
            'success': True,
            'data': {
                'tasks': tasks,
                'total_tasks': len(tasks),
                'completed_tasks': len([t for t in tasks if t['status'] == 'completed']),
                'pending_tasks': len([t for t in tasks if t['status'] == 'pending'])
            }
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500