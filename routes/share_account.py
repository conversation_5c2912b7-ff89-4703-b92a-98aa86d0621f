from flask import Blueprint, render_template, request, send_file, url_for
from data.menu_data import menu
from utils.tenant_utils import require_tenant_access, add_tenant_filter, get_current_tenant_id
from model import Money, ShareAccount, AccountSubject, Department, Project
from database import get_db
import pandas as pd
from io import BytesIO
from datetime import datetime

share_account_bp = Blueprint('share_account', __name__)

# 欄位對應中文標題（根據 model.py 的 comment）
MONEY_COLUMN_LABELS = {
    'id': '編號',
    'money_type': '收支類型',
    'a_time': '記帳時間',
    'name': '名稱',
    'total': '總計',
    'extra_fee': '手續費',
    'subject_name': '科目名稱',
    'is_paper': '憑證取得',
    'number': '發票號碼',
    'tax_type': '稅別',
    'buyer_tax_id': '買方統編',
    'seller_tax_id': '賣方統編',
    'date': '發票日期',
    'note': '備註',
    'department_name': '部門別',
    'project_code': '專案別',
    'project_name': '專案名稱',
    'tags': '標籤',
}

@share_account_bp.route('/share_account/list', methods=['GET', 'POST'])
@require_tenant_access
def share_account_list():
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    type_ = request.values.get('type', '')
    start_date = request.values.get('start_date')
    end_date = request.values.get('end_date')
    user = '高啟峰'
    today = datetime.now().strftime('%Y-%m-%d')
    date_range = ''
    if start_date and end_date:
        date_range = f"{start_date}~{end_date}"
    type_label = '記帳日期' if type_ != 'other' else '發票日期'
    # 新增一筆 ShareAccount 紀錄（只要有查詢條件就新增）
    if start_date and end_date:
        new_share = ShareAccount(
            date=today,
            type=type_label,
            range=date_range,
            user=user,
            query_type=type_label,
            start_date=start_date,
            end_date=end_date,
            expired=False
        )
        session.add(new_share)
        session.commit()
    # 查詢所有 ShareAccount
    with get_db() as db:
        share_accounts = db.query(ShareAccount).order_by(ShareAccount.id.desc()).all()
    share_books = []
    for row in share_accounts:
        download_url = url_for('share_account.download_share_account', share_id=row.id)
        share_books.append({
            "date": row.date,
            "type": row.type,
            "range": row.range,
            "user": row.user,
            "copy_url": "#",
            "download_url": download_url,
            "expired": row.expired
        })
    return render_template(
        'share_account_list.html',
        share_books=share_books,
        sidebar_items=main_menu,
        selected=selected
    )

@share_account_bp.route('/share_account/download/<int:share_id>')
@require_tenant_access
def download_share_account(share_id):
    with get_db() as db:
        share = db.query(ShareAccount).get(share_id)
        if not share:
            return "查無分享帳簿紀錄", 404
        type_ = share.query_type
        start_date = share.start_date
        end_date = share.end_date
        query = db.query(Money)
    if type_ == '發票日期':
        if start_date:
            query = query.filter(Money.date >= start_date)
        if end_date:
            query = query.filter(Money.date <= end_date)
    else:
        if start_date:
            query = query.filter(Money.a_time >= start_date)
        if end_date:
            query = query.filter(Money.a_time <= end_date)
    # 使用 JOIN 避免 N+1 查詢
    data = query.outerjoin(AccountSubject, Money.subject_code == AccountSubject.code)\
               .outerjoin(Department, Money.department_id == Department.id)\
               .outerjoin(Project, Money.project_id == Project.id)\
               .add_columns(
                   AccountSubject.name.label('subject_name'),
                   Department.name.label('department_name'),
                   Project.code.label('project_code'),
                   Project.name.label('project_name')
               ).all()
    
    rows = []
    for row, subject_name, department_name, project_code, project_name in data:
        d = {col: getattr(row, col) for col in row.__table__.columns.keys()}
        # 使用 JOIN 查詢的結果，避免 N+1 查詢
        d['subject_name'] = subject_name or ''
        d['department_name'] = department_name or ''
        d['project_code'] = project_code or ''
        d['project_name'] = project_name or ''
        rows.append(d)
    df = pd.DataFrame(rows)
    # 只保留有對應中文標題的欄位，並改成中文
    cols = [col for col in MONEY_COLUMN_LABELS.keys() if col in df.columns or col in ['department_name','project_code','project_name']]
    df = df[cols]
    df.columns = [MONEY_COLUMN_LABELS[col] for col in cols]
    output = BytesIO()
    df.to_excel(output, index=False)
    output.seek(0)
    return send_file(output, as_attachment=True, download_name='share_account.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') 