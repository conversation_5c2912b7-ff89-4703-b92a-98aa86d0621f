"""資料庫效能測試路由"""
from flask import Blueprint, render_template, jsonify
import time
from datetime import datetime, timedelta
from database import get_db
from model import Money, Account, PaymentIdentity, Department, Project
from utils.common.helpers import get_template_context
from sqlalchemy import text
import statistics

performance_test_bp = Blueprint('performance_test', __name__)

@performance_test_bp.route('/admin/performance_test')
def performance_test_dashboard():
    """效能測試儀表板"""
    context = get_template_context('資料庫效能測試')
    return render_template('performance_test_dashboard.html', **context)

@performance_test_bp.route('/admin/performance_test/api/run_tests')
def run_performance_tests():
    """執行效能測試"""
    test_results = []
    
    # 測試1: 帳戶查詢 (使用新的複合索引)
    test_results.append(test_account_queries())
    
    # 測試2: 收支記錄查詢 (使用現有索引)
    test_results.append(test_money_queries())
    
    # 測試3: 收支對象查詢 (使用新的複合索引)
    test_results.append(test_payment_identity_queries())
    
    # 測試4: 部門查詢 (使用新的複合索引)
    test_results.append(test_department_queries())
    
    # 測試5: 專案查詢 (使用新的複合索引)
    test_results.append(test_project_queries())
    
    # 測試6: 複雜聯合查詢
    test_results.append(test_complex_queries())
    
    return jsonify({
        'success': True,
        'timestamp': datetime.now().isoformat(),
        'test_results': test_results
    })

def measure_query_time(query_func, iterations=10):
    """測量查詢時間"""
    times = []
    for _ in range(iterations):
        start_time = time.time()
        try:
            query_func()
            end_time = time.time()
            times.append((end_time - start_time) * 1000)  # 轉換為毫秒
        except Exception as e:
            return {
                'error': str(e),
                'avg_time': 0,
                'min_time': 0,
                'max_time': 0
            }
    
    return {
        'avg_time': round(statistics.mean(times), 2),
        'min_time': round(min(times), 2),
        'max_time': round(max(times), 2),
        'iterations': iterations
    }

def test_account_queries():
    """測試帳戶查詢效能"""
    def query1():
        # 測試銀行+帳號查詢 (使用新的複合索引 ix_account_bank_info)
        with get_db() as db:
            db.query(Account).filter(
                Account.bank_name.like('%銀行%'),
                Account.account_number.like('%123%')
            ).all()
    
    def query2():
        # 測試分類+預設帳戶查詢 (使用新的複合索引 ix_account_category_default)
        with get_db() as db:
            db.query(Account).filter(
                Account.category == '銀行帳戶',
                Account.is_default
            ).all()
    
    def query3():
        # 測試軟刪除+分類查詢 (使用新的複合索引 ix_account_active)
        with get_db() as db:
            db.query(Account).filter(
                not Account.is_deleted,
                Account.category == '現金'
            ).all()
    
    return {
        'test_name': '帳戶查詢效能',
        'description': '測試新增的複合索引效果',
        'queries': {
            '銀行+帳號查詢 (ix_account_bank_info)': measure_query_time(query1),
            '分類+預設查詢 (ix_account_category_default)': measure_query_time(query2),
            '軟刪除+分類查詢 (ix_account_active)': measure_query_time(query3)
        }
    }

def test_money_queries():
    """測試收支記錄查詢效能"""
    def query1():
        # 測試日期+類型查詢 (使用現有複合索引)
        with get_db() as db:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30)
            db.query(Money).filter(
                Money.a_time >= start_date,
                Money.a_time <= end_date,
                Money.money_type == '支出'
            ).all()
    
    def query2():
        # 測試帳戶+日期查詢 (使用現有複合索引)
        with get_db() as db:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=7)
            db.query(Money).filter(
                Money.account_id == 1,
                Money.a_time >= start_date
            ).all()
    
    def query3():
        # 測試軟刪除+日期查詢 (使用現有複合索引)
        with get_db() as db:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30)
            db.query(Money).filter(
                not Money.is_deleted,
                Money.a_time >= start_date
            ).all()
    
    return {
        'test_name': '收支記錄查詢效能',
        'description': '測試現有索引的效能',
        'queries': {
            '日期+類型查詢': measure_query_time(query1),
            '帳戶+日期查詢': measure_query_time(query2),
            '軟刪除+日期查詢': measure_query_time(query3)
        }
    }

def test_payment_identity_queries():
    """測試收支對象查詢效能"""
    def query1():
        # 測試類型+狀態查詢 (使用新的複合索引 ix_payment_type_active)
        with get_db() as db:
            db.query(PaymentIdentity).filter(
                PaymentIdentity.type == '客戶',
                PaymentIdentity.is_active
            ).all()
    
    def query2():
        # 測試名稱+統編搜尋 (使用新的複合索引 ix_payment_name_search)
        with get_db() as db:
            db.query(PaymentIdentity).filter(
                PaymentIdentity.name.like('%公司%'),
                PaymentIdentity.tax_id.like('%123%')
            ).all()
    
    return {
        'test_name': '收支對象查詢效能',
        'description': '測試新增的複合索引效果',
        'queries': {
            '類型+狀態查詢 (ix_payment_type_active)': measure_query_time(query1),
            '名稱+統編搜尋 (ix_payment_name_search)': measure_query_time(query2)
        }
    }

def test_department_queries():
    """測試部門查詢效能"""
    def query1():
        # 測試父部門+狀態查詢 (使用新的複合索引 ix_dept_parent_active)
        with get_db() as db:
            db.query(Department).filter(
                Department.parent_id.isnot(None),
                Department.is_active
            ).all()
    
    def query2():
        # 測試名稱+代碼查詢 (使用新的複合索引 ix_dept_name_code)
        with get_db() as db:
            db.query(Department).filter(
                Department.name.like('%部%'),
                Department.code.like('%DEPT%')
            ).all()
    
    return {
        'test_name': '部門查詢效能',
        'description': '測試新增的複合索引效果',
        'queries': {
            '父部門+狀態查詢 (ix_dept_parent_active)': measure_query_time(query1),
            '名稱+代碼查詢 (ix_dept_name_code)': measure_query_time(query2)
        }
    }

def test_project_queries():
    """測試專案查詢效能"""
    def query1():
        # 測試部門+狀態查詢 (使用新的複合索引 ix_project_dept_status)
        with get_db() as db:
            db.query(Project).filter(
                Project.department_id == 1,
                Project.status == '進行中'
            ).all()
    
    def query2():
        # 測試日期範圍查詢 (使用新的複合索引 ix_project_date_range)
        with get_db() as db:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=365)
            db.query(Project).filter(
                Project.start_date >= start_date,
                Project.end_date <= end_date
            ).all()
    
    def query3():
        # 測試負責人查詢 (使用新的索引 ix_project_manager)
        with get_db() as db:
            db.query(Project).filter(
                Project.manager.like('%經理%')
            ).all()
    
    return {
        'test_name': '專案查詢效能',
        'description': '測試新增的複合索引效果',
        'queries': {
            '部門+狀態查詢 (ix_project_dept_status)': measure_query_time(query1),
            '日期範圍查詢 (ix_project_date_range)': measure_query_time(query2),
            '負責人查詢 (ix_project_manager)': measure_query_time(query3)
        }
    }

def test_complex_queries():
    """測試複雜聯合查詢效能"""
    def query1():
        # 複雜聯合查詢：收支記錄 + 帳戶 + 收支對象
        with get_db() as db:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=30)
            db.query(Money).join(Account).join(PaymentIdentity).filter(
                Money.a_time >= start_date,
                Money.money_type == '支出',
                Account.category == '銀行帳戶',
                PaymentIdentity.is_active
            ).all()
    
    def query2():
        # 複雜聯合查詢：專案 + 部門 + 收支記錄
        with get_db() as db:
            db.query(Project).join(Department).join(Money).filter(
                Project.status == '進行中',
                Department.is_active,
                not Money.is_deleted
            ).all()
    
    return {
        'test_name': '複雜聯合查詢效能',
        'description': '測試多表聯合查詢的效能',
        'queries': {
            '收支+帳戶+對象聯合查詢': measure_query_time(query1),
            '專案+部門+收支聯合查詢': measure_query_time(query2)
        }
    }

@performance_test_bp.route('/admin/performance_test/api/index_usage')
def get_index_usage():
    """獲取索引使用情況"""
    try:
        with get_db() as db:
            # SQLite 查詢索引使用情況
            result = db.execute(text("""
                SELECT name, tbl_name, sql 
                FROM sqlite_master 
                WHERE type = 'index' 
                AND name NOT LIKE 'sqlite_%'
                ORDER BY tbl_name, name
            """)).fetchall()
            
            indexes = []
            for row in result:
                indexes.append({
                    'name': row[0],
                    'table': row[1],
                    'sql': row[2]
                })
            
            return jsonify({
                'success': True,
                'indexes': indexes,
                'total_count': len(indexes)
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@performance_test_bp.route('/admin/performance_test/api/table_stats')
def get_table_stats():
    """獲取表格統計資訊"""
    try:
        stats = {}
        with get_db() as db:
            # 獲取各表格的記錄數量
            tables = ['account', 'money', 'payment_identity', 'department', 'project', 'account_subject']
            
            for table in tables:
                try:
                    result = db.execute(text(f"SELECT COUNT(*) FROM {table}")).fetchone()
                    stats[table] = result[0] if result else 0
                except Exception:
                    stats[table] = 0
        
        return jsonify({
            'success': True,
            'table_stats': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })