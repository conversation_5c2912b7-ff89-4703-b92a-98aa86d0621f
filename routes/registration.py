"""
租戶註冊路由
讓消費者可以自助註冊並選擇方案
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from database import get_db
from models.tenant_models import Tenant, PlanLevel, TenantStatus
from model import User
from werkzeug.security import generate_password_hash
from services.tenant_service import TenantService
from utils.security.password_policy import validate_password_strength, password_policy
import datetime
import re

registration_bp = Blueprint('registration', __name__, url_prefix='/register')

def validate_slug(slug):
    """驗證slug格式"""
    if len(slug) < 3 or len(slug) > 20:
        return False
    # 只允許小寫字母、數字和連字符
    return re.match(r'^[a-z0-9-]+$', slug) is not None

def validate_email(email):
    """驗證郵箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

@registration_bp.route('/', methods=['GET', 'POST'])
def register():
    """註冊頁面"""
    if request.method == 'POST':
        try:
            # 獲取表單數據
            company_name = request.form.get('company_name', '').strip()
            slug = request.form.get('slug', '').strip().lower()
            plan_level = request.form.get('plan_level', 'BASIC')
            contact_email = request.form.get('contact_email', '').strip()
            contact_person = request.form.get('contact_person', '').strip()
            contact_phone = request.form.get('contact_phone', '').strip()
            admin_username = request.form.get('admin_username', '').strip()
            admin_password = request.form.get('admin_password', '').strip()
            confirm_password = request.form.get('confirm_password', '').strip()
            agree_terms = request.form.get('agree_terms')
            
            # 基本驗證
            errors = []
            
            if not company_name:
                errors.append('公司/組織名稱不能為空')
            
            if not slug:
                errors.append('系統識別名稱不能為空')
            elif not validate_slug(slug):
                errors.append('系統識別名稱只能包含小寫字母、數字和連字符，長度3-20字符')
            
            if not contact_email or not validate_email(contact_email):
                errors.append('請輸入有效的電子郵箱')
            
            if not contact_person:
                errors.append('聯絡人姓名不能為空')
            
            if not admin_username:
                errors.append('管理員帳號不能為空')
            elif len(admin_username) < 3:
                errors.append('管理員帳號至少3個字符')
            
            if not admin_password:
                errors.append('密碼不能為空')
            else:
                # 使用新的密碼策略驗證
                password_result = validate_password_strength(admin_password, admin_username)
                if not password_result['is_valid']:
                    errors.extend(password_result['errors'])
            
            if admin_password != confirm_password:
                errors.append('密碼確認不匹配')
            
            if not agree_terms:
                errors.append('請同意服務條款')
            
            if errors:
                for error in errors:
                    flash(error, 'error')
                return render_template('registration/register.html', 
                                     form_data=request.form,
                                     plan_levels=get_plan_info())
            
            with get_db() as db:
                # 檢查slug是否已被使用
                existing_tenant = db.query(Tenant).filter(Tenant.slug == slug).first()
                if existing_tenant:
                    flash('系統識別名稱已被使用，請選擇其他名稱', 'error')
                    return render_template('registration/register.html', 
                                         form_data=request.form,
                                         plan_levels=get_plan_info())
                
                # 檢查郵箱是否已被使用
                existing_email = db.query(User).filter(User.email == contact_email).first()
                if existing_email:
                    flash('此電子郵箱已被註冊', 'error')
                    return render_template('registration/register.html', 
                                         form_data=request.form,
                                         plan_levels=get_plan_info())
                
                # 檢查用戶名是否已被使用
                existing_user = db.query(User).filter(User.username == admin_username).first()
                if existing_user:
                    flash('此管理員帳號已被使用', 'error')
                    return render_template('registration/register.html', 
                                         form_data=request.form,
                                         plan_levels=get_plan_info())
                
                # 創建租戶
                tenant_data = {
                    'name': company_name,
                    'slug': slug,
                    'domain': '',
                    'plan_level': PlanLevel(plan_level),
                    'status': TenantStatus.TRIAL,  # 預設為試用狀態
                    'contact_email': contact_email,
                    'contact_person': contact_person,
                    'contact_phone': contact_phone,
                    'trial_start_date': datetime.date.today(),
                    'trial_end_date': datetime.date.today() + datetime.timedelta(days=30),  # 30天試用
                    'max_users': get_plan_limits(plan_level)['max_users'],
                    'max_storage_mb': get_plan_limits(plan_level)['max_storage_mb'],
                }
                
                tenant = Tenant(**tenant_data)
                db.add(tenant)
                db.flush()  # 獲取ID
                
                # 創建管理員用戶
                admin_data = {
                    'username': admin_username,
                    'email': contact_email,
                    'password_hash': generate_password_hash(admin_password),
                    'full_name': contact_person,
                    'tenant_id': tenant.id,
                    'is_tenant_admin': True,
                }
                
                admin_user = User(**admin_data)
                db.add(admin_user)
                
                db.commit()
                
                # 自動登入新創建的用戶
                session['user_id'] = admin_user.id
                session['username'] = admin_user.username
                session['tenant_id'] = tenant.id
                
                flash(f'歡迎使用印錢大師！您的{plan_level}方案已開通30天免費試用。', 'success')
                
                # 重定向到租戶專屬URL（使用安全協議）
                from flask import current_app
                scheme = 'https' if current_app.config.get('SESSION_COOKIE_SECURE', False) else 'http'
                return redirect(f'{scheme}://{slug}.localhost:5001/tenant/dashboard')
                
        except Exception as e:
            flash(f'註冊失敗：{str(e)}', 'error')
            return render_template('registration/register.html', 
                                 form_data=request.form,
                                 plan_levels=get_plan_info())
    
    return render_template('registration/register.html', 
                         form_data={},
                         plan_levels=get_plan_info())

@registration_bp.route('/check-slug')
def check_slug():
    """AJAX檢查slug是否可用"""
    slug = request.args.get('slug', '').strip().lower()
    
    if not slug or not validate_slug(slug):
        return {'available': False, 'message': '無效的識別名稱格式'}
    
    with get_db() as db:
        existing = db.query(Tenant).filter(Tenant.slug == slug).first()
        if existing:
            return {'available': False, 'message': '此識別名稱已被使用'}
        else:
            return {'available': True, 'message': '此識別名稱可以使用'}

def get_plan_info():
    """獲取方案資訊"""
    return {
        'BASIC': {
            'name': '基礎版',
            'price': '免費',
            'description': '適合個人工作室或小型企業',
            'features': [
                '基本收支記錄',
                '帳戶管理',
                '交易明細查詢',
                '最多3個用戶',
                '500MB儲存空間',
            ],
            'max_users': 3,
            'max_storage_mb': 500,
        },
        'STANDARD': {
            'name': '標準版',
            'price': 'NT$ 999/月',
            'description': '適合成長中的中小企業',
            'features': [
                '包含基礎版所有功能',
                '財務報表（資產負債表、損益表）',
                '應收應付逾期追蹤',
                '銀行借款管理',
                '勞務報酬管理',
                '最多5個用戶',
                '2GB儲存空間',
            ],
            'max_users': 5,
            'max_storage_mb': 2048,
        },
        'PREMIUM': {
            'name': '專業版',
            'price': 'NT$ 2999/月',
            'description': '適合專業會計師事務所',
            'features': [
                '包含標準版所有功能',
                '現金流量表',
                '部門/專案分析',
                '資產管理（固定資產、預付費用）',
                '薪資管理系統',
                '會計科目管理',
                '數據備份功能',
                '最多10個用戶',
                '5GB儲存空間',
            ],
            'max_users': 10,
            'max_storage_mb': 5120,
        },
        'ENTERPRISE': {
            'name': '企業版',
            'price': '聯絡我們',
            'description': '適合大型企業或集團',
            'features': [
                '包含專業版所有功能',
                '會計傳票管理',
                '多公司管理',
                'API整合',
                '客製化報表',
                '專屬客戶經理',
                '無限用戶',
                '無限儲存空間',
            ],
            'max_users': 999,
            'max_storage_mb': 99999,
        }
    }

def get_plan_limits(plan_level):
    """獲取方案限制"""
    plan_info = get_plan_info()
    return {
        'max_users': plan_info[plan_level]['max_users'],
        'max_storage_mb': plan_info[plan_level]['max_storage_mb'],
    }