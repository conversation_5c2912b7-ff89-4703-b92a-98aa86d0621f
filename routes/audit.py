"""
審計報表路由
提供審計相關的查詢和報表功能
"""
from flask import Blueprint, render_template, request, jsonify
from sqlalchemy import desc, and_
from model import Money, Account, Transfer
from utils.business.audit_helper import get_current_user, apply_audit_filter
from database import get_db
from datetime import datetime, timedelta
from utils.tenant_utils import require_tenant_access, add_tenant_filter, get_current_tenant_id

audit_bp = Blueprint('audit', __name__)

@audit_bp.route('/audit_dashboard')
@require_tenant_access
def audit_dashboard():
    """審計儀表板"""
    with get_db() as db:
        # 統計資料
        stats = {}
        
        # 今日新增記錄數
        today = datetime.now().date()
        stats['today_records'] = apply_audit_filter(
            db.query(Money), Money
        ).filter(Money.created_at >= today).count()
        
        # 本週修改記錄數
        week_ago = today - timedelta(days=7)
        stats['week_updates'] = apply_audit_filter(
            db.query(Money), Money
        ).filter(
            and_(
                Money.updated_at >= week_ago,
                Money.updated_by.isnot(None)
            )
        ).count()
        
        # 軟刪除記錄數
        stats['deleted_records'] = db.query(Money).filter(
            Money.is_deleted
        ).count()
        
        # 最近活動記錄
        recent_activities = []
        
        # 最近建立的記錄
        recent_created = apply_audit_filter(
            db.query(Money), Money
        ).filter(
            Money.created_by.isnot(None)
        ).order_by(desc(Money.created_at)).limit(10).all()
        
        for record in recent_created:
            recent_activities.append({
                'type': '建立',
                'table': '收支記錄',
                'record_id': record.id,
                'record_name': record.name,
                'user': record.created_by,
                'time': record.created_at,
                'amount': record.total
            })
        
        # 最近修改的記錄
        recent_updated = apply_audit_filter(
            db.query(Money), Money
        ).filter(
            and_(
                Money.updated_by.isnot(None),
                Money.updated_at > Money.created_at
            )
        ).order_by(desc(Money.updated_at)).limit(10).all()
        
        for record in recent_updated:
            recent_activities.append({
                'type': '修改',
                'table': '收支記錄',
                'record_id': record.id,
                'record_name': record.name,
                'user': record.updated_by,
                'time': record.updated_at,
                'version': record.version
            })
        
        # 按時間排序
        recent_activities.sort(key=lambda x: x['time'], reverse=True)
        recent_activities = recent_activities[:20]  # 只取最近20筆
    
    return render_template('audit_dashboard.html', 
                         stats=stats, 
                         recent_activities=recent_activities)

@audit_bp.route('/audit_search')
@require_tenant_access
def audit_search():
    """審計搜尋頁面"""
    return render_template('audit_search.html')

@audit_bp.route('/api/audit_search', methods=['POST'])
@require_tenant_access
def api_audit_search():
    """審計搜尋 API"""
    data = request.get_json()
    
    table_name = data.get('table', 'money')
    user_name = data.get('user')
    start_date = data.get('start_date')
    end_date = data.get('end_date')
    action_type = data.get('action_type')  # create/update/delete
    
    with get_db() as db:
        if table_name == 'money':
            query = db.query(Money)
            
            # 根據動作類型篩選
            if action_type == 'create':
                query = apply_audit_filter(query, Money)
                if user_name:
                    query = query.filter(Money.created_by.like(f'%{user_name}%'))
                if start_date:
                    query = query.filter(Money.created_at >= start_date)
                if end_date:
                    query = query.filter(Money.created_at <= end_date)
                    
            elif action_type == 'update':
                query = apply_audit_filter(query, Money)
                query = query.filter(Money.updated_by.isnot(None))
                if user_name:
                    query = query.filter(Money.updated_by.like(f'%{user_name}%'))
                if start_date:
                    query = query.filter(Money.updated_at >= start_date)
                if end_date:
                    query = query.filter(Money.updated_at <= end_date)
                    
            elif action_type == 'delete':
                query = query.filter(Money.is_deleted)
                if user_name:
                    query = query.filter(Money.deleted_by.like(f'%{user_name}%'))
                if start_date:
                    query = query.filter(Money.deleted_at >= start_date)
                if end_date:
                    query = query.filter(Money.deleted_at <= end_date)
            
            results = query.order_by(desc(Money.created_at)).limit(100).all()
            
            # 轉換為 JSON 格式
            records = []
            for record in results:
                record_data = {
                    'id': record.id,
                    'name': record.name,
                    'total': record.total,
                    'money_type': record.money_type,
                    'a_time': record.a_time.isoformat() if record.a_time else None,
                    'created_by': record.created_by,
                    'created_at': record.created_at.isoformat() if record.created_at else None,
                    'updated_by': record.updated_by,
                    'updated_at': record.updated_at.isoformat() if record.updated_at else None,
                    'version': record.version,
                    'is_deleted': record.is_deleted,
                    'deleted_by': record.deleted_by,
                    'deleted_at': record.deleted_at.isoformat() if record.deleted_at else None
                }
                records.append(record_data)
            
            return jsonify({
                'success': True,
                'records': records,
                'total': len(records)
            })
    
    return jsonify({'success': False, 'message': '查詢失敗'})

@audit_bp.route('/deleted_records')
@require_tenant_access
def deleted_records():
    """已刪除記錄管理"""
    with get_db() as db:
        # 查詢所有軟刪除的記錄
        deleted_money = db.query(Money).filter(
            Money.is_deleted
        ).order_by(desc(Money.deleted_at)).all()
        
        deleted_accounts = db.query(Account).filter(
            Account.is_deleted
        ).order_by(desc(Account.deleted_at)).all()
        
        deleted_transfers = db.query(Transfer).filter(
            Transfer.is_deleted
        ).order_by(desc(Transfer.deleted_at)).all()
    
    return render_template('deleted_records.html',
                         deleted_money=deleted_money,
                         deleted_accounts=deleted_accounts,
                         deleted_transfers=deleted_transfers)

@audit_bp.route('/restore_record', methods=['POST'])
@require_tenant_access
def restore_record():
    """復原已刪除的記錄"""
    table_name = request.form.get('table')
    record_id = request.form.get('id')
    
    with get_db() as db:
        if table_name == 'money':
            record = db.query(Money).filter(Money.id == record_id).first()
        elif table_name == 'account':
            record = db.query(Account).filter(Account.id == record_id).first()
        elif table_name == 'transfer':
            record = db.query(Transfer).filter(Transfer.id == record_id).first()
        else:
            return jsonify({'success': False, 'message': '不支援的表格類型'})
        
        if record and record.is_deleted:
            record.is_deleted = False
            record.deleted_at = None
            record.deleted_by = None
            
            # 設定復原審計
            record.updated_by = get_current_user()
            record.updated_at = datetime.now()
            if hasattr(record, 'version'):
                record.version = (record.version or 0) + 1
            
            session.commit()
            return jsonify({'success': True, 'message': '記錄已復原'})
    
    return jsonify({'success': False, 'message': '復原失敗'})