from flask import Blueprint, render_template, request, redirect, url_for, flash
from data.menu_data import menu
from database import get_db
from model import PaymentIdentityType, PaymentIdentity
from sqlalchemy import func

payment_identity_type_bp = Blueprint('payment_identity_type', __name__, url_prefix='/payment_identity_type')

@payment_identity_type_bp.route('/manage')
def manage():
    """對象類別管理頁面"""
    main_menu = list(menu.keys())
    selected = '設定'
    
    with get_db() as db:
        # 查詢所有對象類別，並計算每個類別的使用次數
        types_with_count = db.query(
            PaymentIdentityType,
            func.count(PaymentIdentity.id).label('usage_count')
        ).outerjoin(
            PaymentIdentity,
            PaymentIdentityType.id == PaymentIdentity.type_id
        ).group_by(
            PaymentIdentityType.id
        ).all()
        
        # 轉換為字典格式，避免 DetachedInstanceError
        types_data = []
        for type_obj, count in types_with_count:
            types_data.append({
                'id': type_obj.id,
                'name': type_obj.name,
                'description': type_obj.description or '',
                'usage_count': count
            })
    
    return render_template(
        'payment_identity_type_manage.html',
        types=types_data,
        sidebar_items=main_menu,
        selected=selected
    )

@payment_identity_type_bp.route('/list_identities')
def list_identities():
    """對象列表頁面"""
    main_menu = list(menu.keys())
    selected = '設定'
    
    with get_db() as db:
        # 查詢所有收支對象
        identities = db.query(PaymentIdentity).filter_by(is_active=True).all()
        
        # 轉換為字典格式，避免 DetachedInstanceError
        identities_data = []
        for identity in identities:
            identities_data.append({
                'id': identity.id,
                'name': identity.name,
                'type': identity.type or '待分類',
                'contact': identity.contact or '',
                'mobile': identity.mobile or '',
                'tax_id': identity.tax_id or '',
                'note': identity.note or ''
            })
    
    return render_template(
        'payment_identity_list_page.html',
        types=identities_data,
        sidebar_items=main_menu,
        selected=selected
    )

@payment_identity_type_bp.route('/add', methods=['POST'])
def add():
    """新增收支對象"""
    name = request.form.get('name')
    object_category = request.form.get('object_category')
    full_name = request.form.get('full_name')
    contact_person = request.form.get('contact_person')
    contact_phone = request.form.get('contact_phone')
    email = request.form.get('email')
    address = request.form.get('address')
    tax_id = request.form.get('tax_id')
    note = request.form.get('note')
    
    if not name:
        flash('對象名稱不能為空', 'error')
        return redirect(url_for('payment_identity_type.list_identities'))
    
    with get_db() as db:
        # 檢查是否已存在同名對象
        existing = db.query(PaymentIdentity).filter_by(name=name, is_active=True).first()
        if existing:
            flash(f'對象名稱 "{name}" 已存在', 'error')
            return redirect(url_for('payment_identity_type.list_identities'))
        
        # 創建新對象
        new_identity = PaymentIdentity(
            name=name,
            type=object_category,
            contact=contact_person,
            mobile=contact_phone,
            tax_id=tax_id,
            note=note
        )
        db.add(new_identity)
        db.commit()
        flash(f'收支對象 "{name}" 已成功新增', 'success')
    
    return redirect(url_for('payment_identity_type.list_identities'))

@payment_identity_type_bp.route('/add_type', methods=['POST'])
def add_type():
    """新增對象類別"""
    name = request.form.get('name')
    description = request.form.get('description')
    
    if not name:
        flash('類別名稱不能為空', 'error')
        return redirect(url_for('payment_identity_type.manage'))
    
    with get_db() as db:
        # 檢查是否已存在同名類別
        existing = db.query(PaymentIdentityType).filter_by(name=name).first()
        if existing:
            flash(f'類別名稱 "{name}" 已存在', 'error')
            return redirect(url_for('payment_identity_type.manage'))
        
        # 創建新類別
        new_type = PaymentIdentityType(
            name=name,
            description=description
        )
        db.add(new_type)
        db.commit()
        flash(f'對象類別 "{name}" 已成功新增', 'success')
    
    return redirect(url_for('payment_identity_type.manage'))

@payment_identity_type_bp.route('/edit', methods=['POST'])
def edit():
    """編輯對象類別"""
    type_id = request.form.get('type_id')
    name = request.form.get('name')
    description = request.form.get('description')
    
    if not type_id or not name:
        flash('類別ID和名稱不能為空', 'error')
        return redirect(url_for('payment_identity_type.manage'))
    
    with get_db() as db:
        # 檢查類別是否存在
        type_obj = db.query(PaymentIdentityType).filter_by(id=type_id).first()
        if not type_obj:
            flash('找不到指定的對象類別', 'error')
            return redirect(url_for('payment_identity_type.manage'))
        
        # 檢查是否已存在同名類別（排除自己）
        existing = db.query(PaymentIdentityType).filter(
            PaymentIdentityType.name == name,
            PaymentIdentityType.id != type_id
        ).first()
        if existing:
            flash(f'類別名稱 "{name}" 已存在', 'error')
            return redirect(url_for('payment_identity_type.manage'))
        
        # 更新類別
        type_obj.name = name
        type_obj.description = description
        db.commit()
        flash(f'對象類別 "{name}" 已成功更新', 'success')
    
    return redirect(url_for('payment_identity_type.manage'))

@payment_identity_type_bp.route('/delete', methods=['POST'])
def delete():
    """刪除對象類別"""
    type_id = request.form.get('type_id')
    
    if not type_id:
        flash('類別ID不能為空', 'error')
        return redirect(url_for('payment_identity_type.manage'))
    
    with get_db() as db:
        # 檢查類別是否存在
        type_obj = db.query(PaymentIdentityType).filter_by(id=type_id).first()
        if not type_obj:
            flash('找不到指定的對象類別', 'error')
            return redirect(url_for('payment_identity_type.manage'))
        
        # 檢查是否有關聯的收支對象
        usage_count = db.query(func.count(PaymentIdentity.id)).filter(
            PaymentIdentity.type_id == type_id
        ).scalar()
        
        if usage_count > 0:
            flash(f'無法刪除類別 "{type_obj.name}"，因為它已被 {usage_count} 個收支對象使用', 'error')
            return redirect(url_for('payment_identity_type.manage'))
        
        # 刪除類別
        db.delete(type_obj)
        db.commit()
        flash(f'對象類別 "{type_obj.name}" 已成功刪除', 'success')
    
    return redirect(url_for('payment_identity_type.manage'))

@payment_identity_type_bp.route('/delete_identity', methods=['POST'])
def delete_identity():
    """刪除收支對象"""
    identity_id = request.form.get('type_id')
    
    if not identity_id:
        flash('對象ID不能為空', 'error')
        return redirect(url_for('payment_identity_type.list_identities'))
    
    with get_db() as db:
        # 檢查對象是否存在
        identity = db.query(PaymentIdentity).filter_by(id=identity_id).first()
        if not identity:
            flash('找不到指定的收支對象', 'error')
            return redirect(url_for('payment_identity_type.list_identities'))
        
        # 軟刪除（設置為非啟用狀態）
        identity.is_active = False
        db.commit()
        flash(f'收支對象 "{identity.name}" 已成功刪除', 'success')
    
    return redirect(url_for('payment_identity_type.list_identities'))