"""
銀行借款相關路由
"""
from flask import Blueprint, render_template, request, flash, redirect, jsonify
from data.menu_data import menu
from model import BankLoan, Account, AccountSubject
from database import get_db
from services.account_service import AccountService
from utils.logging.error_handler import (
    handle_database_error, handle_validation_error, handle_business_logic_error,
    ErrorHandler, ValidationError, DatabaseError, BusinessLogicError
)

bankloan_bp = Blueprint('bankloan', __name__, url_prefix='/bankloan')

@bankloan_bp.route('/create', methods=['GET', 'POST'])
@handle_database_error
@handle_validation_error
@handle_business_logic_error
def create():
    """新增銀行借款表單頁"""
    main_menu = list(menu.keys())
    selected = '資金管理'

    if request.method == 'POST':
        # 驗證必填欄位
        required_fields = ['bank_name', 'principal', 'loan_date']
        form_data = {
            'bank_name': request.form.get('bank_name'),
            'principal': request.form.get('principal'),
            'loan_date': request.form.get('loan_date')
        }
        ErrorHandler.validate_required_fields(form_data, required_fields)
        
        # 驗證數值欄位
        principal = ErrorHandler.validate_positive_number(request.form.get('principal', 0), '借款本金')
        fee = ErrorHandler.safe_int_convert(request.form.get('fee', 0))
        
        if fee < 0:
            raise ValidationError("手續費不能為負數", error_code='INVALID_FEE')
        
        if principal <= fee:
            raise BusinessLogicError("借款本金必須大於手續費", error_code='INVALID_LOAN_AMOUNT')

        try:
            with get_db() as db:
                # 獲取表單資料
                bank_name = request.form.get('bank_name').strip()
                subject_code = request.form.get('subject_code')
                repayment_account_id = ErrorHandler.safe_int_convert(request.form.get('repayment_account'))
                
                # 安全的日期轉換
                loan_start_date = ErrorHandler.safe_date_convert(request.form.get('loan_start_date'))
                loan_end_date = ErrorHandler.safe_date_convert(request.form.get('loan_end_date'))
                loan_date = ErrorHandler.safe_date_convert(request.form.get('loan_date'))
                
                if not loan_date:
                    raise ValidationError("借款日期為必填欄位", error_code='MISSING_LOAN_DATE')
                
                installments = ErrorHandler.safe_int_convert(request.form.get('installments', 1))
                fixed_repayment_day = ErrorHandler.safe_int_convert(request.form.get('fixed_repayment_day', 1))
                note = request.form.get('note', '').strip()

                # 業務邏輯驗證
                if installments <= 0:
                    raise ValidationError("分期數必須大於0", error_code='INVALID_INSTALLMENTS')
                
                if fixed_repayment_day < 1 or fixed_repayment_day > 31:
                    raise ValidationError("固定還款日必須在1-31之間", error_code='INVALID_REPAYMENT_DAY')

                # 計算入帳金額
                deposit_amount = int(principal) - fee

                # 創建新的銀行借款記錄
                new_loan = BankLoan(
                    bank_name=bank_name,
                    subject_code=subject_code,
                    repayment_account_id=repayment_account_id if repayment_account_id > 0 else None,
                    loan_start_date=loan_start_date,
                    loan_end_date=loan_end_date,
                    loan_date=loan_date,
                    principal=int(principal),
                    fee=fee,
                    deposit_amount=deposit_amount,
                    installments=installments,
                    fixed_repayment_day=fixed_repayment_day,
                    note=note
                )

                db.add(new_loan)
                db.commit()

                flash('銀行借款記錄新增成功', 'success')
                return redirect('/bankloan/list')

        except Exception as e:
            # 將一般異常轉換為資料庫錯誤
            raise DatabaseError(f"新增銀行借款記錄失敗: {str(e)}", error_code='CREATE_LOAN_FAILED')

    # GET 請求 - 使用服務層獲取帳戶資料
    try:
        accounts_data = AccountService.get_accounts_dropdown()
    except Exception as e:
        raise DatabaseError(f"獲取帳戶資料失敗: {str(e)}", error_code='FETCH_ACCOUNTS_FAILED')

    return render_template('bankloan_create.html',
                        sidebar_items=main_menu,
                        selected=selected,
                        accounts=accounts_data)

@bankloan_bp.route('/list')
@handle_database_error
def list_loans():
    """銀行借款列表頁"""
    main_menu = list(menu.keys())
    selected = '資金管理'

    try:
        with get_db() as db:
            bankloan_records = db.query(BankLoan)\
                .outerjoin(Account, BankLoan.repayment_account_id == Account.id)\
                .outerjoin(AccountSubject, BankLoan.subject_code == AccountSubject.code)\
                .add_columns(
                    Account.name.label('account_name'),
                    AccountSubject.name.label('subject_name')
                )\
                .order_by(BankLoan.loan_date.desc())\
                .all()

            # 轉換為字典格式
            records_data = []
            for record, account_name, subject_name in bankloan_records:
                records_data.append({
                    'id': record.id,
                    'bank_name': record.bank_name or '未指定',
                    'subject_code': record.subject_code or '',
                    'subject_name': subject_name or '未指定科目',
                    'account_name': account_name or '未指定帳戶',
                    'principal': record.principal or 0,
                    'fee': record.fee or 0,
                    'loan_date': record.loan_date.strftime('%Y-%m-%d') if record.loan_date else '未指定',
                    'note': record.note or '',
                    'voucher_image': record.voucher_image or ''
                })

    except Exception as e:
        raise DatabaseError(f"查詢銀行借款記錄失敗: {str(e)}", error_code='FETCH_LOANS_FAILED')

    return render_template('bankloan_list.html',
                        bankloan_records=records_data,
                        sidebar_items=main_menu,
                        selected=selected)

@bankloan_bp.route('/edit/<int:loan_id>', methods=['GET', 'POST'])
@handle_database_error
@handle_validation_error
@handle_business_logic_error
def edit(loan_id):
    """編輯銀行借款頁面"""
    main_menu = list(menu.keys())
    selected = '資金管理'

    if request.method == 'POST':
        # 驗證數值欄位
        principal = ErrorHandler.validate_positive_number(request.form.get('principal', 0), '借款本金')
        fee = ErrorHandler.safe_int_convert(request.form.get('fee', 0))
        
        if fee < 0:
            raise ValidationError("手續費不能為負數", error_code='INVALID_FEE')
        
        if principal <= fee:
            raise BusinessLogicError("借款本金必須大於手續費", error_code='INVALID_LOAN_AMOUNT')

        try:
            with get_db() as db:
                loan_record = db.query(BankLoan).filter_by(id=loan_id).first()
                if not loan_record:
                    raise ValidationError("找不到該借款記錄", error_code='LOAN_NOT_FOUND')

                # 更新資料
                bank_name = request.form.get('bank_name')
                if bank_name and bank_name.strip():
                    loan_record.bank_name = bank_name.strip()

                subject_code = request.form.get('subject_code')
                if subject_code is not None:
                    loan_record.subject_code = subject_code

                repayment_account_id = ErrorHandler.safe_int_convert(request.form.get('repayment_account'))
                if repayment_account_id > 0:
                    loan_record.repayment_account_id = repayment_account_id

                # 安全的日期轉換
                loan_start_date = ErrorHandler.safe_date_convert(request.form.get('loan_start_date'))
                loan_end_date = ErrorHandler.safe_date_convert(request.form.get('loan_end_date'))
                loan_date = ErrorHandler.safe_date_convert(request.form.get('loan_date'))

                if loan_start_date:
                    loan_record.loan_start_date = loan_start_date
                if loan_end_date:
                    loan_record.loan_end_date = loan_end_date
                if loan_date:
                    loan_record.loan_date = loan_date

                # 更新數值欄位
                loan_record.principal = int(principal)
                loan_record.fee = fee
                loan_record.deposit_amount = int(principal) - fee
                
                installments = ErrorHandler.safe_int_convert(request.form.get('installments', 1))
                fixed_repayment_day = ErrorHandler.safe_int_convert(request.form.get('fixed_repayment_day', 1))
                
                # 業務邏輯驗證
                if installments <= 0:
                    raise ValidationError("分期數必須大於0", error_code='INVALID_INSTALLMENTS')
                
                if fixed_repayment_day < 1 or fixed_repayment_day > 31:
                    raise ValidationError("固定還款日必須在1-31之間", error_code='INVALID_REPAYMENT_DAY')
                
                loan_record.installments = installments
                loan_record.fixed_repayment_day = fixed_repayment_day
                loan_record.note = request.form.get('note', '').strip()

                db.commit()
                flash('銀行借款記錄更新成功', 'success')
                return redirect('/bankloan/list')

        except Exception as e:
            raise DatabaseError(f"更新銀行借款記錄失敗: {str(e)}", error_code='UPDATE_LOAN_FAILED')

    # GET 請求 - 顯示編輯表單
    try:
        with get_db() as db:
            loan_record = db.query(BankLoan).filter_by(id=loan_id).first()
            if not loan_record:
                raise ValidationError("找不到該借款記錄", error_code='LOAN_NOT_FOUND')

            loan_data = {
                'id': loan_record.id,
                'bank_name': loan_record.bank_name or '',
                'subject_code': loan_record.subject_code or '',
                'repayment_account_id': loan_record.repayment_account_id,
                'loan_start_date': loan_record.loan_start_date,
                'loan_end_date': loan_record.loan_end_date,
                'loan_date': loan_record.loan_date,
                'principal': loan_record.principal or 0,
                'fee': loan_record.fee or 0,
                'deposit_amount': loan_record.deposit_amount or 0,
                'installments': loan_record.installments or 1,
                'fixed_repayment_day': loan_record.fixed_repayment_day or 1,
                'note': loan_record.note or '',
                'voucher_image': loan_record.voucher_image or ''
            }

        accounts_data = AccountService.get_accounts_dropdown()

    except Exception as e:
        raise DatabaseError(f"獲取借款記錄失敗: {str(e)}", error_code='FETCH_LOAN_FAILED')

    return render_template('bankloan_create.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         edit_mode=True,
                         loan_record=loan_data,
                         accounts=accounts_data)

@bankloan_bp.route('/delete/<int:loan_id>', methods=['DELETE'])
@handle_database_error
@handle_validation_error
def delete(loan_id):
    """刪除銀行借款記錄"""
    if loan_id <= 0:
        raise ValidationError("無效的借款記錄ID", error_code='INVALID_LOAN_ID')
    
    try:
        with get_db() as db:
            loan_record = db.query(BankLoan).filter_by(id=loan_id).first()
            if not loan_record:
                raise ValidationError("找不到該借款記錄", error_code='LOAN_NOT_FOUND')
            
            # 檢查是否有相關的還款記錄（業務邏輯檢查）
            # 這裡可以添加更多的業務邏輯檢查
            
            db.delete(loan_record)
            db.commit()
            
        return jsonify({
            'success': True, 
            'message': '銀行借款記錄刪除成功',
            'deleted_id': loan_id
        })
        
    except Exception as e:
        raise DatabaseError(f"刪除銀行借款記錄失敗: {str(e)}", error_code='DELETE_LOAN_FAILED')