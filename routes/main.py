from flask import Blueprint, render_template, request, session, redirect, url_for, flash
from services.menu_service import MenuService
from utils.web.menu_decorator import with_menu, render_with_menu_data
from utils.security.auth_decorators import login_required

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
# @login_required  # 暫時移除以便測試佈局
def index():
    """主頁面 - 顯示總覽儀表板"""
    user_id = session.get('user_id')
    
    # 獲取用戶可訪問的選單
    user_menu = MenuService.get_user_menu(user_id)

    # 暫時跳過權限檢查以便測試佈局
    if not user_menu:
        # 如果沒有用戶選單，使用預設選單進行測試
        user_menu = {
            '收支帳簿': [],
            '資金管理': [],
            '我的報表': []
        }
    
    # 檢查是否有特定的選單參數
    if request.args.get('main'):
        # 如果有選單參數，顯示選單頁面
        main_menu = list(user_menu.keys())
        selected = request.args.get('main')

        if selected not in user_menu:
            selected = main_menu[0] if main_menu else None

        submenus = user_menu.get(selected, []) if selected else []
        submenu_title = request.args.get('submenu')
        button_label = request.args.get('button')
        selected_buttons = None
        
        if submenu_title and button_label:
            # 找到該submenu下的button
            for submenu in submenus:
                if submenu['title'] == submenu_title:
                    for btn in submenu['buttons']:
                        if btn['label'] == button_label:
                            selected_buttons = btn['children']
                            break
        
        return render_template('index.html', 
                            sidebar_items=main_menu, 
                            submenus=submenus, 
                            selected=selected, 
                            submenu_title=submenu_title, 
                            button_label=button_label, 
                            selected_buttons=selected_buttons)
    
    # 否則顯示總覽儀表板
    from services.dashboard_service import DashboardService
    
    # 獲取儀表板數據
    dashboard_data = DashboardService.get_dashboard_data(user_id)
    
    return render_template('overview.html', **dashboard_data)
    
    
@main_bp.route('/start')
def start():
    """起始頁面"""
    return render_template('start.html')

@main_bp.route('/fund_manage')
@with_menu('資金管理')
def fund_manage():
    """資金管理主頁"""
    return render_with_menu_data('index.html')