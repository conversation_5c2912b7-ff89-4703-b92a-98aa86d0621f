"""
基礎路由類別
提供通用功能，減少路由代碼重複
"""

from functools import wraps
from flask import render_template, request, jsonify, g, current_app
from typing import Dict, Any, Optional, Callable
import logging
import traceback
from database import get_db
from services.account_service import AccountService
from services.optimized_query_service import OptimizedQueryService
from utils.performance.cache_manager import ReferenceDataCache

logger = logging.getLogger(__name__)

class BaseRoute:
    """
    基礎路由類別
    提供通用的表單數據載入、錯誤處理、審計追蹤等功能
    """
    
    @staticmethod
    def get_common_form_data() -> Dict[str, Any]:
        """
        獲取通用表單數據（使用快取）
        
        Returns:
            包含所有下拉選單數據的字典
        """
        try:
            # 使用快取的數據
            return ReferenceDataCache.get_dropdown_data()
        except Exception as e:
            logger.error(f"Failed to get form data: {str(e)}")
            # 降級處理：直接從資料庫獲取
            return AccountService.get_dropdown_data()
    
    @staticmethod
    def get_optimized_form_data() -> Dict[str, Any]:
        """
        獲取優化的表單數據
        
        Returns:
            優化查詢後的表單數據
        """
        try:
            return OptimizedQueryService.get_form_data_optimized()
        except Exception as e:
            logger.error(f"Failed to get optimized form data: {str(e)}")
            return BaseRoute.get_common_form_data()

def with_form_data(func: Callable) -> Callable:
    """
    裝飾器：自動注入表單數據
    
    使用方式：
    @with_form_data
    def income_record():
        # form_data 會自動注入到 g.form_data
        return render_template('income.html', **g.form_data)
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 注入表單數據到 g 對象
        g.form_data = BaseRoute.get_common_form_data()
        return func(*args, **kwargs)
    return wrapper

def handle_errors(return_json: bool = False):
    """
    錯誤處理裝飾器
    
    Args:
        return_json: 是否返回 JSON 格式的錯誤
    
    使用方式：
    @handle_errors(return_json=True)
    def api_endpoint():
        # 錯誤會自動被捕獲並處理
        pass
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 記錄錯誤詳情
                error_id = getattr(g, 'correlation_id', 'unknown')
                logger.error(
                    f"Error in {func.__name__} [ID: {error_id}]: {str(e)}\n"
                    f"Traceback: {traceback.format_exc()}"
                )
                
                if return_json:
                    return jsonify({
                        'success': False,
                        'error': str(e),
                        'error_id': error_id
                    }), 500
                else:
                    return render_template(
                        'error.html',
                        error_message=str(e),
                        error_id=error_id
                    ), 500
        return wrapper
    return decorator

def audit_trail(action_type: str):
    """
    審計追蹤裝飾器
    
    Args:
        action_type: 操作類型（如 'create', 'update', 'delete'）
    
    使用方式：
    @audit_trail('create')
    def create_transaction():
        # 操作會被自動記錄
        pass
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 記錄操作開始
            user_id = g.get('user_id', 'anonymous')
            correlation_id = getattr(g, 'correlation_id', 'unknown')
            
            logger.info(
                f"Audit: User {user_id} started {action_type} "
                f"in {func.__name__} [ID: {correlation_id}]"
            )
            
            try:
                result = func(*args, **kwargs)
                
                # 記錄操作成功
                logger.info(
                    f"Audit: User {user_id} completed {action_type} "
                    f"in {func.__name__} [ID: {correlation_id}]"
                )
                
                return result
            except Exception as e:
                # 記錄操作失敗
                logger.error(
                    f"Audit: User {user_id} failed {action_type} "
                    f"in {func.__name__} [ID: {correlation_id}]: {str(e)}"
                )
                raise
        return wrapper
    return decorator

def require_permission(permission: str):
    """
    權限檢查裝飾器
    
    Args:
        permission: 需要的權限名稱
    
    使用方式：
    @require_permission('income_expense.create')
    def create_income():
        # 只有有權限的用戶才能訪問
        pass
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 檢查用戶權限
            user_permissions = g.get('user_permissions', [])
            
            if permission not in user_permissions:
                logger.warning(
                    f"Permission denied: User {g.get('user_id', 'anonymous')} "
                    f"lacks permission {permission} for {func.__name__}"
                )
                
                if request.is_json:
                    return jsonify({
                        'success': False,
                        'error': '權限不足'
                    }), 403
                else:
                    return render_template('403.html'), 403
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

def validate_request(schema: Dict[str, Any]):
    """
    請求驗證裝飾器
    
    Args:
        schema: 驗證模式
    
    使用方式：
    @validate_request({
        'amount': {'type': 'number', 'required': True},
        'date': {'type': 'date', 'required': True}
    })
    def create_transaction():
        # 請求數據會被自動驗證
        pass
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 獲取請求數據
            if request.is_json:
                data = request.get_json()
            else:
                data = request.form.to_dict()
            
            # 驗證必填欄位
            errors = []
            for field, rules in schema.items():
                if rules.get('required', False) and field not in data:
                    errors.append(f"缺少必填欄位: {field}")
                
                # 這裡可以添加更多驗證規則
            
            if errors:
                logger.warning(
                    f"Validation failed in {func.__name__}: {errors}"
                )
                
                if request.is_json:
                    return jsonify({
                        'success': False,
                        'errors': errors
                    }), 400
                else:
                    return render_template(
                        'error.html',
                        error_message='驗證失敗',
                        errors=errors
                    ), 400
            
            # 將驗證後的數據注入 g 對象
            g.validated_data = data
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

class RouteHelper:
    """
    路由輔助工具類
    提供常用的路由操作方法
    """
    
    @staticmethod
    def get_pagination_params(default_page: int = 1, default_per_page: int = 20) -> tuple:
        """
        獲取分頁參數
        
        Args:
            default_page: 預設頁碼
            default_per_page: 預設每頁數量
        
        Returns:
            (page, per_page) 元組
        """
        page = request.args.get('page', default_page, type=int)
        per_page = request.args.get('per_page', default_per_page, type=int)
        
        # 限制最大每頁數量
        per_page = min(per_page, 100)
        
        return page, per_page
    
    @staticmethod
    def get_date_range_params() -> tuple:
        """
        獲取日期範圍參數
        
        Returns:
            (start_date, end_date) 元組
        """
        from datetime import datetime, date
        
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 轉換日期格式
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                start_date = None
        
        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                end_date = None
        
        # 預設為當月
        if not start_date:
            today = date.today()
            start_date = date(today.year, today.month, 1)
        
        if not end_date:
            end_date = date.today()
        
        return start_date, end_date
    
    @staticmethod
    def format_response(success: bool, data: Any = None, message: str = None) -> Dict[str, Any]:
        """
        格式化 API 響應
        
        Args:
            success: 是否成功
            data: 響應數據
            message: 響應訊息
        
        Returns:
            格式化的響應字典
        """
        response = {
            'success': success,
            'timestamp': g.get('request_start_time', 0),
            'correlation_id': getattr(g, 'correlation_id', 'unknown')
        }
        
        if data is not None:
            response['data'] = data
        
        if message:
            response['message'] = message
        
        return response