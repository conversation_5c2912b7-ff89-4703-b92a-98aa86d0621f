"""
配置管理路由
提供 Web 介面來管理系統配置
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from utils.security.auth_decorators import admin_required, login_required
from utils.web.menu_decorator import with_menu, render_with_menu_data
from utils.common.config_tools import ConfigManager, ConfigValidator
from config.advanced_config import get_config, reload_config
import logging
import json

logger = logging.getLogger(__name__)

config_admin_bp = Blueprint('config_admin', __name__, url_prefix='/admin/config')

@config_admin_bp.route('/')
@admin_required
@with_menu('系統管理')
def index():
    """配置管理主頁"""
    try:
        # 獲取配置摘要
        summary = ConfigManager.get_config_summary()
        
        # 驗證配置
        validation_results = ConfigManager.validate_all_configs()
        
        return render_with_menu_data('config_admin/index.html', 
                                   config_summary=summary,
                                   validation_results=validation_results)
    except Exception as e:
        logger.error(f"載入配置管理頁面失敗: {e}")
        flash(f'載入配置失敗: {str(e)}', 'error')
        return redirect(url_for('admin.admin_panel'))

@config_admin_bp.route('/view')
@admin_required
def view_config():
    """查看完整配置"""
    try:
        config = get_config()
        config_dict = config.to_dict(include_sensitive=False)
        
        return render_with_menu_data('config_admin/view.html',
                                   config_data=config_dict)
    except Exception as e:
        logger.error(f"查看配置失敗: {e}")
        return jsonify({'error': str(e)}), 500

@config_admin_bp.route('/export')
@admin_required
def export_config():
    """匯出配置"""
    format_type = request.args.get('format', 'yaml')
    include_sensitive = request.args.get('sensitive', 'false').lower() == 'true'
    
    try:
        config_content = ConfigManager.export_config(format_type, include_sensitive)
        
        if format_type.lower() == 'json':
            return jsonify({'config': config_content, 'format': format_type})
        else:
            return jsonify({'config': config_content, 'format': format_type})
            
    except Exception as e:
        logger.error(f"匯出配置失敗: {e}")
        return jsonify({'error': str(e)}), 500

@config_admin_bp.route('/validate', methods=['POST'])
@admin_required
def validate_config():
    """驗證配置"""
    try:
        validation_results = ConfigManager.validate_all_configs()
        
        return jsonify({
            'success': True,
            'results': validation_results,
            'all_valid': all(validation_results.values())
        })
        
    except Exception as e:
        logger.error(f"配置驗證失敗: {e}")
        return jsonify({'error': str(e)}), 500

@config_admin_bp.route('/reload', methods=['POST'])
@admin_required
def reload_system_config():
    """重新載入配置"""
    try:
        config_file = request.json.get('config_file') if request.is_json else None
        reload_config(config_file)
        
        logger.info("系統配置已重新載入")
        return jsonify({'success': True, 'message': '配置重新載入成功'})
        
    except Exception as e:
        logger.error(f"重新載入配置失敗: {e}")
        return jsonify({'error': str(e)}), 500

@config_admin_bp.route('/sections/<section_name>')
@admin_required
def view_section(section_name):
    """查看特定配置段"""
    try:
        config = get_config()
        
        if not hasattr(config, section_name):
            return jsonify({'error': f'配置段 {section_name} 不存在'}), 404
        
        section = getattr(config, section_name)
        section_dict = {}
        
        for key, value in section.__dict__.items():
            # 隱藏敏感資訊
            if 'password' in key.lower() or 'secret' in key.lower() or 'key' in key.lower():
                value = '***隱藏***'
            section_dict[key] = value
        
        return jsonify({
            'section': section_name,
            'config': section_dict
        })
        
    except Exception as e:
        logger.error(f"查看配置段失敗: {e}")
        return jsonify({'error': str(e)}), 500

@config_admin_bp.route('/sections/<section_name>/edit', methods=['GET', 'POST'])
@admin_required
def edit_section(section_name):
    """編輯配置段"""
    try:
        config = get_config()
        
        if not hasattr(config, section_name):
            flash(f'配置段 {section_name} 不存在', 'error')
            return redirect(url_for('config_admin.index'))
        
        section = getattr(config, section_name)
        
        if request.method == 'GET':
            # 顯示編輯表單
            section_dict = {}
            for key, value in section.__dict__.items():
                section_dict[key] = value
            
            return render_with_menu_data('config_admin/edit_section.html',
                                       section_name=section_name,
                                       section_config=section_dict)
        
        elif request.method == 'POST':
            # 處理配置更新
            updated_fields = []
            
            for key, value in request.form.items():
                if hasattr(section, key):
                    # 類型轉換
                    old_value = getattr(section, key)
                    if isinstance(old_value, bool):
                        value = value.lower() in ['true', '1', 'on', 'yes']
                    elif isinstance(old_value, int):
                        value = int(value)
                    elif isinstance(old_value, float):
                        value = float(value)
                    
                    # 更新值
                    if getattr(section, key) != value:
                        setattr(section, key, value)
                        updated_fields.append(key)
            
            if updated_fields:
                logger.info(f"配置段 {section_name} 已更新: {updated_fields}")
                flash(f'配置段 {section_name} 更新成功: {", ".join(updated_fields)}', 'success')
            else:
                flash('沒有變更需要儲存', 'info')
            
            return redirect(url_for('config_admin.edit_section', section_name=section_name))
            
    except Exception as e:
        logger.error(f"編輯配置段失敗: {e}")
        flash(f'編輯配置失敗: {str(e)}', 'error')
        return redirect(url_for('config_admin.index'))

@config_admin_bp.route('/status')
@admin_required  
def config_status():
    """配置狀態 API"""
    try:
        config = get_config()
        summary = ConfigManager.get_config_summary()
        validation_results = ConfigManager.validate_all_configs()
        
        # 統計資訊
        stats = {
            'total_sections': len(summary),
            'valid_sections': sum(1 for result in validation_results.values() if result),
            'invalid_sections': sum(1 for result in validation_results.values() if not result),
            'config_source': 'advanced_config' if hasattr(config, 'cache') else 'legacy_config'
        }
        
        return jsonify({
            'summary': summary,
            'validation': validation_results,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"獲取配置狀態失敗: {e}")
        return jsonify({'error': str(e)}), 500

@config_admin_bp.route('/test_connection', methods=['POST'])
@admin_required
def test_connection():
    """測試連接"""
    connection_type = request.json.get('type') if request.is_json else request.form.get('type')
    
    try:
        config = get_config()
        
        if connection_type == 'database':
            result = ConfigValidator.validate_database_connection(config)
            return jsonify({
                'success': result,
                'message': '資料庫連接成功' if result else '資料庫連接失敗'
            })
            
        elif connection_type == 'redis':
            result = ConfigValidator.validate_redis_connection(config)
            return jsonify({
                'success': result,
                'message': 'Redis 連接成功' if result else 'Redis 連接失敗'
            })
            
        elif connection_type == 'mail':
            result = ConfigValidator.validate_mail_config(config)
            return jsonify({
                'success': result,
                'message': '郵件配置有效' if result else '郵件配置無效'
            })
        
        else:
            return jsonify({'error': f'未知的連接類型: {connection_type}'}), 400
            
    except Exception as e:
        logger.error(f"測試連接失敗: {e}")
        return jsonify({'error': str(e)}), 500

@config_admin_bp.route('/backup', methods=['POST'])
@admin_required
def backup_config():
    """備份當前配置"""
    try:
        from datetime import datetime
        import os
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'config_backup_{timestamp}.yaml'
        backup_path = os.path.join('backups', 'config', backup_filename)
        
        # 確保備份目錄存在
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
        
        # 儲存配置
        config = get_config()
        config.save_config(backup_path, 'yaml')
        
        logger.info(f"配置已備份到: {backup_path}")
        return jsonify({
            'success': True,
            'message': f'配置已備份',
            'backup_file': backup_filename
        })
        
    except Exception as e:
        logger.error(f"備份配置失敗: {e}")
        return jsonify({'error': str(e)}), 500

# API 端點
@config_admin_bp.route('/api/summary')
def api_config_summary():
    """API: 獲取配置摘要（無需認證，但隱藏敏感資訊）"""
    try:
        summary = ConfigManager.get_config_summary()
        return jsonify(summary)
    except Exception as e:
        logger.error(f"API 獲取配置摘要失敗: {e}")
        return jsonify({'error': 'Internal server error'}), 500