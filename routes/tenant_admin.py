"""
租戶管理路由
提供租戶CRUD、用戶管理、方案設定等功能
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from database import get_db
from models.tenant_models import Tenant, PlanLevel, TenantStatus, PlanFeature
from model import User
from utils.security.tenant_decorators import admin_only, require_feature
from werkzeug.security import generate_password_hash
import datetime

tenant_admin_bp = Blueprint('tenant_admin', __name__, url_prefix='/admin/tenants')

@tenant_admin_bp.route('/')
@admin_only()
def list_tenants():
    """租戶列表"""
    with get_db() as db:
        tenants = db.query(Tenant).filter(Tenant.is_deleted == False).all()
    return render_template('admin/tenant_list.html', tenants=tenants)

@tenant_admin_bp.route('/create', methods=['GET', 'POST'])
@admin_only()
def create_tenant():
    """創建租戶"""
    if request.method == 'POST':
        try:
            tenant_data = {
                'name': request.form['name'],
                'slug': request.form['slug'],
                'domain': request.form.get('domain', ''),
                'plan_level': PlanLevel(request.form['plan_level']),
                'contact_email': request.form['contact_email'],
                'contact_phone': request.form.get('contact_phone', ''),
                'contact_person': request.form.get('contact_person', ''),
                'address': request.form.get('address', ''),
                'tax_id': request.form.get('tax_id', ''),
                'max_users': int(request.form.get('max_users', 5)),
                'max_storage_mb': int(request.form.get('max_storage_mb', 1024)),
                'status': TenantStatus.TRIAL,
                'trial_start_date': datetime.date.today(),
                'trial_end_date': datetime.date.today() + datetime.timedelta(days=30),
            }
            
            with get_db() as db:
                # 檢查slug是否重複
                existing = db.query(Tenant).filter(Tenant.slug == tenant_data['slug']).first()
                if existing:
                    flash('租戶識別符已存在', 'error')
                    return render_template('admin/tenant_form.html')
                
                tenant = Tenant(**tenant_data)
                db.add(tenant)
                db.commit()
                
                # 創建租戶管理員帳號
                admin_data = {
                    'username': f"{tenant.slug}_admin",
                    'email': tenant.contact_email,
                    'password_hash': generate_password_hash('admin123'),  # 預設密碼
                    'full_name': f"{tenant.name} 管理員",
                    'tenant_id': tenant.id,
                    'is_tenant_admin': True
                }
                
                admin_user = User(**admin_data)
                db.add(admin_user)
                db.commit()
                
                flash(f'租戶 {tenant.name} 創建成功！管理員帳號：{admin_user.username}，預設密碼：admin123', 'success')
                return redirect(url_for('tenant_admin.list_tenants'))
                
        except Exception as e:
            flash(f'創建失敗：{str(e)}', 'error')
    
    return render_template('admin/tenant_form.html')

@tenant_admin_bp.route('/<int:tenant_id>/edit', methods=['GET', 'POST'])
@admin_only()
def edit_tenant(tenant_id):
    """編輯租戶"""
    with get_db() as db:
        tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
        if not tenant:
            flash('租戶不存在', 'error')
            return redirect(url_for('tenant_admin.list_tenants'))
        
        if request.method == 'POST':
            try:
                tenant.name = request.form['name']
                tenant.plan_level = PlanLevel(request.form['plan_level'])
                tenant.status = TenantStatus(request.form['status'])
                tenant.contact_email = request.form['contact_email']
                tenant.contact_phone = request.form.get('contact_phone', '')
                tenant.contact_person = request.form.get('contact_person', '')
                tenant.address = request.form.get('address', '')
                tenant.tax_id = request.form.get('tax_id', '')
                tenant.max_users = int(request.form.get('max_users', 5))
                tenant.max_storage_mb = int(request.form.get('max_storage_mb', 1024))
                
                db.commit()
                flash('租戶資料更新成功', 'success')
                return redirect(url_for('tenant_admin.list_tenants'))
                
            except Exception as e:
                flash(f'更新失敗：{str(e)}', 'error')
    
    return render_template('admin/tenant_form.html', tenant=tenant)

@tenant_admin_bp.route('/<int:tenant_id>/users')
@admin_only()
def tenant_users(tenant_id):
    """租戶用戶管理"""
    with get_db() as db:
        tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
        if not tenant:
            flash('租戶不存在', 'error')
            return redirect(url_for('tenant_admin.list_tenants'))
        
        users = db.query(User).filter(User.tenant_id == tenant_id).all()
    
    return render_template('admin/tenant_users.html', tenant=tenant, users=users)

@tenant_admin_bp.route('/features')
@admin_only()
def manage_features():
    """功能管理"""
    with get_db() as db:
        features = db.query(PlanFeature).order_by(PlanFeature.module_name, PlanFeature.feature_name).all()
    
    return render_template('admin/plan_features.html', features=features)

@tenant_admin_bp.route('/api/tenant/<int:tenant_id>/toggle_status', methods=['POST'])
@admin_only()
def toggle_tenant_status(tenant_id):
    """切換租戶狀態"""
    try:
        with get_db() as db:
            tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
            if not tenant:
                return jsonify({'success': False, 'message': '租戶不存在'})
            
            # 切換狀態
            if tenant.status == TenantStatus.ACTIVE:
                tenant.status = TenantStatus.SUSPENDED
            else:
                tenant.status = TenantStatus.ACTIVE
            
            db.commit()
            return jsonify({
                'success': True,
                'status': tenant.status.value,
                'message': f'租戶狀態已更新為 {tenant.status.value}'
            })
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 租戶用戶前台路由
tenant_bp = Blueprint('tenant', __name__, url_prefix='/tenant')

@tenant_bp.route('/dashboard')
@require_feature('system', 'dashboard')
def dashboard():
    """租戶儀表板"""
    from flask import g
    tenant = g.get('tenant')
    if not tenant:
        return redirect(url_for('auth.login'))
    
    return render_template('tenant/dashboard.html', tenant=tenant)

@tenant_bp.route('/plan')
@require_feature('system', 'plan_info')
def plan_info():
    """方案資訊"""
    from flask import g
    from services.tenant_service import FeatureService
    
    tenant = g.get('tenant')
    if not tenant:
        return redirect(url_for('auth.login'))
    
    # 獲取可用功能
    accessible_modules = FeatureService.get_accessible_modules(tenant)
    
    with get_db() as db:
        all_features = db.query(PlanFeature).filter(PlanFeature.is_active == True).all()
    
    return render_template('tenant/plan_info.html', 
                         tenant=tenant, 
                         accessible_modules=accessible_modules,
                         all_features=all_features)