"""
新的報表路由 - 使用重構後的 Transaction 和 JournalEntry 模型
"""
from flask import Blueprint, render_template, request, jsonify
from sqlalchemy.orm import sessionmaker
from datetime import datetime, date, timedelta
import calendar

from model import engine
from services.new_balance_sheet_service import NewBalanceSheetService
from services.new_income_statement_service import NewIncomeStatementService
from data.menu_data import menu

Session = sessionmaker(bind=engine)
new_reports_bp = Blueprint('new_reports', __name__)


def parse_date(date_str, default=None):
    """解析日期字符串"""
    if not date_str:
        return default
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return default


@new_reports_bp.route('/new_balance_sheet')
def new_balance_sheet():
    """新的資產負債表"""
    db = Session()
    try:
        # 獲取查詢參數
        as_of_date_str = request.args.get('as_of_date')
        as_of_date = parse_date(as_of_date_str, date.today())
        
        # 生成資產負債表
        service = NewBalanceSheetService(db)
        balance_sheet_data = service.generate_balance_sheet(as_of_date)
        
        # 計算百分比
        balance_sheet_data = service.calculate_percentages(balance_sheet_data)
        
        return render_template('new_balance_sheet.html',
                             sidebar_items=menu,
                             selected='新資產負債表',
                             data=balance_sheet_data,
                             as_of_date=as_of_date)
    
    finally:
        db.close()


@new_reports_bp.route('/new_income_statement')
def new_income_statement():
    """新的損益表"""
    db = Session()
    try:
        # 獲取查詢參數
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        # 默認為本月
        today = date.today()
        default_start = today.replace(day=1)
        default_end = today
        
        start_date = parse_date(start_date_str, default_start)
        end_date = parse_date(end_date_str, default_end)
        
        # 生成損益表
        service = NewIncomeStatementService(db)
        income_statement_data = service.generate_income_statement(start_date, end_date)
        
        return render_template('new_income_statement.html',
                             sidebar_items=menu,
                             selected='新損益表',
                             data=income_statement_data,
                             start_date=start_date,
                             end_date=end_date)
    
    finally:
        db.close()


@new_reports_bp.route('/trial_balance')
def trial_balance():
    """試算表"""
    db = Session()
    try:
        # 獲取查詢參數
        as_of_date_str = request.args.get('as_of_date')
        as_of_date = parse_date(as_of_date_str, date.today())
        
        # 生成試算表數據
        service = NewBalanceSheetService(db)
        subject_balances = service._calculate_all_subject_balances(as_of_date)
        
        # 按科目代碼排序
        sorted_balances = sorted(subject_balances.values(), key=lambda x: x['subject_code'])
        
        # 計算總計
        total_debit = sum(item['total_debit'] for item in sorted_balances)
        total_credit = sum(item['total_credit'] for item in sorted_balances)
        
        # 按科目類別分組
        categories = {
            '1': {'name': '資產', 'items': []},
            '2': {'name': '負債', 'items': []},
            '3': {'name': '權益', 'items': []},
            '4': {'name': '收入', 'items': []},
            '5': {'name': '成本', 'items': []},
            '6': {'name': '費用', 'items': []}
        }
        
        for item in sorted_balances:
            first_digit = item['subject_code'][0]
            if first_digit in categories:
                categories[first_digit]['items'].append(item)
        
        return render_template('trial_balance.html',
                             sidebar_items=menu,
                             selected='試算表',
                             categories=categories,
                             totals={
                                 'total_debit': total_debit,
                                 'total_credit': total_credit,
                                 'is_balanced': abs(total_debit - total_credit) < 1
                             },
                             as_of_date=as_of_date)
    
    finally:
        db.close()


@new_reports_bp.route('/subject_detail/<subject_code>')
def subject_detail(subject_code):
    """科目明細"""
    db = Session()
    try:
        # 獲取查詢參數
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        # 默認為本月
        today = date.today()
        default_start = today.replace(day=1)
        default_end = today
        
        start_date = parse_date(start_date_str, default_start)
        end_date = parse_date(end_date_str, default_end)
        
        # 獲取科目明細
        service = NewBalanceSheetService(db)
        detail_data = service.get_subject_detail(subject_code, start_date, end_date)
        
        return render_template('subject_detail.html',
                             sidebar_items=menu,
                             selected='科目明細',
                             data=detail_data,
                             subject_code=subject_code,
                             start_date=start_date,
                             end_date=end_date)
    
    finally:
        db.close()


@new_reports_bp.route('/monthly_comparison')
def monthly_comparison():
    """月度比較報表"""
    db = Session()
    try:
        # 獲取年份參數
        year = int(request.args.get('year', date.today().year))
        
        # 生成月度數據
        service = NewIncomeStatementService(db)
        monthly_data = []
        
        for month in range(1, 13):
            start_date = date(year, month, 1)
            # 計算月末日期
            last_day = calendar.monthrange(year, month)[1]
            end_date = date(year, month, last_day)
            
            # 生成該月損益表
            statement = service.generate_income_statement(start_date, end_date)
            
            monthly_data.append({
                'month': month,
                'month_name': calendar.month_name[month],
                'revenue': statement['summary']['total_revenue'],
                'expenses': statement['summary']['total_operating_expenses'],
                'profit': statement['summary']['net_profit'],
                'profit_margin': statement['summary']['profit_margin']
            })
        
        # 計算年度總計
        year_totals = {
            'revenue': sum(m['revenue'] for m in monthly_data),
            'expenses': sum(m['expenses'] for m in monthly_data),
            'profit': sum(m['profit'] for m in monthly_data)
        }
        year_totals['profit_margin'] = (year_totals['profit'] / year_totals['revenue'] * 100) if year_totals['revenue'] > 0 else 0
        
        return render_template('monthly_comparison.html',
                             sidebar_items=menu,
                             selected='月度比較',
                             monthly_data=monthly_data,
                             year_totals=year_totals,
                             year=year)
    
    finally:
        db.close()


@new_reports_bp.route('/api/balance_sheet_data')
def api_balance_sheet_data():
    """資產負債表 API"""
    db = Session()
    try:
        as_of_date_str = request.args.get('as_of_date')
        as_of_date = parse_date(as_of_date_str, date.today())
        
        service = NewBalanceSheetService(db)
        data = service.generate_balance_sheet(as_of_date)
        data = service.calculate_percentages(data)
        
        # 轉換日期為字符串
        data['as_of_date'] = data['as_of_date'].isoformat()
        
        return jsonify(data)
    
    finally:
        db.close()


@new_reports_bp.route('/api/income_statement_data')
def api_income_statement_data():
    """損益表 API"""
    db = Session()
    try:
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        today = date.today()
        start_date = parse_date(start_date_str, today.replace(day=1))
        end_date = parse_date(end_date_str, today)
        
        service = NewIncomeStatementService(db)
        data = service.generate_income_statement(start_date, end_date)
        
        # 轉換日期為字符串
        data['period']['start_date'] = data['period']['start_date'].isoformat()
        data['period']['end_date'] = data['period']['end_date'].isoformat()
        
        return jsonify(data)
    
    finally:
        db.close()


@new_reports_bp.route('/reports_dashboard')
def reports_dashboard():
    """報表儀表板"""
    db = Session()
    try:
        # 獲取基本統計數據
        today = date.today()
        this_month_start = today.replace(day=1)
        
        balance_service = NewBalanceSheetService(db)
        income_service = NewIncomeStatementService(db)
        
        # 資產負債表摘要
        balance_sheet = balance_service.generate_balance_sheet(today)
        
        # 本月損益表
        income_statement = income_service.generate_income_statement(this_month_start, today)
        
        # 利潤趨勢（最近6個月）
        profit_trend = income_service.calculate_profit_trend(6)
        
        dashboard_data = {
            'balance_sheet_summary': balance_sheet['totals'],
            'income_statement_summary': income_statement['summary'],
            'profit_trend': profit_trend
        }
        
        return render_template('new_reports_dashboard.html',
                             sidebar_items=menu,
                             selected='報表儀表板',
                             data=dashboard_data)
    
    finally:
        db.close()
