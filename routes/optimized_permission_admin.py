"""
優化版本的權限管理路由
主要優化：
1. 使用 eager loading 避免 N+1 查詢
2. 批量載入關聯資料
3. 使用查詢快取減少重複查詢
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from services.auth_service import AuthService, RoleService
from services.menu_service import MenuService
from utils.security.auth_decorators import admin_required
from database import get_db
from models.auth_models import Role, Permission, role_permissions
from model import User
from sqlalchemy.orm import joinedload, selectinload
from functools import lru_cache

optimized_permission_admin_bp = Blueprint('optimized_permission_admin', __name__, url_prefix='/admin/optimized_permissions')

@optimized_permission_admin_bp.route('/')
@admin_required
def index():
    """權限管理首頁 - 優化版本"""
    with get_db() as db:
        # === 優化 1: 使用 eager loading 一次載入所有關聯資料 ===
        
        # 載入角色及其權限（避免 N+1）
        roles = db.query(Role).options(
            selectinload(Role.permissions)  # 預載權限
        ).filter(Role.is_active).all()
        
        roles_data = []
        for role in roles:
            role_dict = {
                'id': role.id,
                'name': role.name,
                'display_name': role.display_name,
                'description': role.description,
                'is_active': role.is_active,
                'created_at': role.created_at,
                'updated_at': role.updated_at,
                'permission_count': len(role.permissions)  # 不會觸發額外查詢
            }
            roles_data.append(role_dict)
        
        # 權限查詢保持不變（已經是單一查詢）
        permissions = db.query(Permission).all()
        permissions_data = [{
            'id': perm.id,
            'name': perm.name,
            'display_name': perm.display_name,
            'module': perm.module,
            'action': perm.action,
            'description': perm.description
        } for perm in permissions]
        
        # === 優化 2: 用戶查詢優化，預載角色資訊 ===
        from models.auth_models import user_roles
        
        # 使用子查詢獲取用戶及其角色數量
        users = db.query(User).filter(User.is_active).all()
        
        # 批量查詢所有用戶的角色
        user_ids = [user.id for user in users]
        user_role_counts = db.query(
            user_roles.c.user_id,
            func.count(user_roles.c.role_id).label('role_count')
        ).filter(
            user_roles.c.user_id.in_(user_ids)
        ).group_by(user_roles.c.user_id).all()
        
        # 轉換為字典方便查找
        role_count_map = {row.user_id: row.role_count for row in user_role_counts}
        
        users_data = []
        for user in users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'full_name': user.full_name,
                'is_active': user.is_active,
                'last_login': user.last_login,
                'created_at': user.created_at,
                'updated_at': user.updated_at,
                'role_count': role_count_map.get(user.id, 0)
            })
    
    return render_template('admin/permissions/index.html', 
                         roles=roles_data, 
                         permissions=permissions_data,
                         users=users_data)

@optimized_permission_admin_bp.route('/roles')
@admin_required
def roles():
    """角色管理 - 優化版本"""
    with get_db() as db:
        # 預載權限和用戶，避免 N+1 查詢
        roles = db.query(Role).options(
            selectinload(Role.permissions),
            selectinload(Role.users) if hasattr(Role, 'users') else None
        ).filter(Role.is_active).all()
        
        roles_data = []
        for role in roles:
            roles_data.append({
                'id': role.id,
                'name': role.name,
                'display_name': role.display_name,
                'description': role.description,
                'is_active': role.is_active,
                'created_at': role.created_at,
                'updated_at': role.updated_at,
                'permission_count': len(role.permissions),
                'user_count': len(role.users) if hasattr(role, 'users') else 0
            })
    
    return render_template('admin/permissions/roles.html', roles=roles_data)

@optimized_permission_admin_bp.route('/roles/<int:role_id>/permissions', methods=['GET', 'POST'])
@admin_required
def role_permissions(role_id):
    """管理角色權限 - 優化版本"""
    with get_db() as db:
        # === 優化: 一次查詢載入角色及其權限 ===
        role = db.query(Role).options(
            selectinload(Role.permissions)
        ).filter(Role.id == role_id).first()
        
        if not role:
            flash('角色不存在', 'error')
            return redirect(url_for('optimized_permission_admin.roles'))
        
        if request.method == 'POST':
            # 處理權限更新
            permission_ids = request.form.getlist('permissions')
            permission_ids = [int(pid) for pid in permission_ids]
            
            try:
                RoleService.assign_permissions_to_role(role_id, permission_ids)
                flash('權限更新成功', 'success')
                return redirect(url_for('optimized_permission_admin.role_permissions', role_id=role_id))
            except Exception as e:
                flash(f'更新失敗：{str(e)}', 'error')
        
        # 獲取所有權限並組織成模組
        all_permissions = db.query(Permission).all()
        
        # 角色已有的權限 ID（從預載的資料中獲取，不觸發額外查詢）
        role_permission_ids = [p.id for p in role.permissions]
        
        # 按模組組織權限
        permissions_by_module = {}
        for perm in all_permissions:
            if perm.module not in permissions_by_module:
                permissions_by_module[perm.module] = []
            permissions_by_module[perm.module].append({
                'id': perm.id,
                'name': perm.name,
                'display_name': perm.display_name,
                'action': perm.action,
                'description': perm.description,
                'is_assigned': perm.id in role_permission_ids
            })
    
    return render_template('admin/permissions/role_permissions.html', 
                         role=role,
                         permissions_by_module=permissions_by_module,
                         role_permission_ids=role_permission_ids)

# === 額外優化：使用快取來減少重複查詢 ===
@lru_cache(maxsize=128)
def get_cached_permissions():
    """獲取快取的權限列表（5分鐘有效）"""
    with get_db() as db:
        permissions = db.query(Permission).all()
        return [{
            'id': p.id,
            'name': p.name,
            'display_name': p.display_name,
            'module': p.module,
            'action': p.action
        } for p in permissions]

@lru_cache(maxsize=128)
def get_cached_roles():
    """獲取快取的角色列表（5分鐘有效）"""
    with get_db() as db:
        roles = db.query(Role).filter(Role.is_active).all()
        return [{
            'id': r.id,
            'name': r.name,
            'display_name': r.display_name
        } for r in roles]

# 定期清理快取（可透過 APScheduler 或其他方式實現）
def clear_permission_cache():
    """清理權限相關快取"""
    get_cached_permissions.cache_clear()
    get_cached_roles.cache_clear()

# === API 端點優化範例 ===
@optimized_permission_admin_bp.route('/api/user/<int:user_id>/permissions')
@admin_required
def get_user_permissions_optimized(user_id):
    """獲取用戶權限 - 優化版本，使用單一查詢"""
    with get_db() as db:
        # 使用 JOIN 一次查詢獲取用戶的所有權限
        from sqlalchemy import select
        from models.auth_models import user_roles
        
        # 構建複雜查詢，一次獲取所有需要的資料
        permissions = db.query(Permission).select_from(Permission).join(
            role_permissions,
            Permission.id == role_permissions.c.permission_id
        ).join(
            Role,
            Role.id == role_permissions.c.role_id
        ).join(
            user_roles,
            user_roles.c.role_id == Role.id
        ).filter(
            user_roles.c.user_id == user_id,
            Role.is_active == True
        ).distinct().all()
        
        permission_list = [{
            'id': p.id,
            'name': p.name,
            'module': p.module,
            'action': p.action
        } for p in permissions]
        
        return jsonify({
            'user_id': user_id,
            'permissions': permission_list,
            'total': len(permission_list)
        })

from sqlalchemy import func