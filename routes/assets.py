from flask import Blueprint, request
from model import AccountSubject
from database import get_db
from utils.web.menu_decorator import with_menu, render_with_menu_data

assets_bp = Blueprint('assets', __name__)

@assets_bp.route('/add_prepaid_expense', methods=['GET', 'POST'])
@with_menu('資產管理')
def add_prepaid_expense():
    with get_db() as db:
        subjects = db.query(AccountSubject).filter(
            AccountSubject.code.in_(['6020', '6030', '6050', '6080', '6060', '6230'])
        ).all()
        subject_list = [{'id': s.id, 'name': s.name, 'code': s.code} for s in subjects]
        
    # 使用通用函數獲取帳戶和收支對象資料
    from main import get_dropdown_data
    dropdown_data = get_dropdown_data()
    account_list = dropdown_data['accounts']
    identity_list = dropdown_data['payment_identities']
    
    return render_with_menu_data(
        'add_prepaid_expense.html',
        subjects=subject_list,
        accounts=account_list,
        identities=identity_list
    )

@assets_bp.route('/add_amortization', methods=['GET', 'POST'])
@with_menu('資產管理')
def add_amortization():
    """新增各項攤提"""
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    
    return render_with_menu_data('add_amortization.html')

@assets_bp.route('/add_fixed_asset', methods=['GET', 'POST'])
@with_menu('資產管理')
def add_fixed_asset():
    """新增固定資產"""
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    
    return render_with_menu_data('add_fixed_asset.html')

@assets_bp.route('/add_intangible_asset', methods=['GET', 'POST'])
@with_menu('資產管理')
def add_intangible_asset():
    """新增無形資產"""
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    
    return render_with_menu_data('add_intangible_asset.html')

@assets_bp.route('/asset_list', methods=['GET'])
@with_menu('資產管理')
def asset_list():
    """財產列表"""
    return render_with_menu_data('asset_list.html') 