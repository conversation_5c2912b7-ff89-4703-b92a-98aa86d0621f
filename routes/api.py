"""
API 相關路由 - 採用標準化響應格式和安全機制
"""
from flask import Blueprint, request, jsonify
from model import Money, PaymentIdentity, PaymentIdentityType
from sqlalchemy import extract, or_
from database import get_db
from utils.web import APIResponse, api_route, APIStatus, APIErrorCode
from utils.security.security import rate_limit
from utils.security.api_security import (
    require_api_auth, api_rate_limit_decorator, validate_api_input, 
    api_error_handler
)
import datetime
from utils.tenant_utils import require_tenant_access, add_tenant_filter, get_current_tenant_id

api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/bank_heads')
@api_route(methods=['GET'])
@require_api_auth(['api_key', 'session'])
@api_rate_limit_decorator(max_requests=30, window_minutes=1)
@api_error_handler
def bank_heads():
    """取得所有銀行總行列表"""
    try:
        from database import engine
        from sqlalchemy import text
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT code, name FROM bank_head_offices ORDER BY code"))
            data = {row.code: row.name for row in result}
            
        return APIResponse.success(
            data=data,
            message=f"成功取得 {len(data)} 個銀行總行"
        )
        
    except Exception as e:
        return APIResponse.error(
            message="取得銀行總行失敗",
            error_code=APIErrorCode.DATABASE_ERROR,
            details={"error": str(e)},
            status_code=APIStatus.INTERNAL_ERROR
        )

@api_bp.route('/bank_branches/<head_code>')
@api_route(methods=['GET'])
@require_api_auth(['api_key', 'session'])
@api_rate_limit_decorator(max_requests=30, window_minutes=1)
@validate_api_input()  # 路徑參數會自動驗證
@api_error_handler
def bank_branches(head_code):
    """取得指定總行下的所有分行"""
    if not head_code or not head_code.strip():
        return APIResponse.error(
            message="總行代碼不能為空",
            error_code=APIErrorCode.VALIDATION_ERROR,
            status_code=APIStatus.BAD_REQUEST
        )
    
    try:
        from database import engine
        from sqlalchemy import text
        
        with engine.connect() as conn:
            result = conn.execute(
                text("SELECT code, name FROM bank_branches WHERE head_office_code = :head_code ORDER BY code"),
                {"head_code": head_code}
            )
            data = {row.code: row.name for row in result}
            
        if not data:
            return APIResponse.not_found(
                resource="銀行分行",
                message=f"找不到總行代碼 '{head_code}' 的分行資料"
            )
            
        return APIResponse.success(
            data=data,
            message=f"成功取得總行 {head_code} 的 {len(data)} 個分行"
        )
        
    except Exception as e:
        return APIResponse.error(
            message="取得銀行分行失敗",
            error_code=APIErrorCode.DATABASE_ERROR,
            details={"head_code": head_code, "error": str(e)},
            status_code=APIStatus.INTERNAL_ERROR
        )

@api_bp.route('/check_invoice_number')
@api_route(methods=['GET'])
@require_api_auth(['api_key', 'session'])
@api_rate_limit_decorator(max_requests=20, window_minutes=1)
@api_error_handler
def check_invoice_number():
    """檢查今年發票號碼是否已存在"""
    number = request.args.get('number', '').strip()
    
    if not number:
        return APIResponse.error(
            message="發票號碼不能為空",
            error_code=APIErrorCode.VALIDATION_ERROR,
            status_code=APIStatus.BAD_REQUEST
        )
    
    try:
        this_year = datetime.datetime.now().year
        with get_db() as db:
            exists = db.query(Money).filter(
                Money.number == number,
                Money.date.is_not(None),
                extract('year', Money.date) == this_year
            ).first() is not None
        
        return APIResponse.success(
            data={
                'exists': exists,
                'number': number,
                'year': this_year
            },
            message=f"發票號碼 {number} {'已存在' if exists else '可使用'}"
        )
        
    except Exception as e:
        return APIResponse.error(
            message="檢查發票號碼失敗",
            error_code=APIErrorCode.DATABASE_ERROR,
            details={"number": number, "error": str(e)},
            status_code=APIStatus.INTERNAL_ERROR
        )

@api_bp.route('/payment_identities/search', methods=['GET'])
@require_api_auth(['api_key', 'session'])
@api_rate_limit_decorator(max_requests=50, window_minutes=1)
@api_error_handler
def search_payment_identities():
    """搜索收支對象"""
    from urllib.parse import unquote
    
    # 獲取原始查詢參數
    raw_query = request.args.get('q', '')
    
    # 嘗試不同的解碼方式
    try:
        # 首先嘗試標準的URL解碼
        query = unquote(raw_query).strip()
    except:
        try:
            # 如果失敗，嘗試latin-1編碼
            query = raw_query.encode('latin-1').decode('utf-8').strip()
        except:
            # 最後使用原始字符串
            query = raw_query.strip()
    
    if not query:
        return jsonify([])
    
    try:
        with get_db() as db:
            # 獲取所有對象
            all_identities = db.query(PaymentIdentity).all()
            
            # 使用Python過濾
            identities = []
            for identity in all_identities:
                if query.lower() in identity.name.lower():
                    identities.append(identity)
                    if len(identities) >= 10:
                        break
            
            results = []
            for identity in identities:
                type_name = identity.identity_type.name if identity.identity_type else '（未分類）'
                results.append({
                    'id': identity.id,
                    'name': identity.name,
                    'type_name': type_name,
                    'tax_id': identity.tax_id or '',
                    'display_text': f'{identity.name} ({type_name})'
                })
            
            return jsonify(results)
    except Exception as e:
        return jsonify({'error': str(e)}), 500



@api_bp.route('/payment_identities/create', methods=['POST'])
@require_api_auth(['api_key', 'session'])
@api_rate_limit_decorator(max_requests=10, window_minutes=5)
@validate_api_input(
    required_fields=['name'],
    type_rules={'name': str, 'type_name': str},
    length_rules={'name': (1, 100), 'type_name': (0, 50)}
)
@api_error_handler
def create_payment_identity():
    """快速創建收支對象"""
    from flask import g
    
    # 使用驗證後的數據
    data = g.api_data
    name = data.get('name', '').strip()
    type_name = data.get('type_name', '').strip()
    
    with get_db() as db:
        # 檢查是否已存在同名對象
        existing = db.query(PaymentIdentity).filter_by(name=name).first()
        if existing:
            return jsonify({
                'id': existing.id,
                'name': existing.name,
                'type_name': existing.identity_type.name if existing.identity_type else '（未分類）',
                'message': '已存在同名對象'
            })
        
        # 查找或創建對象類別
        type_obj = None
        if type_name:
            type_obj = db.query(PaymentIdentityType).filter_by(name=type_name).first()
            if not type_obj:
                # 自動創建新的對象類別
                type_obj = PaymentIdentityType(name=type_name, description=f'自動創建的{type_name}類別')
                db.add(type_obj)
                db.flush()  # 獲取ID
        
        # 創建新的收支對象
        new_identity = PaymentIdentity(
            name=name,
            type_id=type_obj.id if type_obj else None
        )
        db.add(new_identity)
        db.commit()
        
        return jsonify({
            'id': new_identity.id,
            'name': new_identity.name,
            'type_name': type_obj.name if type_obj else '（未分類）',
            'message': '創建成功'
        })

@api_bp.route('/income_expense_trend')
@require_api_auth(['api_key', 'session'])
@api_rate_limit_decorator(max_requests=30, window_minutes=1)
@api_error_handler
def income_expense_trend():
    """獲取收入/支出趨勢數據"""
    from services.dashboard_service import DashboardService
    
    period = request.args.get('period', 'month')
    if period not in ['week', 'month', 'quarter', 'year']:
        period = 'month'
    
    with get_db() as db:
        chart_data = DashboardService.get_trend_chart_data(db, period)
        
        return jsonify({
            'labels': chart_data['labels'],
            'income_data': chart_data['income_data'],
            'expense_data': chart_data['expense_data']
        })