"""
錯誤監控管理界面路由
"""
from flask import Blueprint, render_template, jsonify, request
from utils.logging.error_monitor import error_monitor
from utils.logging.error_handler import handle_database_error
from datetime import datetime

error_monitor_bp = Blueprint('error_monitor', __name__, url_prefix='/admin/errors')

@error_monitor_bp.route('/dashboard')
@handle_database_error
def dashboard():
    """錯誤監控儀表板"""
    return render_template('error_monitor/dashboard.html')

@error_monitor_bp.route('/api/summary')
@handle_database_error
def api_summary():
    """獲取錯誤摘要API"""
    summary = error_monitor.get_error_summary()
    return jsonify({
        'success': True,
        'data': summary,
        'timestamp': datetime.now().isoformat()
    })

@error_monitor_bp.route('/api/trends')
@handle_database_error
def api_trends():
    """獲取錯誤趨勢API"""
    hours = request.args.get('hours', 24, type=int)
    trends = error_monitor.get_error_trends(hours=hours)
    return jsonify({
        'success': True,
        'data': trends,
        'timestamp': datetime.now().isoformat()
    })

@error_monitor_bp.route('/api/details/<error_type>')
@handle_database_error
def api_error_details(error_type):
    """獲取特定錯誤類型的詳細信息API"""
    limit = request.args.get('limit', 50, type=int)
    details = error_monitor.get_error_details_by_type(error_type, limit=limit)
    return jsonify({
        'success': True,
        'data': details,
        'error_type': error_type,
        'timestamp': datetime.now().isoformat()
    })

@error_monitor_bp.route('/api/export')
@handle_database_error
def api_export_report():
    """導出錯誤報告API"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    report = error_monitor.export_error_report(
        start_date=start_date,
        end_date=end_date
    )
    
    return jsonify({
        'success': True,
        'data': report,
        'timestamp': datetime.now().isoformat()
    })

@error_monitor_bp.route('/api/clear')
@handle_database_error
def api_clear_errors():
    """清理舊錯誤記錄API"""
    days = request.args.get('days', 7, type=int)
    error_monitor.clear_old_errors(days=days)
    
    return jsonify({
        'success': True,
        'message': f'已清理 {days} 天前的錯誤記錄',
        'timestamp': datetime.now().isoformat()
    })

@error_monitor_bp.route('/list')
@handle_database_error
def error_list():
    """錯誤列表頁面"""
    return render_template('error_monitor/error_list.html')

@error_monitor_bp.route('/report')
@handle_database_error
def error_report():
    """錯誤報告頁面"""
    return render_template('error_monitor/error_report.html')