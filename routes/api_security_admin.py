"""
API 安全管理介面
提供 API 密鑰管理、使用統計、安全監控等功能
"""
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from utils.security.api_security import api_key_manager, api_rate_limit
from utils.security.auth_decorators import login_required
from utils.security.security_monitor import security_monitor
import logging

logger = logging.getLogger(__name__)

api_security_admin_bp = Blueprint('api_security_admin', __name__, url_prefix='/api-security')

@api_security_admin_bp.route('/')
@login_required
def dashboard():
    """API 安全管理儀表板"""
    user_id = session.get('user_id')
    
    # 獲取用戶的 API 密鑰
    api_keys = session.get('api_keys', {})
    user_keys = [
        {**key_info, 'key_id': key_id} 
        for key_id, key_info in api_keys.items() 
        if key_info['user_id'] == user_id
    ]
    
    # 獲取使用統計
    usage_stats = []
    for key_info in user_keys:
        key_id = key_info['key_id']
        identifier = f"api_key:{key_id}"
        stats = api_rate_limit.get_usage_stats(identifier)
        usage_stats.append({
            'key_id': key_id,
            'description': key_info.get('description', ''),
            **stats
        })
    
    return render_template('api_security/dashboard.html', 
                         api_keys=user_keys,
                         usage_stats=usage_stats)

@api_security_admin_bp.route('/generate-key', methods=['POST'])
@login_required
def generate_api_key():
    """生成新的 API 密鑰"""
    try:
        user_id = session.get('user_id')
        description = request.form.get('description', '').strip()
        expires_days = int(request.form.get('expires_days', 90))
        
        if not description:
            return jsonify({
                'success': False,
                'error': '請提供密鑰描述'
            }), 400
        
        if expires_days < 1 or expires_days > 365:
            return jsonify({
                'success': False,
                'error': '過期天數必須在 1-365 之間'
            }), 400
        
        # 檢查用戶密鑰數量限制
        api_keys = session.get('api_keys', {})
        user_key_count = sum(1 for key_info in api_keys.values() 
                           if key_info['user_id'] == user_id and key_info.get('is_active', True))
        
        if user_key_count >= 5:  # 每個用戶最多 5 個活躍密鑰
            return jsonify({
                'success': False,
                'error': '每個用戶最多可擁有 5 個活躍的 API 密鑰'
            }), 400
        
        # 生成密鑰
        result = api_key_manager.generate_api_key(user_id, description, expires_days)
        
        if result['success']:
            # 記錄到安全監控
            security_monitor.log_sensitive_operation(
                'API_KEY_GENERATED',
                f'user_{user_id}',
                {
                    'key_id': result['key_id'],
                    'description': description,
                    'expires_days': expires_days
                }
            )
            
            logger.info(f"用戶 {user_id} 生成了新的 API 密鑰: {result['key_id']}")
            
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"生成 API 密鑰錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': '生成密鑰時發生錯誤'
        }), 500

@api_security_admin_bp.route('/revoke-key', methods=['POST'])
@login_required
def revoke_api_key():
    """撤銷 API 密鑰"""
    try:
        user_id = session.get('user_id')
        key_id = request.form.get('key_id', '').strip()
        
        if not key_id:
            return jsonify({
                'success': False,
                'error': '請提供密鑰 ID'
            }), 400
        
        # 撤銷密鑰
        success = api_key_manager.revoke_api_key(key_id, user_id)
        
        if success:
            # 記錄到安全監控
            security_monitor.log_sensitive_operation(
                'API_KEY_REVOKED',
                f'user_{user_id}',
                {'key_id': key_id}
            )
            
            logger.info(f"用戶 {user_id} 撤銷了 API 密鑰: {key_id}")
            
            return jsonify({
                'success': True,
                'message': '密鑰已成功撤銷'
            })
        else:
            return jsonify({
                'success': False,
                'error': '無法撤銷指定的密鑰'
            }), 400
        
    except Exception as e:
        logger.error(f"撤銷 API 密鑰錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': '撤銷密鑰時發生錯誤'
        }), 500

@api_security_admin_bp.route('/usage-stats')
@login_required
def get_usage_stats():
    """獲取 API 使用統計"""
    try:
        user_id = session.get('user_id')
        
        # 獲取用戶的所有密鑰
        api_keys = session.get('api_keys', {})
        user_keys = [
            key_id for key_id, key_info in api_keys.items() 
            if key_info['user_id'] == user_id
        ]
        
        # 收集統計數據
        stats_data = []
        total_requests = 0
        
        for key_id in user_keys:
            identifier = f"api_key:{key_id}"
            stats = api_rate_limit.get_usage_stats(identifier)
            stats_data.append(stats)
            total_requests += stats['global_requests_last_hour']
        
        return jsonify({
            'success': True,
            'data': {
                'total_api_keys': len(user_keys),
                'total_requests_last_hour': total_requests,
                'key_stats': stats_data
            }
        })
        
    except Exception as e:
        logger.error(f"獲取使用統計錯誤: {str(e)}")
        return jsonify({
            'success': False,
            'error': '獲取統計數據時發生錯誤'
        }), 500

@api_security_admin_bp.route('/security-logs')
@login_required
def security_logs():
    """查看 API 安全日誌"""
    try:
        user_id = session.get('user_id')
        
        # 獲取與該用戶相關的安全事件
        logs = []
        try:
            # 這裡應該從安全監控系統獲取日誌
            # 目前簡化處理，返回模擬數據
            from datetime import datetime, timedelta
            
            logs = [
                {
                    'timestamp': (datetime.now() - timedelta(hours=1)).isoformat(),
                    'event_type': 'API_KEY_USED',
                    'details': {'endpoint': '/api/bank_heads', 'status': 'success'},
                    'ip_address': '127.0.0.1'
                },
                {
                    'timestamp': (datetime.now() - timedelta(hours=2)).isoformat(),
                    'event_type': 'RATE_LIMIT_EXCEEDED',
                    'details': {'endpoint': '/api/payment_identities/create'},
                    'ip_address': '127.0.0.1'
                }
            ]
            
        except Exception:
            pass  # 如果無法獲取日誌，返回空列表
        
        return render_template('api_security/security_logs.html', logs=logs)
        
    except Exception as e:
        logger.error(f"獲取安全日誌錯誤: {str(e)}")
        return redirect(url_for('api_security_admin.dashboard'))

@api_security_admin_bp.route('/test-api')
@login_required  
def test_api():
    """API 測試頁面"""
    return render_template('api_security/test_api.html')