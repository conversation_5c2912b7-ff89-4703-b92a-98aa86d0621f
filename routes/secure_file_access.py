"""
安全檔案存取路由
提供經過授權驗證的檔案存取服務
"""
from flask import Blueprint, send_file, abort, session, current_app
from database import get_db
from model import Transaction, JournalEntry
from utils.security.auth_decorators import login_required
from utils.security.security_monitor import security_monitor
import os
import logging

logger = logging.getLogger(__name__)

secure_file_bp = Blueprint('secure_file', __name__, url_prefix='/secure-files')

@secure_file_bp.route('/download/<path:file_path>')
@login_required
def download_file(file_path):
    """
    安全的檔案下載
    只允許已登入用戶下載他們有權限存取的檔案
    """
    try:
        user_id = session.get('user_id')
        username = session.get('username', 'unknown')
        
        # 驗證檔案路徑安全性
        if not _is_safe_path(file_path):
            logger.warning(f'不安全的檔案路徑存取嘗試: {file_path} by {username}')
            abort(403)
        
        # 構建完整的檔案路徑
        secure_upload_dir = current_app.config.get('SECURE_UPLOAD_FOLDER', 'secure_uploads')
        full_file_path = os.path.join(secure_upload_dir, file_path)
        
        # 確保檔案存在
        if not os.path.exists(full_file_path):
            logger.info(f'檔案不存在: {full_file_path}')
            abort(404)
        
        # 檢查用戶是否有權限存取此檔案
        if not _user_can_access_file(user_id, file_path):
            logger.warning(f'未授權的檔案存取嘗試: {file_path} by {username}')
            security_monitor.log_sensitive_operation(
                'UNAUTHORIZED_FILE_ACCESS',
                username,
                {
                    'file_path': file_path,
                    'user_id': user_id
                }
            )
            abort(403)
        
        # 記錄檔案存取
        security_monitor.log_sensitive_operation(
            'FILE_DOWNLOAD',
            username,
            {
                'file_path': file_path,
                'file_size': os.path.getsize(full_file_path)
            }
        )
        
        logger.info(f'檔案下載: {file_path} by {username}')
        
        # 安全地提供檔案下載
        return send_file(
            full_file_path,
            as_attachment=True,
            download_name=os.path.basename(file_path)
        )
        
    except Exception as e:
        logger.error(f'檔案下載錯誤: {str(e)}')
        abort(500)

@secure_file_bp.route('/view/<path:file_path>')
@login_required
def view_file(file_path):
    """
    安全的檔案瀏覽（在瀏覽器中顯示）
    只允許安全的檔案類型在瀏覽器中顯示
    """
    try:
        user_id = session.get('user_id')
        username = session.get('username', 'unknown')
        
        # 驗證檔案路徑安全性
        if not _is_safe_path(file_path):
            abort(403)
        
        # 構建完整的檔案路徑
        secure_upload_dir = current_app.config.get('SECURE_UPLOAD_FOLDER', 'secure_uploads')
        full_file_path = os.path.join(secure_upload_dir, file_path)
        
        # 確保檔案存在
        if not os.path.exists(full_file_path):
            abort(404)
        
        # 檢查用戶權限
        if not _user_can_access_file(user_id, file_path):
            abort(403)
        
        # 只允許安全的檔案類型在瀏覽器中顯示
        safe_view_types = {'.pdf', '.jpg', '.jpeg', '.png', '.gif', '.txt'}
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext not in safe_view_types:
            # 不安全的檔案類型，強制下載
            return download_file(file_path)
        
        # 記錄檔案檢視
        security_monitor.log_sensitive_operation(
            'FILE_VIEW',
            username,
            {
                'file_path': file_path,
                'file_size': os.path.getsize(full_file_path)
            }
        )
        
        logger.info(f'檔案檢視: {file_path} by {username}')
        
        # 在瀏覽器中顯示檔案
        return send_file(full_file_path)
        
    except Exception as e:
        logger.error(f'檔案檢視錯誤: {str(e)}')
        abort(500)

def _is_safe_path(file_path):
    """檢查檔案路徑是否安全"""
    # 防止路徑遍歷攻擊
    if '..' in file_path or file_path.startswith('/'):
        return False
    
    # 檢查危險字符
    dangerous_chars = ['<', '>', '|', ':', '*', '?', '"', '\0']
    for char in dangerous_chars:
        if char in file_path:
            return False
    
    return True

def _user_can_access_file(user_id, file_path):
    """
    檢查用戶是否有權限存取特定檔案
    目前簡單實現：所有登入用戶都可以存取
    未來可以根據業務需求加強權限控制
    """
    try:
        with get_db() as db:
            # 檢查檔案是否屬於用戶上傳的記錄
            # 這裡可以根據實際業務邏輯調整
            
            # 方法1: 檢查交易記錄
            transaction = db.query(Transaction).filter(
                Transaction.image_path == file_path
            ).first()
            
            if transaction:
                return True  # 找到相關記錄
            
            # 方法2: 檢查日記帳記錄
            journal_entry = db.query(JournalEntry).filter(
                JournalEntry.image_path == file_path
            ).first()
            
            if journal_entry:
                return True  # 找到相關記錄
            
            # 如果都沒找到，可能是孤立檔案，暫時允許存取
            # 在生產環境中，建議更嚴格的權限控制
            return True
            
    except Exception as e:
        logger.error(f'檢查檔案權限時發生錯誤: {str(e)}')
        return False  # 發生錯誤時拒絕存取

@secure_file_bp.route('/upload-test')
@login_required
def upload_test_page():
    """檔案上傳測試頁面"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>安全檔案上傳測試</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    </head>
    <body>
        <div class="container mt-5">
            <h1 class="title">安全檔案上傳測試</h1>
            <div class="card">
                <div class="card-content">
                    <form method="post" enctype="multipart/form-data" action="/test-upload">
                        <div class="field">
                            <label class="label">選擇檔案</label>
                            <div class="control">
                                <input class="input" type="file" name="test_file">
                            </div>
                        </div>
                        <div class="field">
                            <div class="control">
                                <button class="button is-primary" type="submit">上傳測試</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="mt-4">
                <h2 class="subtitle">安全檔案上傳功能：</h2>
                <ul>
                    <li>✅ 檔案類型白名單驗證</li>
                    <li>✅ 檔案內容真實性檢查</li>
                    <li>✅ 檔案大小限制</li>
                    <li>✅ 惡意內容掃描</li>
                    <li>✅ 安全檔案命名</li>
                    <li>✅ 隔離存儲</li>
                    <li>✅ 權限控制存取</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    '''

@secure_file_bp.route('/test-upload', methods=['POST'])
@login_required
def test_upload():
    """測試檔案上傳功能"""
    from flask import request, flash, redirect, url_for
    from utils.security.file_upload_security import secure_file_upload
    
    file = request.files.get('test_file')
    if not file:
        flash('請選擇檔案', 'error')
        return redirect(url_for('secure_file.upload_test_page'))
    
    user_id = session.get('user_id')
    result = secure_file_upload(file, user_id)
    
    if result['is_valid'] and result.get('success'):
        flash(f'檔案上傳成功！安全檔名: {result["safe_filename"]}', 'success')
        flash(f'檔案大小: {result["file_info"]["size"]} bytes', 'info')
        flash(f'檔案類型: {result["file_info"]["mime_type"]}', 'info')
        
        for warning in result.get('warnings', []):
            flash(f'警告: {warning}', 'warning')
    else:
        for error in result.get('errors', []):
            flash(f'錯誤: {error}', 'error')
    
    return redirect(url_for('secure_file.upload_test_page'))