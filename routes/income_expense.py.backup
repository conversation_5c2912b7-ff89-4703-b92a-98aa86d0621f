from flask import Blueprint, render_template, request, redirect, url_for, flash
from data.menu_data import menu
from model import Account, PaymentIdentity, Money, AccountSubject, Transaction
from model import PaymentIdentityType
from utils.audit_helper import AuditHelper, get_current_user
from services.money_service import MoneyService
from services.journal_validator import JournalValidator, JournalValidationError
from datetime import datetime
import os

# 導入基礎路由功能
from routes.base_route import BaseRoute, with_form_data, handle_errors, audit_trail, RouteHelper
from database import get_db, Session

income_expense_bp = Blueprint('income_expense', __name__)

@income_expense_bp.route('/income_record', methods=['GET', 'POST'])
@with_form_data
@handle_errors()
@audit_trail('income_record')
def income_record():
    """收入紀錄頁面（優化版）"""
    from flask import g
    
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    
    # 表單數據已經通過 @with_form_data 注入到 g.form_data
    form_data = g.form_data
    accounts = form_data['accounts']
    departments = form_data['departments']
    projects = form_data['projects']
    identities_by_type = form_data['identities_by_type']
    company_id = form_data['company_id']

    def parse_date(val):
        if val:
            try:
                return datetime.strptime(val, '%Y-%m-%d')
            except Exception:
                return None
        return None

    # 檢查是否為編輯模式
    edit_id = request.args.get('edit_id')
    edit_record = None
    if edit_id:
        try:
            with get_db() as db:
                edit_record = db.query(Money).filter_by(id=edit_id, money_type='收入', entry_side='CREDIT').first()
                if not edit_record:
                    flash('找不到該收入記錄', 'error')
                    return redirect('/income_list')
        except Exception as e:
            flash(f'載入記錄失敗：{str(e)}', 'error')
            return redirect('/income_list')

    if request.method == 'POST':
        print('收到POST', request.form)
        form = request.form
        
        # 檢查是否為編輯模式
        edit_id = form.get('edit_id')
        if edit_id:
            # 編輯模式
            try:
                edit_record = db.query(Money).filter_by(id=edit_id, money_type='收入', entry_side='CREDIT').first()
                if not edit_record:
                    flash('找不到該收入記錄', 'error')
                    return redirect('/income_list')
                
                # 更新記錄
                edit_record.a_time = parse_date(form.get('a_time'))
                edit_record.name = form.get('name')
                edit_record.total = int(form.get('total') or 0)
                edit_record.extra_fee = int(form.get('extra_fee') or 0)
                edit_record.subject_code = form.get('subject_code')
                edit_record.account_id = form.get('account_id')
                edit_record.payment_identity_id = form.get('payment_identity_id')
                edit_record.note = form.get('note')
                edit_record.department_id = form.get('department_id')
                edit_record.project_id = form.get('project_id')
                edit_record.tags = form.get('tag')
                
                db.commit()
                flash('收入記錄更新成功', 'success')
                return redirect('/income_list')
                
            except Exception as e:
                db.rollback()
                flash(f'更新失敗：{str(e)}', 'error')
                return render_template('income_record.html',
                                    sidebar_items=main_menu,
                                    selected=selected,
                                    accounts=accounts,
                                    departments=departments,
                                    projects=projects,
                                    identities_by_type=identities_by_type,
                                    company_id=company_id,
                                    edit_record=edit_record,
                                    edit_mode=True)
        
        # 新增模式
        invoice_number = form.get('invoice_number')
        paper_status = form.get('paper_status')  # 憑證狀態 radio
        is_paper = form.get('is_paper')         # 收據類憑證 checkbox
        # 只有「有憑證」且「收據類憑證」沒打勾時才檢查
        if paper_status == 'has' and not is_paper and invoice_number:
            exists = db.query(Money).filter(Money.number == invoice_number).first()
            if exists:
                db.close()
                return render_template(
                    'income_record.html',
                    sidebar_items=main_menu,
                    selected=selected,
                    accounts=accounts,
                    departments=departments,
                    projects=projects,
                    identities_by_type=identities_by_type,
                    company_id=company_id,
                    error='發票號碼已存在，請重新輸入'
                )
        # 生成分錄參考號
        import uuid
        journal_ref = f"JE-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8]}"

        # 計算金額
        revenue_amount = int(form.get('total') or 0)  # 用戶輸入的是未稅金額
        tax_amount = int(form.get('tax') or 0)        # 稅額
        total_amount = revenue_amount + tax_amount    # 含稅總額 = 未稅金額 + 稅額

        # 創建貸方記錄（收入科目）- 記錄未稅金額
        credit_entry = Money(
            a_time = parse_date(form.get('a_time')),
            name = form.get('name'),
            total = revenue_amount,  # 未稅金額
            tax = 0,  # 收入科目不記錄稅額
            extra_fee = int(form.get('extra_fee') or 0),
            subject_code = form.get('subject_code'),
            account_id = form.get('account_id'),
            payment_identity_id = form.get('payment_identity_id'),
            money_type = '收入',
            is_paper = bool(form.get('is_paper')),
            number = invoice_number,
            tax_type = form.get('tax_type'),
            buyer_tax_id = form.get('buyer_tax_id'),
            seller_tax_id = form.get('seller_tax_id'),
            date = form.get('invoice_date'),
            is_paid = form.get('is_paid') == '1',
            should_paid_date = parse_date(form.get('should_paid_date')),
            paid_date = parse_date(form.get('paid_date')),
            note = form.get('note'),
            department_id = form.get('department_id'),
            project_id = form.get('project_id'),
            tags = form.get('tag'),
            image_path = None,
            entry_side = 'CREDIT',  # 收入是貸方
            journal_reference = journal_ref
        )

        # 設定審計欄位
        AuditHelper.set_create_audit(credit_entry, get_current_user())
        db.add(credit_entry)

        # 創建銷項稅額分錄（如果有稅額）
        if tax_amount > 0:
            output_tax_entry = Money(
                a_time = parse_date(form.get('a_time')),
                name = f'{form.get("name")} - 銷項稅額',
                total = tax_amount,  # 稅額
                tax = 0,
                extra_fee = 0,
                subject_code = '2290',  # 銷項稅額科目
                account_id = form.get('account_id'),
                payment_identity_id = form.get('payment_identity_id'),
                money_type = '收入',
                is_paper = bool(form.get('is_paper')),
                number = invoice_number,
                tax_type = form.get('tax_type'),
                buyer_tax_id = form.get('buyer_tax_id'),
                seller_tax_id = form.get('seller_tax_id'),
                date = form.get('invoice_date'),
                is_paid = form.get('is_paid') == '1',
                should_paid_date = parse_date(form.get('should_paid_date')),
                paid_date = parse_date(form.get('paid_date')),
                note = f'{form.get("note")} - 銷項稅額',
                department_id = form.get('department_id'),
                project_id = form.get('project_id'),
                tags = form.get('tag'),
                image_path = None,
                entry_side = 'CREDIT',  # 銷項稅額是貸方
                journal_reference = journal_ref
            )
            AuditHelper.set_create_audit(output_tax_entry, get_current_user())
            db.add(output_tax_entry)

        # 創建對應的借方記錄（銀行存款增加）
        account_id = form.get('account_id')
        if account_id:
            account = db.query(Account).filter_by(id=account_id).first()
            if account and account.subject_code:
                # 獲取帳戶的完整會計科目代碼
                if account.category == '銀行帳戶' and account.subject_code:
                    full_subject_code = f'1110{str(account.subject_code).zfill(3)}'
                elif account.category == '現金' and account.subject_code:
                    full_subject_code = f'1105{str(account.subject_code).zfill(3)}'
                else:
                    full_subject_code = account.subject_code

                debit_entry = Money(
                    a_time = parse_date(form.get('a_time')),
                    name = f'{form.get("name")} - 收款',
                    total = total_amount,  # 含稅總額
                    tax = 0,
                    extra_fee = 0,
                    subject_code = full_subject_code,
                    account_id = account_id,
                    payment_identity_id = form.get('payment_identity_id'),
                    money_type = '收入',
                    is_paper = bool(form.get('is_paper')),
                    number = invoice_number,
                    tax_type = form.get('tax_type'),
                    buyer_tax_id = form.get('buyer_tax_id'),
                    seller_tax_id = form.get('seller_tax_id'),
                    date = form.get('invoice_date'),
                    is_paid = form.get('is_paid') == '1',
                    should_paid_date = parse_date(form.get('should_paid_date')),
                    paid_date = parse_date(form.get('paid_date')),
                    note = f'{form.get("note")} - 自動產生的借方分錄',
                    department_id = form.get('department_id'),
                    project_id = form.get('project_id'),
                    tags = form.get('tag'),
                    image_path = None,
                    entry_side = 'DEBIT',  # 銀行存款增加是借方
                    journal_reference = journal_ref
                )

                # 設定審計欄位
                AuditHelper.set_create_audit(debit_entry, get_current_user())
                db.add(debit_entry)

        # 驗證複式記帳完整性
        try:
            validator = JournalValidator(db)
            entries_to_validate = [credit_entry]
            if 'debit_entry' in locals():
                entries_to_validate.append(debit_entry)
            if 'output_tax_entry' in locals():
                entries_to_validate.append(output_tax_entry)

            validator.validate_before_commit(entries_to_validate)
            db.commit()

            # 提交後再次驗證
            validation_result = validator.validate_journal_entries(journal_ref)
            if not validation_result['is_balanced']:
                print(f"警告：分錄 {journal_ref} 借貸不平衡")
                print(f"驗證結果：{validation_result}")

        except JournalValidationError as e:
            db.rollback()
            return render_template('income_record.html',
                                sidebar_items=menu,
                                selected='收入記錄',
                                accounts=db.query(Account).all(),
                                departments=[],
                                projects=[],
                                identities_by_type={},
                                company_id=None,
                                error=f'分錄驗證失敗：{str(e)}'
                            )
        # 檔案上傳處理
        file = request.files.get('other_file')
        print('收到檔案:', file, file.filename if file else None)
        if file and file.filename:
            upload_dir = os.path.join(os.path.dirname(__file__), '../static/uploads')
            os.makedirs(upload_dir, exist_ok=True)
            today_str = datetime.now().strftime('%Y%m%d')
            ext = os.path.splitext(file.filename)[1]
            filename = f"{today_str}_{credit_entry.id}{ext}"
            save_path = os.path.join(upload_dir, filename)
            file.save(save_path)
            # 更新主要記錄的 image_path
            credit_entry.image_path = f"/static/uploads/{filename}"
            print('更新 image_path:', credit_entry.image_path)
            db.commit()
        return redirect(url_for('income_expense.income_record'))

    return render_template('income_record.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        accounts=accounts,
                        departments=departments,
                        projects=projects,
                        identities_by_type=identities_by_type,
                        company_id=company_id,
                        edit_record=edit_record,
                        edit_mode=bool(edit_record))

@income_expense_bp.route('/expense_record', methods=['GET', 'POST'])
def expense_record():
    """支出紀錄頁面"""
    # 使用優化的查詢服務，一次性獲取所有表單資料
    from services.optimized_query_service import OptimizedQueryService

    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'

    # 優化：一次性獲取所有表單資料
    form_data = OptimizedQueryService.get_form_data_optimized()
    accounts = form_data['accounts']
    departments = form_data['departments']
    projects = form_data['projects']
    identities_by_type = form_data['identities_by_type']
    company_id = form_data['company_id']

    def parse_date(val):
        if val:
            try:
                return datetime.strptime(val, '%Y-%m-%d')
            except Exception:
                return None
        return None

    if request.method == 'POST':
        print('收到POST', request.form)
        form = request.form
        invoice_number = form.get('invoice_number')
        paper_status = form.get('paper_status')  # 憑證狀態 radio
        is_paper = form.get('is_paper')         # 收據類憑證 checkbox
        # 只有「有憑證」且「收據類憑證」沒打勾時才檢查
        if paper_status == 'has' and not is_paper and invoice_number:
            exists = db.query(Money).filter(Money.number == invoice_number).first()
            if exists:
                db.close()
                return render_template(
                    'expense_record.html',
                    sidebar_items=main_menu,
                    selected=selected,
                    accounts=accounts,
                    departments=departments,
                    projects=projects,
                    identities_by_type=identities_by_type,
                    company_id=company_id,
                    error='發票號碼已存在，請重新輸入'
                )
        # 生成分錄參考號
        import uuid
        journal_ref = f"JE-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8]}"

        # 計算金額
        expense_amount = int(form.get('total') or 0)  # 用戶輸入的是未稅金額
        tax_amount = int(form.get('tax') or 0)        # 稅額
        total_amount = expense_amount + tax_amount    # 含稅總額 = 未稅金額 + 稅額

        # 創建借方記錄（費用/成本科目）- 記錄未稅金額
        debit_entry = Money(
            a_time = parse_date(form.get('a_time')),
            name = form.get('name'),
            total = expense_amount,  # 未稅金額
            tax = 0,  # 費用科目不記錄稅額
            extra_fee = int(form.get('extra_fee') or 0),
            subject_code = form.get('subject_code'),
            account_id = form.get('account_id'),
            payment_identity_id = form.get('payment_identity_id'),
            money_type = '支出',
            is_paper = bool(form.get('is_paper')),
            number = invoice_number,
            tax_type = form.get('tax_type'),
            buyer_tax_id = form.get('buyer_tax_id'),
            seller_tax_id = form.get('seller_tax_id'),
            date = form.get('invoice_date'),
            is_paid = form.get('is_paid') == '1',
            should_paid_date = parse_date(form.get('should_paid_date')),
            paid_date = parse_date(form.get('paid_date')),
            note = form.get('note'),
            department_id = form.get('department_id'),
            project_id = form.get('project_id'),
            tags = form.get('tag'),
            image_path = None,
            entry_side = 'DEBIT',  # 費用/成本是借方
            journal_reference = journal_ref
        )

        # 設定審計欄位
        AuditHelper.set_create_audit(debit_entry, get_current_user())
        db.add(debit_entry)

        # 創建進項稅額分錄（如果有稅額）
        if tax_amount > 0:
            input_tax_entry = Money(
                a_time = parse_date(form.get('a_time')),
                name = f'{form.get("name")} - 進項稅額',
                total = tax_amount,  # 稅額
                tax = 0,
                extra_fee = 0,
                subject_code = '1290',  # 進項稅額科目
                account_id = form.get('account_id'),
                payment_identity_id = form.get('payment_identity_id'),
                money_type = '支出',
                is_paper = bool(form.get('is_paper')),
                number = invoice_number,
                tax_type = form.get('tax_type'),
                buyer_tax_id = form.get('buyer_tax_id'),
                seller_tax_id = form.get('seller_tax_id'),
                date = form.get('invoice_date'),
                is_paid = form.get('is_paid') == '1',
                should_paid_date = parse_date(form.get('should_paid_date')),
                paid_date = parse_date(form.get('paid_date')),
                note = f'{form.get("note")} - 進項稅額',
                department_id = form.get('department_id'),
                project_id = form.get('project_id'),
                tags = form.get('tag'),
                image_path = None,
                entry_side = 'DEBIT',  # 進項稅額是借方
                journal_reference = journal_ref
            )
            AuditHelper.set_create_audit(input_tax_entry, get_current_user())
            db.add(input_tax_entry)

        # 創建對應的貸方記錄（銀行存款減少）
        account_id = form.get('account_id')
        if account_id:
            account = db.query(Account).filter_by(id=account_id).first()
            if account and account.subject_code:
                # 獲取帳戶的完整會計科目代碼
                if account.category == '銀行帳戶' and account.subject_code:
                    full_subject_code = f'1110{str(account.subject_code).zfill(3)}'
                elif account.category == '現金' and account.subject_code:
                    full_subject_code = f'1105{str(account.subject_code).zfill(3)}'
                else:
                    full_subject_code = account.subject_code

                credit_entry = Money(
                    a_time = parse_date(form.get('a_time')),
                    name = f'{form.get("name")} - 付款',
                    total = total_amount,  # 含稅總額
                    tax = 0,
                    extra_fee = 0,
                    subject_code = full_subject_code,
                    account_id = account_id,
                    payment_identity_id = form.get('payment_identity_id'),
                    money_type = '支出',
                    is_paper = bool(form.get('is_paper')),
                    number = invoice_number,
                    tax_type = form.get('tax_type'),
                    buyer_tax_id = form.get('buyer_tax_id'),
                    seller_tax_id = form.get('seller_tax_id'),
                    date = form.get('invoice_date'),
                    is_paid = form.get('is_paid') == '1',
                    should_paid_date = parse_date(form.get('should_paid_date')),
                    paid_date = parse_date(form.get('paid_date')),
                    note = f'{form.get("note")} - 自動產生的貸方分錄',
                    department_id = form.get('department_id'),
                    project_id = form.get('project_id'),
                    tags = form.get('tag'),
                    image_path = None,
                    entry_side = 'CREDIT',  # 銀行存款減少是貸方
                    journal_reference = journal_ref
                )

                # 設定審計欄位
                AuditHelper.set_create_audit(credit_entry, get_current_user())
                db.add(credit_entry)

        # 驗證複式記帳完整性
        try:
            validator = JournalValidator(db)
            entries_to_validate = [debit_entry]
            if 'credit_entry' in locals():
                entries_to_validate.append(credit_entry)
            if 'input_tax_entry' in locals():
                entries_to_validate.append(input_tax_entry)

            validator.validate_before_commit(entries_to_validate)
            db.commit()

            # 提交後再次驗證
            validation_result = validator.validate_journal_entries(journal_ref)
            if not validation_result['is_balanced']:
                # 如果驗證失敗，記錄錯誤但不回滾（因為已提交）
                print(f"警告：分錄 {journal_ref} 借貸不平衡")
                print(f"驗證結果：{validation_result}")

        except JournalValidationError as e:
            db.rollback()
            return render_template('expense_record.html',
                                sidebar_items=menu,
                                selected='支出記錄',
                                accounts=db.query(Account).all(),
                                departments=[],
                                projects=[],
                                identities_by_type={},
                                company_id=None,
                                error=f'分錄驗證失敗：{str(e)}'
                            )
        # 檔案上傳處理
        file = request.files.get('other_file')
        print('收到檔案:', file, file.filename if file else None)
        if file and file.filename:
            upload_dir = os.path.join(os.path.dirname(__file__), '../static/uploads')
            os.makedirs(upload_dir, exist_ok=True)
            today_str = datetime.now().strftime('%Y%m%d')
            ext = os.path.splitext(file.filename)[1]
            filename = f"{today_str}_{debit_entry.id}{ext}"
            save_path = os.path.join(upload_dir, filename)
            file.save(save_path)
            # 更新主要記錄的 image_path
            debit_entry.image_path = f"/static/uploads/{filename}"
            db.commit()
        return redirect(url_for('income_expense.expense_record'))

    return render_template('expense_record.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        accounts=accounts,
                        departments=departments,
                        projects=projects,
                        identities_by_type=identities_by_type,
                        company_id=company_id)

@income_expense_bp.route('/income_list')
def income_list():
    """收入列表頁面"""
    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    
    # 取得查詢參數
    date_type = request.args.get('date_type', 'a_time')
    date_start = request.args.get('date_start')
    date_end = request.args.get('date_end')
    hide_paid = request.args.get('hide_paid')

    # 構建查詢條件
    query = db.query(Money).filter(Money.money_type == '收入')
    
    # 只顯示主要交易記錄（貸方分錄，收入科目）
    query = query.filter(Money.entry_side == 'CREDIT')
    
    # 排除稅額分錄（銷項稅額科目）
    query = query.filter(Money.subject_code != '2290')
    
    if date_start and date_end and date_type in ['a_time', 'should_paid_date', 'paid_date', 'invoice_date']:
        if date_type == 'invoice_date':
            query = query.filter(Money.date >= date_start, Money.date <= date_end)
        else:
            query = query.filter(getattr(Money, date_type) >= date_start, getattr(Money, date_type) <= date_end)
    if hide_paid:
        query = query.filter(Money.is_paid == False)
    # 使用 JOIN 避免 N+1 查詢
    income_records = query.outerjoin(AccountSubject, Money.subject_code == AccountSubject.code)\
                          .outerjoin(Account, Money.account_id == Account.id)\
                          .outerjoin(PaymentIdentity, Money.payment_identity_id == PaymentIdentity.id)\
                          .add_columns(
                              AccountSubject.name.label('subject_name'),
                              Account.name.label('account_name'),
                              PaymentIdentity.name.label('payment_identity_name')
                          )\
                          .order_by(Money.a_time.desc()).all()
    
    # 準備顯示資料
    income_data = []
    for record, subject_name, account_name, payment_identity_name in income_records:
        subject_name = subject_name or ''
        account_name = account_name or ''
        payment_identity_name = payment_identity_name or ''
        a_time_str = record.a_time.strftime('%Y-%m-%d') if record.a_time else ''
        should_paid_date_str = record.should_paid_date.strftime('%Y-%m-%d') if record.should_paid_date else ''
        paid_date_str = record.paid_date.strftime('%Y-%m-%d') if record.paid_date else ''
        invoice_date_str = record.date if record.date else ''
        total_str = f"{record.total:,}" if record.total else '0'
        payment_status = '已收付款' if record.is_paid else '未收付款'
        income_data.append({
            'id': record.id,
            'a_time': a_time_str,
            'total': total_str,
            'subject_name': subject_name,
            'name': record.name or '',
            'payment_identity_name': payment_identity_name,
            'account_name': account_name,
            'payment_status': payment_status,
            'should_paid_date': should_paid_date_str,
            'paid_date': paid_date_str,
            'invoice_number': record.number or '',
            'invoice_date': invoice_date_str,
            'note': record.note or ''
        })
    db.close()
    return render_template('income_list.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        income_records=income_data)

@income_expense_bp.route('/expense_list')
def expense_list():
    """支出列表頁面"""
    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    
    # 取得查詢參數
    date_type = request.args.get('date_type', 'a_time')
    date_start = request.args.get('date_start')
    date_end = request.args.get('date_end')
    hide_paid = request.args.get('hide_paid')

    # 構建查詢條件
    query = db.query(Money).filter(Money.money_type == '支出')
    
    # 只顯示主要交易記錄（借方分錄，費用/成本科目）
    query = query.filter(Money.entry_side == 'DEBIT')
    
    # 排除稅額分錄（進項稅額科目）
    query = query.filter(Money.subject_code != '1290')
    
    if date_start and date_end and date_type in ['a_time', 'should_paid_date', 'paid_date', 'invoice_date']:
        if date_type == 'invoice_date':
            query = query.filter(Money.date >= date_start, Money.date <= date_end)
        else:
            query = query.filter(getattr(Money, date_type) >= date_start, getattr(Money, date_type) <= date_end)
    if hide_paid:
        query = query.filter(Money.is_paid == False)
    # 使用 JOIN 避免 N+1 查詢
    expense_records = query.outerjoin(AccountSubject, Money.subject_code == AccountSubject.code)\
                           .outerjoin(Account, Money.account_id == Account.id)\
                           .outerjoin(PaymentIdentity, Money.payment_identity_id == PaymentIdentity.id)\
                           .add_columns(
                               AccountSubject.name.label('subject_name'),
                               Account.name.label('account_name'),
                               PaymentIdentity.name.label('payment_identity_name')
                           )\
                           .order_by(Money.a_time.desc()).all()
    
    # 準備顯示資料
    expense_data = []
    for record, subject_name, account_name, payment_identity_name in expense_records:
        subject_name = subject_name or ''
        account_name = account_name or ''
        payment_identity_name = payment_identity_name or ''
        a_time_str = record.a_time.strftime('%Y-%m-%d') if record.a_time else ''
        should_paid_date_str = record.should_paid_date.strftime('%Y-%m-%d') if record.should_paid_date else ''
        paid_date_str = record.paid_date.strftime('%Y-%m-%d') if record.paid_date else ''
        invoice_date_str = record.date if record.date else ''
        total_str = f"{record.total:,}" if record.total else '0'
        payment_status = '已收付款' if record.is_paid else '未收付款'
        expense_data.append({
            'id': record.id,
            'a_time': a_time_str,
            'total': total_str,
            'subject_name': subject_name,
            'name': record.name or '',
            'payment_identity_name': payment_identity_name,
            'account_name': account_name,
            'payment_status': payment_status,
            'should_paid_date': should_paid_date_str,
            'paid_date': paid_date_str,
            'invoice_number': record.number or '',
            'invoice_date': invoice_date_str,
            'note': record.note or ''
        })
    db.close()
    return render_template('expense_list.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        expense_records=expense_data)

@income_expense_bp.route('/transaction_details')
def transaction_details():
    """交易明細頁面"""
    # 獲取查詢參數
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    payment_daterange = request.args.get('payment_daterange')
    voucher_daterange = request.args.get('voucher_daterange')
    expected_daterange = request.args.get('expected_daterange')
    payment_identity = request.args.get('payment_identity')
    project = request.args.get('project')
    account = request.args.get('account')
    voucher_type = request.args.get('voucher_type')
    attachment = request.args.get('attachment')
    subject_code = request.args.get('subject_code')
    transaction_status = request.args.get('transaction_status')
    real_start_date = request.args.get('real_start_date')
    real_end_date = request.args.get('real_end_date')
    note = request.args.get('note')
    target = request.args.get('target')
    keyword = request.args.get('keyword')
    payment_identity_type = request.args.get('payment_identity_type')
    amount_min = request.args.get('amount_min')
    amount_max = request.args.get('amount_max')
    order_by = request.args.get('order_by', 'a_time_desc')  # 新增排序參數
    # 解析 daterangepicker 的日期區間格式 "YYYY-MM-DD ~ YYYY-MM-DD"
    def parse_daterange(daterange_str):
        if not daterange_str or '~' not in daterange_str:
            return None, None
        try:
            parts = daterange_str.split(' ~ ')
            if len(parts) == 2:
                start = parts[0].strip()
                end = parts[1].strip()
                return start, end
        except:
            pass
        return None, None
    
    # 處理收付日期區間
    if payment_daterange:
        parsed_start, parsed_end = parse_daterange(payment_daterange)
        if parsed_start and parsed_end:
            start_date = parsed_start
            end_date = parsed_end
    
    # 處理憑證日期區間
    if voucher_daterange:
        parsed_start, parsed_end = parse_daterange(voucher_daterange)
        if parsed_start and parsed_end:
            real_start_date = parsed_start
            real_end_date = parsed_end
    
    # 處理預計日期區間
    if expected_daterange:
        parsed_start, parsed_end = parse_daterange(expected_daterange)
        if parsed_start and parsed_end:
            expected_start_date = parsed_start
            expected_end_date = parsed_end
        else:
            expected_start_date = None
            expected_end_date = None
    else:
        expected_start_date = None
        expected_end_date = None
    
    # 獲取菜單數據
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    
    # 執行查詢
    transactions = []
    db = Session()
    try:
        # 構建查詢條件
        query = db.query(Transaction)
        
        # 過濾掉開帳交易（描述以「開帳-」開頭的交易）
        query = query.filter(~Transaction.description.like('開帳-%'))
        
        # 除錯資訊
        print(f"查詢參數: start_date={start_date}, end_date={end_date}, account={account}, subject_code={subject_code}, keyword={keyword}")
        print(f"日期區間參數: payment_daterange={payment_daterange}, voucher_daterange={voucher_daterange}")
        
        # 檢查資料庫中的總記錄數
        total_count = query.count()
        print(f"資料庫中交易記錄總數（排除開帳）: {total_count}")
        
        # 如果有查詢參數，則應用篩選條件
        if any([start_date, end_date, payment_identity, project, account, 
                voucher_type, attachment, subject_code, transaction_status,
                real_start_date, real_end_date, note, target, keyword, payment_identity_type,
                amount_min, amount_max, expected_start_date, expected_end_date]):
            
            print("有篩選條件，開始應用篩選...")
            
            # 應用篩選條件
            if start_date:
                query = query.filter(Transaction.transaction_date >= start_date)
                print(f"應用開始日期篩選: {start_date}")
            if end_date:
                query = query.filter(Transaction.transaction_date <= end_date)
                print(f"應用結束日期篩選: {end_date}")
            if real_start_date:
                query = query.filter(Transaction.invoice_date >= real_start_date)
                print(f"應用憑證開始日期篩選: {real_start_date}")
            if real_end_date:
                query = query.filter(Transaction.invoice_date <= real_end_date)
                print(f"應用憑證結束日期篩選: {real_end_date}")
            if expected_start_date:
                query = query.filter(Transaction.should_paid_date >= expected_start_date)
                print(f"應用預計開始日期篩選: {expected_start_date}")
            if expected_end_date:
                query = query.filter(Transaction.should_paid_date <= expected_end_date)
                print(f"應用預計結束日期篩選: {expected_end_date}")
            if payment_identity:
                query = query.filter(Transaction.payment_identity_id == payment_identity)
                print(f"應用交易對象篩選: {payment_identity}")
            if account:
                query = query.filter(Transaction.account_id == account)
                print(f"應用帳戶篩選: {account}")
            if keyword:
                query = query.filter(Transaction.description.contains(keyword))
                print(f"應用關鍵字篩選: {keyword}")
            if amount_min:
                try:
                    min_amount = int(amount_min)
                    query = query.filter(Transaction.total_amount >= min_amount)
                    print(f"應用最低金額篩選: {min_amount}")
                except ValueError:
                    print(f"最低金額格式錯誤: {amount_min}")
            if amount_max:
                try:
                    max_amount = int(amount_max)
                    query = query.filter(Transaction.total_amount <= max_amount)
                    print(f"應用最高金額篩選: {max_amount}")
                except ValueError:
                    print(f"最高金額格式錯誤: {amount_max}")
            if attachment:
                if attachment == 'has':
                    query = query.filter(Transaction.image_path.isnot(None), Transaction.image_path != '')
                    print("應用附件篩選: 有附件")
                elif attachment == 'none':
                    query = query.filter((Transaction.image_path.is_(None)) | (Transaction.image_path == ''))
                    print("應用附件篩選: 無附件")
            if transaction_status:
                if transaction_status == 'completed':
                    query = query.filter(Transaction.is_paid == True)
                    print("應用交易狀況篩選: 已完成")
                elif transaction_status == 'pending':
                    query = query.filter(Transaction.is_paid == False)
                    print("應用交易狀況篩選: 未完成")
            # 新增：根據交易對象類型查詢
            if payment_identity_type:
                identity_ids = [i.id for i in db.query(PaymentIdentity).filter(PaymentIdentity.type_id == payment_identity_type).all()]
                if identity_ids:
                    query = query.filter(Transaction.payment_identity_id.in_(identity_ids))
                else:
                    query = query.filter(Transaction.payment_identity_id == -1)  # 不會有資料
                print(f"應用交易對象類型篩選: {payment_identity_type}, identity_ids: {identity_ids}")
            
            filtered_count = query.count()
            print(f"篩選後的記錄數: {filtered_count}")
            
            # 決定排序方式
            from sqlalchemy import asc, desc
            order_mapping = {
                'a_time_desc': desc(Transaction.transaction_date),
                'a_time_asc': asc(Transaction.transaction_date),
                'voucher_date_desc': desc(Transaction.invoice_date),
                'voucher_date_asc': asc(Transaction.invoice_date),
                'invoice_number_desc': desc(Transaction.invoice_number),
                'invoice_number_asc': asc(Transaction.invoice_number),
                'created_at_desc': desc(Transaction.created_at),
                'created_at_asc': asc(Transaction.created_at),
                'amount_desc': desc(Transaction.total_amount),
                'amount_asc': asc(Transaction.total_amount),
                'prepay_date_asc': asc(Transaction.should_paid_date),
                'prepay_date_desc': desc(Transaction.should_paid_date),
            }
            order_clause = order_mapping.get(order_by, desc(Transaction.transaction_date))
            transactions = query.order_by(order_clause).all()
        else:
            print("沒有篩選條件，顯示最近的 50 筆記錄")
            from sqlalchemy import desc
            order_mapping = {
                'a_time_desc': desc(Transaction.transaction_date),
                'a_time_asc': Transaction.transaction_date.asc(),
                'voucher_date_desc': desc(Transaction.invoice_date),
                'voucher_date_asc': Transaction.invoice_date.asc(),
                'invoice_number_desc': desc(Transaction.invoice_number),
                'invoice_number_asc': Transaction.invoice_number.asc(),
                'created_at_desc': desc(Transaction.created_at),
                'created_at_asc': Transaction.created_at.asc(),
                'amount_desc': desc(Transaction.total_amount),
                'amount_asc': Transaction.total_amount.asc(),
                'prepay_date_asc': Transaction.should_paid_date.asc(),
                'prepay_date_desc': desc(Transaction.should_paid_date),
            }
            order_clause = order_mapping.get(order_by, desc(Transaction.transaction_date))
            transactions = query.order_by(order_clause).limit(50).all()
        
        print(f"最終查詢結果數量: {len(transactions)}")
        
    finally:
        db.close()
    
    # 計算統計資料
    total_count = len(transactions)
    income_amount = sum(t.total_amount for t in transactions if t.transaction_type == 'income' and t.total_amount)
    expense_amount = sum(t.total_amount for t in transactions if t.transaction_type == 'expense' and t.total_amount)
    
    # 獲取下拉選單選項
    db = Session()
    try:
        accounts = db.query(Account).filter(Account.is_deleted == False).all()
        payment_identities = db.query(PaymentIdentity).all()
        subjects = db.query(AccountSubject).all()
        payment_identity_types = db.query(PaymentIdentityType).all()
    finally:
        db.close()
    
    return render_template('transaction_details.html',
                        sidebar_items=main_menu,
                        selected=selected,
                        transactions=transactions,
                        accounts=accounts,
                        payment_identities=payment_identities,
                        subjects=subjects,
                        payment_identity_types=payment_identity_types,
                        total_count=total_count,
                        income_amount=income_amount,
                        expense_amount=expense_amount)

@income_expense_bp.route('/ac_delay_list')
def ac_delay_list():
    """應收應付逾期列表"""
    db = Session()
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    from datetime import date
    today = date.today()
    # 使用優化的查詢方法取得逾期記錄
    overdue_records = MoneyService.get_overdue_payments()
    
    # 取得即將到期的記錄（7天內）
    # upcoming_records = MoneyService.get_upcoming_payments(days=7)  # 暫時註解，未使用
    
    # 準備顯示資料 - 已在 MoneyService.get_overdue_payments() 中使用 JOIN 優化
    overdue_data = []
    for record in overdue_records:
        # 由於已經使用 JOIN 查詢，這些關聯屬性已經預載入，不會觸發額外查詢
        subject_name = record.subject.name if record.subject else ''
        account_name = record.account.name if record.account else ''
        payment_identity_name = record.payment_identity.name if record.payment_identity else ''
        a_time_str = record.a_time.strftime('%Y-%m-%d') if record.a_time else ''
        should_paid_date_str = record.should_paid_date.strftime('%Y-%m-%d') if record.should_paid_date else ''
        paid_date_str = record.paid_date.strftime('%Y-%m-%d') if record.paid_date else ''
        invoice_date_str = record.date if record.date else ''
        total_str = f"{record.total:,}" if record.total else '0'
        payment_status = '已收付款' if record.is_paid else '未收付款'
        
        # 計算逾期天數
        overdue_days = (today - record.should_paid_date.date()).days if record.should_paid_date else 0
        
        overdue_data.append({
            'id': record.id,
            'a_time': a_time_str,
            'total': total_str,
            'subject_name': subject_name,
            'name': record.name or '',
            'payment_identity_name': payment_identity_name,
            'account_name': account_name,
            'payment_status': payment_status,
            'should_paid_date': should_paid_date_str,
            'paid_date': paid_date_str,
            'invoice_date': invoice_date_str,
            'note': record.note or '',
            'money_type': record.money_type,
            'overdue_days': overdue_days
        })
    
    db.close()
    return render_template('ac_delay_list.html', 
                        sidebar_items=main_menu, 
                        selected=selected,
                        overdue_records=overdue_data) 

@income_expense_bp.route('/journal_entries/<int:money_id>')
def journal_entries(money_id):
    """顯示某一交易的所有分錄（只讀）"""
    db = Session()
    try:
        # 先找到這筆交易
        main_entry = db.query(Money).filter(Money.id == money_id).first()
        if not main_entry:
            return '找不到該交易', 404
        # 以 journal_reference 查詢所有同分錄的明細
        journal_ref = main_entry.journal_reference
        entries = db.query(Money).filter(Money.journal_reference == journal_ref).order_by(Money.entry_side.desc()).all()
        # 取得科目名稱
        subjects = {s.code: s.name for s in db.query(AccountSubject).all()}
    finally:
        db.close()
    return render_template('journal_entries.html', entries=entries, main_entry=main_entry, subjects=subjects) 