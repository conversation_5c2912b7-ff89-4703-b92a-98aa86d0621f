"""
管理員相關路由
"""
from flask import Blueprint, request, jsonify
import os

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/verify_password', methods=['POST'])
def verify_password():
    """驗證管理員密碼"""
    try:
        data = request.get_json()
        input_password = data.get('password', '')
        
        # 從環境變數獲取管理員密碼，如果沒有設置則使用預設值（僅開發環境）
        admin_password = os.environ.get('ADMIN_PASSWORD')
        if not admin_password:
            from flask import current_app
            from utils.web.debug_helpers import is_debug_mode, get_development_config
            
            if is_debug_mode():
                # 開發環境預設密碼（應該在生產環境設置環境變數）
                dev_config = get_development_config()
                admin_password = dev_config.get('admin_password', '0980347570')
                current_app.logger.warning('未設置 ADMIN_PASSWORD 環境變數，使用預設密碼（僅開發環境）')
            else:
                current_app.logger.error('生產環境未設置 ADMIN_PASSWORD 環境變數')
                return jsonify({'success': False, 'message': '系統配置錯誤'})
        
        # 簡單的密碼驗證（生產環境建議使用更安全的方式）
        if input_password == admin_password:
            return jsonify({'success': True})
        else:
            from flask import current_app
            current_app.logger.warning(f'管理員密碼驗證失敗，來源IP: {request.remote_addr}')
            return jsonify({'success': False, 'message': '密碼錯誤'})
            
    except Exception as e:
        from flask import current_app
        current_app.logger.error(f'管理員密碼驗證異常: {str(e)}')
        return jsonify({'success': False, 'message': '系統錯誤'})