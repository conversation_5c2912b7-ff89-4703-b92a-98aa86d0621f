"""
資金轉移相關路由
"""
from flask import Blueprint, render_template, request, redirect, flash
from data.menu_data import menu
from model import Account, Transfer
from database import db_session, get_db
from datetime import datetime
from utils.logging.error_handler import handle_database_error, handle_validation_error, ErrorHandler

transfer_bp = Blueprint('transfer', __name__, url_prefix='/transfer')

@transfer_bp.route('/add')
def add():
    """資金移轉紀錄新增表單"""
    main_menu = list(menu.keys())
    selected = '資金管理'
    
    # 獲取帳戶資料，並在 Session 開啟時轉換為字典格式
    accounts_data = []
    with get_db() as db:
        accounts = db.query(Account).all()
        # 在 Session 還開啟時，將資料轉換為字典格式
        for account in accounts:
            accounts_data.append({
                'id': account.id,
                'name': account.name,
                'subject_code': account.subject_code
            })
    
    return render_template('index.html',
                         sidebar_items=main_menu,
                         selected=selected,
                         transfer_form=True,
                         accounts=accounts_data)

@transfer_bp.route('/list')
def list_transfers():
    """資金移轉紀錄列表頁"""
    main_menu = list(menu.keys())
    selected = '資金管理'

    # 處理日期篩選參數
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # 使用 get_db 上下文管理器，確保在 Session 關閉前處理所有數據
    processed_records = []
    with get_db() as db:
        # 從資料庫查詢 Transfer 資料表
        query = db.query(Transfer).join(
            Account, Transfer.out_account_id == Account.id, isouter=True
        ).add_entity(Account)

        # 處理日期篩選
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(Transfer.transfer_date >= start_date_obj)
            except ValueError:
                pass

        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(Transfer.transfer_date <= end_date_obj)
            except ValueError:
                pass

        # 按日期降序排列
        transfer_records = query.order_by(Transfer.transfer_date.desc()).all()

        # 處理查詢結果，在 Session 還開啟時將數據轉換為字典格式
        for transfer, out_account in transfer_records:
            # 查詢轉入帳戶
            in_account = db.query(Account).filter_by(id=transfer.in_account_id).first()
            
            # 創建一個包含完整信息的字典
            transfer_dict = {
                'id': transfer.id,
                'out_account_id': transfer.out_account_id,
                'in_account_id': transfer.in_account_id,
                'subject_code': transfer.subject_code,
                'amount': transfer.amount,
                'fee': transfer.fee,
                'note': transfer.note,
                'transfer_date': transfer.transfer_date.strftime('%Y-%m-%d') if transfer.transfer_date else '',
                'voucher': transfer.voucher,
                'out_account_name': out_account.name if out_account else '未指定',
                'in_account_name': in_account.name if in_account else '未指定'
            }
            processed_records.append(transfer_dict)

    return render_template('transfer_list.html',
                        transfer_records=processed_records,
                        sidebar_items=main_menu,
                        selected=selected)

@transfer_bp.route('/', methods=['GET', 'POST'])
@handle_database_error
@handle_validation_error
def transfer():
    """資金轉移主頁面"""
    main_menu = list(menu.keys())
    selected = '資金管理'
    submenus = menu[selected]
    
    # 獲取帳戶資料，並在 Session 開啟時轉換為字典格式
    accounts_data = []
    transfer_data_dict = None
    
    with get_db() as db:
        accounts = db.query(Account).all()
        # 在 Session 還開啟時，將資料轉換為字典格式
        for account in accounts:
            accounts_data.append({
                'id': account.id,
                'name': account.name,
                'subject_code': account.subject_code
            })
            
        # 檢查是否為編輯模式
        edit_id = request.args.get('edit')
        if edit_id:
            transfer_data = db.query(Transfer).filter_by(id=edit_id).first()
            if transfer_data:
                transfer_data_dict = {
                    'id': transfer_data.id,
                    'out_account_id': transfer_data.out_account_id,
                    'in_account_id': transfer_data.in_account_id,
                    'subject_code': transfer_data.subject_code,
                    'amount': transfer_data.amount,
                    'fee': transfer_data.fee,
                    'note': transfer_data.note,
                    'transfer_date': transfer_data.transfer_date
                }

    if request.method == 'POST':
        out_account_str = request.form.get('out_account')
        in_account_str = request.form.get('in_account')
        amount_str = request.form.get('amount')
        fee_str = request.form.get('fee')

        out_account_id = ErrorHandler.safe_int_convert(out_account_str)
        in_account_id = ErrorHandler.safe_int_convert(in_account_str)
        subject_out = request.form.get('subject_out') or ''
        amount = ErrorHandler.safe_float_convert(amount_str)
        fee = ErrorHandler.safe_float_convert(fee_str)
        note = request.form.get('note') or ''
        transfer_date_str = request.form.get('transfer_date')
        
        transfer_date = None
        if transfer_date_str:
            transfer_date = datetime.strptime(transfer_date_str, '%Y-%m-%d').date()
        
        # 檔案處理略（可加上傳邏輯）
        voucher = None

        if edit_id and transfer_data:
            # 更新現有記錄
            if out_account_id > 0:
                transfer_data.out_account_id = out_account_id
            if in_account_id > 0:
                transfer_data.in_account_id = in_account_id
            transfer_data.subject_code = subject_out
            transfer_data.amount = amount
            transfer_data.fee = fee
            transfer_data.note = note
            transfer_data.transfer_date = transfer_date
            transfer_data.voucher = voucher
        else:
            # 新增記錄
            transfer = Transfer(
                out_account_id=out_account_id if out_account_id > 0 else None,
                in_account_id=in_account_id if in_account_id > 0 else None,
                subject_code=subject_out,  # 這裡存轉出帳號的科目
                amount=amount,
                fee=fee,
                note=note,
                transfer_date=transfer_date,
                voucher=voucher
            )
            db_session.add(transfer)

        db_session.commit()
        flash('轉移記錄操作成功', 'success')
        return redirect('/transfer/list')

    return render_template(
        'index.html',
        sidebar_items=main_menu,
        selected=selected,
        submenus=submenus,
        transfer_form=True,
        accounts=accounts_data,
        transfer_data=transfer_data_dict,
        edit_mode=bool(edit_id)
    )