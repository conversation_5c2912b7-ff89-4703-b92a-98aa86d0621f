from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from flask_login import login_user, logout_user, current_user
from services.auth_service import AuthService
from utils.security.auth_decorators import login_required
from utils.security.security import rate_limit
from utils.security.security_monitor import security_monitor

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['GET', 'POST'])
@rate_limit(max_requests=5, window_minutes=15)  # 15分鐘內最多5次登入嘗試
def login():
    """登入頁面"""
    # 檢查CSRF相關錯誤
    from flask_wtf.csrf import CSRFError
    try:
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')
            
            if not username or not password:
                flash('請輸入用戶名和密碼', 'error')
                return render_template('auth/login.html')
            
            # 驗證用戶
            user = AuthService.authenticate_user(username, password)
            
            # 記錄登入嘗試
            security_monitor.log_login_attempt(
                username=username,
                ip=request.remote_addr,
                success=user is not None,
                user_agent=request.headers.get('User-Agent')
            )
            
            if user:
                # 建立會話
                session_token = AuthService.create_user_session(user.id)
                session['user_id'] = user.id
                session['username'] = user.username
                session['session_token'] = session_token
                session['tenant_id'] = user.tenant_id  # 記錄租戶ID
                
                # 直接設置角色和管理員狀態，不再嘗試查詢角色
                # 對於用戶名為'admin'的用戶或者租戶管理員，直接設置為管理員
                is_admin = (user.username == 'admin' or user.is_tenant_admin)
                
                # 如果是管理員，則設置'admin'角色，否則設置為空列表
                session['user_roles'] = ['admin'] if is_admin else []
                session['is_admin'] = is_admin
                
                print(f"DEBUG: 直接設置管理員狀態為 {is_admin} (根據用戶名或租戶管理員狀態)")
                print(f"DEBUG: 用戶名: {user.username}, 是否為租戶管理員: {user.is_tenant_admin}")
                
                # 如果需要實際角色，可以稍後再從其他地方獲取，但在登入時不必檢查
                
                # 使用 Flask-Login 登入用戶
                login_user(user, remember=True)
                
                flash(f'歡迎回來，{user.full_name or user.username}！', 'success')
                
                # 重新導向到原本要訪問的頁面或首頁
                next_page = request.args.get('next')
                return redirect(next_page) if next_page else redirect(url_for('main.index'))
            else:
                flash('用戶名或密碼錯誤', 'error')
        
    except CSRFError:
        # 處理CSRF錯誤，提供友好的錯誤訊息
        flash('表單已過期或無效，請重新整理頁面後再試。', 'error')
    except Exception as e:
        # 捕獲其他可能的錯誤
        flash(f'登入時發生錯誤，請稍後再試。錯誤訊息：{str(e)}', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """登出"""
    # 記錄登出操作
    user = session.get('username', 'unknown')
    security_monitor.log_sensitive_operation(
        'LOGOUT',
        user,
        {'ip': request.remote_addr}
    )
    
    # 結束數據庫中的會話記錄
    if 'session_token' in session:
        AuthService.end_user_session(session['session_token'])
    
    # 清除會話
    session.clear()
    flash('您已成功登出', 'info')
    return redirect(url_for('auth.login'))

