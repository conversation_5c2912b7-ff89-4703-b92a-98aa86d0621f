#!/usr/bin/env python3
"""
檢查和設置管理員帳戶
"""

from database import get_db
from sqlalchemy import text
from werkzeug.security import generate_password_hash

def check_and_setup_admin():
    """檢查並設置管理員帳戶"""
    
    with get_db() as db:
        print("=== 檢查系統狀態 ===")
        
        # 1. 檢查是否有 admin 角色
        admin_role = db.execute(text("SELECT id, name FROM roles WHERE name = 'admin'")).fetchone()
        if not admin_role:
            print("❌ 沒有找到 admin 角色，正在創建...")
            db.execute(text("""
                INSERT INTO roles (name, display_name, description, is_active, created_at)
                VALUES ('admin', '系統管理員', '擁有所有權限的系統管理員', 1, datetime('now'))
            """))
            db.commit()
            admin_role = db.execute(text("SELECT id, name FROM roles WHERE name = 'admin'")).fetchone()
            print(f"✅ 創建 admin 角色成功，ID: {admin_role[0]}")
        else:
            print(f"✅ admin 角色已存在，ID: {admin_role[0]}")
        
        # 2. 檢查是否有 admin 用戶
        admin_user = db.execute(text("SELECT id, username FROM users WHERE username = 'admin'")).fetchone()
        if not admin_user:
            print("❌ 沒有找到 admin 用戶，正在創建...")
            password_hash = generate_password_hash('admin123')
            result = db.execute(text("""
                INSERT INTO users (username, email, password_hash, full_name, is_active, created_at)
                VALUES ('admin', '<EMAIL>', :password_hash, '系統管理員', 1, datetime('now'))
            """), {"password_hash": password_hash})
            db.commit()
            admin_user = db.execute(text("SELECT id, username FROM users WHERE username = 'admin'")).fetchone()
            print(f"✅ 創建 admin 用戶成功，ID: {admin_user[0]}")
        else:
            print(f"✅ admin 用戶已存在，ID: {admin_user[0]}")
        
        # 3. 檢查用戶角色關聯
        user_role_link = db.execute(text("""
            SELECT ur.user_id, ur.role_id 
            FROM user_roles ur 
            WHERE ur.user_id = :user_id AND ur.role_id = :role_id
        """), {"user_id": admin_user[0], "role_id": admin_role[0]}).fetchone()
        
        if not user_role_link:
            print("❌ admin 用戶沒有 admin 角色，正在分配...")
            db.execute(text("""
                INSERT INTO user_roles (user_id, role_id)
                VALUES (:user_id, :role_id)
            """), {"user_id": admin_user[0], "role_id": admin_role[0]})
            db.commit()
            print("✅ 成功為 admin 用戶分配 admin 角色")
        else:
            print("✅ admin 用戶已有 admin 角色")
        
        # 4. 驗證設置
        print("\n=== 驗證設置 ===")
        result = db.execute(text("""
            SELECT u.username, r.name 
            FROM users u 
            JOIN user_roles ur ON u.id = ur.user_id 
            JOIN roles r ON ur.role_id = r.id 
            WHERE u.username = 'admin'
        """)).fetchall()
        
        if result:
            for username, role_name in result:
                print(f"✅ 用戶 {username} 擁有角色: {role_name}")
        else:
            print("❌ 驗證失敗：admin 用戶沒有任何角色")
        
        print("\n=== 設置完成 ===")
        print("管理員登入資訊：")
        print("用戶名: admin")
        print("密碼: admin123")

if __name__ == '__main__':
    try:
        check_and_setup_admin()
    except Exception as e:
        print(f"❌ 設置過程中發生錯誤: {str(e)}")
        import traceback
        traceback.print_exc()
