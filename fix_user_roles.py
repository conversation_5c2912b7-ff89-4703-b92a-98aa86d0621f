#!/usr/bin/env python3
"""
修正用戶角色分配問題
確保只有真正的管理員才有 admin 角色
"""
import sys
import os

# 添加專案路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from model import User
from models.auth_models import Role, user_roles
from services.auth_service import RoleService
from sqlalchemy import delete

def fix_user_roles():
    """修正用戶角色分配"""
    print("=== 修正用戶角色分配 ===\n")
    
    with get_db() as db:
        # 1. 獲取admin和user角色
        admin_role = db.query(Role).filter(Role.name == 'admin').first()
        user_role = db.query(Role).filter(Role.name == 'user').first()
        
        if not admin_role:
            print("錯誤：找不到 admin 角色")
            return
        
        if not user_role:
            print("錯誤：找不到 user 角色")
            return
        
        print(f"找到角色：admin (ID: {admin_role.id}), user (ID: {user_role.id})")
        
        # 2. 獲取所有用戶
        users = db.query(User).filter(User.is_active == True).all()
        
        for user in users:
            print(f"\n處理用戶: {user.username}")
            print(f"  is_tenant_admin: {user.is_tenant_admin}")
            
            # 檢查用戶現有角色
            current_roles = user.get_roles()
            current_role_names = [role.name for role in current_roles]
            print(f"  目前角色: {current_role_names}")
            
            # 決定應該有的角色
            should_have_admin = user.is_tenant_admin or user.username == 'admin'
            target_role_id = admin_role.id if should_have_admin else user_role.id
            target_role_name = 'admin' if should_have_admin else 'user'
            
            print(f"  應該有角色: {target_role_name}")
            
            # 如果角色不對，則修正
            if (should_have_admin and 'admin' not in current_role_names) or \
               (not should_have_admin and 'user' not in current_role_names):
                
                print(f"  >>> 修正角色分配...")
                
                # 清除現有角色
                RoleService.assign_role_to_user(user.id, [target_role_id])
                
                print(f"  >>> 已分配角色: {target_role_name}")
            else:
                print(f"  >>> 角色正確，無需修改")
        
        print("\n=== 角色修正完成 ===")

def verify_role_assignment():
    """驗證角色分配結果"""
    print("\n=== 驗證角色分配結果 ===\n")
    
    with get_db() as db:
        users = db.query(User).filter(User.is_active == True).all()
        
        for user in users:
            roles = user.get_roles()
            role_names = [role.name for role in roles]
            
            expected_role = 'admin' if (user.is_tenant_admin or user.username == 'admin') else 'user'
            actual_role = role_names[0] if role_names else 'none'
            
            status = "✓" if actual_role == expected_role else "✗"
            
            print(f"{status} {user.username}: 期望={expected_role}, 實際={actual_role}")

def create_user_role_if_missing():
    """如果缺少user角色則創建"""
    print("檢查是否需要創建user角色...")
    
    with get_db() as db:
        user_role = db.query(Role).filter(Role.name == 'user').first()
        
        if not user_role:
            print("創建user角色...")
            from services.auth_service import RoleService, PermissionService
            
            # 創建user角色
            user_role = RoleService.create_role(
                name='user',
                display_name='一般用戶',
                description='系統一般用戶，具有基本的查看和操作權限'
            )
            
            # 為user角色分配基本權限
            basic_permissions = db.query(Permission).filter(
                Permission.module.in_(['income_expense', 'reports']),
                Permission.action.in_(['view', 'create', 'edit'])
            ).all()
            
            if basic_permissions:
                permission_ids = [p.id for p in basic_permissions]
                RoleService.assign_permissions_to_role(user_role.id, permission_ids)
                print(f"為user角色分配了 {len(permission_ids)} 個基本權限")
            
            print("user角色創建完成")
        else:
            print("user角色已存在")

def main():
    """主要執行函式"""
    try:
        create_user_role_if_missing()
        fix_user_roles()
        verify_role_assignment()
        
        print("\n>>> 角色修正完成！現在管理員和一般用戶應該有不同的權限了。")
        
    except Exception as e:
        print(f"執行過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()