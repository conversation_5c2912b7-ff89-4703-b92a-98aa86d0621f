# 印錢大師會計系統 - 配置文件示例
# 複製此檔案為 config.yaml 並根據需要修改

database:
  type: sqlite  # sqlite, postgresql, mysql
  host: localhost
  port: 5432
  name: accounting
  user: ""
  password: ""
  pool_size: 30
  max_overflow: 50
  pool_timeout: 60
  pool_recycle: 7200
  pool_pre_ping: true
  echo_sql: false

cache:
  type: memory  # memory, redis, memcached
  redis_host: localhost
  redis_port: 6379
  redis_db: 0
  redis_password: ""
  default_timeout: 300
  cleanup_interval: 300
  max_entries: 10000

security:
  secret_key: ""  # 必需！請設定一個強密鑰
  session_timeout: 3600
  csrf_enabled: true
  csrf_timeout: 3600
  cookie_secure: false  # 生產環境建議設為 true
  cookie_httponly: true
  cookie_samesite: Lax
  rate_limit_enabled: true
  max_requests: 100
  window_minutes: 15
  bcrypt_rounds: 12

logging:
  level: INFO  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  max_bytes: ********  # 10MB
  backup_count: 10
  log_dir: logs
  enable_file_logging: true
  enable_console_logging: true
  security_log_enabled: true
  performance_log_enabled: true

mail:
  smtp_server: ""  # 例如: smtp.gmail.com
  smtp_port: 587
  username: ""
  password: ""
  use_tls: true
  use_ssl: false
  from_address: ""
  admin_email: ""

performance:
  enable_monitoring: true
  slow_request_threshold: 1.0  # 秒
  memory_threshold: 85         # 百分比
  cpu_threshold: 80           # 百分比
  alert_cooldown: 300         # 秒
  benchmark_enabled: true
  profiling_enabled: false    # 僅開發環境使用

files:
  upload_folder: uploads
  max_content_length: 16777216  # 16MB
  temp_folder: temp
  backup_folder: backups
  # allowed_extensions 在程式中設定

api:
  items_per_page: 20
  max_items_per_page: 100
  api_version: v1
  enable_api_docs: true
  rate_limit_per_minute: 60
  cors_enabled: true
  cors_origins:
    - "*"  # 生產環境請限制為具體域名