import os
import secrets
import warnings
from typing import Dict, Any

class BaseConfig:
    """基礎配置類"""
    
    # 資料庫設定
    @staticmethod
    def get_database_uri():
        """獲取資料庫連接字符串"""
        # 支援多種資料庫類型
        db_type = os.environ.get('DB_TYPE', 'sqlite')
        
        if db_type == 'sqlite':
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            db_path = os.path.join(project_root, 'app.db')
            return f"sqlite:///{db_path}"
        elif db_type == 'postgresql':
            return (f"postgresql://{os.environ.get('DB_USER')}:"
                   f"{os.environ.get('DB_PASSWORD')}@"
                   f"{os.environ.get('DB_HOST', 'localhost')}:"
                   f"{os.environ.get('DB_PORT', '5432')}/"
                   f"{os.environ.get('DB_NAME')}")
        elif db_type == 'mysql':
            return (f"mysql+pymysql://{os.environ.get('DB_USER')}:"
                   f"{os.environ.get('DB_PASSWORD')}@"
                   f"{os.environ.get('DB_HOST', 'localhost')}:"
                   f"{os.environ.get('DB_PORT', '3306')}/"
                   f"{os.environ.get('DB_NAME')}")
        else:
            raise ValueError(f"不支援的資料庫類型: {db_type}")
    
    @staticmethod
    def _generate_secret_key():
        """生成隨機密鑰"""
        warnings.warn(
            "未設置 SECRET_KEY 環境變數！正在使用隨機生成的密鑰。"
            "生產環境請設置 SECRET_KEY 環境變數。",
            UserWarning
        )
        return secrets.token_hex(32)
    
    # Flask 基礎設定
    SECRET_KEY = os.environ.get('SECRET_KEY') or os.getenv('SECRET_KEY') or 'dev-secret-key-for-accounting-system-2025'
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() in ['true', '1', 'yes']
    TESTING = False
    
    # 安全設定
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # CSRF 令牌有效期1小時
    SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'False').lower() == 'true'
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Strict'  # 更嚴格的CSRF保護
    
    # 會話管理
    PERMANENT_SESSION_LIFETIME = int(os.environ.get('SESSION_LIFETIME', 3600))  # 1小時
    SESSION_REFRESH_EACH_REQUEST = True  # 每次請求刷新會話
    SESSION_COOKIE_NAME = 'accounting_session'  # 自定義Cookie名稱
    
    # 安全HTTP頭配置
    SECURITY_HEADERS_ENABLED = True
    CSP_ALLOW_UNSAFE_INLINE = True  # 開發環境允許內聯腳本（生產環境會更嚴格）
    
    # 檔案上傳設定
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 10 * 1024 * 1024))  # 10MB (降低限制)
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'uploads')  # 舊設定，保留相容性
    SECURE_UPLOAD_FOLDER = os.environ.get('SECURE_UPLOAD_FOLDER', 'secure_uploads')  # 新的安全上傳目錄
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls', 'csv'}  # 舊設定，保留相容性
    
    # 安全檔案上傳設定
    FILE_UPLOAD_SECURITY_ENABLED = True
    MAX_UPLOAD_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_IMAGE_SIZE = 5 * 1024 * 1024    # 5MB for images
    
    # 分頁設定
    ITEMS_PER_PAGE = int(os.environ.get('ITEMS_PER_PAGE', 20))
    MAX_ITEMS_PER_PAGE = int(os.environ.get('MAX_ITEMS_PER_PAGE', 100))
    
    # 快取設定
    CACHE_DEFAULT_TIMEOUT = int(os.environ.get('CACHE_DEFAULT_TIMEOUT', 300))  # 5分鐘
    CACHE_CLEANUP_INTERVAL = int(os.environ.get('CACHE_CLEANUP_INTERVAL', 300))  # 5分鐘
    
    # 日誌設定
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_MAX_BYTES = int(os.environ.get('LOG_MAX_BYTES', 10 * 1024 * 1024))  # 10MB
    LOG_BACKUP_COUNT = int(os.environ.get('LOG_BACKUP_COUNT', 10))
    
    # 安全設定
    RATE_LIMIT_ENABLED = os.environ.get('RATE_LIMIT_ENABLED', 'True').lower() == 'true'
    RATE_LIMIT_MAX_REQUESTS = int(os.environ.get('RATE_LIMIT_MAX_REQUESTS', 100))
    RATE_LIMIT_WINDOW_MINUTES = int(os.environ.get('RATE_LIMIT_WINDOW_MINUTES', 15))
    
    # 管理員設定
    @staticmethod
    def _get_admin_password():
        """安全獲取管理員密碼"""
        admin_pwd = os.environ.get('ADMIN_PASSWORD')
        if not admin_pwd:
            # 開發環境：生成隨機密碼並警告
            if os.environ.get('FLASK_ENV', 'development').lower() == 'development':
                import secrets
                random_pwd = secrets.token_hex(8)
                warnings.warn(
                    f"未設置 ADMIN_PASSWORD 環境變數！\n"
                    f"臨時管理員密碼: {random_pwd}\n"
                    f"請設置 ADMIN_PASSWORD 環境變數以使用固定密碼。",
                    UserWarning
                )
                return random_pwd
            else:
                # 生產環境：必須設置密碼
                raise ValueError("生產環境必須設置 ADMIN_PASSWORD 環境變數")
        return admin_pwd
    
    ADMIN_PASSWORD = _get_admin_password.__func__()
    
    @classmethod
    def get_config_dict(cls) -> Dict[str, Any]:
        """獲取所有配置項（用於調試）"""
        config_dict = {}
        for attr in dir(cls):
            if not attr.startswith('_') and not callable(getattr(cls, attr)):
                value = getattr(cls, attr)
                # 隱藏敏感信息
                if 'password' in attr.lower() or 'secret' in attr.lower() or 'key' in attr.lower():
                    value = '***隱藏***'
                config_dict[attr] = value
        return config_dict

class DevelopmentConfig(BaseConfig):
    """開發環境配置"""
    DEBUG = True
    TESTING = False
    WTF_CSRF_ENABLED = True  # ✅ 開發環境也啟用 CSRF 保護
    # 開發環境使用HTTP時設為False，但在localhost上仍有基本保護
    SESSION_COOKIE_SECURE = False  
    # 開發環境會話時間稍短便於測試
    PERMANENT_SESSION_LIFETIME = 1800  # 30分鐘
    RATE_LIMIT_ENABLED = False  # 開發環境關閉頻率限制
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(BaseConfig):
    """生產環境配置"""
    DEBUG = False
    TESTING = False
    WTF_CSRF_ENABLED = True
    SESSION_COOKIE_SECURE = True  # 生產環境強制HTTPS
    SESSION_COOKIE_HTTPONLY = True  # 防止XSS攻擊
    SESSION_COOKIE_SAMESITE = 'Strict'  # 最嚴格的CSRF保護
    PERMANENT_SESSION_LIFETIME = 3600  # 1小時會話超時
    RATE_LIMIT_ENABLED = True
    LOG_LEVEL = 'WARNING'
    
    # 生產環境安全設定
    CSP_ALLOW_UNSAFE_INLINE = False  # 生產環境禁止內聯腳本
    HSTS_MAX_AGE = 31536000  # HSTS 一年有效期
    
    # 生產環境必須設置的環境變數檢查
    @classmethod
    def validate_production_config(cls):
        """驗證生產環境必需的配置"""
        required_vars = ['SECRET_KEY', 'ADMIN_PASSWORD']
        missing_vars = []
        
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ValueError(f"生產環境缺少必需的環境變數: {', '.join(missing_vars)}")

class TestingConfig(BaseConfig):
    """測試環境配置"""
    DEBUG = True
    TESTING = True
    WTF_CSRF_ENABLED = False
    RATE_LIMIT_ENABLED = False
    LOG_LEVEL = 'ERROR'
    
    # 使用記憶體資料庫進行測試
    @staticmethod
    def get_database_uri():
        return "sqlite:///:memory:"

class Config:
    """配置工廠類"""
    
    @staticmethod
    def get_config():
        """根據環境變數選擇配置"""
        env = os.environ.get('FLASK_ENV', 'development').lower()
        
        if env == 'production':
            ProductionConfig.validate_production_config()
            return ProductionConfig
        elif env == 'testing':
            return TestingConfig
        else:
            return DevelopmentConfig
    
    @staticmethod
    def get_database_uri():
        """向後相容的資料庫 URI 獲取方法"""
        return Config.get_config().get_database_uri()
    
    # 向後相容的屬性
    SECRET_KEY = BaseConfig.SECRET_KEY
    DEBUG = BaseConfig.DEBUG
    ITEMS_PER_PAGE = BaseConfig.ITEMS_PER_PAGE
