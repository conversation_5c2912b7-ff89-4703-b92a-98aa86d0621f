"""
進階配置管理系統
統一管理所有應用配置，支援環境變數、配置文件和動態載入
"""

import os
import json
import yaml
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, field
from datetime import timedelta

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """資料庫配置"""
    type: str = 'sqlite'
    host: str = 'localhost' 
    port: int = 5432
    name: str = 'accounting'
    user: str = ''
    password: str = ''
    pool_size: int = 30
    max_overflow: int = 50
    pool_timeout: int = 60
    pool_recycle: int = 7200
    pool_pre_ping: bool = True
    echo_sql: bool = False

@dataclass 
class CacheConfig:
    """快取配置"""
    type: str = 'memory'  # memory, redis, memcached
    redis_host: str = 'localhost'
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: str = ''
    default_timeout: int = 300
    cleanup_interval: int = 300
    max_entries: int = 10000
    
@dataclass
class SecurityConfig:
    """安全配置"""
    secret_key: str = ''
    session_timeout: int = 3600
    csrf_enabled: bool = True
    csrf_timeout: int = 3600
    cookie_secure: bool = False
    cookie_httponly: bool = True
    cookie_samesite: str = 'Lax'
    rate_limit_enabled: bool = True
    max_requests: int = 100
    window_minutes: int = 15
    bcrypt_rounds: int = 12

@dataclass
class LoggingConfig:
    """日誌配置"""
    level: str = 'INFO'
    format: str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    max_bytes: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 10
    log_dir: str = 'logs'
    enable_file_logging: bool = True
    enable_console_logging: bool = True
    security_log_enabled: bool = True
    performance_log_enabled: bool = True

@dataclass
class MailConfig:
    """郵件配置"""
    smtp_server: str = ''
    smtp_port: int = 587
    username: str = ''
    password: str = ''
    use_tls: bool = True
    use_ssl: bool = False
    from_address: str = ''
    admin_email: str = ''

@dataclass
class PerformanceConfig:
    """效能配置"""
    enable_monitoring: bool = True
    slow_request_threshold: float = 1.0  # 秒
    memory_threshold: int = 85  # 百分比
    cpu_threshold: int = 80     # 百分比
    alert_cooldown: int = 300   # 秒
    benchmark_enabled: bool = True
    profiling_enabled: bool = False

@dataclass
class FileConfig:
    """檔案處理配置"""
    upload_folder: str = 'uploads'
    max_content_length: int = 16 * 1024 * 1024  # 16MB
    allowed_extensions: set = field(default_factory=lambda: {
        'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'xlsx', 'xls', 'csv'
    })
    temp_folder: str = 'temp'
    backup_folder: str = 'backups'

@dataclass 
class APIConfig:
    """API 配置"""
    items_per_page: int = 20
    max_items_per_page: int = 100
    api_version: str = 'v1'
    enable_api_docs: bool = True
    rate_limit_per_minute: int = 60
    cors_enabled: bool = True
    cors_origins: list = field(default_factory=lambda: ['*'])

class ConfigManager:
    """統一配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.config_cache = {}
        self._load_config()
    
    def _load_config(self):
        """載入配置"""
        try:
            # 1. 載入預設配置
            self._load_defaults()
            
            # 2. 載入配置檔案（如果存在）
            if self.config_file and os.path.exists(self.config_file):
                self._load_config_file()
            
            # 3. 載入環境變數（優先級最高）
            self._load_env_vars()
            
            logger.info("配置載入完成")
            
        except Exception as e:
            logger.error(f"配置載入失敗: {e}")
            raise
    
    def _load_defaults(self):
        """載入預設配置"""
        self.database = DatabaseConfig()
        self.cache = CacheConfig()
        self.security = SecurityConfig()
        self.logging = LoggingConfig()
        self.mail = MailConfig()
        self.performance = PerformanceConfig()
        self.files = FileConfig()
        self.api = APIConfig()
    
    def _load_config_file(self):
        """載入配置檔案"""
        try:
            file_path = Path(self.config_file)
            
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            elif file_path.suffix.lower() in ['.yml', '.yaml']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
            else:
                raise ValueError(f"不支援的配置檔案格式: {file_path.suffix}")
            
            # 更新各配置段
            for section_name, section_data in data.items():
                if hasattr(self, section_name) and isinstance(section_data, dict):
                    section = getattr(self, section_name)
                    for key, value in section_data.items():
                        if hasattr(section, key):
                            setattr(section, key, value)
            
            logger.info(f"成功載入配置檔案: {file_path}")
            
        except Exception as e:
            logger.error(f"載入配置檔案失敗: {e}")
    
    def _load_env_vars(self):
        """載入環境變數"""
        env_mappings = {
            # 資料庫
            'DB_TYPE': ('database', 'type'),
            'DB_HOST': ('database', 'host'),
            'DB_PORT': ('database', 'port', int),
            'DB_NAME': ('database', 'name'),
            'DB_USER': ('database', 'user'),
            'DB_PASSWORD': ('database', 'password'),
            'DB_POOL_SIZE': ('database', 'pool_size', int),
            
            # 快取
            'CACHE_TYPE': ('cache', 'type'),
            'REDIS_HOST': ('cache', 'redis_host'),
            'REDIS_PORT': ('cache', 'redis_port', int),
            'REDIS_DB': ('cache', 'redis_db', int),
            'REDIS_PASSWORD': ('cache', 'redis_password'),
            'CACHE_DEFAULT_TIMEOUT': ('cache', 'default_timeout', int),
            
            # 安全
            'SECRET_KEY': ('security', 'secret_key'),
            'SESSION_TIMEOUT': ('security', 'session_timeout', int),
            'CSRF_ENABLED': ('security', 'csrf_enabled', bool),
            'RATE_LIMIT_ENABLED': ('security', 'rate_limit_enabled', bool),
            'MAX_REQUESTS': ('security', 'max_requests', int),
            
            # 日誌
            'LOG_LEVEL': ('logging', 'level'),
            'LOG_MAX_BYTES': ('logging', 'max_bytes', int),
            'LOG_BACKUP_COUNT': ('logging', 'backup_count', int),
            
            # 郵件
            'MAIL_SERVER': ('mail', 'smtp_server'),
            'MAIL_PORT': ('mail', 'smtp_port', int),
            'MAIL_USERNAME': ('mail', 'username'),
            'MAIL_PASSWORD': ('mail', 'password'),
            'MAIL_USE_TLS': ('mail', 'use_tls', bool),
            
            # 效能
            'PERFORMANCE_MONITORING': ('performance', 'enable_monitoring', bool),
            'SLOW_REQUEST_THRESHOLD': ('performance', 'slow_request_threshold', float),
            'MEMORY_THRESHOLD': ('performance', 'memory_threshold', int),
            
            # 檔案
            'UPLOAD_FOLDER': ('files', 'upload_folder'),
            'MAX_CONTENT_LENGTH': ('files', 'max_content_length', int),
            
            # API
            'ITEMS_PER_PAGE': ('api', 'items_per_page', int),
            'MAX_ITEMS_PER_PAGE': ('api', 'max_items_per_page', int),
            'API_RATE_LIMIT': ('api', 'rate_limit_per_minute', int),
        }
        
        for env_var, mapping in env_mappings.items():
            value = os.environ.get(env_var)
            if value is not None:
                section_name = mapping[0]
                attr_name = mapping[1]
                converter = mapping[2] if len(mapping) > 2 else str
                
                # 特殊處理布林值
                if converter == bool:
                    value = value.lower() in ['true', '1', 'yes', 'on']
                else:
                    value = converter(value)
                
                section = getattr(self, section_name)
                setattr(section, attr_name, value)
    
    def get_database_uri(self) -> str:
        """獲取資料庫連接字符串"""
        db = self.database
        
        if db.type == 'sqlite':
            if db.name == ':memory:':
                return 'sqlite:///:memory:'
            
            project_root = Path(__file__).parent.parent
            db_path = project_root / f'{db.name}.db'
            return f'sqlite:///{db_path}'
            
        elif db.type == 'postgresql':
            return (f'postgresql://{db.user}:{db.password}@'
                   f'{db.host}:{db.port}/{db.name}')
                   
        elif db.type == 'mysql':
            return (f'mysql+pymysql://{db.user}:{db.password}@'
                   f'{db.host}:{db.port}/{db.name}')
        else:
            raise ValueError(f'不支援的資料庫類型: {db.type}')
    
    def get_redis_url(self) -> str:
        """獲取 Redis 連接字符串"""
        cache = self.cache
        if cache.redis_password:
            return f'redis://:{cache.redis_password}@{cache.redis_host}:{cache.redis_port}/{cache.redis_db}'
        return f'redis://{cache.redis_host}:{cache.redis_port}/{cache.redis_db}'
    
    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """轉換為字典格式"""
        result = {}
        
        for attr_name in ['database', 'cache', 'security', 'logging', 
                         'mail', 'performance', 'files', 'api']:
            attr_obj = getattr(self, attr_name)
            section_dict = {}
            
            for field_name, field_value in attr_obj.__dict__.items():
                # 隱藏敏感資訊
                if not include_sensitive and any(keyword in field_name.lower() 
                                               for keyword in ['password', 'secret', 'key']):
                    field_value = '***隱藏***'
                section_dict[field_name] = field_value
            
            result[attr_name] = section_dict
        
        return result
    
    def save_config(self, file_path: str, format: str = 'yaml'):
        """儲存配置到檔案"""
        try:
            data = self.to_dict(include_sensitive=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                if format.lower() == 'json':
                    json.dump(data, f, indent=2, ensure_ascii=False)
                elif format.lower() in ['yml', 'yaml']:
                    yaml.dump(data, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                else:
                    raise ValueError(f'不支援的格式: {format}')
            
            logger.info(f'配置已儲存到: {file_path}')
            
        except Exception as e:
            logger.error(f'儲存配置失敗: {e}')
            raise
    
    def validate_config(self):
        """驗證配置"""
        errors = []
        
        # 必需的配置檢查
        if not self.security.secret_key:
            errors.append('SECRET_KEY 未設定')
        
        if self.cache.type == 'redis' and not self.cache.redis_host:
            errors.append('Redis 快取類型需要設定 redis_host')
        
        if self.mail.smtp_server and not self.mail.username:
            errors.append('郵件服務需要設定使用者名稱')
        
        # 數值範圍檢查
        if self.database.pool_size < 1:
            errors.append('資料庫連接池大小必須大於 0')
        
        if not 1 <= self.security.bcrypt_rounds <= 20:
            errors.append('bcrypt rounds 必須在 1-20 之間')
        
        if errors:
            raise ValueError(f'配置驗證失敗: {"; ".join(errors)}')
        
        logger.info('配置驗證通過')

# 全局配置管理器實例
config_manager = ConfigManager()

def get_config() -> ConfigManager:
    """獲取配置管理器實例"""
    return config_manager

def reload_config(config_file: Optional[str] = None):
    """重新載入配置"""
    global config_manager
    config_manager = ConfigManager(config_file)
    logger.info('配置已重新載入')

# 向後相容的配置類
class Config:
    """向後相容的配置類"""
    
    @staticmethod
    def get_database_uri():
        return config_manager.get_database_uri()
    
    @staticmethod
    def get_config():
        env = os.environ.get('FLASK_ENV', 'development').lower()
        # 這裡保持向後相容性，但實際使用 ConfigManager
        return config_manager
    
    # 常用配置的快速訪問
    SECRET_KEY = property(lambda self: config_manager.security.secret_key)
    DEBUG = property(lambda self: os.environ.get('FLASK_DEBUG', 'False').lower() == 'true')
    ITEMS_PER_PAGE = property(lambda self: config_manager.api.items_per_page)