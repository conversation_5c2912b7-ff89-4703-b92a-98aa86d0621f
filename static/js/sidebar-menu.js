// 側邊欄交互邏輯
document.addEventListener('DOMContentLoaded', function () {
    const menuItems = document.querySelectorAll('.sidebar-item[data-menu]');
    const submenus = document.querySelectorAll('.submenu-container');

    // 設置延遲時間（毫秒）
    const hoverDelay = 200;
    let hoverTimer = null;
    let leaveTimer = null;
    let currentSubmenu = null;

    // 隱藏所有子選單
    function hideAllSubmenus() {
        submenus.forEach(submenu => {
            submenu.classList.remove('show');
            submenu.style.display = 'none';
        });
    }

    // 顯示子選單的函數
    function showSubmenu(menuItem) {
        const menuName = menuItem.getAttribute('data-menu');

        // 如果當前已經顯示了這個子選單，不做任何操作
        if (currentSubmenu && currentSubmenu.id === `${menuName}-submenu`) {
            return;
        }

        hideAllSubmenus();

        // 顯示對應的子選單
        const targetSubmenu = document.getElementById(`${menuName}-submenu`);
        if (targetSubmenu) {
            currentSubmenu = targetSubmenu;

            // 計算主選單項目的位置
            const itemRect = menuItem.getBoundingClientRect();
            const sidebarRect = document.querySelector('.sidebar').getBoundingClientRect();

            // 設置子選單的top位置，讓它對齊主選單項目
            let topPosition = itemRect.top - sidebarRect.top;

            // 確保子選單不會超出視窗底部
            const windowHeight = window.innerHeight;
            const submenuHeight = Math.min(targetSubmenu.scrollHeight + 40, windowHeight * 0.8);

            if (topPosition + submenuHeight > windowHeight - 20) {
                topPosition = windowHeight - submenuHeight - 20;
            }

            // 確保子選單不會超出視窗頂部
            if (topPosition < 20) {
                topPosition = 20;
            }

            targetSubmenu.style.top = topPosition + 'px';
            targetSubmenu.style.display = 'block';

            // 添加動畫效果
            setTimeout(() => {
                targetSubmenu.classList.add('show');
            }, 10);
        }
    }

    // 為每個選單項目添加滑鼠事件
    menuItems.forEach(item => {
        // 滑鼠移入時，延遲顯示子選單
        item.addEventListener('mouseenter', function () {
            clearTimeout(leaveTimer);

            hoverTimer = setTimeout(() => {
                showSubmenu(this);
            }, hoverDelay);
        });

        // 滑鼠移出時，如果不是移到子選單上，則延遲隱藏子選單
        item.addEventListener('mouseleave', function (event) {
            clearTimeout(hoverTimer);

            // 檢查滑鼠是否移到了子選單上
            const menuName = this.getAttribute('data-menu');
            const targetSubmenu = document.getElementById(`${menuName}-submenu`);
            const relatedTarget = event.relatedTarget;

            if (targetSubmenu && !targetSubmenu.contains(relatedTarget)) {
                leaveTimer = setTimeout(() => {
                    if (currentSubmenu && currentSubmenu.id === targetSubmenu.id) {
                        hideAllSubmenus();
                        currentSubmenu = null;
                    }
                }, hoverDelay);
            }
        });

        // 保留點擊事件，以支持移動設備
        item.addEventListener('click', function (event) {
            // 防止事件冒泡
            event.stopPropagation();

            const menuName = this.getAttribute('data-menu');

            // 如果點擊的是同一個選單，切換顯示/隱藏
            if (currentSubmenu && currentSubmenu.id === `${menuName}-submenu`) {
                if (currentSubmenu.classList.contains('show')) {
                    // 如果已顯示，則隱藏
                    currentSubmenu.classList.remove('show');
                    setTimeout(() => {
                        currentSubmenu.style.display = 'none';
                    }, 200);
                    currentSubmenu = null;
                }
                return;
            }

            showSubmenu(this);
        });
    });

    // 為子選單添加滑鼠事件
    submenus.forEach(submenu => {
        // 滑鼠移入子選單時，清除隱藏計時器
        submenu.addEventListener('mouseenter', function () {
            clearTimeout(leaveTimer);
        });

        // 滑鼠移出子選單時，延遲隱藏子選單
        submenu.addEventListener('mouseleave', function (event) {
            // 檢查滑鼠是否移到了主選單項目上
            const relatedTarget = event.relatedTarget;
            const isMovingToMenuItem = Array.from(menuItems).some(item => item.contains(relatedTarget));

            if (!isMovingToMenuItem) {
                leaveTimer = setTimeout(() => {
                    hideAllSubmenus();
                    currentSubmenu = null;
                }, hoverDelay);
            }
        });
    });

    // 點擊子選單外部時隱藏子選單
    document.addEventListener('click', function (event) {
        const isClickInsideSidebar = event.target.closest('.sidebar');
        const isClickInsideSubmenu = event.target.closest('.submenu-container');
        const isClickOnCloseBtn = event.target.closest('.submenu-close-btn');

        // 如果點擊的是關閉按鈕，不處理（讓關閉按鈕自己的事件處理）
        if (isClickOnCloseBtn) {
            return;
        }

        if (!isClickInsideSidebar && !isClickInsideSubmenu) {
            hideAllSubmenus();
            currentSubmenu = null;
        }
    });

    // 添加ESC鍵隱藏子選單的功能
    document.addEventListener('keydown', function (event) {
        if (event.key === 'Escape') {
            hideAllSubmenus();
            currentSubmenu = null;
        }
    });

    // 關閉指定子選單的函數
    window.closeSubmenu = function (submenuId) {
        const submenu = document.getElementById(submenuId);
        if (submenu) {
            submenu.classList.remove('show');
            setTimeout(() => {
                submenu.style.display = 'none';
            }, 200);
            currentSubmenu = null;
        }
    };

    // 隱藏所有子選單的全域函數
    window.hideSubmenu = function () {
        hideAllSubmenus();
        currentSubmenu = null;
    };
});