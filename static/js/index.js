        document.addEventListener('DOMContentLoaded', function () {
            const adminToggle = document.getElementById('adminToggle');
            const adminMenu = document.getElementById('adminMenu');
            const adminClose = document.getElementById('adminClose');
            const adminClose2 = document.getElementById('adminClose2');
            const passwordScreen = document.getElementById('passwordScreen');
            const adminContent = document.getElementById('adminContent');
            const adminPassword = document.getElementById('adminPassword');
            const loginBtn = document.getElementById('loginBtn');
            const logoutBtn = document.getElementById('logoutBtn');
            const passwordError = document.getElementById('passwordError');

            // 管理員密碼驗證移至後端處理
            let isAuthenticated = false;

            // 切換管理面板
            adminToggle.addEventListener('click', function () {
                if (adminMenu.classList.contains('is-hidden')) {
                    showAdminPanel();
                } else {
                    hideAdminPanel();
                }
            });

            // 顯示管理面板
            function showAdminPanel() {
                adminMenu.classList.remove('is-hidden');
                adminMenu.classList.add('show');

                if (isAuthenticated) {
                    showAdminContent();
                } else {
                    showPasswordScreen();
                }
            }

            // 隱藏管理面板
            function hideAdminPanel() {
                adminMenu.classList.add('is-hidden');
                adminMenu.classList.remove('show');
                clearPassword();
            }

            // 顯示密碼輸入畫面
            function showPasswordScreen() {
                passwordScreen.classList.remove('is-hidden');
                adminContent.classList.add('is-hidden');
                adminPassword.focus();
            }

            // 顯示管理功能畫面
            function showAdminContent() {
                passwordScreen.classList.add('is-hidden');
                adminContent.classList.remove('is-hidden');
            }

            // 清除密碼輸入
            function clearPassword() {
                adminPassword.value = '';
                passwordError.classList.add('is-hidden');
            }

            // 驗證密碼 - 發送到後端驗證
            function verifyPassword() {
                const inputPassword = adminPassword.value;

                // 發送密碼到後端驗證
                fetch('/admin/verify_password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ password: inputPassword })
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            isAuthenticated = true;
                            showAdminContent();
                            clearPassword();
                            // 儲存認證狀態到 sessionStorage
                            sessionStorage.setItem('adminAuthenticated', 'true');
                        } else {
                            showPasswordError();
                        }
                    })
                    .catch(error => {
                        console.error('驗證錯誤:', error);
                        showPasswordError();
                    });
            }

            // 顯示密碼錯誤
            function showPasswordError() {
                passwordError.classList.remove('is-hidden');
                adminPassword.classList.add('password-error-shake');
                adminPassword.value = '';
                adminPassword.focus();

                // 移除震動效果
                setTimeout(() => {
                    adminPassword.classList.remove('password-error-shake');
                }, 500);

                // 3秒後隱藏錯誤訊息
                setTimeout(() => {
                    passwordError.classList.add('is-hidden');
                }, 3000);
            }

            // 登入按鈕點擊
            loginBtn.addEventListener('click', verifyPassword);

            // 密碼輸入框按 Enter 鍵
            adminPassword.addEventListener('keypress', function (event) {
                if (event.key === 'Enter') {
                    verifyPassword();
                }
            });

            // 登出功能
            logoutBtn.addEventListener('click', function () {
                isAuthenticated = false;
                showPasswordScreen();
            });

            // 關閉管理面板
            adminClose.addEventListener('click', hideAdminPanel);
            adminClose2.addEventListener('click', hideAdminPanel);

            // 點擊外部關閉面板
            document.addEventListener('click', function (event) {
                // 只有在管理面板顯示時才處理點擊事件
                if (!adminMenu.classList.contains('is-hidden') && !event.target.closest('.admin-panel') && !event.target.closest('#adminToggle')) {
                    hideAdminPanel();
                }
            });

            // ESC 鍵關閉面板
            document.addEventListener('keydown', function (event) {
                if (event.key === 'Escape') {
                    hideAdminPanel();
                }
            });

            // 管理功能連結點擊時記住認證狀態
            document.querySelectorAll('.admin-link').forEach(link => {
                link.addEventListener('click', function () {
                    // 在新頁面中可以通過 sessionStorage 檢查認證狀態
                    if (isAuthenticated) {
                        sessionStorage.setItem('adminAuthenticated', 'true');
                    }
                });
            });

            // 頁面載入時檢查認證狀態
            if (sessionStorage.getItem('adminAuthenticated') === 'true') {
                isAuthenticated = true;
            }
        });
