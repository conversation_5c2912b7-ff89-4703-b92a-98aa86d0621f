    /* 側邊欄基本樣式 - Updated 2025-09-07 v2 */
    .sidebar {
        background-color: #2d3748;
        color: #ffffff;
        min-height: 100vh;
        width: 200px;
        padding: 0;
        display: flex;
        flex-direction: column;
    }

    /* 主選單項目樣式 */
    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    /* 用戶區域樣式 */
    .user-section {
        margin-top: auto;
        border-top: 1px solid #4a5568;
        padding: 15px 20px;
        background-color: #1a202c;
    }

    .user-info {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        color: #cbd5e0;
        font-size: 16px;
    }

    .user-info i {
        margin-right: 10px;
    }

    .logout-link {
        color: #ffffff;
        text-decoration: none;
        display: flex;
        align-items: center;
        padding: 10px;
        border-radius: 4px;
        transition: background-color 0.3s;
    }

    .logout-link:hover {
        background-color: #2d3748;
    }

    .sidebar-item {
        padding: 15px 20px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.3s;
        font-size: 20px;
    }

    .sidebar-item:hover {
        background-color: #3a4556;
    }

    .sidebar-item.active {
        background-color: #3a4556;
    }

    .sidebar-item[data-menu] {
        cursor: pointer;
    }

    .sidebar-item[data-menu]:hover .expand-icon {
        transform: rotate(90deg);
        transition: transform 0.2s ease;
    }

    /* 圖示樣式 */
    .sidebar-icon {
        margin-right: 15px;
        width: 20px;
        text-align: center;
    }

    /* 展開箭頭樣式 */
    .expand-icon {
        margin-left: auto !important;
        display: inline-block !important;
        visibility: visible !important;
    }

    .expand-icon i {
        display: inline-block !important;
        visibility: visible !important;
    }

    /* 子選單容器樣式 */
    .submenu-container {
        position: absolute;
        left: 250px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: none;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 4px 12px rgba(0, 0, 0, 0.05);
        border-radius: 16px;
        min-width: 750px;
        min-height: 420px;
        max-height: 80vh;
        display: none;
        padding: 25px;
        z-index: 100;
        overflow-y: auto;
        opacity: 0;
        transform: translateX(-10px) scale(0.98);
        transition: opacity 0.3s ease, transform 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .submenu-container.show {
        opacity: 1;
        transform: translateX(0) scale(1);
    }

    /* 子選單箭頭指示器 */
    .submenu-container::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 20px;
        width: 0;
        height: 0;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
        border-right: 8px solid white;
        filter: drop-shadow(-2px 0 2px rgba(0, 0, 0, 0.1));
    }

    /* 子選單關閉按鈕 */
    .submenu-close-btn {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 32px;
        height: 32px;
        border: none;
        background: #f8fafc;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        color: #64748b;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .submenu-close-btn:hover {
        background: #ef4444;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    }

    /* 子選單標題樣式 */
    .submenu-section {
        margin-bottom: 25px;
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .submenu-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .submenu-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        color: #2d3748;
        font-weight: 600;
        font-size: 18px;
        border-bottom: 2px solid #e2e8f0;
        padding-bottom: 8px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .submenu-header-icon {
        margin-right: 12px;
        width: 24px;
        text-align: center;
        color: #667eea;
        -webkit-text-fill-color: #667eea;
    }

    /* 子選單項目樣式 */
    .submenu-items {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
    }

    .submenu-item {
        padding: 0;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .submenu-item:hover {
        transform: translateY(-1px);
    }

    .submenu-item a {
        color: #4a5568;
        text-decoration: none;
        font-size: 16px;
        display: block;
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        background: white;
        transition: all 0.3s ease;
        font-weight: 500;
        position: relative;
        overflow: hidden;
    }

    .submenu-item a::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 0;
        background: linear-gradient(45deg, #667eea, #764ba2);
        transition: width 0.3s ease;
        z-index: -1;
    }

    .submenu-item a:hover {
        color: white;
        border-color: #667eea;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        transform: translateX(2px);
    }

    .submenu-item a:hover::before {
        width: 100%;
    }

    /* 頂部標籤樣式 */
    .top-tabs {
        background-color: #f7fafc;
        padding: 10px 20px;
        border-bottom: 1px solid #e2e8f0;
    }

    .tab {
        display: inline-block;
        padding: 8px 16px;
        margin-right: 10px;
        cursor: pointer;
    }

    .tab.active {
        font-weight: bold;
        border-bottom: 2px solid #4299e1;
    }
