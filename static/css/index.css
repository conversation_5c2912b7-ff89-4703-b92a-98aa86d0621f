        body {
            margin: 0;
            padding: 0;
        }

        .column.is-narrow {
            flex: none;
            width: 200px !important;
            max-width: 200px !important;
            min-width: 200px !important;
        }

        .sidebar {
            max-width: 200px;
            min-width: 180px;
            width: 200px;
        }
        .admin-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .admin-toggle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .admin-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .admin-toggle i {
            animation: rotate 2s linear infinite;
        }

        @keyframes rotate {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        .admin-menu {
            position: absolute;
            bottom: 70px;
            right: 0;
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .admin-menu-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-menu-content {
            padding: 15px;
        }

        .admin-section {
            margin-bottom: 15px;
        }

        .admin-section:last-child {
            margin-bottom: 0;
        }

        .admin-section-title {
            font-size: 12px;
            font-weight: bold;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }

        .admin-link {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            color: #333;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            margin-bottom: 4px;
        }

        .admin-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            text-decoration: none;
        }

        .admin-link i {
            margin-right: 10px;
            width: 16px;
            text-align: center;
        }

        /* 密碼驗證樣式 */
        .password-screen {
            width: 100%;
        }

        .password-content {
            padding: 20px;
        }

        .password-content .field {
            margin-bottom: 15px;
        }

        .password-content .label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .password-content .input {
            border-radius: 6px;
            border: 1px solid #ddd;
            padding: 10px 12px 10px 40px;
        }

        .password-content .input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.125em rgba(102, 126, 234, 0.25);
        }

        .admin-content {
            width: 100%;
        }

        .header-buttons {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-buttons .button {
            padding: 4px 8px;
            height: auto;
            min-height: auto;
        }

        /* 錯誤訊息動畫 */
        .password-error-shake {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {

            0%,
            100% {
                transform: translateX(0);
            }

            25% {
                transform: translateX(-5px);
            }

            75% {
                transform: translateX(5px);
            }
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .admin-panel {
                bottom: 15px;
                right: 15px;
            }

            .admin-toggle {
                width: 50px;
                height: 50px;
                font-size: 18px;
            }

            .admin-menu {
                width: 250px;
                bottom: 60px;
            }
        }

        /* 動畫效果 */
        .admin-menu.show {
            animation: slideInUp 0.3s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
