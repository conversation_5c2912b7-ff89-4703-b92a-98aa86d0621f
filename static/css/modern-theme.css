/* 現代化主題 CSS - 漸層色彩與玻璃效果 */

:root {
  /* 現代色彩變數 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  
  /* 背景色彩 */
  --bg-light: #f8fafc;
  --bg-card: rgba(255, 255, 255, 0.95);
  --bg-sidebar: rgba(255, 255, 255, 0.98);
  
  /* 陰影效果 */
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-hover: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* 玻璃效果 */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* 動畫時間 */
  --transition-fast: 0.15s ease-out;
  --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 深色模式支援 */
[data-theme="dark"] {
  --bg-light: #1a1a2e;
  --bg-card: rgba(22, 27, 34, 0.95);
  --bg-sidebar: rgba(22, 27, 34, 0.98);
  --glass-bg: rgba(22, 27, 34, 0.25);
  --glass-border: rgba(255, 255, 255, 0.1);
}

/* 全域樣式 */
body {
  background: var(--bg-light);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  transition: background-color var(--transition-smooth);
}

/* 現代化卡片設計 */
.modern-card {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
  transition: all var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  opacity: 0;
  transition: opacity var(--transition-smooth);
}

.modern-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-hover);
}

.modern-card:hover::before {
  opacity: 1;
}

/* 漸層按鈕 */
.btn-gradient-primary {
  background: var(--primary-gradient);
  border: none;
  color: white;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.btn-gradient-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-gradient-primary:hover::before {
  left: 100%;
}

.btn-gradient-success {
  background: var(--success-gradient);
}

.btn-gradient-danger {
  background: var(--danger-gradient);
}

.btn-gradient-warning {
  background: var(--warning-gradient);
}

/* 摘要卡片增強 */
.summary-card-enhanced {
  background: var(--bg-card);
  border-radius: 20px;
  padding: 24px;
  box-shadow: var(--shadow-soft);
  transition: all var(--transition-smooth);
  position: relative;
  overflow: hidden;
}

.summary-card-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-smooth);
}

.summary-card-enhanced:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
}

.summary-card-enhanced:hover::after {
  transform: scaleX(1);
}

/* 數值動畫 */
.animated-counter {
  font-size: 2rem;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

/* 表格現代化 */
.modern-table {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--shadow-soft);
  border: none;
}

.modern-table thead th {
  background: var(--primary-gradient);
  color: white;
  font-weight: 600;
  padding: 16px;
  border: none;
}

.modern-table tbody tr {
  transition: all var(--transition-fast);
}

.modern-table tbody tr:hover {
  background: rgba(102, 126, 234, 0.05);
  transform: scale(1.01);
}

.modern-table tbody td {
  padding: 16px;
  border: none;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

/* 側邊欄現代化 */
.modern-sidebar {
  background: var(--bg-sidebar);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--glass-border);
  box-shadow: var(--shadow-medium);
}

.sidebar-menu-item {
  transition: all var(--transition-fast);
  border-radius: 12px;
  margin: 4px 8px;
}

.sidebar-menu-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(4px);
}

.sidebar-menu-item.is-active {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-soft);
}

/* 圖表容器增強 */
.chart-container-modern {
  position: relative;
  background: var(--bg-card);
  border-radius: 20px;
  padding: 24px;
  box-shadow: var(--shadow-soft);
  overflow: hidden;
}

.chart-container-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient);
  opacity: 0.02;
  z-index: 0;
}

.chart-container-modern > * {
  position: relative;
  z-index: 1;
}

/* 響應式設計增強 */
@media (max-width: 768px) {
  .modern-card {
    margin: 8px;
    border-radius: 12px;
  }
  
  .summary-card-enhanced {
    padding: 16px;
    margin: 8px 0;
  }
  
  .modern-table {
    font-size: 0.9rem;
  }
  
  .modern-table thead th,
  .modern-table tbody td {
    padding: 12px 8px;
  }
}

/* 載入動畫 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp var(--transition-slow) ease-out forwards;
}

/* 脈衝動畫用於重要元素 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* 狀態標籤現代化 */
.status-tag-modern {
  padding: 6px 16px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.875rem;
  border: 2px solid transparent;
  transition: all var(--transition-fast);
}

.status-tag-modern.is-success {
  background: var(--success-gradient);
  color: white;
}

.status-tag-modern.is-danger {
  background: var(--danger-gradient);
  color: white;
}

.status-tag-modern.is-warning {
  background: var(--warning-gradient);
  color: white;
}

.status-tag-modern.is-info {
  background: var(--info-gradient);
  color: white;
}

/* 通知徽章現代化 */
.notification-badge-modern {
  background: var(--danger-gradient);
  color: white;
  border-radius: 50%;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
  box-shadow: var(--shadow-soft);
  animation: pulse 2s infinite;
}

/* 深色模式切換 */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: var(--bg-card);
  border: 1px solid var(--glass-border);
  border-radius: 50px;
  padding: 8px 16px;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-soft);
  cursor: pointer;
  transition: all var(--transition-smooth);
}

.theme-toggle:hover {
  box-shadow: var(--shadow-medium);
  transform: scale(1.05);
}