# 📚 會計系統文檔索引

本文件夾包含會計系統的所有技術文檔。

## 📁 文檔分類

### 🔄 Migration (遷移相關)
系統架構遷移、代碼重構相關文檔。

### 🔒 Security (安全性)
安全性測試、SQL 注入防護、安全分析報告。

### ⚡ Performance (性能)
性能測試、負載測試、優化建議相關文檔。

### 🧪 Testing (測試)
測試策略、測試結果、路由測試相關文檔。

### 📋 General (一般文檔)
README、URL 映射、一般性說明文檔。

## 📖 文檔列表


### MIGRATION
- [migration_plan.md](./migration/migration_plan.md)
- [MIGRATION_GUIDE.md](./migration/MIGRATION_GUIDE.md)
- [MIGRATION_REPORT.md](./migration/MIGRATION_REPORT.md)

### SECURITY
- [SECURITY_ANALYSIS_REPORT.md](./security/SECURITY_ANALYSIS_REPORT.md)
- [FINAL_SECURITY_SUMMARY.md](./security/FINAL_SECURITY_SUMMARY.md)

### PERFORMANCE
- [PERFORMANCE_SOLUTION_SUMMARY.md](./performance/PERFORMANCE_SOLUTION_SUMMARY.md)
- [PERFORMANCE_TEST_FIXES.md](./performance/PERFORMANCE_TEST_FIXES.md)
- [FINAL_PERFORMANCE_SUMMARY.md](./performance/FINAL_PERFORMANCE_SUMMARY.md)

### TESTING
- [ROUTE_FIX_SUMMARY.md](./testing/ROUTE_FIX_SUMMARY.md)

### GENERAL
- [README.md](./general/README.md)
- [URL_MAPPING.md](./general/URL_MAPPING.md)
- [ICON_LICENSE.md](./general/ICON_LICENSE.md)

## 🔧 使用說明

1. **查看特定類別文檔**: 進入對應的子文件夾
2. **搜索文檔**: 使用 `grep -r "關鍵字" docs/`
3. **更新文檔**: 直接編輯對應的 .md 文件

## 📝 文檔維護

- 新增文檔時請放入適當的分類文件夾
- 重要變更請更新此索引文件
- 定期檢查文檔的時效性

---
*此索引文件由 organize_docs.py 自動生成*

應該設計一個一人公司版，很多模組都不需要有(不能備份，沒有薪資模塊)
然後五人版(應該設計一個權限分別)

只剩下我的報表前兩個區塊
帳簿那裡有是否抵扣營業稅？不知道啥意思

對象，輸入以後，變成固定的下拉選單（太聰明）已經做好
匯入功能不知道要不要做
是否需要交易明細查詢，發票列表查詢
目前是交易紀錄有借貸方，但是明細不知道需不需要
