#!/usr/bin/env python3
"""
安全監控系統測試腳本
模擬各種安全事件來測試監控功能
"""
import requests
import time
from datetime import datetime

# 測試服務器地址
BASE_URL = 'http://localhost:5000'

def test_failed_login_attempts():
    """測試失敗登入監控"""
    print("🔍 測試失敗登入監控...")
    
    # 模擬多次失敗登入
    failed_passwords = ['wrong1', 'wrong2', 'wrong3', 'admin', 'password']
    
    for i, password in enumerate(failed_passwords, 1):
        print(f"  嘗試 {i}: 用戶名 'admin', 密碼 '{password}'")
        
        try:
            # 先獲取登入頁面以取得 CSRF token
            session = requests.Session()
            login_page = session.get(f'{BASE_URL}/auth/login')
            
            if 'csrf_token' in login_page.text:
                # 嘗試提取 CSRF token（簡化處理）
                response = session.post(f'{BASE_URL}/auth/login', {
                    'username': 'admin',
                    'password': password
                }, allow_redirects=False)
                
                print(f"     響應狀態: {response.status_code}")
            else:
                print("     無法獲取 CSRF token")
        
        except Exception as e:
            print(f"     請求錯誤: {e}")
        
        time.sleep(1)  # 避免太快的請求

def test_api_rate_limiting():
    """測試 API 速率限制"""
    print("\n🔍 測試 API 速率限制...")
    
    # 快速發送多個 API 請求
    for i in range(15):
        try:
            response = requests.get(f'{BASE_URL}/api/bank_heads', timeout=2)
            print(f"  請求 {i+1}: 狀態 {response.status_code}")
            
            if response.status_code == 429:
                print("     ✅ 速率限制已觸發！")
                break
                
        except Exception as e:
            print(f"  請求 {i+1}: 錯誤 {e}")
        
        time.sleep(0.2)

def test_suspicious_access_patterns():
    """測試可疑訪問模式偵測"""
    print("\n🔍 測試可疑訪問模式偵測...")
    
    # 快速訪問多個敏感頁面
    sensitive_pages = [
        '/admin',
        '/settings',
        '/department_manage',
        '/payment_identity_list',
        '/basic_info'
    ]
    
    for page in sensitive_pages:
        for i in range(3):
            try:
                response = requests.get(f'{BASE_URL}{page}', 
                                      allow_redirects=False, timeout=2)
                print(f"  訪問 {page}: 狀態 {response.status_code}")
                
            except Exception as e:
                print(f"  訪問 {page}: 錯誤 {e}")
            
            time.sleep(0.1)

def check_security_stats():
    """檢查安全統計（需要登入）"""
    print("\n📊 安全統計檢查...")
    print("注意: 需要登入才能查看完整的安全統計")
    print("請手動訪問: http://localhost:5000/security-dashboard")

def generate_security_report():
    """生成安全測試報告"""
    print("\n📋 安全監控測試報告")
    print("=" * 50)
    print(f"測試時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n已測試的安全監控功能:")
    print("✅ 失敗登入嘗試監控")
    print("✅ API 速率限制")
    print("✅ 可疑訪問模式偵測")
    print("\n建議檢查項目:")
    print("1. 查看應用程式日誌中的安全事件記錄")
    print("2. 訪問安全儀表板查看詳細統計")
    print("3. 確認告警系統是否正常觸發")
    print("\n儀表板訪問:")
    print("URL: http://localhost:5000/security-dashboard")
    print("需要先登入系統才能訪問")

if __name__ == '__main__':
    print("🚀 安全監控系統測試開始")
    print("=" * 50)
    
    # 執行各項測試
    test_failed_login_attempts()
    test_api_rate_limiting()
    test_suspicious_access_patterns()
    check_security_stats()
    generate_security_report()
    
    print("\n🎉 測試完成！")
    print("請檢查系統日誌和安全儀表板以驗證監控功能。")