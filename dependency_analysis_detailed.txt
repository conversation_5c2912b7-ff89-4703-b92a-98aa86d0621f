請=== 會計系統 Python 檔案依賴分析報告 ===

總檔案數: 131
已使用檔案數: 76
疑似未使用檔案數: 55

=== 疑似未使用的檔案 ===
  alembic/env.py
  alembic/versions/2401f6dab479_add_soft_delete_fields_to_.py
  alembic/versions/54d22b62dce6_add_tax_column_to_money_table.py
  alembic/versions/84cba4485354_add_payment_identity_type_model.py
  alembic/versions/8f3129f38236_change_a_time_from_datetime_to_date_in_.py
  alembic/versions/cca36410de1e_add_indexes_and_backref_relationships.py
  dependency_analyzer.py
  migrations/add_entry_type_and_transaction_type.py
  models/bank_model.py
  models/new_models.py
  models/payment_identity_type.py
  routes/new_income_expense.py
  routes/payment_identity_type.py
  scripts/add_database_indexes.py
  scripts/add_missing_permissions.py
  scripts/archive/create_employee_table.py
  scripts/archive/create_salary_setting_table.py
  scripts/archive/migrate_bank_data.py
  scripts/archive/update_sidebar_layout.py
  scripts/archive/update_subject_categories.py
  scripts/archive/update_subject_categories_v2.py
  scripts/bank_data_gen.py
  scripts/log_monitor.py
  scripts/migrate_user_table.py
  scripts/organize_docs.py
  scripts/performance_test.py
  scripts/run_tests.py
  services/bank_service.py
  services/income_statement_service.py
  services/subject_service.py
  tests/conftest.py
  tests/main_old.py
  tests/model2.py
  tests/test_api_endpoints.py
  tests/test_business_logic.py
  tests/test_employee_system.py
  tests/test_integration.py
  tests/test_load_testing.py
  tests/test_logging.py
  tests/test_models.py
  tests/test_performance.py
  tests/test_performance_fixed.py
  tests/test_quick_fixes.py
  tests/test_routes.py
  tests/test_security_recommendations.py
  tests/test_security_sql_injection.py
  tests/test_subject_manage.py
  tests/test_utils.py
  utils/api_response.py
  utils/backup_manager.py
  utils/base_view.py
  utils/batch_operations.py
  utils/db_session_helper.py
  utils/logging_middleware.py
  utils/security.py

=== 按目錄分析 ===

目錄: alembic
  已使用: 0 個檔案
  未使用: 1 個檔案
    - env.py

目錄: alembic/versions
  已使用: 0 個檔案
  未使用: 5 個檔案
    - 2401f6dab479_add_soft_delete_fields_to_.py
    - 54d22b62dce6_add_tax_column_to_money_table.py
    - 84cba4485354_add_payment_identity_type_model.py
    - 8f3129f38236_change_a_time_from_datetime_to_date_in_.py
    - cca36410de1e_add_indexes_and_backref_relationships.py

目錄: config
  已使用: 3 個檔案

目錄: data
  已使用: 5 個檔案

目錄: migrations
  已使用: 0 個檔案
  未使用: 1 個檔案
    - add_entry_type_and_transaction_type.py

目錄: models
  已使用: 1 個檔案
  未使用: 3 個檔案
    - bank_model.py
    - new_models.py
    - payment_identity_type.py

目錄: root
  已使用: 3 個檔案
  未使用: 1 個檔案
    - dependency_analyzer.py

目錄: routes
  已使用: 29 個檔案
  未使用: 2 個檔案
    - new_income_expense.py
    - payment_identity_type.py

目錄: scripts
  已使用: 0 個檔案
  未使用: 8 個檔案
    - add_database_indexes.py
    - add_missing_permissions.py
    - bank_data_gen.py
    - log_monitor.py
    - migrate_user_table.py
    - organize_docs.py
    - performance_test.py
    - run_tests.py

目錄: scripts/archive
  已使用: 0 個檔案
  未使用: 6 個檔案
    - create_employee_table.py
    - create_salary_setting_table.py
    - migrate_bank_data.py
    - update_sidebar_layout.py
    - update_subject_categories.py
    - update_subject_categories_v2.py

目錄: services
  已使用: 13 個檔案
  未使用: 3 個檔案
    - bank_service.py
    - income_statement_service.py
    - subject_service.py

目錄: tests
  已使用: 0 個檔案
  未使用: 18 個檔案
    - conftest.py
    - main_old.py
    - model2.py
    - test_api_endpoints.py
    - test_business_logic.py
    - test_employee_system.py
    - test_integration.py
    - test_load_testing.py
    - test_logging.py
    - test_models.py
    - test_performance.py
    - test_performance_fixed.py
    - test_quick_fixes.py
    - test_routes.py
    - test_security_recommendations.py
    - test_security_sql_injection.py
    - test_subject_manage.py
    - test_utils.py

目錄: utils
  已使用: 22 個檔案
  未使用: 7 個檔案
    - api_response.py
    - backup_manager.py
    - base_view.py
    - batch_operations.py
    - db_session_helper.py
    - logging_middleware.py
    - security.py