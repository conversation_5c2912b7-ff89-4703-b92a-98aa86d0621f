#!/usr/bin/env python3
"""
創建管理員權限
確保系統管理模組有相應的權限記錄
"""
import sys
import os

# 添加專案路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from models.auth_models import Permission, Role
from services.auth_service import PermissionService, RoleService

def create_missing_admin_permissions():
    """創建缺少的管理員權限"""
    print("=== 創建管理員權限 ===\n")
    
    with get_db() as db:
        # 需要創建的管理員權限
        admin_permissions = [
            {
                'name': 'admin.view',
                'display_name': '查看系統管理',
                'module': 'admin',
                'action': 'view',
                'description': '允許訪問系統管理模組'
            },
            {
                'name': 'settings.view',
                'display_name': '查看設定',
                'module': 'settings', 
                'action': 'view',
                'description': '允許查看系統設定'
            },
            {
                'name': 'settings.manage',
                'display_name': '管理設定',
                'module': 'settings',
                'action': 'manage', 
                'description': '允許管理系統設定'
            },
            {
                'name': 'reports.view',
                'display_name': '查看報表',
                'module': 'reports',
                'action': 'view',
                'description': '允許查看報表'
            },
            {
                'name': 'reports.manage',
                'display_name': '管理報表',
                'module': 'reports',
                'action': 'manage',
                'description': '允許管理報表設定'
            },
            {
                'name': 'income_expense.view',
                'display_name': '查看收支記錄',
                'module': 'income_expense',
                'action': 'view',
                'description': '允許查看收支記錄'
            },
            {
                'name': 'income_expense.create',
                'display_name': '建立收支記錄',
                'module': 'income_expense',
                'action': 'create',
                'description': '允許建立收支記錄'
            },
            {
                'name': 'fund_management.view',
                'display_name': '查看資金管理',
                'module': 'fund_management',
                'action': 'view',
                'description': '允許查看資金管理'
            }
        ]
        
        created_permissions = []
        
        for perm_data in admin_permissions:
            # 檢查是否已存在
            existing = db.query(Permission).filter(Permission.name == perm_data['name']).first()
            if not existing:
                # 創建新權限
                permission = PermissionService.create_permission(**perm_data)
                created_permissions.append(permission)
                print(f"✓ 創建權限: {perm_data['name']} - {perm_data['display_name']}")
            else:
                print(f"- 權限已存在: {perm_data['name']}")
        
        print(f"\n共創建了 {len(created_permissions)} 個新權限")
        
        return created_permissions

def update_admin_role_permissions():
    """更新管理員角色權限"""
    print("\n=== 更新管理員角色權限 ===\n")
    
    with get_db() as db:
        # 獲取admin角色
        admin_role = db.query(Role).filter(Role.name == 'admin').first()
        if not admin_role:
            print("錯誤：找不到admin角色")
            return
        
        # 獲取所有權限
        all_permissions = db.query(Permission).all()
        permission_ids = [p.id for p in all_permissions]
        
        print(f"找到 {len(all_permissions)} 個權限")
        
        # 為admin角色分配所有權限
        RoleService.assign_permissions_to_role(admin_role.id, permission_ids)
        
        print(f"✓ 成功為admin角色分配了 {len(permission_ids)} 個權限")

def update_user_role_permissions():
    """更新一般用戶角色權限"""
    print("\n=== 更新一般用戶角色權限 ===\n")
    
    with get_db() as db:
        # 獲取user角色
        user_role = db.query(Role).filter(Role.name == 'user').first()
        if not user_role:
            print("錯誤：找不到user角色")
            return
        
        # 一般用戶應該有的權限名稱（不包括admin和delete權限）
        user_permission_names = [
            'income_expense.view',
            'income_expense.create', 
            'income_expense.edit',
            'accounting.view',
            'reports.view',
            'reports.export',
            'asset_management.view',
            'asset_management.create',
            'asset_management.edit',
            'payroll.view',
            'payroll.create',
            'payroll.edit',
            'service_reward.view',
            'service_reward.create',
            'service_reward.edit',
            'fund_management.view',
            'fund_management.edit',
        ]
        
        # 獲取對應的權限ID
        user_permissions = db.query(Permission).filter(
            Permission.name.in_(user_permission_names)
        ).all()
        
        permission_ids = [p.id for p in user_permissions]
        
        print(f"找到 {len(user_permissions)} 個一般用戶權限")
        
        # 為user角色分配權限
        RoleService.assign_permissions_to_role(user_role.id, permission_ids)
        
        print(f"✓ 成功為user角色分配了 {len(permission_ids)} 個權限")
        
        print("\n分配的權限：")
        for perm in user_permissions:
            print(f"  - {perm.name} ({perm.display_name})")

def main():
    """主要執行函式"""
    try:
        # 1. 創建缺少的權限
        create_missing_admin_permissions()
        
        # 2. 更新管理員角色權限
        update_admin_role_permissions()
        
        # 3. 更新一般用戶角色權限
        update_user_role_permissions()
        
        print("\n>>> 權限創建和分配完成！")
        print(">>> 現在管理員和一般用戶應該有正確的權限，選單會根據角色顯示不同內容。")
        
    except Exception as e:
        print(f"執行過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()