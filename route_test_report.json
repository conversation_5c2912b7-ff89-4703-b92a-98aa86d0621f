{"timestamp": "2025-09-09T08:59:15.825408", "summary": {"total": 34, "success": 21, "redirects": 0, "auth_required": 0, "not_found": 11, "server_errors": 2, "connection_errors": 0, "avg_response_time": 0.008859985014971565, "success_rate": 61.76470588235294}, "results": [{"url": "http://localhost:5000/", "method": "GET", "status_code": 500, "response_time": 0.0061740875244140625, "error_message": ""}, {"url": "http://localhost:5000/overview", "method": "GET", "status_code": 404, "response_time": 0.007189035415649414, "error_message": ""}, {"url": "http://localhost:5000/income_expense", "method": "GET", "status_code": 404, "response_time": 0.003111124038696289, "error_message": ""}, {"url": "http://localhost:5000/income_expense/monthly_income_expense", "method": "GET", "status_code": 404, "response_time": 0.0027840137481689453, "error_message": ""}, {"url": "http://localhost:5000/new_income_expense", "method": "GET", "status_code": 404, "response_time": 0.0028772354125976562, "error_message": ""}, {"url": "http://localhost:5000/accounting/subject_manage", "method": "GET", "status_code": 200, "response_time": 0.*****************, "error_message": ""}, {"url": "http://localhost:5000/accounting/edit_account_subject", "method": "GET", "status_code": 404, "response_time": 0.003290891647338867, "error_message": ""}, {"url": "http://localhost:5000/voucher_manage", "method": "GET", "status_code": 500, "response_time": 0.*****************, "error_message": ""}, {"url": "http://localhost:5000/journal_validation", "method": "GET", "status_code": 200, "response_time": 0.014438152313232422, "error_message": ""}, {"url": "http://localhost:5000/department_manage", "method": "GET", "status_code": 200, "response_time": 0.007076740264892578, "error_message": ""}, {"url": "http://localhost:5000/project_manage", "method": "GET", "status_code": 200, "response_time": 0.008328914642333984, "error_message": ""}, {"url": "http://localhost:5000/payment_identity_list", "method": "GET", "status_code": 200, "response_time": 0.013737201690673828, "error_message": ""}, {"url": "http://localhost:5000/basic_info", "method": "GET", "status_code": 200, "response_time": 0.0067899227142333984, "error_message": ""}, {"url": "http://localhost:5000/account_setting", "method": "GET", "status_code": 200, "response_time": 0.003919124603271484, "error_message": ""}, {"url": "http://localhost:5000/opening_setting", "method": "GET", "status_code": 200, "response_time": 0.*****************, "error_message": ""}, {"url": "http://localhost:5000/payroll_process", "method": "GET", "status_code": 200, "response_time": 0.003973960876464844, "error_message": ""}, {"url": "http://localhost:5000/service_reward", "method": "GET", "status_code": 404, "response_time": 0.003490924835205078, "error_message": ""}, {"url": "http://localhost:5000/withholding_declare", "method": "GET", "status_code": 200, "response_time": 0.0038089752197265625, "error_message": ""}, {"url": "http://localhost:5000/reports", "method": "GET", "status_code": 200, "response_time": 0.*****************, "error_message": ""}, {"url": "http://localhost:5000/reports/monthly_report", "method": "GET", "status_code": 404, "response_time": 0.002783060073852539, "error_message": ""}, {"url": "http://localhost:5000/reports/balance_sheet", "method": "GET", "status_code": 200, "response_time": 0.043550968170166016, "error_message": ""}, {"url": "http://localhost:5000/reports/income_statement", "method": "GET", "status_code": 200, "response_time": 0.022462844848632812, "error_message": ""}, {"url": "http://localhost:5000/reports/cash_flow", "method": "GET", "status_code": 200, "response_time": 0.015825986862182617, "error_message": ""}, {"url": "http://localhost:5000/reports/trial_balance", "method": "GET", "status_code": 404, "response_time": 0.003219*************, "error_message": ""}, {"url": "http://localhost:5000/add_prepaid_expense", "method": "GET", "status_code": 200, "response_time": 0.009799003601074219, "error_message": ""}, {"url": "http://localhost:5000/fund_record/create", "method": "GET", "status_code": 200, "response_time": 0.011286020278930664, "error_message": ""}, {"url": "http://localhost:5000/transfer/add", "method": "GET", "status_code": 200, "response_time": 0.*****************, "error_message": ""}, {"url": "http://localhost:5000/bankloan/create", "method": "GET", "status_code": 200, "response_time": 0.009139060974121094, "error_message": ""}, {"url": "http://localhost:5000/api/bank_heads", "method": "GET", "status_code": 200, "response_time": 0.004239797592163086, "error_message": ""}, {"url": "http://localhost:5000/api/check_invoice_number?number=TEST123", "method": "GET", "status_code": 200, "response_time": 0.004089832305908203, "error_message": ""}, {"url": "http://localhost:5000/admin/errors/dashboard", "method": "GET", "status_code": 200, "response_time": 0.004754304885864258, "error_message": ""}, {"url": "http://localhost:5000/admin/performance", "method": "GET", "status_code": 404, "response_time": 0.003019094467163086, "error_message": ""}, {"url": "http://localhost:5000/admin/database/health", "method": "GET", "status_code": 404, "response_time": 0.0030641555786132812, "error_message": ""}, {"url": "http://localhost:5000/admin", "method": "GET", "status_code": 404, "response_time": 0.0029180049896240234, "error_message": ""}]}