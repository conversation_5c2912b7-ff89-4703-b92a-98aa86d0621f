# 🔗 系統管理連結參考

## 📋 **所有管理功能連結**

### **系統監控與效能**
| 功能 | 連結 | 說明 |
|------|------|------|
| 系統監控 | `/admin/monitoring` | CPU、記憶體、磁碟使用率監控 |
| 效能測試 | `/admin/performance_test` | 資料庫查詢效能測試工具 |
| 健康檢查 | `/admin/monitoring/health` | 系統健康狀態 API |
| 日誌查看 | `/admin/monitoring/logs` | 系統日誌查看 |

### **審計系統**
| 功能 | 連結 | 說明 |
|------|------|------|
| 審計儀表板 | `/audit_dashboard` | 審計統計和最近活動 |
| 審計搜尋 | `/audit_search` | 按條件搜尋審計記錄 |
| 已刪除記錄 | `/deleted_records` | 查看和復原軟刪除記錄 |

### **API 端點**
| 功能 | 連結 | 說明 |
|------|------|------|
| 系統統計 API | `/admin/monitoring/api/stats` | 系統統計數據 JSON |
| 效能測試 API | `/admin/performance_test/api/run_tests` | 執行效能測試 |
| 審計搜尋 API | `/api/audit_search` | 審計搜尋 API |
| 索引使用 API | `/admin/performance_test/api/index_usage` | 資料庫索引資訊 |
| 表格統計 API | `/admin/performance_test/api/table_stats` | 表格記錄統計 |

## 🎯 **快速訪問方式**

### **方式1：從側邊欄訪問**
1. 點擊側邊欄的「系統管理」
2. 選擇「監控與效能」分類
3. 點擊相應的功能按鈕

### **方式2：直接輸入網址**
在瀏覽器地址欄輸入：
```
http://localhost:5002/admin/monitoring
http://localhost:5002/admin/performance_test
http://localhost:5002/audit_dashboard
```

### **方式3：書籤收藏**
建議將常用的管理功能加入瀏覽器書籤：
- 🔖 系統監控
- 🔖 效能測試  
- 🔖 審計儀表板

## 📱 **行動裝置訪問**
所有管理功能都支援響應式設計，可在手機或平板上正常使用。

## 🔐 **權限說明**
- 目前所有管理功能都可直接訪問
- 建議未來可考慮添加管理員權限驗證

## 🆘 **故障排除**
如果無法訪問某個功能：
1. 確認 Flask 應用程式正在運行
2. 檢查控制台是否有錯誤訊息
3. 確認路由已正確註冊
4. 檢查模板文件是否存在

## 📝 **功能說明**

### **系統監控 (`/admin/monitoring`)**
- CPU、記憶體、磁碟使用率
- 應用程式效能統計
- 日誌文件管理
- 系統健康檢查

### **效能測試 (`/admin/performance_test`)**
- 資料庫查詢效能測試
- 索引使用情況分析
- 表格統計資訊
- 效能對比圖表

### **審計儀表板 (`/audit_dashboard`)**
- 今日新增記錄統計
- 本週修改記錄統計
- 已刪除記錄統計
- 最近活動記錄

### **審計搜尋 (`/audit_search`)**
- 按表格類型搜尋
- 按動作類型篩選（建立/修改/刪除）
- 按用戶名稱搜尋
- 按日期範圍篩選

### **已刪除記錄 (`/deleted_records`)**
- 查看軟刪除的收支記錄
- 查看軟刪除的帳戶
- 查看軟刪除的轉帳記錄
- 一鍵復原功能