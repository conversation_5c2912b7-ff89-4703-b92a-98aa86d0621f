# API 層標準化指南

## 📋 概述

本系統已實施統一的 API 響應格式標準化，提供一致的前後端協作體驗。所有 API 端點都遵循相同的響應結構和錯誤處理機制。

## 🎯 標準響應格式

### 成功響應格式
```json
{
    "success": true,
    "message": "操作成功",
    "data": { ... },
    "timestamp": "2024-03-15T10:30:45.123Z",
    "error": null,
    "meta": {
        "pagination": { ... },
        "performance": { ... }
    }
}
```

### 錯誤響應格式
```json
{
    "success": false,
    "message": "操作失敗原因",
    "data": null,
    "timestamp": "2024-03-15T10:30:45.123Z",
    "error": {
        "code": "ERROR_CODE",
        "details": { ... }
    }
}
```

### 分頁響應格式
```json
{
    "success": true,
    "message": "查詢成功",
    "data": [ ... ],
    "timestamp": "2024-03-15T10:30:45.123Z",
    "error": null,
    "meta": {
        "pagination": {
            "page": 1,
            "per_page": 20,
            "total": 150,
            "total_pages": 8,
            "has_next": true,
            "has_prev": false
        }
    }
}
```

## 🔧 使用方式

### 基本 API 響應
```python
from utils.web import APIResponse, APIStatus, APIErrorCode

# 成功響應
return APIResponse.success(
    data={"user_id": 123, "name": "張三"},
    message="用戶資料取得成功"
)

# 錯誤響應
return APIResponse.error(
    message="用戶不存在",
    error_code=APIErrorCode.NOT_FOUND,
    status_code=APIStatus.NOT_FOUND
)

# 分頁響應
return APIResponse.paginated(
    data=users,
    page=1,
    per_page=20,
    total=150,
    message="用戶列表查詢成功"
)
```

### API 路由裝飾器
```python
from utils.web import api_route, require_pagination

@api_bp.route('/users')
@api_route(methods=['GET'])
@require_pagination(max_per_page=50)
def get_users(page, per_page):
    """取得用戶列表 - 自動處理分頁參數"""
    # page 和 per_page 參數會自動注入
    users = get_user_list(page, per_page)
    total = get_user_count()
    
    return APIResponse.paginated(users, page, per_page, total)

@api_bp.route('/users', methods=['POST'])
@api_route(methods=['POST'], require_json=True)
def create_user():
    """創建用戶 - 要求 JSON Content-Type"""
    user_data = request.get_json()
    user = create_new_user(user_data)
    
    return APIResponse.created(user, "用戶創建成功")
```

## 📊 標準狀態碼

### 成功狀態碼
- `200 OK` - 請求成功
- `201 Created` - 資源創建成功
- `202 Accepted` - 請求已接受，正在處理
- `204 No Content` - 成功，無返回內容

### 客戶端錯誤
- `400 Bad Request` - 請求參數錯誤
- `401 Unauthorized` - 未授權
- `403 Forbidden` - 權限不足
- `404 Not Found` - 資源不存在
- `409 Conflict` - 資源衝突
- `422 Unprocessable Entity` - 資料驗證失敗
- `429 Too Many Requests` - 請求過於頻繁

### 服務器錯誤
- `500 Internal Server Error` - 服務器內部錯誤
- `501 Not Implemented` - 功能未實現
- `503 Service Unavailable` - 服務暫時不可用

## 🏷️ 標準錯誤代碼

### 通用錯誤
- `VALIDATION_ERROR` - 資料驗證錯誤
- `AUTHENTICATION_ERROR` - 認證錯誤
- `PERMISSION_DENIED` - 權限被拒
- `NOT_FOUND` - 資源不存在
- `CONFLICT` - 資源衝突
- `INTERNAL_ERROR` - 內部錯誤

### 業務錯誤
- `INSUFFICIENT_FUNDS` - 餘額不足
- `DUPLICATE_ENTRY` - 重複條目
- `INVALID_OPERATION` - 無效操作
- `DATA_INTEGRITY_ERROR` - 資料完整性錯誤

### 系統錯誤
- `DATABASE_ERROR` - 資料庫錯誤
- `EXTERNAL_SERVICE_ERROR` - 外部服務錯誤
- `RATE_LIMIT_EXCEEDED` - 超出速率限制

## 📝 API 端點示例

### 1. 銀行總行列表
```http
GET /api/bank_heads

# 響應
{
    "success": true,
    "message": "成功取得 15 個銀行總行",
    "data": {
        "001": "台灣銀行",
        "004": "台灣土地銀行",
        "005": "台灣企業銀行"
    },
    "timestamp": "2024-03-15T10:30:45.123Z",
    "error": null
}
```

### 2. 銀行分行列表
```http
GET /api/bank_branches/001

# 成功響應
{
    "success": true,
    "message": "成功取得總行 001 的 25 個分行",
    "data": {
        "0010001": "台銀總行營業部",
        "0010002": "台銀信義分行"
    },
    "timestamp": "2024-03-15T10:30:45.123Z",
    "error": null
}

# 錯誤響應
{
    "success": false,
    "message": "找不到總行代碼 '999' 的分行資料",
    "data": null,
    "timestamp": "2024-03-15T10:30:45.123Z",
    "error": {
        "code": "NOT_FOUND",
        "details": {}
    }
}
```

### 3. 發票號碼檢查
```http
GET /api/check_invoice_number?number=AB12345678

# 響應
{
    "success": true,
    "message": "發票號碼 AB12345678 可使用",
    "data": {
        "exists": false,
        "number": "AB12345678",
        "year": 2024
    },
    "timestamp": "2024-03-15T10:30:45.123Z",
    "error": null
}
```

## 🔨 遷移現有 API

### 步驟 1: 引入標準化工具
```python
from utils.web import APIResponse, api_route, APIStatus, APIErrorCode
```

### 步驟 2: 更新路由裝飾器
```python
# 舊方式
@app.route('/api/users')
def get_users():
    return jsonify(users)

# 新方式
@app.route('/api/users')
@api_route(methods=['GET'])
def get_users():
    return APIResponse.success(users, "用戶列表取得成功")
```

### 步驟 3: 統一錯誤處理
```python
# 舊方式
try:
    user = get_user(user_id)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    return jsonify(user)
except Exception as e:
    return jsonify({'error': str(e)}), 500

# 新方式
try:
    user = get_user(user_id)
    if not user:
        return APIResponse.not_found("用戶")
    return APIResponse.success(user, "用戶資料取得成功")
except Exception as e:
    return APIResponse.error(
        message="取得用戶資料失敗",
        error_code=APIErrorCode.DATABASE_ERROR,
        status_code=APIStatus.INTERNAL_ERROR
    )
```

## 🧪 測試範例

### 單元測試
```python
def test_api_success_response():
    response = APIResponse.success({"id": 1}, "測試成功")
    data = json.loads(response.data)
    
    assert data["success"] is True
    assert data["message"] == "測試成功"
    assert data["data"]["id"] == 1
    assert "timestamp" in data
    assert data["error"] is None

def test_api_error_response():
    response, status_code = APIResponse.error(
        message="測試錯誤",
        error_code="TEST_ERROR",
        status_code=400
    )
    data = json.loads(response.data)
    
    assert data["success"] is False
    assert data["message"] == "測試錯誤"
    assert data["error"]["code"] == "TEST_ERROR"
    assert status_code == 400
```

### 整合測試
```python
def test_bank_heads_api(client):
    response = client.get('/api/bank_heads')
    data = json.loads(response.data)
    
    assert response.status_code == 200
    assert data["success"] is True
    assert "data" in data
    assert isinstance(data["data"], dict)
```

## 📈 效益與優勢

1. **一致性**: 所有 API 響應格式統一，前端處理邏輯簡化
2. **可維護性**: 集中的錯誤處理和響應格式管理
3. **可讀性**: 清晰的錯誤代碼和狀態碼系統
4. **可擴展性**: 易於添加新的響應類型和錯誤處理
5. **測試性**: 標準化格式便於自動化測試
6. **調試性**: 豐富的錯誤詳情和時間戳資訊
7. **性能監控**: 內建響應時間追蹤功能

## 🔄 最佳實踐

1. **總是使用標準響應格式**: 避免直接使用 `jsonify()`
2. **提供有意義的錯誤訊息**: 幫助前端和用戶理解問題
3. **使用適當的 HTTP 狀態碼**: 遵循 RESTful API 標準
4. **添加適當的元資料**: 如分頁資訊、性能資訊等
5. **記錄詳細的錯誤資訊**: 便於問題排查和監控
6. **驗證輸入參數**: 使用內建的驗證器確保資料品質
7. **統一的日誌記錄**: 便於系統監控和維護

通過採用這套標準化系統，我們的 API 將更加穩定、一致和易於維護！