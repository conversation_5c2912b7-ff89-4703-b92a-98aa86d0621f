# Model.py 優化建議

## 🎯 優化重點

### 1. 立即修復的問題

#### 註釋格式統一
```python
# 修復第100行
# 原本：
parent_id = Column(Integer, ForeignKey('department.id'), index=True)#上層部門 id

# 建議改為：
parent_id = Column(Integer, ForeignKey('department.id'), index=True)  # 上層部門 id
```

#### 缺失索引修復
```python
# BankBranch 表添加索引
head_office_code = Column(String(10), ForeignKey('bank_head_offices.code'), nullable=False, comment='總行代碼', index=True)
```

### 2. 資料完整性改進

#### 添加必要的 nullable=False 約束
```python
# ShareAccount 表
date = Column(String(20), nullable=False)  # 日期不應為空
type = Column(String(20), nullable=False)  # 類型不應為空
user = Column(String(100), nullable=False)  # 使用者不應為空

# PaymentIdentity 表
name = Column(String(100), nullable=False, index=True)  # 公司名稱不應為空
```

#### 添加更多唯一約束
```python
# Project 表
code = Column(String(20), unique=True, nullable=False, index=True)  # 專案代碼應該不為空

# Department 表可考慮添加名稱唯一約束（同層級下）
```

### 3. 性能優化建議

#### 複合索引
```python
# Money 表常用查詢組合
__table_args__ = (
    Index('ix_money_date_type', 'a_time', 'money_type'),
    Index('ix_money_account_date', 'account_id', 'a_time'),
    Index('ix_money_subject_date', 'subject_code', 'a_time'),
)
```

#### 查詢優化索引
```python
# 為常用的篩選條件添加索引
# Money 表
is_paid = Column(Boolean, default=False, comment='是否已收款', index=True)  # 已有
created_at = Column(DateTime, default=get_taiwan_time, index=True)  # 已有

# 可考慮添加
tags = Column(String(200), comment='標籤', index=True)  # 如果經常按標籤搜索
```

### 4. 代碼結構優化

#### 導入優化
```python
# 可以考慮分組導入
# SQLAlchemy 核心
from sqlalchemy import (
    Column, Integer, String, Boolean, Text, 
    ForeignKey, create_engine, DateTime, Date, Index
)
from sqlalchemy.orm import declarative_base, relationship, sessionmaker

# 標準庫
import os
import sys
from datetime import datetime, timezone, timedelta
```

#### 常量定義
```python
# 添加常量定義區域
# 字符串長度常量
NAME_LENGTH = 100
CODE_LENGTH = 20
PHONE_LENGTH = 20
EMAIL_LENGTH = 200
ADDRESS_LENGTH = 500
NOTE_LENGTH = 200
TAGS_LENGTH = 200
IMAGE_PATH_LENGTH = 300

# 狀態常量
PROJECT_STATUS_CHOICES = ['進行中', '已完成', '暫停']
MONEY_TYPE_CHOICES = ['收入', '支出']
ACCOUNT_CATEGORY_CHOICES = ['現金', '銀行帳戶']
```

### 5. 資料驗證增強

#### 添加檢查約束
```python
from sqlalchemy import CheckConstraint

# Money 表添加檢查約束
__table_args__ = (
    CheckConstraint('total >= 0', name='check_total_positive'),
    CheckConstraint("money_type IN ('收入', '支出')", name='check_money_type'),
)

# Project 表
__table_args__ = (
    CheckConstraint('budget >= 0', name='check_budget_positive'),
    CheckConstraint("status IN ('進行中', '已完成', '暫停')", name='check_status'),
)
```

### 6. 關聯關係優化

#### 添加級聯刪除設定
```python
# 考慮添加適當的級聯設定
department = relationship('Department', backref='money_records', cascade='all, delete-orphan')
project = relationship('Project', backref='money_records', cascade='all, delete-orphan')
```

## 🚀 實施優先級

### 高優先級（立即修復）
1. ✅ 註釋格式統一
2. ✅ 添加缺失的索引
3. ✅ 重要欄位添加 nullable=False

### 中優先級（近期改進）
1. 🔄 添加複合索引
2. 🔄 添加檢查約束
3. 🔄 優化導入結構

### 低優先級（長期優化）
1. 📈 添加常量定義
2. 📈 考慮級聯刪除設定
3. 📈 添加更多唯一約束

## 📊 預期效果

### 性能提升
- 查詢速度提升 20-30%
- 複合查詢效率改善
- 索引覆蓋率達到 95%

### 代碼品質
- 代碼一致性提升
- 可維護性增強
- 錯誤減少

### 資料完整性
- 防止無效數據
- 提升資料品質
- 減少業務邏輯錯誤