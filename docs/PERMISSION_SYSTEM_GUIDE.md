# 🔐 權限管理系統使用指南

## 📋 系統概述

這個權限管理系統讓你可以為不同等級的用戶設定不同的功能模組訪問權限。系統採用 **角色-權限** 模式，支援靈活的權限控制。

## 🚀 快速開始

### 1. 初始化權限系統

```bash
python init_auth_system.py
```

這會建立：
- 權限相關資料表
- 基本權限和角色
- 管理員帳號 (admin/admin123)

### 2. 啟動應用程式

```bash
python main.py
```

### 3. 登入系統

訪問 `http://localhost:5001/auth/login`
- 用戶名: `admin`
- 密碼: `admin123`

## 🏗️ 系統架構

### 核心概念

1. **用戶 (User)** - 系統使用者
2. **角色 (Role)** - 用戶的身份角色
3. **權限 (Permission)** - 具體的功能權限
4. **模組 (Module)** - 功能模組分組

### 權限層級

```
用戶 → 角色 → 權限 → 模組功能
```

## 📊 預設角色設定

### 系統管理員 (admin)
- **權限**: 所有功能的完整權限
- **適用**: 系統管理員、IT人員

### 一般用戶 (user)  
- **權限**: 基本的查看和建立權限
- **適用**: 一般員工、操作人員

## 🎯 模組權限對應

| 模組 | 權限代碼 | 說明 |
|------|----------|------|
| 收支帳簿 | `income_expense.*` | 收入支出記錄管理 |
| 資金管理 | `fund_management.*` | 資金轉移、銀行借款 |
| 資產管理 | `asset_management.*` | 固定資產、攤提管理 |
| 薪資報酬 | `payroll.*` | 員工薪資、勞健保 |
| 勞務報酬 | `service_reward.*` | 外包勞務報酬 |
| 我的報表 | `reports.*` | 各種財務報表 |
| 會計科目 | `accounting.*` | 科目管理、傳票 |
| 扣繳申報 | `tax_declaration.*` | 稅務申報 |
| 設定 | `settings.*` | 系統設定 |

## 🔧 權限操作類型

- **view** - 查看權限
- **create** - 建立權限  
- **edit** - 編輯權限
- **delete** - 刪除權限
- **manage** - 管理權限
- **export** - 匯出權限

## 👥 用戶管理

### 建立新用戶

1. 登入管理員帳號
2. 訪問 `/admin/permissions/`
3. 在用戶管理區域建立新用戶
4. 分配適當的角色

### 角色分配

```python
# 範例：為用戶分配角色
from services.auth_service import RoleService

# 為用戶ID=2分配一般用戶角色
RoleService.assign_role_to_user(user_id=2, role_ids=[2])
```

## 🛡️ 在路由中使用權限控制

### 基本用法

```python
from utils.auth_decorators import login_required, permission_required, module_required

# 需要登入
@app.route('/dashboard')
@login_required
def dashboard():
    return render_template('dashboard.html')

# 需要特定權限
@app.route('/income/create')
@permission_required('income_expense.create')
def create_income():
    return render_template('create_income.html')

# 需要模組訪問權限
@app.route('/reports')
@module_required('reports')
def reports():
    return render_template('reports.html')
```

### 角色控制

```python
from utils.auth_decorators import role_required, admin_required

# 需要特定角色
@app.route('/manager-panel')
@role_required(['manager', 'admin'])
def manager_panel():
    return render_template('manager_panel.html')

# 需要管理員權限
@app.route('/admin')
@admin_required
def admin_panel():
    return render_template('admin.html')
```

## 🎨 前端選單過濾

系統會自動根據用戶權限過濾選單：

```python
from services.menu_service import MenuService

# 獲取用戶可訪問的選單
user_menu = MenuService.get_user_menu(user_id)

# 檢查用戶是否可訪問特定功能
can_access = MenuService.user_can_access_function(user_id, '新增帳務')
```

## 📝 自訂權限

### 建立新權限

```python
from services.auth_service import PermissionService

# 建立新權限
permission = PermissionService.create_permission(
    name="custom_module.special_action",
    display_name="特殊操作權限",
    module="custom_module", 
    action="special_action",
    description="允許執行特殊操作"
)
```

### 建立新角色

```python
from services.auth_service import RoleService

# 建立新角色
role = RoleService.create_role(
    name="supervisor",
    display_name="主管",
    description="部門主管角色"
)

# 為角色分配權限
RoleService.assign_permissions_to_role(role.id, [1, 2, 3])
```

## 🔍 權限檢查

### 在程式中檢查權限

```python
from services.auth_service import AuthService

# 檢查用戶是否有特定權限
has_permission = AuthService.user_has_permission(user_id, 'reports.view')

# 獲取用戶所有權限
user_permissions = AuthService.get_user_permissions(user_id)

# 獲取用戶可訪問的模組
user_modules = AuthService.get_user_modules(user_id)
```

## 🎯 實際應用場景

### 場景1：一人公司版
- 只需要 `admin` 角色
- 開放所有功能模組
- 簡化權限控制

### 場景2：五人公司版
- `admin` - 老闆/會計師
- `manager` - 部門主管  
- `employee` - 一般員工
- `viewer` - 只能查看報表

### 場景3：大型企業版
- 細分更多角色
- 按部門分配權限
- 專案別權限控制

## 🚨 注意事項

1. **安全性**
   - 定期更換管理員密碼
   - 監控用戶登入狀況
   - 定期檢查權限分配

2. **效能**
   - 權限檢查會增加查詢負擔
   - 考慮使用快取機制
   - 避免過度細分權限

3. **維護性**
   - 定期清理無效會話
   - 備份權限設定
   - 記錄權限變更日誌

## 🔧 故障排除

### 常見問題

1. **無法登入**
   - 檢查用戶是否啟用 (`is_active=True`)
   - 確認密碼正確
   - 檢查會話是否過期

2. **權限不生效**
   - 確認角色權限分配正確
   - 檢查裝飾器使用是否正確
   - 清除瀏覽器快取

3. **選單不顯示**
   - 檢查 `MenuService` 權限映射
   - 確認用戶有對應模組權限
   - 檢查選單資料結構

### 除錯指令

```python
# 檢查用戶權限
python -c "
from services.auth_service import AuthService
print(AuthService.get_user_permissions(1))
"

# 檢查用戶角色
python -c "
from database import get_db
from model import User
with get_db() as db:
    user = db.query(User).filter(User.id==1).first()
    print(user.get_roles())
"
```

## 📚 進階功能

### 動態權限控制
- 根據資料擁有者控制權限
- 時間範圍權限控制
- IP 位址限制

### 審計日誌
- 記錄權限變更
- 追蹤用戶操作
- 安全事件監控

---

**🎉 恭喜！你已經成功設置了完整的權限管理系統！**

現在你可以：
- 為不同用戶設定不同的功能模組訪問權限
- 靈活控制誰可以看到哪些功能
- 輕鬆管理用戶角色和權限

如有問題，請參考程式碼註解或聯繫開發團隊。