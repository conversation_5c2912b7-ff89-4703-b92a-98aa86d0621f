# Bulma統一主題使用指南

## 概述

我們已經為印錢大師系統實施了基於Bulma框架的統一主題設計，確保所有頁面都有一致的外觀和用戶體驗。

## 主要改進

### 1. 統一的CSS框架
- **新文件**: `static/css/bulma-unified-theme.css`
- **替換**: `static/css/modern-theme.css`
- 基於Bulma 0.9.4版本
- 包含自定義CSS變數和組件樣式

### 2. 基礎模板系統
- **新文件**: `templates/base_layout.html`
- 所有頁面現在繼承自這個基礎模板
- 統一的側邊欄、頁面結構和樣式

### 3. 更新的模板文件
以下模板文件已更新為使用新的統一主題：
- `index.html` - 主儀表板
- `balance_sheet.html` - 資產負債表
- `account_list.html` - 帳戶列表
- `income_statement.html` - 損益表
- `trial_balance.html` - 試算表
- `reports_dashboard.html` - 報表儀表板
- `expense_record.html` - 支出紀錄
- `income_record.html` - 收入紀錄
- `transfer_list.html` - 移轉列表
- `subject_manage.html` - 科目管理
- `employee_list.html` - 員工列表
- `asset_list.html` - 資產列表
- `company_setting.html` - 公司設定
- `tax_manage.html` - 稅務管理
- `insurance_manage.html` - 保險管理
- `salary_setting.html` - 薪資設定
- `payroll_process.html` - 薪資處理

## 主要特色

### 1. 色彩系統
```css
--primary-color: #3273dc;     /* 主要藍色 */
--success-color: #48c774;     /* 成功綠色 */
--warning-color: #ffdd57;     /* 警告黃色 */
--danger-color: #f14668;      /* 危險紅色 */
--info-color: #3298dc;        /* 資訊藍色 */
```

### 2. 統一組件

#### 卡片樣式
```html
<div class="card">
    <div class="card-header">
        <p class="card-header-title">標題</p>
    </div>
    <div class="card-content">
        內容
    </div>
</div>
```

#### 摘要卡片
```html
<div class="summary-card">
    <div class="summary-card-title">標題</div>
    <div class="summary-card-value">數值</div>
</div>
```

#### 表格樣式
```html
<table class="table">
    <thead>
        <tr><th>標題</th></tr>
    </thead>
    <tbody>
        <tr><td>內容</td></tr>
    </tbody>
</table>
```

#### 特殊表格（如資產負債表）
```html
<table class="balance-sheet-table">
    <thead>
        <tr>
            <th class="assets-header">資產</th>
            <th class="liabilities-header">負債</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="amount-cell">1,234,567</td>
            <td class="percentage-cell">12.5%</td>
        </tr>
    </tbody>
</table>
```

### 3. 側邊欄系統
- 固定位置側邊欄
- 子選單彈出功能
- 響應式設計支援
- 平滑動畫效果

### 4. 響應式設計
- 桌面版：固定側邊欄 + 主內容區
- 平板版：可收合側邊欄
- 手機版：全螢幕側邊欄

## 如何創建新頁面

### 1. 基本模板結構
```html
{% extends "base_layout.html" %}

{% block title %}頁面標題 - 印錢大師{% endblock %}

{% block page_title %}頁面標題{% endblock %}

{% block content %}
<!-- 頁面內容 -->
{% endblock %}
```

### 2. 添加自定義CSS
```html
{% block extra_css %}
<style>
/* 頁面特定樣式 */
</style>
{% endblock %}
```

### 3. 添加自定義JavaScript
```html
{% block extra_js %}
<script>
// 頁面特定JavaScript
</script>
{% endblock %}
```

## 常用CSS類別

### 工具類別
- `.text-right` - 右對齊文字
- `.text-center` - 置中文字
- `.font-mono` - 等寬字體
- `.font-weight-bold` - 粗體
- `.shadow-light` - 淺陰影
- `.shadow-medium` - 中等陰影

### 動畫類別
- `.fade-in` - 淡入動畫
- `.slide-in-left` - 左滑入動畫

### 狀態類別
- `.is-loading` - 載入狀態

## 維護建議

### 1. 保持一致性
- 使用統一的色彩變數
- 遵循Bulma的命名規範
- 使用預定義的組件樣式

### 2. 響應式考量
- 測試不同螢幕尺寸
- 使用Bulma的響應式類別
- 確保觸控友好

### 3. 效能優化
- 避免內聯樣式
- 使用CSS變數
- 最小化自定義CSS

## 瀏覽器支援
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 更新記錄
- 2024-01-15: 初始實施統一Bulma主題
- 批量更新15個模板文件
- 創建基礎模板系統
- 實施響應式側邊欄