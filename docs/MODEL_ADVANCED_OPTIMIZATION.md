# 🚀 Model.py 進階優化建議

## 🎯 **高優先級優化**

### 1. **資料類型優化**

#### **A. 金額欄位精度問題**
```python
# 現在：使用 Integer（可能溢出，無法處理小數）
total = Column(Integer)

# 建議：使用 DECIMAL 或 NUMERIC
from sqlalchemy import DECIMAL
total = Column(DECIMAL(15, 2), comment='金額（最大13位整數+2位小數）')
# 或者如果確定只用整數：
total = Column(BIGINT, comment='金額（分為單位，避免浮點誤差）')
```

#### **B. 日期時間標準化**
```python
# 現在：混用 Date, DateTime, String
date = Column(String(20))  # 不好
a_time = Column(Date)      # 好
created_at = Column(DateTime)  # 好

# 建議：統一使用適當類型
invoice_date = Column(Date, comment='發票日期')  # 只需日期
created_at = Column(DateTime(timezone=True), comment='建立時間')  # 需要時區
```

#### **C. 布林值預設值**
```python
# 現在：部分沒有預設值
is_paid = Column(Boolean)

# 建議：所有布林值都有預設值
is_paid = Column(Boolean, default=False, nullable=False, comment='是否已付款')
```

### 2. **索引策略進階優化**

#### **A. 部分索引（條件索引）**
```python
# 針對常用查詢條件建立部分索引
class Money(Base):
    __table_args__ = (
        # 只為未刪除的記錄建立索引
        Index('ix_money_active_date', 'a_time', 
              postgresql_where=text('is_deleted = false')),
        # 只為未付款記錄建立索引
        Index('ix_money_unpaid', 'should_paid_date', 
              postgresql_where=text('is_paid = false')),
    )
```

#### **B. 覆蓋索引（包含索引）**
```python
# 包含常用查詢欄位，避免回表查詢
Index('ix_money_cover', 'account_id', 'a_time', 
      postgresql_include=['total', 'money_type', 'name'])
```

### 3. **關聯關係優化**

#### **A. 延遲載入策略**
```python
# 現在：預設載入
subject = relationship('AccountSubject', backref='accounts')

# 建議：根據使用場景優化
subject = relationship('AccountSubject', 
                      lazy='select',  # 需要時才載入
                      backref=backref('accounts', lazy='dynamic'))  # 大量關聯時使用查詢物件
```

#### **B. 關聯載入優化**
```python
# 預載入常用關聯
money_with_account = session.query(Money).options(
    joinedload(Money.account),
    joinedload(Money.subject)
).all()
```

### 4. **資料驗證增強**

#### **A. 自定義驗證器**
```python
from sqlalchemy.orm import validates

class Money(Base):
    @validates('total')
    def validate_total(self, key, value):
        if value is not None and value < 0:
            raise ValueError('金額不能為負數')
        return value
    
    @validates('money_type')
    def validate_money_type(self, key, value):
        allowed_types = ['收入', '支出', '轉帳']
        if value not in allowed_types:
            raise ValueError(f'收支類型必須是: {allowed_types}')
        return value
```

#### **B. 複雜業務邏輯驗證**
```python
from sqlalchemy import event

@event.listens_for(Money, 'before_insert')
@event.listens_for(Money, 'before_update')
def validate_money_logic(mapper, connection, target):
    # 驗證收入必須有正數金額
    if target.money_type == '收入' and target.total <= 0:
        raise ValueError('收入金額必須大於0')
    
    # 驗證付款日期邏輯
    if target.paid_date and target.should_paid_date:
        if target.paid_date < target.should_paid_date:
            raise ValueError('付款日期不能早於應付日期')
```

## 🔧 **中優先級優化**

### 5. **效能優化**

#### **A. 查詢優化方法**
```python
class Money(Base):
    @classmethod
    def get_monthly_summary(cls, year, month):
        """月度匯總查詢"""
        return session.query(
            cls.money_type,
            func.sum(cls.total).label('total_amount'),
            func.count(cls.id).label('count')
        ).filter(
            extract('year', cls.a_time) == year,
            extract('month', cls.a_time) == month,
            cls.is_deleted == False
        ).group_by(cls.money_type).all()
    
    @classmethod
    def get_account_balance(cls, account_id, date=None):
        """計算帳戶餘額"""
        query = session.query(func.sum(
            case(
                (cls.money_type == '收入', cls.total),
                (cls.money_type == '支出', -cls.total),
                else_=0
            )
        )).filter(
            cls.account_id == account_id,
            cls.is_deleted == False
        )
        
        if date:
            query = query.filter(cls.a_time <= date)
            
        return query.scalar() or 0
```

#### **B. 快取策略**
```python
from functools import lru_cache

class AccountSubject(Base):
    @classmethod
    @lru_cache(maxsize=128)
    def get_by_code(cls, code):
        """快取科目查詢"""
        return session.query(cls).filter(cls.code == code).first()
    
    @classmethod
    def clear_cache(cls):
        """清除快取"""
        cls.get_by_code.cache_clear()
```

### 6. **資料完整性增強**

#### **A. 軟刪除改進**
```python
class SoftDeleteMixin:
    """軟刪除混入類"""
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True))
    deleted_by = Column(String(100))
    
    @classmethod
    def active_query(cls):
        """只查詢未刪除記錄"""
        return session.query(cls).filter(cls.is_deleted == False)
    
    def soft_delete(self, user_id=None):
        """軟刪除"""
        self.is_deleted = True
        self.deleted_at = get_taiwan_time()
        self.deleted_by = user_id
    
    def restore(self):
        """復原"""
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None

# 使用混入類
class Money(Base, SoftDeleteMixin):
    # ... 其他欄位
```

#### **B. 版本控制改進**
```python
class VersionMixin:
    """版本控制混入類"""
    version = Column(Integer, default=1, nullable=False)
    
    def increment_version(self):
        """增加版本號"""
        self.version = (self.version or 0) + 1

# 自動版本控制
@event.listens_for(Money, 'before_update')
def increment_version(mapper, connection, target):
    if hasattr(target, 'increment_version'):
        target.increment_version()
```

### 7. **安全性增強**

#### **A. 敏感資料加密**
```python
from cryptography.fernet import Fernet
import base64

class EncryptedType(TypeDecorator):
    """加密欄位類型"""
    impl = String
    
    def __init__(self, secret_key, *args, **kwargs):
        self.secret_key = secret_key
        self.fernet = Fernet(secret_key)
        super().__init__(*args, **kwargs)
    
    def process_bind_param(self, value, dialect):
        if value is not None:
            return self.fernet.encrypt(value.encode()).decode()
        return value
    
    def process_result_value(self, value, dialect):
        if value is not None:
            return self.fernet.decrypt(value.encode()).decode()
        return value

# 使用加密欄位
class PaymentIdentity(Base):
    tax_id = Column(EncryptedType(secret_key), comment='加密統編')
    bank_account = Column(EncryptedType(secret_key), comment='加密帳號')
```

## 🎨 **低優先級但重要的優化**

### 8. **程式碼組織**

#### **A. 模型分離**
```python
# models/base.py
class BaseModel(Base):
    """基礎模型類"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True)
    created_at = Column(DateTime(timezone=True), default=get_taiwan_time)
    updated_at = Column(DateTime(timezone=True), default=get_taiwan_time, onupdate=get_taiwan_time)

# models/accounting.py
from .base import BaseModel

class Money(BaseModel):
    __tablename__ = 'money'
    # ... 欄位定義

# models/hr.py
class Employee(BaseModel):
    __tablename__ = 'employees'
    # ... 欄位定義
```

#### **B. 配置外部化**
```python
# config/database.py
class DatabaseConfig:
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL', 'sqlite:///app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'echo': os.getenv('SQL_DEBUG', 'false').lower() == 'true'
    }
```

### 9. **監控和日誌**

#### **A. 查詢監控**
```python
from sqlalchemy import event
import logging

logger = logging.getLogger('sqlalchemy.performance')

@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    context._query_start_time = time.time()

@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - context._query_start_time
    if total > 0.1:  # 記錄慢查詢
        logger.warning(f"Slow query: {total:.3f}s - {statement[:100]}...")
```

#### **B. 資料變更日誌**
```python
class AuditLog(Base):
    """資料變更日誌"""
    __tablename__ = 'audit_logs'
    
    id = Column(Integer, primary_key=True)
    table_name = Column(String(50), nullable=False)
    record_id = Column(Integer, nullable=False)
    action = Column(String(20), nullable=False)  # INSERT/UPDATE/DELETE
    old_values = Column(JSON)
    new_values = Column(JSON)
    user_id = Column(String(100))
    timestamp = Column(DateTime(timezone=True), default=get_taiwan_time)

# 自動記錄變更
@event.listens_for(Money, 'after_insert')
@event.listens_for(Money, 'after_update')
@event.listens_for(Money, 'after_delete')
def log_changes(mapper, connection, target):
    # 記錄變更到 audit_logs
    pass
```

### 10. **測試支援**

#### **A. 測試資料工廠**
```python
# tests/factories.py
import factory
from factory.alchemy import SQLAlchemyModelFactory

class AccountFactory(SQLAlchemyModelFactory):
    class Meta:
        model = Account
        sqlalchemy_session_persistence = "commit"
    
    name = factory.Sequence(lambda n: f"測試帳戶{n}")
    category = factory.Iterator(['現金', '銀行帳戶'])
    is_default = False

class MoneyFactory(SQLAlchemyModelFactory):
    class Meta:
        model = Money
        sqlalchemy_session_persistence = "commit"
    
    name = factory.Faker('sentence', nb_words=3)
    total = factory.Faker('random_int', min=1, max=100000)
    money_type = factory.Iterator(['收入', '支出'])
    account = factory.SubFactory(AccountFactory)
```

## 📋 **實施優先順序建議**

### **立即實施（1週內）**
1. 資料類型標準化（DECIMAL、布林預設值）
2. 基本驗證器添加
3. 軟刪除混入類

### **短期實施（1個月內）**
1. 索引策略優化
2. 關聯載入優化
3. 查詢方法添加

### **中期實施（3個月內）**
1. 敏感資料加密
2. 審計日誌系統
3. 效能監控

### **長期實施（6個月內）**
1. 模型重構分離
2. 完整測試覆蓋
3. 快取策略實施

這些優化將大幅提升你的資料庫系統的效能、安全性和可維護性！