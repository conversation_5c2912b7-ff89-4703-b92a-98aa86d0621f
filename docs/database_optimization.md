# 資料庫查詢優化方案

## 1. 主要性能問題

### 🔴 問題 1: 資產負債表載入全部交易記錄
**位置**: `services/balance_sheet_service.py:27-31`
```python
# 現有問題代碼
transactions = db.query(Money).options(
    joinedload(Money.account)
).filter(
    Money.a_time <= report_date
).all()  # 載入所有記錄到記憶體
```

**優化方案 A - 使用聚合查詢**：
```python
# 直接在資料庫層級聚合
from sqlalchemy import func

# 按科目分組計算總額
subject_totals = db.query(
    Money.subject_code,
    func.sum(Money.lend).label('total_lend'),
    func.sum(Money.borrow).label('total_borrow')
).filter(
    Money.a_time <= report_date
).group_by(Money.subject_code).all()
```

**優化方案 B - 分批處理**：
```python
# 分批載入，每批 1000 筆
BATCH_SIZE = 1000
offset = 0
subject_balances = {}

while True:
    batch = db.query(Money).options(
        joinedload(Money.account)
    ).filter(
        Money.a_time <= report_date
    ).limit(BATCH_SIZE).offset(offset).all()
    
    if not batch:
        break
    
    # 處理這批資料
    for transaction in batch:
        # 處理邏輯...
    
    offset += BATCH_SIZE
```

### 🔴 問題 2: N+1 查詢問題
**位置**: `routes/permission_admin.py:18-51`
```python
# 現有問題：分別查詢 roles, permissions, users
for role in db.query(Role).filter(Role.is_active).all():
    # 可能觸發額外查詢
```

**優化方案 - 使用 eager loading**：
```python
# 一次載入所有關聯資料
roles = db.query(Role).options(
    joinedload(Role.permissions),
    joinedload(Role.users)
).filter(Role.is_active).all()
```

### 🔴 問題 3: 重複查詢相同資料
**位置**: `routes/income_expense.py:823`
```python
# 每次請求都查詢一次
identity_ids = [i.id for i in db.query(PaymentIdentity).filter(PaymentIdentity.type_id == payment_identity_type).all()]
```

**優化方案 - 使用快取**：
```python
from functools import lru_cache

@lru_cache(maxsize=128)
def get_identity_ids_by_type(type_id):
    with get_db() as db:
        return [i.id for i in db.query(PaymentIdentity).filter(
            PaymentIdentity.type_id == type_id
        ).all()]
```

## 2. 資料庫索引優化

### 建立必要索引的 Alembic 遷移腳本
```python
# migrations/add_performance_indexes.py
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Money 表索引
    op.create_index('ix_money_a_time', 'money', ['a_time'])
    op.create_index('ix_money_subject_code', 'money', ['subject_code'])
    op.create_index('ix_money_time_subject', 'money', ['a_time', 'subject_code'])
    
    # Transaction 表索引
    op.create_index('ix_transaction_date', 'transaction', ['transaction_date'])
    op.create_index('ix_transaction_type_date', 'transaction', ['transaction_type', 'transaction_date'])
    op.create_index('ix_transaction_payment_identity', 'transaction', ['payment_identity_id'])
    
    # Account 表索引（如果還沒有）
    op.create_index('ix_account_tenant_deleted', 'account', ['tenant_id', 'is_deleted'])
    
    # PaymentIdentity 表索引
    op.create_index('ix_payment_identity_type', 'payment_identity', ['type_id'])

def downgrade():
    op.drop_index('ix_money_a_time', 'money')
    op.drop_index('ix_money_subject_code', 'money')
    op.drop_index('ix_money_time_subject', 'money')
    op.drop_index('ix_transaction_date', 'transaction')
    op.drop_index('ix_transaction_type_date', 'transaction')
    op.drop_index('ix_transaction_payment_identity', 'transaction')
    op.drop_index('ix_account_tenant_deleted', 'account')
    op.drop_index('ix_payment_identity_type', 'payment_identity')
```

## 3. 實施步驟

### 第一階段：快速優化（立即可做）
1. **添加資料庫索引**
   ```bash
   # 創建遷移檔案
   alembic revision -m "add_performance_indexes"
   # 執行遷移
   alembic upgrade head
   ```

2. **優化 balance_sheet_service.py**
   - 將 `.all()` 改為聚合查詢
   - 減少記憶體使用

### 第二階段：中期優化
1. **實施查詢快取**
   - 對常用查詢結果進行快取
   - 設定適當的快取過期時間

2. **優化 N+1 查詢**
   - 使用 `joinedload()` 或 `selectinload()`
   - 減少資料庫往返次數

### 第三階段：長期優化
1. **實施查詢分析器**
   - 監控慢查詢
   - 自動告警

2. **資料分區**
   - 對大表進行分區（如 Money, Transaction）
   - 按時間範圍分區

## 4. 預期效果

| 優化項目 | 預期改善 | 影響範圍 |
|---------|---------|---------|
| 添加索引 | 查詢速度提升 50-80% | 全系統 |
| 聚合查詢 | 記憶體使用減少 70% | 報表生成 |
| 消除 N+1 | 減少 90% 資料庫查詢 | 權限管理 |
| 查詢快取 | 響應時間減少 60% | 常用功能 |

## 5. 監控指標

```python
# utils/query_monitor.py
import time
import logging
from functools import wraps

def monitor_query_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        elapsed = time.time() - start_time
        
        if elapsed > 1.0:  # 超過 1 秒的查詢
            logging.warning(f"Slow query in {func.__name__}: {elapsed:.2f}s")
        
        return result
    return wrapper
```

## 6. 注意事項

1. **測試優化效果**
   - 優化前先備份資料庫
   - 在測試環境驗證
   - 監控優化後的性能

2. **逐步實施**
   - 不要一次改動太多
   - 每個優化都要測試
   - 保留回滾方案

3. **持續監控**
   - 使用性能監控工具
   - 定期檢查慢查詢日誌
   - 根據實際使用調整索引