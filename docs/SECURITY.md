# 安全配置指南

## 🔐 重要安全設定

### 1. SECRET_KEY 設定

**⚠️ 重要：生產環境必須設置 SECRET_KEY 環境變數！**

#### 生成安全的 SECRET_KEY：
```bash
# 方法 1：使用 Python
python -c "import secrets; print(secrets.token_hex(32))"

# 方法 2：使用 OpenSSL
openssl rand -hex 32

# 方法 3：使用系統隨機數
head -c 32 /dev/urandom | base64
```

#### 設置環境變數：
```bash
# Linux/macOS
export SECRET_KEY="your-generated-secret-key-here"

# Windows
set SECRET_KEY=your-generated-secret-key-here

# 或在 .env 文件中設置
echo "SECRET_KEY=your-generated-secret-key-here" > .env
```

### 2. 生產環境部署檢查清單

#### 必須設置的環境變數：
- [ ] `SECRET_KEY` - Flask 安全密鑰
- [ ] `ADMIN_PASSWORD` - 管理員密碼
- [ ] `FLASK_ENV=production` - 生產環境模式
- [ ] `FLASK_DEBUG=False` - 關閉除錯模式

### 2. 管理員密碼設定

**⚠️ 重要：生產環境必須設置 ADMIN_PASSWORD 環境變數！**

```bash
# 設置管理員密碼
export ADMIN_PASSWORD="your-secure-admin-password"

# 或在 .env 文件中設置
echo "ADMIN_PASSWORD=your-secure-admin-password" >> .env
```

**安全建議**：
- 使用強密碼（至少12位，包含大小寫字母、數字、特殊字符）
- 定期更換密碼
- 不要在代碼中硬編碼密碼

#### 安全設定：
- [ ] 使用 HTTPS
- [ ] 設置安全的 Cookie 屬性
- [ ] 配置防火牆
- [ ] 定期備份資料庫
- [ ] 監控日誌文件

### 3. 資料庫安全

#### 備份策略：
- 定期自動備份
- 加密備份文件
- 異地存儲備份

#### 存取控制：
- 限制資料庫文件權限
- 使用強密碼
- 定期更新密碼

### 4. 應用程式安全

#### 輸入驗證：
- 所有用戶輸入都經過驗證
- 防止 SQL 注入
- 防止 XSS 攻擊

#### 會話管理：
- 安全的會話 Cookie
- 會話超時設定
- 登出時清除會話

### 5. 監控和日誌

#### 安全事件監控：
- 登入失敗記錄
- 異常存取模式
- 系統錯誤追蹤

#### 日誌管理：
- 定期輪轉日誌
- 安全存儲日誌
- 日誌分析和警報

## 🚨 安全事件回應

### 如果懷疑安全漏洞：

1. **立即行動**：
   - 更改所有密碼和密鑰
   - 檢查系統日誌
   - 隔離受影響的系統

2. **評估影響**：
   - 確定受影響的資料
   - 評估潛在損失
   - 通知相關人員

3. **修復和恢復**：
   - 修補安全漏洞
   - 恢復系統功能
   - 加強監控

## 📞 聯絡資訊

如發現安全問題，請立即聯絡系統管理員。
