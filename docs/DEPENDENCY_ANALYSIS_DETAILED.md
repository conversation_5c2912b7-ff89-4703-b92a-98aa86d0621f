# Python 專案依賴關係詳細分析報告

## 執行摘要

該 Python 會計系統專案包含 **153個Python檔案**，其中 **70個檔案** (45.8%) 被實際使用，**84個檔案** 可能未被使用。

## 1. 核心檔案分析

### 1.1 主入口點
- **main.py**: 主應用程式入口點，包含Flask應用初始化和所有Blueprint註冊

### 1.2 直接依賴核心檔案
| 檔案 | 類型 | 用途 |
|------|------|------|
| `/config/config.py` | 配置 | 應用配置管理 |
| `/database.py` | 數據庫 | 數據庫連接和初始化 |
| `/model.py` | 模型 | 主要數據模型定義 |

## 2. 模組使用分析

### 2.1 Routes 模組 (28/31 檔案被使用，90.3%使用率)

#### 被使用的Routes:
- `main.py` - 主頁路由
- `income_expense.py` - 收支記錄 (45KB, 較大檔案)
- `settings.py` - 系統設定 (30KB)
- `reports.py` - 報表功能 (29KB)
- `permission_admin.py` - 權限管理 (17KB)
- `payroll.py` - 薪資管理 (14KB)
- `bankloan.py` - 銀行借款 (12KB)
- `performance_dashboard.py` - 性能監控面板 (11KB)
- `performance_test.py` - 性能測試 (11KB)
- `fund_record.py` - 資金記錄 (8KB)
- `api.py` - API路由 (7KB)
- `transfer.py` - 資金轉移 (7KB)
- `audit.py` - 審計功能 (8KB)
- `database_monitor.py` - 數據庫監控 (7KB)
- `new_reports.py` - 新版報表 (10KB)
- `journal_validation.py` - 日記帳驗證 (5KB)
- `share_account.py` - 股份帳戶 (4KB)
- `debug_balance_sheet.py` - 資產負債表除錯 (4KB)
- `error_monitor.py` - 錯誤監控 (2KB)
- `monitoring.py` - 系統監控 (5KB)
- `account.py` - 帳戶管理 (12KB)
- `accounting.py` - 會計功能 (6KB)
- `auth.py` - 認證 (1KB)
- `admin.py` - 管理功能 (1KB)
- `assets.py` - 資產管理 (2KB)
- `service_reward.py` - 服務獎金 (14KB)
- `accounting_other.py` - 其他會計功能 (677B)
- `base_route.py` - 基礎路由類 (10KB)

#### 未被使用的Routes:
- `__init__.py` - 空的初始化檔案
- `modal_demo.py` - 模態框演示 (316B, 演示用)
- `new_income_expense.py` - 新版收支記錄 (10KB, 可能是舊版本)
- `payment_identity_type.py` - 付款身份類型 (8KB, 重複功能)

### 2.2 Utils 模組 (21/29 檔案被使用，72.4%使用率)

#### 被使用的Utils:
- `performance_monitor.py` - 性能監控核心
- `error_handler.py` - 全域錯誤處理 (13KB)
- `error_monitor.py` - 錯誤監控 (10KB)
- `error_test.py` - 錯誤測試 (15KB)
- `cache_manager.py` - 快取管理 (7KB)
- `db_pool_monitor.py` - 數據庫連接池監控 (9KB)
- `advanced_cache.py` - 進階快取 (16KB)
- `memory_optimizer.py` - 記憶體優化 (17KB)
- `query_optimizer.py` - 查詢優化 (17KB)
- `performance_benchmarking.py` - 性能基準測試 (19KB)
- `alert_system.py` - 告警系統 (17KB)
- `db_maintenance.py` - 數據庫維護 (17KB)
- `audit_helper.py` - 審計助手 (3KB)
- `auth_decorators.py` - 認證裝飾器 (3KB)
- `menu_decorator.py` - 選單裝飾器 (2KB)
- `helpers.py` - 通用助手函數 (3KB)
- `format_helper.py` - 格式化助手 (297B)
- `query_helper.py` - 查詢助手 (9KB)
- `report_generator.py` - 報表生成器 (19KB)
- `db_analyzer.py` - 數據庫分析器 (17KB)
- `db_optimizer.py` - 數據庫優化器 (17KB)

#### 未被使用的Utils:
- `__init__.py` - 初始化檔案
- `api_response.py` - API響應工具 (7KB)
- `backup_manager.py` - 備份管理器 (9KB)
- `base_view.py` - 基礎視圖類 (2KB)
- `batch_operations.py` - 批次操作 (13KB)
- `db_session_helper.py` - 數據庫會話助手 (2KB)
- `logging_middleware.py` - 日誌中間件 (6KB)
- `security.py` - 安全工具 (8KB)

### 2.3 Services 模組 (10/15 檔案被使用，66.7%使用率)

#### 被使用的Services:
- `account_service.py` - 帳戶服務 (3KB)
- `menu_service.py` - 選單服務 (7KB)
- `dashboard_service.py` - 儀表板服務 (18KB)
- `auth_service.py` - 認證服務 (7KB)
- `balance_sheet_service.py` - 資產負債表服務 (32KB, 最大檔案)
- `money_service.py` - 資金服務 (11KB)
- `journal_validator.py` - 日記帳驗證器 (7KB)
- `new_balance_sheet_service.py` - 新版資產負債表服務 (16KB)
- `new_income_statement_service.py` - 新版損益表服務 (10KB)
- `optimized_query_service.py` - 優化查詢服務 (14KB)

#### 未被使用的Services:
- `__init__.py` - 初始化檔案
- `bank_service.py` - 銀行服務 (5KB)
- `income_statement_service.py` - 損益表服務 (19KB, 可能被新版本替代)
- `subject_service.py` - 科目服務 (4KB)
- `transaction_service.py` - 交易服務 (11KB)
- `new_journal_validator.py` - 新版日記帳驗證器 (8KB, 重複功能)

### 2.4 Models 模組 (1/4 檔案被使用，25%使用率)

#### 被使用的Models:
- `auth_models.py` - 認證模型 (2KB)

#### 未被使用的Models:
- `bank_model.py` - 銀行模型 (3KB)
- `new_models.py` - 新模型 (4KB)
- `payment_identity_type.py` - 付款身份類型模型 (84B, 幾乎空白)

### 2.5 Config 模組 (1/3 檔案被使用，33.3%使用率)

#### 被使用的Config:
- `config.py` - 主配置檔案 (6KB)

#### 未被使用的Config:
- `__init__.py` - 初始化檔案
- `logging_config.py` - 日誌配置 (3KB, 未被main.py使用)

## 3. 可能未使用的檔案分析

### 3.1 根目錄工具檔案 (全部未使用)
這些檔案看起來是一次性腳本或工具：
- `add_tax_subjects.py` - 添加稅務科目工具
- `check_accounts.py` - 檢查帳戶工具  
- `check_opening_records.py` - 檢查期初記錄工具
- `clear_opening_records.py` - 清除期初記錄工具
- `debug_opening.py` - 除錯期初資料工具
- `fix_account_subject_codes.py` - 修復帳戶科目代碼工具
- `fix_all_layouts.py` - 修復所有佈局工具
- `fix_sidebar_colors.py` - 修復側邊欄顏色工具
- `init_auth_system.py` - 初始化認證系統工具
- `migrate_money_to_transaction.py` - 資金到交易遷移工具
- `test_bulma_integration.py` - Bulma整合測試
- `test_opening.py` - 期初測試

### 3.2 Trash 目錄 (全部可刪除)
這些檔案已被移到trash目錄，可以安全刪除：
- 過期的數據庫備份檔案 (.db 檔案)
- 重複的工具檔案
- 舊版本檔案

### 3.3 Scripts 目錄 (全部未使用)
包含各種維護和遷移腳本，通常不會被主應用程式直接使用。

### 3.4 Tests 目錄 (全部未使用於正式執行)
測試檔案不會被主應用程式使用，但對開發很重要。

## 4. 建議

### 4.1 立即可清理的檔案
1. **Trash目錄**: 可以完全刪除
2. **重複功能檔案**:
   - `routes/new_income_expense.py` (如果不再使用)
   - `services/income_statement_service.py` (如果已被新版替代)
   - `services/new_journal_validator.py` (與現有功能重複)

### 4.2 可考慮清理的檔案
1. **工具檔案**: 根目錄的一次性工具檔案可移至scripts目錄
2. **未使用的models**: `models/`目錄下的未使用檔案
3. **未使用的utils**: 某些工具函數如果確定不會使用

### 4.3 保留的檔案
1. **Scripts目錄**: 維護腳本應該保留
2. **Tests目錄**: 測試檔案應該保留
3. **配置檔案**: 即使未直接使用，配置檔案也應保留

## 5. 檔案大小統計

### 最大的檔案 (>20KB):
1. `model.py` - 36KB (主模型檔案)
2. `services/balance_sheet_service.py` - 32KB
3. `routes/settings.py` - 30KB
4. `routes/reports.py` - 29KB

### 模組平均檔案大小:
- Routes: 平均 8.2KB
- Utils: 平均 10.1KB  
- Services: 平均 11.3KB
- Models: 平均 2.4KB

---

**報告生成時間**: 2024年9月7日
**分析工具**: analyze_dependencies.py
**專案路徑**: `/Users/<USER>/Library/CloudStorage/Dropbox/Code/python/accounting`