# 多用戶系統架構解析

## 📋 目錄
1. [多用戶架構概念](#多用戶架構概念)
2. [目前系統狀態](#目前系統狀態)
3. [架構設計模式](#架構設計模式)
4. [資料隔離策略](#資料隔離策略)
5. [系統層級結構](#系統層級結構)
6. [完整改造計劃](#完整改造計劃)

## 🎯 多用戶架構概念

### 什麼是多用戶系統？
多用戶系統（Multi-Tenant System）是一種軟體架構，讓**多個獨立的客戶（租戶）共享同一套應用程式**，但**資料完全隔離**。

### 實際應用場景
```
🏢 公司A (租戶1) → 獨立的會計帳簿
🏪 公司B (租戶2) → 獨立的會計帳簿  
🏭 公司C (租戶3) → 獨立的會計帳簿
     ↓
   同一套系統，不同資料
```

## 📊 目前系統狀態

### ✅ 已完成的部分

#### 1. **租戶管理層**
```
tenants 表 (完整)
├── id: 1 → 公司A
├── id: 2 → 公司B  
├── id: 3 → 公司C
└── plan_level: basic/standard/premium
```

#### 2. **用戶管理層**
```
users 表 (含tenant_id)
├── user1 → tenant_id: 1 (屬於公司A)
├── user2 → tenant_id: 1 (屬於公司A)  
├── user3 → tenant_id: 2 (屬於公司B)
└── user4 → tenant_id: 3 (屬於公司C)
```

#### 3. **部分業務層**
```
account 表 (含tenant_id)
├── 公司A的銀行帳戶 → tenant_id: 1
├── 公司B的現金帳戶 → tenant_id: 2
└── 公司C的電子支付 → tenant_id: 3
```

### ❌ 尚未完成的部分

#### 1. **核心交易資料**
```
money 表 (缺少tenant_id) ⚠️
├── 交易1 → 無租戶標識 (所有公司混合)
├── 交易2 → 無租戶標識 (資料洩露風險)
└── 交易3 → 無租戶標識 (無法區分歸屬)
```

#### 2. **關聯業務資料**
```
payment_identity 表 (缺少tenant_id) ⚠️
department 表 (缺少tenant_id) ⚠️
project 表 (缺少tenant_id) ⚠️
```

## 🏛️ 架構設計模式

### 選擇的模式：**共享資料庫 + 租戶ID隔離**

```
應用層
├── 租戶A用戶登入
├── 租戶B用戶登入
└── 租戶C用戶登入
       ↓
中介層 (租戶過濾)
├── 自動添加 WHERE tenant_id = ?
├── 查詢結果過濾
└── 權限檢查
       ↓
資料庫層 (共享)
├── money 表 (需要加 tenant_id)
├── account 表 (已有 tenant_id)
└── users 表 (已有 tenant_id)
```

### 為什麼選這個模式？

#### ✅ 優點
1. **成本效益**：一個資料庫服務所有租戶
2. **維護簡單**：統一備份、更新、監控
3. **資源共享**：充分利用硬體資源
4. **擴展彈性**：容易添加新租戶

#### ⚠️ 需要注意
1. **資料隔離**：必須確保查詢包含租戶過濾
2. **性能考量**：大租戶可能影響小租戶
3. **安全風險**：程式錯誤可能導致資料洩露

## 🛡️ 資料隔離策略

### 1. **模型層隔離**

```python
# 目前的設計 (TenantMixin)
class TenantMixin:
    tenant_id = Column(Integer, ForeignKey('tenants.id'), 
                      nullable=False, index=True)

class Account(Base, AuditMixin, TenantMixin):
    # 自動包含 tenant_id
    pass
```

### 2. **查詢層隔離**

```python
# 應該要這樣查詢
def get_user_accounts(user_id):
    user = get_current_user(user_id)
    return db.query(Account).filter(
        Account.tenant_id == user.tenant_id  # 租戶過濾
    ).all()

# 而不是這樣 (會看到所有租戶的資料)
def get_all_accounts():
    return db.query(Account).all()  # ⚠️ 危險！
```

### 3. **服務層隔離**

```python
class BaseService:
    def __init__(self, current_user):
        self.tenant_id = current_user.tenant_id
    
    def _filter_by_tenant(self, query):
        return query.filter(Model.tenant_id == self.tenant_id)
```

## 🏗️ 系統層級結構

```
┌─────────────────────────────────────────┐
│              前端應用層                    │
│  ├── 公司A登入 ├── 公司B登入 ├── 公司C登入   │
└─────────────────┬───────────────────────┘
                 │
┌─────────────────▼───────────────────────┐
│              路由控制層                    │
│  ├── 身份驗證   ├── 租戶識別   ├── 權限檢查   │
└─────────────────┬───────────────────────┘
                 │
┌─────────────────▼───────────────────────┐
│              業務邏輯層                    │
│  ├── 自動租戶過濾 ├── 業務規則 ├── 資料驗證  │
└─────────────────┬───────────────────────┘
                 │
┌─────────────────▼───────────────────────┐
│              資料存取層                    │
│  ├── ORM查詢  ├── 租戶隔離  ├── 快取策略   │
└─────────────────┬───────────────────────┘
                 │
┌─────────────────▼───────────────────────┐
│              資料庫層                      │
│  ├── 租戶A資料 ├── 租戶B資料 ├── 租戶C資料  │
│  └── (通過tenant_id區分)                  │
└─────────────────────────────────────────┘
```

## 📋 目前缺失的部分

### 1. **模型定義 vs 實際資料庫不一致**

#### 模型中有定義（model.py）
```python
class Money(Base, AuditMixin, TenantMixin):  # 宣告有TenantMixin
    # 但實際資料庫表沒有 tenant_id 欄位
```

#### 實際資料庫表
```sql
-- money 表實際結構（缺少 tenant_id）
CREATE TABLE money (
    id INTEGER PRIMARY KEY,
    money_type VARCHAR(50),
    name VARCHAR(100),
    total INTEGER,
    -- tenant_id INTEGER,  ← 這個欄位不存在！
    ...
)
```

### 2. **查詢邏輯未考慮租戶隔離**

```python
# 目前的查詢 (錯誤 - 會看到所有租戶資料)
def get_transactions():
    return db.query(Money).all()  # ⚠️ 無租戶過濾

# 應該要的查詢
def get_transactions(tenant_id):
    return db.query(Money).filter(
        Money.tenant_id == tenant_id  # 租戶隔離
    ).all()
```

### 3. **身份驗證未串接租戶識別**

```python
# 目前登入後
session['user_id'] = user.id  # 只記錄用戶ID

# 應該要記錄
session['user_id'] = user.id
session['tenant_id'] = user.tenant_id  # 租戶ID
```

## 🚀 完整改造計劃

### 階段1：資料庫結構遷移 (必須)
1. 為核心表添加 `tenant_id` 欄位
2. 設定外鍵約束
3. 為現有資料分配預設租戶

### 階段2：程式碼改造 (關鍵)
1. 更新所有查詢邏輯
2. 實作租戶過濾中介軟體
3. 修改服務層邏輯

### 階段3：安全強化 (重要)
1. 實作租戶隔離檢查
2. 防止跨租戶資料存取
3. 審計日誌記錄

### 階段4：功能完善 (增值)
1. 租戶管理介面
2. 使用量統計
3. 計費系統整合

## 🎯 關鍵理解

### 為什麼看起來跟之前一樣？

1. **模型定義領先**：程式碼已經定義了多用戶結構
2. **資料庫滯後**：實際資料庫表還沒更新
3. **查詢邏輯**：還在使用舊的單用戶查詢方式
4. **漸進式改造**：系統正在轉換期

### 真正的多用戶系統需要：

```
完整的租戶隔離 = 資料結構 + 業務邏輯 + 安全機制
```

目前你的系統：
- ✅ **租戶管理結構** (已完成)
- ❌ **核心資料隔離** (未完成)  ← 這是關鍵問題
- ❌ **查詢邏輯隔離** (未完成)
- ❌ **安全機制** (未完成)

這就是為什麼你覺得「資料庫格式架構都跟之前的一樣」！