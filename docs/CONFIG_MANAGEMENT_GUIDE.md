# 🔧 配置管理系統指南

## 📋 概述

印錢大師會計系統現已升級為統一的配置管理系統，提供集中化、模組化和類型安全的配置管理功能。

## 🎯 主要特點

### ✅ 已實現功能

1. **📊 統一配置結構**
   - 按功能分類的配置段落（資料庫、快取、安全、日誌等）
   - 資料類型驗證和自動轉換
   - 環境變數自動映射

2. **🔧 配置來源支援**
   - 環境變數（最高優先級）
   - YAML/JSON 配置檔案
   - 程式碼預設值

3. **🛡️ 安全特性**
   - 敏感資訊自動隱藏
   - 配置驗證和完整性檢查
   - 生產環境必需配置檢查

4. **🌐 Web 管理介面**
   - 視覺化配置查看和編輯
   - 即時配置驗證
   - 配置匯出和備份功能

5. **🔄 動態管理**
   - 熱重載配置功能
   - 連接測試工具
   - 配置遷移支援

## 🏗️ 系統架構

### 配置檔案結構

```
config/
├── advanced_config.py      # 新配置系統核心
├── config.py               # 傳統配置（向後相容）
├── config.example.yaml     # 配置範例
└── logging_config.py       # 日誌配置

utils/common/
└── config_tools.py         # 配置管理工具

routes/
└── config_admin.py         # Web 管理介面

templates/config_admin/
└── index.html              # 管理介面模板
```

### 配置段落說明

| 段落 | 用途 | 主要設定 |
|------|------|----------|
| `database` | 資料庫連接 | 類型、主機、連接池設定 |
| `cache` | 快取系統 | Redis、記憶體快取設定 |
| `security` | 安全設定 | 密鑰、CSRF、限速設定 |
| `logging` | 日誌系統 | 等級、檔案大小、備份數量 |
| `mail` | 郵件服務 | SMTP 服務器、認證資訊 |
| `performance` | 效能監控 | 監控開關、閾值設定 |
| `files` | 檔案處理 | 上傳目錄、大小限制 |
| `api` | API 設定 | 分頁、限速、CORS 設定 |

## 📝 使用方式

### 1. 環境變數配置

建議的環境變數設定：

```bash
# 基本設定
SECRET_KEY=your-super-secret-key-here
FLASK_ENV=development

# 資料庫
DB_TYPE=sqlite
DB_POOL_SIZE=30

# 快取
CACHE_TYPE=memory
CACHE_DEFAULT_TIMEOUT=300

# 安全
CSRF_ENABLED=true
RATE_LIMIT_ENABLED=true

# 日誌
LOG_LEVEL=INFO
LOG_MAX_BYTES=10485760

# 效能監控
PERFORMANCE_MONITORING=true
SLOW_REQUEST_THRESHOLD=1.0
```

### 2. YAML 配置檔案

創建 `config/config.yaml`：

```yaml
database:
  type: sqlite
  pool_size: 30
  pool_recycle: 7200

security:
  secret_key: "your-secret-key"
  csrf_enabled: true
  rate_limit_enabled: true

logging:
  level: INFO
  max_bytes: 10485760
  backup_count: 10

performance:
  enable_monitoring: true
  slow_request_threshold: 1.0
  memory_threshold: 85
```

### 3. 程式碼中使用

```python
from config.advanced_config import get_config

# 獲取配置
config = get_config()

# 使用配置
database_uri = config.get_database_uri()
redis_url = config.get_redis_url()
log_level = config.logging.level
cache_timeout = config.cache.default_timeout

# 檢查配置
if config.performance.enable_monitoring:
    setup_performance_monitoring()
```

### 4. Web 管理介面

訪問 `/admin/config` 來使用 Web 管理介面：

- 📊 查看配置狀態和驗證結果
- ⚙️ 編輯各配置段落
- 💾 匯出配置為 YAML/JSON
- 🔄 重新載入配置
- 💾 備份當前配置

## 🔧 命令行工具

配置管理工具提供命令行介面：

```bash
# 查看配置狀態
python -m utils.common.config_tools status

# 驗證所有配置
python -m utils.common.config_tools validate

# 匯出配置
python -m utils.common.config_tools export yaml

# 從舊配置遷移
python -m utils.common.config_tools migrate
```

## 🛠️ 進階功能

### 配置驗證

系統自動驗證：

- ✅ **基本驗證**: 必需欄位、資料類型
- ✅ **連接測試**: 資料庫、Redis、郵件服務
- ✅ **數值範圍**: 連接池大小、超時設定等
- ✅ **相容性檢查**: 配置之間的依賴關係

### 熱重載

支援不重啟應用程式更新配置：

```python
from config.advanced_config import reload_config

# 重新載入配置
reload_config('config/new_config.yaml')
```

### 配置遷移

從舊系統遷移配置：

```python
from utils.common.config_tools import ConfigMigrator

# 執行遷移
ConfigMigrator.migrate_from_old_config()
```

### 備份管理

自動配置備份：

```python
from config.advanced_config import get_config

config = get_config()
config.save_config('backups/config_backup.yaml')
```

## 🚀 效能優勢

1. **⚡ 載入效率**: 配置快取，避免重複讀取
2. **💾 記憶體優化**: 按需載入配置段落
3. **🔐 安全提升**: 敏感資訊隔離和保護
4. **🎯 類型安全**: 編譯時配置錯誤檢測
5. **📊 可觀測性**: 配置變更日誌和監控

## 📈 遷移指南

### 從舊配置系統遷移

1. **備份現有配置**
   ```bash
   cp config/config.py config/config.py.backup
   ```

2. **使用遷移工具**
   ```bash
   python -m utils.common.config_tools migrate
   ```

3. **檢查遷移結果**
   ```bash
   python -m utils.common.config_tools validate
   ```

4. **更新程式碼引用**
   ```python
   # 舊方式
   from config.config import Config
   database_uri = Config.get_database_uri()

   # 新方式  
   from config.advanced_config import get_config
   config = get_config()
   database_uri = config.get_database_uri()
   ```

### 程式碼更新清單

- [x] `utils/performance/alert_system.py` - 已更新郵件配置獲取
- [ ] `utils/database/` - 需要更新連接設定
- [ ] `utils/logging/` - 需要整合日誌配置  
- [ ] `main.py` - 需要使用新配置初始化

## 🔍 故障排除

### 常見問題

1. **配置載入失敗**
   ```bash
   # 檢查配置檔案格式
   python -c "import yaml; yaml.safe_load(open('config/config.yaml'))"
   ```

2. **環境變數不生效**
   ```bash
   # 檢查變數名稱映射
   python -m utils.common.config_tools status
   ```

3. **連接測試失敗**
   ```bash
   # 單獨測試各組件
   python -m utils.common.config_tools validate
   ```

### 日誌位置

- 配置相關日誌: `logs/accounting.log`
- 錯誤日誌: `logs/error.log`
- 安全日誌: `logs/security.log`

## 🎉 總結

新的配置管理系統提供了：

- 🎯 **統一管理**: 所有配置集中管理
- 🛡️ **安全可靠**: 驗證和保護機制
- 🌐 **易於使用**: Web 介面和命令行工具
- 🔄 **靈活擴展**: 支援多種配置來源
- ⚡ **高效能**: 快取和優化機制

系統現在具備了企業級配置管理能力，支援開發、測試和生產環境的不同需求！