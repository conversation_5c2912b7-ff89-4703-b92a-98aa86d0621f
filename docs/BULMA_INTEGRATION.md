# Bulma CSS 框架集成指南

## 概述

本項目已成功集成 Bulma CSS 框架，提供現代化、響應式的用戶界面。所有組件都基於 Bulma 的設計系統，同時保留了自定義的視覺效果和交互體驗。

## 主要特性

### 🎨 設計系統
- **基於 Bulma 0.9.4** - 使用最新版本的 Bulma CSS 框架
- **自定義主題** - 保留原有的色彩系統和漸變效果
- **現代化視覺** - 毛玻璃效果、陰影和動畫
- **響應式設計** - 完美適配桌面、平板和手機

### 🧩 組件系統
- **側邊欄導航** - 基於 Bulma menu 組件的自定義側邊欄
- **卡片布局** - 增強的 Bulma card 組件
- **表格樣式** - 美化的 Bulma table 組件
- **表單元素** - 統一的 Bulma form 組件
- **按鈕系統** - 漸變效果的 Bulma button 組件
- **通知系統** - 增強的 Bulma notification 組件

### 📱 響應式特性
- **移動端優先** - 完全響應式設計
- **漢堡選單** - 移動端導航體驗
- **觸控友好** - 適合觸控設備的交互
- **自適應布局** - 根據屏幕尺寸自動調整

## 文件結構

```
├── static/css/
│   └── unified-modern-theme.css    # 基於 Bulma 的主題文件
├── static/js/
│   └── sidebar-menu.js             # 側邊欄交互邏輯
├── templates/
│   ├── admin/
│   │   └── base.html              # 基礎模板
│   ├── sidebar.html               # 側邊欄模板
│   └── example_bulma_page.html    # 示例頁面
└── BULMA_INTEGRATION.md           # 本文件
```

## 使用方法

### 1. 基礎模板

所有頁面都應該繼承 `admin/base.html`：

```html
{% extends "admin/base.html" %}

{% block title %}頁面標題{% endblock %}
{% block page_icon %}fa-star{% endblock %}
{% block page_title %}頁面標題{% endblock %}

{% block content %}
<!-- 頁面內容 -->
{% endblock %}
```

### 2. 常用組件

#### 卡片
```html
<div class="card">
    <div class="card-header">
        <p class="card-header-title">卡片標題</p>
    </div>
    <div class="card-content">
        <!-- 卡片內容 -->
    </div>
</div>
```

#### 按鈕
```html
<button class="button is-primary">
    <span class="icon">
        <i class="fas fa-save"></i>
    </span>
    <span>儲存</span>
</button>
```

#### 表格
```html
<table class="table is-fullwidth is-striped is-hoverable">
    <thead>
        <tr>
            <th>欄位1</th>
            <th>欄位2</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>資料1</td>
            <td>資料2</td>
        </tr>
    </tbody>
</table>
```

#### 表單
```html
<div class="field">
    <label class="label">標籤</label>
    <div class="control has-icons-left">
        <input class="input" type="text" placeholder="請輸入">
        <span class="icon is-small is-left">
            <i class="fas fa-user"></i>
        </span>
    </div>
</div>
```

#### 通知
```html
<div class="notification is-success">
    <button class="delete"></button>
    成功訊息
</div>
```

### 3. 布局系統

使用 Bulma 的 columns 系統：

```html
<div class="columns">
    <div class="column is-half">
        <!-- 左側內容 -->
    </div>
    <div class="column is-half">
        <!-- 右側內容 -->
    </div>
</div>
```

### 4. 響應式工具類

```html
<!-- 在不同屏幕尺寸隱藏/顯示 -->
<div class="is-hidden-mobile">桌面顯示</div>
<div class="is-hidden-desktop">移動端顯示</div>

<!-- 響應式文字大小 -->
<h1 class="title is-3-desktop is-4-tablet is-5-mobile">響應式標題</h1>
```

## 自定義樣式

### CSS 變數
```css
:root {
    --bulma-primary: #667eea;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 工具類別
```css
.text-gradient-primary {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.bg-glass {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
}
```

## 示例頁面

訪問 `/example-bulma` 查看完整的組件示例，包括：
- 統計卡片
- 各種按鈕樣式
- 表單元素
- 通知組件
- 表格樣式
- 進度條

## 最佳實踐

### 1. 使用 Bulma 類別
優先使用 Bulma 提供的 CSS 類別，而不是自定義樣式。

### 2. 響應式設計
始終考慮不同屏幕尺寸的顯示效果。

### 3. 語義化 HTML
使用語義化的 HTML 標籤和 Bulma 組件。

### 4. 一致性
保持整個應用程序的視覺和交互一致性。

## 瀏覽器支持

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 更新日誌

### v1.0.0 (2024-01-15)
- 完成 Bulma CSS 框架集成
- 實現響應式側邊欄導航
- 添加自定義主題和組件增強
- 創建示例頁面和文檔

## 技術支持

如有問題或建議，請聯繫開發團隊。