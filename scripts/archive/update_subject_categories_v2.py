#!/usr/bin/env python3
"""
更新科目分類的腳本 v2
移除「其他」分類，新增「所得稅」分類
"""

from model import AccountSubject, session

def update_subject_categories_v2():
    """更新科目分類，移除其他分類，新增所得稅分類"""
    
    # 定義科目代碼範圍對應的分類
    category_mapping = {
        '資產': ['11', '12', '13', '14', '15', '16', '17', '18', '19'],
        '負債': ['21', '22', '23', '24', '25', '26', '27', '28', '29'],
        '權益': ['31', '32', '33', '34', '35', '36', '37', '38', '39'],
        '營業收入': ['41', '42', '43', '44', '45', '46', '47', '48', '49'],
        '營業成本': ['50', '51', '52', '53', '54', '55', '56', '57', '58', '59'],
        '營業費用': ['60', '61', '62', '63', '64', '65', '66', '67', '68', '69'],
        '營業外收益及費損': ['70', '71', '72', '73', '74', '75', '76', '77', '78', '79'],
        '所得稅': ['80', '81', '82', '83', '84', '85', '86', '87', '88', '89']
    }
    
    # 特殊科目的手動分類（針對不符合代碼規則的科目）
    special_mapping = {
        # 將原本在「其他」分類的科目重新分類
        '5000': '營業成本',  # 銷貨成本
        '6010': '營業費用',  # 薪資支出
        '6011': '營業費用',  # 薪資支出-公司提撥
        '6012': '營業費用',  # 薪資支出-自願提撥
        '6013': '營業費用',  # 薪資支出-員工酬勞
        '6020': '營業費用',  # 租金支出
        '6030': '營業費用',  # 文具用品
        '6040': '營業費用',  # 旅費
        '6050': '營業費用',  # 運費
        '6060': '營業費用',  # 郵電費
        '6070': '營業費用',  # 修繕費
        '6080': '營業費用',  # 廣告費
        '6090': '營業費用',  # 水電瓦斯費
        '7040': '營業外收益及費損',  # 利息收入
        '7050': '營業外收益及費損',  # 租賃收入
        '7060': '營業外收益及費損',  # 處分資產利益
        '7065': '營業外收益及費損',  # 處分投資利益
        '7090': '營業外收益及費損',  # 兌換盈益
    }
    
    try:
        # 查詢所有科目
        subjects = session.query(AccountSubject).all()
        updated_count = 0
        
        for subject in subjects:
            new_category = None
            
            # 首先檢查特殊映射
            if subject.code in special_mapping:
                new_category = special_mapping[subject.code]
            else:
                # 根據科目代碼前兩位判斷分類
                if len(subject.code) >= 2:
                    code_prefix = subject.code[:2]
                    
                    # 找到對應的分類
                    for category, prefixes in category_mapping.items():
                        if code_prefix in prefixes:
                            new_category = category
                            break
            
            # 如果找到新分類且與現有分類不同，則更新
            if new_category and subject.top_category != new_category:
                print(f"更新科目 {subject.code} {subject.name}: {subject.top_category} -> {new_category}")
                subject.top_category = new_category
                updated_count += 1
        
        # 提交變更
        session.commit()
        print(f"\n✅ 成功更新 {updated_count} 個科目的分類")
        
        # 顯示分類統計
        print("\n📊 更新後的分類統計:")
        category_order = [
            '資產', '負債', '權益', '營業收入', '營業成本', 
            '營業費用', '營業外收益及費損', '所得稅'
        ]
        
        for category in category_order:
            count = session.query(AccountSubject).filter_by(top_category=category).count()
            print(f"  {category}: {count} 個科目")
        
        # 檢查是否還有其他分類的科目
        other_categories = session.query(AccountSubject.top_category).distinct().all()
        other_categories = [cat[0] for cat in other_categories if cat[0] not in category_order and cat[0] is not None]
        
        if other_categories:
            print(f"\n⚠️  發現其他分類: {other_categories}")
            for cat in other_categories:
                count = session.query(AccountSubject).filter_by(top_category=cat).count()
                subjects_in_cat = session.query(AccountSubject).filter_by(top_category=cat).limit(5).all()
                print(f"  {cat}: {count} 個科目")
                for subj in subjects_in_cat:
                    print(f"    - {subj.code} {subj.name}")
        
        # 檢查是否有 None 分類的科目
        none_count = session.query(AccountSubject).filter(AccountSubject.top_category.is_(None)).count()
        if none_count > 0:
            print(f"\n⚠️  還有 {none_count} 個科目沒有分類")
            none_subjects = session.query(AccountSubject).filter(AccountSubject.top_category.is_(None)).limit(5).all()
            for subj in none_subjects:
                print(f"    - {subj.code} {subj.name}")
        
    except Exception as e:
        session.rollback()
        print(f"❌ 更新失敗: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    update_subject_categories_v2()
