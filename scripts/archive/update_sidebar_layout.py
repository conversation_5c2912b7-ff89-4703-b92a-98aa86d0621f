#!/usr/bin/env python3
"""
批量更新模板文件的側邊選單佈局
將 is-3 / is-9 改為 is-narrow / (無指定)
"""

import os
import re

def update_template_layout():
    """更新模板文件的佈局"""
    
    templates_dir = "templates"
    
    # 自動搜尋所有需要更新的模板文件
    template_files = []

    # 搜尋所有包含側邊選單佈局的文件
    for root, dirs, files in os.walk(templates_dir):
        for file in files:
            if file.endswith('.html'):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                    # 檢查是否包含側邊選單的佈局模式
                    if 'column is-3' in content and 'sidebar.html' in content:
                        template_files.append(os.path.relpath(filepath, templates_dir))
                except Exception:
                    continue
    
    # 搜尋模式
    pattern1 = r'<div class="column is-3">\s*{% include \'sidebar\.html\' %}\s*</div>\s*<div class="column is-9">'
    pattern2 = r'<div class="column is-3">\s*{% include \'sidebar\.html\' %}\s*</div>'
    
    # 替換模式
    replacement1 = '<div class="column is-narrow">\\n                {% include \'sidebar.html\' %}\\n            </div>\\n            <div class="column">'
    replacement2 = '<div class="column is-narrow">\\n                {% include \'sidebar.html\' %}\\n            </div>'
    
    updated_files = []
    
    print(f"🔍 找到 {len(template_files)} 個需要檢查的文件")

    for filename in template_files:
        filepath = os.path.join(templates_dir, filename)
        
        if not os.path.exists(filepath):
            print(f"⚠️  文件不存在: {filepath}")
            continue
            
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 替換佈局
            content = re.sub(pattern1, replacement1, content, flags=re.MULTILINE | re.DOTALL)
            content = re.sub(pattern2, replacement2, content, flags=re.MULTILINE | re.DOTALL)
            
            # 如果有變更，寫回文件
            if content != original_content:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(content)
                updated_files.append(filename)
                print(f"✅ 已更新: {filename}")
            else:
                print(f"📝 無需更新: {filename}")
                
        except Exception as e:
            print(f"❌ 更新失敗 {filename}: {e}")
    
    print("\n🎯 總結:")
    print(f"  - 成功更新: {len(updated_files)} 個文件")
    if updated_files:
        print(f"  - 更新的文件: {', '.join(updated_files)}")

if __name__ == "__main__":
    update_template_layout()
