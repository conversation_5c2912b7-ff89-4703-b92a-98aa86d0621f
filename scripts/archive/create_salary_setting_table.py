#!/usr/bin/env python3
"""
建立薪資設定資料表的腳本
執行此腳本來創建薪資設定資料表
"""

from model import Base, engine
from sqlalchemy import text

def create_salary_setting_table():
    """建立薪資設定資料表"""
    try:
        # 建立所有資料表
        Base.metadata.create_all(engine)
        print("✅ 薪資設定資料表建立成功！")
        
        # 檢查表是否存在
        with engine.connect() as conn:
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='salary_settings'"))
            if result.fetchone():
                print("✅ 薪資設定資料表已存在於資料庫中")
            else:
                print("❌ 薪資設定資料表建立失敗")
                
    except Exception as e:
        print(f"❌ 建立薪資設定資料表時發生錯誤：{e}")

if __name__ == "__main__":
    create_salary_setting_table()
