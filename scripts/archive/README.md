# 📦 归档脚本文件夹

这个文件夹包含已经执行完成的一次性脚本文件。

## 📁 文件说明

### 数据库表创建脚本
- `create_employee_table.py` - 创建员工表的脚本
- `create_salary_setting_table.py` - 创建薪资设置表的脚本

### 数据迁移脚本
- `migrate_bank_data.py` - 银行数据迁移脚本

### 界面更新脚本
- `update_sidebar_layout.py` - 更新侧边栏布局的脚本
- `update_subject_categories.py` - 更新科目分类的脚本（第一版）
- `update_subject_categories_v2.py` - 更新科目分类的脚本（第二版）

## ⚠️ 注意事项

- 这些脚本都是**一次性执行**的，通常不需要重复运行
- 如果需要重新执行，请先检查数据库状态，避免重复操作
- 建议在执行前备份数据库

## 🔄 如果需要重新执行

1. 检查当前数据库状态
2. 确认是否需要回滚之前的操作
3. 谨慎执行脚本
4. 验证执行结果

---
*归档时间：$(date)*