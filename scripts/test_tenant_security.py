#!/usr/bin/env python3
"""
租戶安全機制測試腳本
驗證多租戶系統的安全保護措施
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_data_isolation():
    """測試資料隔離"""
    print("\n🔒 測試租戶資料隔離...")
    
    try:
        from utils.security.tenant_security import TenantAccessControl
        from model import Money, Account, Transaction
        from database import get_db
        from main import create_app
        
        # 創建應用程式上下文來避免 Flask 錯誤
        app = create_app()
        with app.app_context():
            # 設置模擬的 g 變數
            from flask import g
            g.tenant_id = 1
            g.user_id = 1
            
            # 測試資料隔離強制執行
            with get_db() as db:
                # 測試 Money 模型
                query = db.query(Money)
                
                isolated_query = TenantAccessControl.enforce_data_isolation(query, Money)
                
                # 檢查是否添加了租戶過濾
                query_str = str(isolated_query)
                if 'tenant_id' in query_str:
                    print("  ✅ Money 模型資料隔離: 已應用")
                else:
                    print("  ⚠️ Money 模型資料隔離: 需要檢查")
                
                # 測試查詢限制
                if 'LIMIT' in query_str.upper():
                    print("  ✅ 查詢限制: 已應用")
                else:
                    print("  ⚠️ 查詢限制: 未應用")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 資料隔離測試失敗: {e}")
        return False

def test_access_control():
    """測試存取控制"""
    print("\n🔐 測試租戶存取控制...")
    
    try:
        from utils.security.tenant_security import TenantAccessControl
        
        print("  ✅ TenantAccessControl 類別可用")
        
        # 測試方法存在性
        methods = [
            'verify_tenant_access',
            'get_user_tenants',
            'enforce_data_isolation'
        ]
        
        for method in methods:
            if hasattr(TenantAccessControl, method):
                print(f"  ✅ 方法 {method}: 存在")
            else:
                print(f"  ❌ 方法 {method}: 缺失")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 存取控制模組導入失敗: {e}")
        return False

def test_audit_logging():
    """測試審計日誌"""
    print("\n📝 測試審計日誌功能...")
    
    try:
        from utils.security.tenant_security import TenantAuditLogger
        
        print("  ✅ TenantAuditLogger 類別可用")
        
        # 測試日誌方法
        if hasattr(TenantAuditLogger, 'log_access'):
            print("  ✅ 存取日誌方法: 可用")
        
        if hasattr(TenantAuditLogger, 'log_security_event'):
            print("  ✅ 安全事件日誌方法: 可用")
        
        # 測試日誌記錄（不實際寫入）
        try:
            # 這只是測試方法是否可調用
            TenantAuditLogger.log_security_event(
                "TEST_EVENT",
                "INFO",
                "測試安全事件記錄"
            )
            print("  ✅ 安全事件記錄: 正常")
        except Exception as e:
            print(f"  ⚠️ 安全事件記錄: {e}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 審計日誌測試失敗: {e}")
        return False

def test_session_security():
    """測試會話安全"""
    print("\n🔑 測試會話安全機制...")
    
    try:
        from utils.security.tenant_security import TenantSessionManager, TenantSecurityConfig
        
        print("  ✅ TenantSessionManager 類別可用")
        
        # 檢查安全配置
        print(f"  📊 會話超時設定: {TenantSecurityConfig.SESSION_TIMEOUT_MINUTES} 分鐘")
        print(f"  📊 最大並發會話: {TenantSecurityConfig.MAX_CONCURRENT_SESSIONS}")
        
        # 測試會話方法
        methods = [
            'create_secure_session',
            'validate_session',
            'destroy_session'
        ]
        
        for method in methods:
            if hasattr(TenantSessionManager, method):
                print(f"  ✅ 會話方法 {method}: 存在")
            else:
                print(f"  ❌ 會話方法 {method}: 缺失")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 會話安全測試失敗: {e}")
        return False

def test_rate_limiting():
    """測試速率限制"""
    print("\n⏱️ 測試速率限制機制...")
    
    try:
        from utils.security.tenant_security import TenantRateLimiter, TenantSecurityConfig
        
        print("  ✅ TenantRateLimiter 類別可用")
        print(f"  📊 請求速率限制: {TenantSecurityConfig.REQUEST_RATE_LIMIT} 請求/分鐘")
        print(f"  📊 登入嘗試限制: {TenantSecurityConfig.LOGIN_ATTEMPT_LIMIT} 次")
        
        # 測試速率限制功能
        test_identifier = "test_tenant_1_user_1"
        
        # 測試正常請求
        result = TenantRateLimiter.check_rate_limit(test_identifier, limit=5)
        if result:
            print("  ✅ 速率限制檢查: 正常")
        
        # 測試超過限制
        for i in range(10):
            TenantRateLimiter.check_rate_limit(test_identifier, limit=5)
        
        result = TenantRateLimiter.check_rate_limit(test_identifier, limit=5)
        if not result:
            print("  ✅ 速率限制執行: 正確阻止超限請求")
        else:
            print("  ⚠️ 速率限制執行: 未能阻止超限請求")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 速率限制測試失敗: {e}")
        return False

def test_data_encryption():
    """測試資料加密"""
    print("\n🔐 測試資料加密機制...")
    
    try:
        from utils.security.tenant_security import TenantDataEncryption, TenantSecurityConfig
        
        print("  ✅ TenantDataEncryption 類別可用")
        print(f"  📊 加密啟用: {TenantSecurityConfig.USE_ENCRYPTION}")
        
        if TenantSecurityConfig.USE_ENCRYPTION:
            # 測試加密和解密
            test_data = "敏感資料測試"
            tenant_id = 1
            
            # 加密
            encrypted = TenantDataEncryption.encrypt_sensitive_data(test_data, tenant_id)
            if encrypted != test_data:
                print("  ✅ 資料加密: 成功")
            else:
                print("  ⚠️ 資料加密: 未加密")
            
            # 解密
            decrypted = TenantDataEncryption.decrypt_sensitive_data(encrypted, tenant_id)
            if decrypted == test_data:
                print("  ✅ 資料解密: 成功")
            else:
                print("  ❌ 資料解密: 失敗")
        else:
            print("  ℹ️ 加密功能未啟用")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 資料加密測試失敗: {e}")
        return False

def test_input_validation():
    """測試輸入驗證"""
    print("\n🛡️ 測試輸入驗證和清理...")
    
    try:
        from utils.security.tenant_security import validate_tenant_input
        
        # 測試危險字段移除
        dangerous_input = {
            'name': '正常資料',
            'tenant_id': 999,  # 危險字段
            'user_id': 888,    # 危險字段
            'is_admin': True   # 危險字段
        }
        
        cleaned_data = validate_tenant_input(dangerous_input.copy())
        
        if 'tenant_id' not in cleaned_data:
            print("  ✅ 危險字段過濾: tenant_id 已移除")
        if 'user_id' not in cleaned_data:
            print("  ✅ 危險字段過濾: user_id 已移除")
        if 'is_admin' not in cleaned_data:
            print("  ✅ 危險字段過濾: is_admin 已移除")
        
        # 測試SQL注入防護
        sql_injection_input = {
            'query': "'; DROP TABLE users; --"
        }
        
        cleaned_sql = validate_tenant_input(sql_injection_input.copy())
        
        if 'DROP' not in cleaned_sql.get('query', ''):
            print("  ✅ SQL注入防護: 危險模式已清理")
        else:
            print("  ❌ SQL注入防護: 未能清理危險模式")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 輸入驗證測試失敗: {e}")
        return False

def test_security_decorator():
    """測試安全裝飾器"""
    print("\n🎯 測試安全路由裝飾器...")
    
    try:
        from utils.security.tenant_security import secure_tenant_route
        
        print("  ✅ secure_tenant_route 裝飾器可用")
        
        # 測試裝飾器應用
        @secure_tenant_route
        def test_route():
            return "測試路由"
        
        print("  ✅ 裝飾器可應用於函數")
        
        # 檢查裝飾器保留函數名稱
        if test_route.__name__ == 'test_route':
            print("  ✅ 裝飾器正確保留函數元資料")
        else:
            print("  ⚠️ 裝飾器可能未使用 functools.wraps")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 安全裝飾器測試失敗: {e}")
        return False

def analyze_security_coverage():
    """分析安全覆蓋率"""
    print("\n📊 分析安全機制覆蓋率...")
    
    security_features = {
        '資料隔離': test_data_isolation(),
        '存取控制': test_access_control(),
        '審計日誌': test_audit_logging(),
        '會話安全': test_session_security(),
        '速率限制': test_rate_limiting(),
        '資料加密': test_data_encryption(),
        '輸入驗證': test_input_validation(),
        '安全裝飾器': test_security_decorator()
    }
    
    passed = sum(1 for v in security_features.values() if v)
    total = len(security_features)
    coverage = (passed / total) * 100
    
    print(f"\n🎯 安全覆蓋率: {coverage:.1f}% ({passed}/{total})")
    
    print("\n📋 詳細結果:")
    for feature, status in security_features.items():
        icon = "✅" if status else "❌"
        print(f"  {icon} {feature}: {'通過' if status else '失敗'}")
    
    return coverage >= 80  # 80% 以上覆蓋率視為成功

def main():
    """主執行函數"""
    print("🔒 租戶安全機制測試")
    print("=" * 50)
    
    # 執行所有測試
    overall_success = analyze_security_coverage()
    
    # 生成報告
    print("\n" + "=" * 60)
    print("🛡️ 安全機制測試報告")
    print("=" * 60)
    
    if overall_success:
        print("\n🎉 安全機制測試通過！")
        print("✅ 多租戶系統具備完整的安全保護措施:")
        print("  • 資料隔離確保租戶間資料完全分離")
        print("  • 存取控制防止未授權存取")
        print("  • 審計日誌追蹤所有操作")
        print("  • 會話管理確保安全認證")
        print("  • 速率限制防止濫用")
        print("  • 資料加密保護敏感資訊")
        print("  • 輸入驗證防止注入攻擊")
    else:
        print("\n⚠️ 部分安全機制需要進一步加強")
        print("💡 建議檢查失敗的測試項目並進行修正")
    
    print("\n🔐 系統安全狀態: " + ("安全" if overall_success else "需要加強"))
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)