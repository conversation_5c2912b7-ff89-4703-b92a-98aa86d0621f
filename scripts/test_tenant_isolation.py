#!/usr/bin/env python3
"""
租戶隔離功能測試腳本
驗證多用戶系統的資料隔離是否正常運作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import engine, get_db
from sqlalchemy import text
from utils.tenant_utils import TenantQueryBuilder, add_tenant_filter, safe_get_by_id
from model import Money, PaymentIdentity, Department, Project, Account
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_structure():
    """測試資料庫結構是否正確"""
    print("🔍 測試資料庫結構...")
    
    with engine.connect() as conn:
        # 檢查核心表是否都有 tenant_id 欄位
        core_tables = ['money', 'payment_identity', 'department', 'project']
        
        for table in core_tables:
            try:
                # 檢查欄位存在性
                pragma_sql = f"PRAGMA table_info({table})"
                columns = conn.execute(text(pragma_sql)).fetchall()
                column_names = [col[1] for col in columns]
                
                if 'tenant_id' in column_names:
                    print(f"  ✅ {table}: 有 tenant_id 欄位")
                else:
                    print(f"  ❌ {table}: 缺少 tenant_id 欄位")
            
            except Exception as e:
                print(f"  ❌ {table}: 檢查失敗 - {e}")

def test_tenant_data_distribution():
    """測試租戶資料分布"""
    print("\n📊 測試租戶資料分布...")
    
    with engine.connect() as conn:
        core_tables = ['money', 'payment_identity', 'department', 'project']
        
        for table in core_tables:
            try:
                # 檢查每個租戶的資料量
                count_sql = f"""
                SELECT tenant_id, COUNT(*) as count 
                FROM {table} 
                GROUP BY tenant_id 
                ORDER BY tenant_id
                """
                results = conn.execute(text(count_sql)).fetchall()
                
                print(f"\n  📋 {table} 表資料分布:")
                total_records = 0
                for tenant_id, count in results:
                    tenant_name = f"租戶{tenant_id}" if tenant_id else "NULL"
                    print(f"    └─ {tenant_name}: {count} 筆記錄")
                    total_records += count
                
                if total_records == 0:
                    print(f"    └─ 無資料")
                    
            except Exception as e:
                print(f"  ❌ {table}: 查詢失敗 - {e}")

def test_tenant_query_builder():
    """測試租戶查詢建構器"""
    print("\n🏗️ 測試租戶查詢建構器...")
    
    try:
        with get_db() as db:
            # 測試租戶ID 1
            builder = TenantQueryBuilder(db, tenant_id=1)
            
            # 測試查詢 Money 記錄
            money_records = builder.query(Money).limit(5).all()
            print(f"  💰 租戶1的 Money 記錄: {len(money_records)} 筆")
            
            # 測試查詢 PaymentIdentity 記錄
            payment_identities = builder.query(PaymentIdentity).all()
            print(f"  💳 租戶1的 PaymentIdentity 記錄: {len(payment_identities)} 筆")
            
            # 測試計數功能
            money_count = builder.count(Money)
            print(f"  🔢 租戶1的 Money 總數: {money_count} 筆")
            
            # 測試安全取得記錄
            if money_records:
                first_money_id = money_records[0].id
                safe_record = builder.get_by_id(Money, first_money_id)
                if safe_record:
                    print(f"  🔒 安全取得記錄: ID {first_money_id} ✅")
                else:
                    print(f"  🔒 安全取得記錄: ID {first_money_id} ❌")
    
    except Exception as e:
        print(f"  ❌ 租戶查詢建構器測試失敗: {e}")

def test_cross_tenant_isolation():
    """測試跨租戶隔離"""
    print("\n🛡️ 測試跨租戶隔離...")
    
    try:
        with get_db() as db:
            # 取得所有租戶的 Money 記錄
            all_money = db.query(Money).all()
            
            if not all_money:
                print("  ⚠️ 沒有 Money 記錄可供測試")
                return
            
            # 測試不同租戶的查詢建構器
            tenant_1_builder = TenantQueryBuilder(db, tenant_id=1)
            tenant_2_builder = TenantQueryBuilder(db, tenant_id=2)
            
            tenant_1_money = tenant_1_builder.query(Money).all()
            tenant_2_money = tenant_2_builder.query(Money).all()
            
            print(f"  🏢 租戶1的記錄: {len(tenant_1_money)} 筆")
            print(f"  🏢 租戶2的記錄: {len(tenant_2_money)} 筆")
            print(f"  📊 總記錄數: {len(all_money)} 筆")
            
            # 驗證隔離效果
            if len(tenant_1_money) + len(tenant_2_money) <= len(all_money):
                print("  ✅ 租戶隔離正常運作")
            else:
                print("  ❌ 租戶隔離可能有問題")
            
            # 測試跨租戶存取保護
            if tenant_1_money:
                tenant_1_record_id = tenant_1_money[0].id
                
                # 租戶2嘗試存取租戶1的記錄
                cross_access_record = tenant_2_builder.get_by_id(Money, tenant_1_record_id)
                
                if cross_access_record is None:
                    print("  🔒 跨租戶存取保護: ✅")
                else:
                    print("  🔒 跨租戶存取保護: ❌ (可能有安全漏洞)")
    
    except Exception as e:
        print(f"  ❌ 跨租戶隔離測試失敗: {e}")

def test_tenant_filter_utility():
    """測試租戶過濾工具函數"""
    print("\n🔧 測試租戶過濾工具函數...")
    
    try:
        with get_db() as db:
            # 測試 add_tenant_filter 函數
            base_query = db.query(Money)
            filtered_query = add_tenant_filter(base_query, Money, tenant_id=1)
            
            base_count = base_query.count()
            filtered_count = filtered_query.count()
            
            print(f"  📊 基礎查詢結果: {base_count} 筆")
            print(f"  📊 過濾後查詢結果: {filtered_count} 筆")
            
            if filtered_count <= base_count:
                print("  ✅ 租戶過濾工具正常運作")
            else:
                print("  ❌ 租戶過濾工具可能有問題")
            
            # 測試 safe_get_by_id 函數
            if filtered_count > 0:
                first_record = filtered_query.first()
                if first_record:
                    safe_record = safe_get_by_id(Money, first_record.id, tenant_id=1)
                    if safe_record:
                        print("  🔒 safe_get_by_id: ✅")
                    else:
                        print("  🔒 safe_get_by_id: ❌")
    
    except Exception as e:
        print(f"  ❌ 租戶過濾工具測試失敗: {e}")

def test_data_consistency():
    """測試資料一致性"""
    print("\n📋 測試資料一致性...")
    
    try:
        with engine.connect() as conn:
            # 檢查是否有 tenant_id 為 NULL 的記錄
            core_tables = ['money', 'payment_identity', 'department', 'project']
            
            for table in core_tables:
                try:
                    null_count_sql = f"SELECT COUNT(*) FROM {table} WHERE tenant_id IS NULL"
                    null_count = conn.execute(text(null_count_sql)).scalar()
                    
                    if null_count == 0:
                        print(f"  ✅ {table}: 沒有 NULL 租戶ID")
                    else:
                        print(f"  ⚠️ {table}: 有 {null_count} 筆記錄的租戶ID為NULL")
                
                except Exception as e:
                    print(f"  ❌ {table}: 檢查失敗 - {e}")
            
            # 檢查外鍵關係的一致性
            print("\n  🔗 檢查外鍵關係一致性:")
            
            # Money 與 Account 的租戶一致性
            inconsistent_sql = """
            SELECT COUNT(*) 
            FROM money m 
            LEFT JOIN account a ON m.account_id = a.id 
            WHERE m.tenant_id != a.tenant_id
            """
            inconsistent_count = conn.execute(text(inconsistent_sql)).scalar()
            
            if inconsistent_count == 0:
                print("    ✅ Money-Account 租戶關係一致")
            else:
                print(f"    ⚠️ 有 {inconsistent_count} 筆 Money-Account 租戶關係不一致")
    
    except Exception as e:
        print(f"  ❌ 資料一致性測試失敗: {e}")

def generate_test_report():
    """生成測試報告摘要"""
    print("\n" + "=" * 60)
    print("📋 多用戶租戶隔離測試報告")
    print("=" * 60)
    
    print("\n✅ 完成的測試項目:")
    print("  1. 資料庫結構檢查")
    print("  2. 租戶資料分布檢查") 
    print("  3. 租戶查詢建構器功能測試")
    print("  4. 跨租戶隔離保護測試")
    print("  5. 租戶過濾工具函數測試")
    print("  6. 資料一致性檢查")
    
    print("\n💡 建議:")
    print("  - 如果發現任何❌錯誤，請檢查相關配置")
    print("  - 如果有⚠️警告，建議進一步調查") 
    print("  - 定期執行此測試確保租戶隔離正常")

def main():
    """主執行函數"""
    print("🧪 開始租戶隔離功能測試")
    print("=" * 50)
    
    try:
        # 執行所有測試
        test_database_structure()
        test_tenant_data_distribution()
        test_tenant_query_builder()
        test_cross_tenant_isolation()
        test_tenant_filter_utility()
        test_data_consistency()
        
        # 生成測試報告
        generate_test_report()
        
        print(f"\n🎉 租戶隔離測試完成！")
        
    except Exception as e:
        logger.error(f"測試過程發生錯誤: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()