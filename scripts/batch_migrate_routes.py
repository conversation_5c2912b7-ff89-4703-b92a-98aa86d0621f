#!/usr/bin/env python3
"""
批量遷移路由腳本
自動為路由添加租戶隔離裝飾器和更新查詢邏輯
"""

import sys
import os
import re
from pathlib import Path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def add_tenant_imports(file_content):
    """添加租戶相關的導入"""
    # 檢查是否已經導入
    if 'require_tenant_access' in file_content:
        return file_content, False
    
    # 尋找合適的位置插入導入
    import_pattern = r'(from data\.menu_data import menu\n)'
    replacement = r'\1from utils.tenant_utils import require_tenant_access, add_tenant_filter, get_current_tenant_id\n'
    
    if 'from data.menu_data import menu' in file_content:
        file_content = re.sub(import_pattern, replacement, file_content)
        return file_content, True
    
    # 如果沒有找到menu導入，在最後一個導入語句後添加
    import_lines = []
    lines = file_content.split('\n')
    last_import_line = -1
    
    for i, line in enumerate(lines):
        if line.startswith('from ') or line.startswith('import '):
            last_import_line = i
    
    if last_import_line >= 0:
        lines.insert(last_import_line + 1, 'from utils.tenant_utils import require_tenant_access, add_tenant_filter, get_current_tenant_id')
        return '\n'.join(lines), True
    
    return file_content, False

def add_tenant_decorators(file_content):
    """為路由添加租戶隔離裝飾器"""
    # 找到所有路由定義
    route_pattern = r'(@[\w_]+\.route\([^)]+\)\s*\n)(def\s+[\w_]+\([^)]*\):)'
    
    def replace_route(match):
        route_decorator = match.group(1)
        function_def = match.group(2)
        
        # 檢查是否已經有租戶裝飾器
        if '@require_tenant_access' in route_decorator:
            return match.group(0)
        
        # 添加租戶裝飾器
        return route_decorator + '@require_tenant_access\n' + function_def
    
    modified_content = re.sub(route_pattern, replace_route, file_content, flags=re.MULTILINE)
    return modified_content

def update_account_queries(file_content):
    """更新Account相關查詢以支持租戶過濾"""
    # 替換Account查詢模式
    patterns = [
        # db.query(Account) 模式
        (r'(db\.query\(Account\))', r'add_tenant_filter(\1, Account)'),
        # session.query(Account) 模式
        (r'(session\.query\(Account\))', r'add_tenant_filter(\1, Account)'),
        # 其他常見模式
    ]
    
    for pattern, replacement in patterns:
        # 只替換沒有已經被add_tenant_filter包裹的查詢
        if 'add_tenant_filter' not in file_content:
            file_content = re.sub(pattern, replacement, file_content)
    
    return file_content

def migrate_route_file(file_path, dry_run=True):
    """遷移單個路由檔案"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        print(f"\n🔄 處理 {file_path.name}...")
        
        # 添加導入
        content, imports_added = add_tenant_imports(original_content)
        
        # 添加裝飾器
        content = add_tenant_decorators(content)
        
        # 更新查詢（如果是Account相關的檔案）
        if 'Account' in content and file_path.name == 'account.py':
            content = update_account_queries(content)
        
        # 檢查是否有改變
        if content != original_content:
            print(f"  📝 檢測到需要更新")
            if imports_added:
                print(f"    ✅ 已添加租戶導入")
            
            if not dry_run:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"    💾 已儲存更新")
            else:
                print(f"    ⚠️ 試運行模式 - 未儲存")
                
            return True
        else:
            print(f"  ℹ️ 無需更新")
            return False
            
    except Exception as e:
        print(f"  ❌ 處理失敗: {e}")
        return False

def main():
    """主執行函數"""
    print("🔧 批量遷移路由到租戶隔離")
    print("=" * 50)
    
    # 高優先級路由檔案
    high_priority_files = [
        'account.py',
        'api.py', 
        'settings.py',
        'fund_record.py',
        'audit.py',
        'journal_validation.py',
        'new_income_expense.py',  # 已手動遷移，跳過
        'reports.py',
        'share_account.py'
    ]
    
    routes_dir = Path("routes")
    dry_run = '--apply' not in sys.argv  # 預設為試運行
    
    updated_count = 0
    
    for filename in high_priority_files:
        file_path = routes_dir / filename
        if file_path.exists():
            if migrate_route_file(file_path, dry_run):
                updated_count += 1
        else:
            print(f"⚠️ 檔案不存在: {filename}")
    
    print(f"\n📊 遷移結果:")
    print(f"  📁 處理檔案: {len(high_priority_files)} 個")
    print(f"  ✅ 需要更新: {updated_count} 個")
    
    if dry_run:
        print(f"\n💡 這是試運行模式")
        print(f"   執行 python scripts/batch_migrate_routes.py --apply 來套用更改")
    else:
        print(f"\n🎉 批量遷移完成！")
        print(f"   建議測試更新後的路由功能")

if __name__ == "__main__":
    main()