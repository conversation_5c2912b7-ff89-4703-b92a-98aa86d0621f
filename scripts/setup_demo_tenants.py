"""
設置演示租戶數據
創建測試租戶和用戶，用於演示多租戶功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db
from models.tenant_models import Tenant, PlanLevel, TenantStatus
from model import User
from werkzeug.security import generate_password_hash
import datetime

def setup_demo_data():
    """設置演示數據"""
    
    with get_db() as db:
        # 創建演示租戶
        demo_tenants = [
            {
                'name': '小明會計師事務所',
                'slug': 'xiaoming',
                'domain': '',
                'plan_level': PlanLevel.BASIC,
                'status': TenantStatus.ACTIVE,
                'contact_email': '<EMAIL>',
                'contact_person': '小明',
                'contact_phone': '02-1234-5678',
                'address': '台北市信義區信義路五段7號',
                'max_users': 3,
                'max_storage_mb': 500,
            },
            {
                'name': '大華企業股份有限公司',
                'slug': 'dahua',
                'domain': '',
                'plan_level': PlanLevel.PREMIUM,
                'status': TenantStatus.ACTIVE,
                'contact_email': '<EMAIL>',
                'contact_person': '張經理',
                'contact_phone': '03-987-6543',
                'address': '桃園市中壢區中山路100號',
                'tax_id': '12345678',
                'max_users': 10,
                'max_storage_mb': 5120,
            },
            {
                'name': '美食天堂餐廳',
                'slug': 'foodheaven',
                'domain': '',
                'plan_level': PlanLevel.STANDARD,
                'status': TenantStatus.TRIAL,
                'contact_email': '<EMAIL>',
                'contact_person': '王老闆',
                'contact_phone': '07-555-9999',
                'address': '高雄市前金區中正四路200號',
                'max_users': 5,
                'max_storage_mb': 2048,
                'trial_start_date': datetime.date.today(),
                'trial_end_date': datetime.date.today() + datetime.timedelta(days=30),
            }
        ]
        
        created_tenants = []
        
        for tenant_data in demo_tenants:
            # 檢查是否已存在
            existing = db.query(Tenant).filter(Tenant.slug == tenant_data['slug']).first()
            if existing:
                print(f"租戶 {tenant_data['slug']} 已存在，跳過")
                created_tenants.append(existing)
                continue
            
            tenant = Tenant(**tenant_data)
            db.add(tenant)
            db.flush()  # 獲取ID
            created_tenants.append(tenant)
            
            # 為每個租戶創建管理員用戶
            admin_data = {
                'username': f"{tenant.slug}_admin",
                'email': tenant.contact_email,
                'password_hash': generate_password_hash('demo123'),
                'full_name': f"{tenant.name} 管理員",
                'tenant_id': tenant.id,
                'is_tenant_admin': True,
            }
            
            admin_user = User(**admin_data)
            db.add(admin_user)
            
            # 創建一般用戶
            if tenant.plan_level != PlanLevel.BASIC:
                user_data = {
                    'username': f"{tenant.slug}_user",
                    'email': f"user@{tenant.slug}.example.com",
                    'password_hash': generate_password_hash('user123'),
                    'full_name': f"{tenant.name} 用戶",
                    'tenant_id': tenant.id,
                    'is_tenant_admin': False,
                }
                
                regular_user = User(**user_data)
                db.add(regular_user)
            
            print(f"創建租戶: {tenant.name} (slug: {tenant.slug})")
        
        db.commit()
        
        print("\n=== 演示租戶設置完成 ===")
        print("可使用以下方式訪問:")
        
        for tenant in created_tenants:
            print(f"\n{tenant.name}:")
            print(f"  URL: http://{tenant.slug}.localhost:5001")
            print(f"  管理員: {tenant.slug}_admin / demo123")
            print(f"  方案: {tenant.plan_level.value}")
            print(f"  狀態: {tenant.status.value}")
        
        print(f"\n管理後台:")
        print(f"  URL: http://admin.localhost:5001")
        print(f"  (需要設置系統管理員)")

if __name__ == '__main__':
    print("設置演示租戶數據...")
    
    # 先初始化功能數據
    print("1. 初始化功能數據...")
    from init_tenant_features import init_plan_features
    init_plan_features()
    
    # 再設置租戶數據
    print("2. 設置演示租戶...")
    setup_demo_data()
    
    print("\n設置完成！")
    print("\n使用說明:")
    print("1. 在 /etc/hosts 中添加:")
    print("   127.0.0.1 xiaoming.localhost")
    print("   127.0.0.1 dahua.localhost") 
    print("   127.0.0.1 foodheaven.localhost")
    print("   127.0.0.1 admin.localhost")
    print("\n2. 重新啟動Flask應用")
    print("3. 使用上述URL和帳密進行測試")