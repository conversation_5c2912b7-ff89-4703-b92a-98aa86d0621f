#!/usr/bin/env python3
"""
簡單路由測試腳本
直接測試路由模組導入和裝飾器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_route_imports():
    """測試路由模組導入"""
    print("🔍 測試路由模組導入...")
    
    route_modules = [
        ('routes.account', '帳戶管理'),
        ('routes.api', 'API 路由'),
        ('routes.settings', '設定管理'),
        ('routes.fund_record', '資金記錄'),
        ('routes.audit', '審計功能'),
        ('routes.journal_validation', '分錄驗證'),
        ('routes.reports', '報表功能'),
        ('routes.share_account', '分享帳戶'),
    ]
    
    success_count = 0
    
    for module_name, display_name in route_modules:
        try:
            __import__(module_name)
            print(f"  ✅ {display_name} ({module_name}): 導入成功")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {display_name} ({module_name}): 導入失敗 - {e}")
        except Exception as e:
            print(f"  ⚠️ {display_name} ({module_name}): 其他錯誤 - {e}")
    
    print(f"\n📊 模組導入測試結果: {success_count}/{len(route_modules)} 成功")
    return success_count == len(route_modules)

def test_tenant_decorators():
    """測試租戶裝飾器"""
    print("\n🏢 測試租戶裝飾器...")
    
    try:
        from utils.tenant_utils import require_tenant_access, add_tenant_filter
        print("  ✅ 租戶工具導入成功")
        
        # 測試裝飾器函數
        print("  ✅ require_tenant_access 裝飾器可用")
        print("  ✅ add_tenant_filter 函數可用")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 租戶工具導入失敗: {e}")
        return False

def test_route_registrations():
    """測試路由註冊"""
    print("\n📋 測試路由註冊...")
    
    try:
        # 導入並測試主要路由模組
        from routes.account import account_bp
        from routes.new_income_expense import new_income_expense_bp
        
        print("  ✅ account_bp Blueprint 註冊成功")
        print("  ✅ new_income_expense_bp Blueprint 註冊成功")
        
        # 檢查路由數量
        account_rules = len(list(account_bp.iter_rules()))
        income_rules = len(list(new_income_expense_bp.iter_rules()))
        
        print(f"  📊 帳戶路由: {account_rules} 個")
        print(f"  📊 收支路由: {income_rules} 個")
        
        return account_rules > 0 and income_rules > 0
        
    except Exception as e:
        print(f"  ❌ 路由註冊測試失敗: {e}")
        return False

def analyze_migration_status():
    """分析遷移狀態"""
    print("\n📈 分析遷移狀態...")
    
    migration_files = [
        'account.py',
        'api.py', 
        'settings.py',
        'fund_record.py',
        'audit.py',
        'journal_validation.py',
        'new_income_expense.py',
        'reports.py',
        'share_account.py'
    ]
    
    migrated_count = 0
    
    for filename in migration_files:
        filepath = f"routes/{filename}"
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                has_tenant_import = 'require_tenant_access' in content
                has_decorators = '@require_tenant_access' in content
                
                if has_tenant_import and has_decorators:
                    print(f"  ✅ {filename}: 已完成遷移")
                    migrated_count += 1
                elif has_tenant_import:
                    print(f"  🟡 {filename}: 部分遷移 (有導入但缺少裝飾器)")
                else:
                    print(f"  ❌ {filename}: 未遷移")
                    
            except Exception as e:
                print(f"  ❌ {filename}: 無法分析 - {e}")
        else:
            print(f"  ⚠️ {filename}: 檔案不存在")
    
    print(f"\n📊 遷移進度: {migrated_count}/{len(migration_files)} 檔案已完成")
    return migrated_count

def main():
    """主執行函數"""
    print("🧪 路由遷移驗證測試")
    print("=" * 50)
    
    # 執行測試
    imports_ok = test_route_imports()
    decorators_ok = test_tenant_decorators()
    registrations_ok = test_route_registrations()
    migrated_count = analyze_migration_status()
    
    # 生成報告
    print("\n" + "=" * 60)
    print("📋 路由遷移測試報告")
    print("=" * 60)
    
    print(f"\n✅ 測試結果:")
    print(f"  📦 模組導入: {'✅ 正常' if imports_ok else '❌ 異常'}")
    print(f"  🏢 租戶裝飾器: {'✅ 正常' if decorators_ok else '❌ 異常'}")
    print(f"  📋 路由註冊: {'✅ 正常' if registrations_ok else '❌ 異常'}")
    print(f"  📈 遷移進度: {migrated_count}/9 檔案")
    
    overall_success = imports_ok and decorators_ok and registrations_ok
    print(f"\n🎯 整體狀態: {'✅ 成功' if overall_success else '⚠️ 需要檢查'}")
    
    if overall_success and migrated_count >= 7:
        print("\n🎉 路由遷移基本完成！")
        print("💡 多用戶租戶隔離功能已成功整合到主要路由中")
    else:
        print("\n⚠️ 遷移進行中，但核心功能應該可用")
        print("💡 建議繼續完成剩餘路由的遷移")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)