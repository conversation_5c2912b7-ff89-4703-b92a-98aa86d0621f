#!/usr/bin/env python3
"""
增強版資料庫索引腳本
基於慢查詢分析和代碼審查結果，添加額外的性能優化索引
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from database import engine
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_enhanced_indexes():
    """添加增強版索引，針對實際使用場景優化"""
    
    print("🚀 開始添加增強版資料庫索引...")
    
    # 基於代碼分析的額外索引建議
    enhanced_indexes = [
        
        # === Money 表優化索引 ===
        {
            'name': 'idx_money_lend_borrow',
            'table': 'money',
            'columns': ['lend', 'borrow'],
            'type': 'composite',
            'reason': '優化 lend-borrow 計算查詢（統一服務中常用）',
            'priority': 'HIGH',
            'usage': 'balance_sheet_service, income_statement_service'
        },
        {
            'name': 'idx_money_subject_lend_borrow',
            'table': 'money', 
            'columns': ['subject_code', 'lend', 'borrow'],
            'type': 'composite',
            'reason': '按科目計算借貸餘額（最頻繁查詢）',
            'priority': 'HIGH',
            'usage': 'optimized queries in unified services'
        },
        {
            'name': 'idx_money_account_code',
            'table': 'money',
            'columns': ['account_code'],
            'type': 'single',
            'reason': '按帳戶代碼查詢（帳戶餘額計算）',
            'priority': 'HIGH',
            'usage': 'unified_account_service.calculate_account_current_balance'
        },
        {
            'name': 'idx_money_name_fulltext',
            'table': 'money',
            'columns': ['name'],
            'type': 'single',
            'reason': '收支記錄名稱搜索（包含開帳等關鍵詞）',
            'priority': 'MEDIUM',
            'usage': 'name LIKE queries in services'
        },
        {
            'name': 'idx_money_created_by_date',
            'table': 'money',
            'columns': ['created_by', 'created_at'],
            'type': 'composite', 
            'reason': '按建立者和時間查詢（審計追蹤）',
            'priority': 'MEDIUM',
            'usage': 'audit.py route queries'
        },
        
        # === Transaction 表索引 ===
        {
            'name': 'idx_transaction_description',
            'table': 'transaction',
            'columns': ['description'],
            'type': 'single',
            'reason': '交易描述模糊查詢（如光明頂查詢）',
            'priority': 'MEDIUM',
            'usage': 'dashboard_service.py:116'
        },
        {
            'name': 'idx_transaction_date_account',
            'table': 'transaction',
            'columns': ['date', 'account_id'],
            'type': 'composite',
            'reason': '按帳戶和日期範圍查詢交易記錄',
            'priority': 'HIGH',
            'usage': 'account transaction history queries'
        },
        
        # === Account 表額外索引 ===
        {
            'name': 'idx_account_bank_number',
            'table': 'account',
            'columns': ['bank_name', 'account_number'],
            'type': 'composite',
            'reason': '銀行帳戶查詢優化',
            'priority': 'MEDIUM',
            'usage': 'bank account searches'
        },
        {
            'name': 'idx_account_active_category',
            'table': 'account',
            'columns': ['is_active', 'category'],
            'type': 'composite',
            'reason': '啟用帳戶按類別查詢',
            'priority': 'HIGH',
            'usage': 'unified_account_service.get_active_accounts'
        },
        {
            'name': 'idx_account_code_name',
            'table': 'account',
            'columns': ['code', 'name'],
            'type': 'composite',
            'reason': '帳戶搜索優化（代碼或名稱）',
            'priority': 'MEDIUM',
            'usage': 'unified_account_service.search_accounts'
        },
        
        # === PaymentIdentity 表索引 ===
        {
            'name': 'idx_payment_identity_name',
            'table': 'payment_identity',
            'columns': ['name'],
            'type': 'single',
            'reason': '收支對象名稱搜索',
            'priority': 'MEDIUM',
            'usage': 'payment identity searches'
        },
        {
            'name': 'idx_payment_identity_tax_id',
            'table': 'payment_identity',
            'columns': ['tax_id'],
            'type': 'single',
            'reason': '統編查詢優化',
            'priority': 'MEDIUM',
            'usage': 'tax ID searches'
        },
        
        # === Department 表索引 ===
        {
            'name': 'idx_department_active',
            'table': 'department',
            'columns': ['is_deleted'],
            'type': 'single',
            'reason': '查詢啟用部門',
            'priority': 'LOW',
            'usage': 'active department queries'
        },
        
        # === Project 表索引 ===
        {
            'name': 'idx_project_status_active',
            'table': 'project',
            'columns': ['status', 'is_deleted'],
            'type': 'composite',
            'reason': '按狀態查詢專案',
            'priority': 'LOW',
            'usage': 'active project queries'
        },
        
        # === JournalEntry 表索引 ===
        {
            'name': 'idx_journal_transaction_subject',
            'table': 'journal_entry',
            'columns': ['transaction_id', 'subject_code'],
            'type': 'composite',
            'reason': '分錄查詢優化',
            'priority': 'HIGH',
            'usage': 'journal validation and queries'
        },
        {
            'name': 'idx_journal_subject_side',
            'table': 'journal_entry',
            'columns': ['subject_code', 'side'],
            'type': 'composite',
            'reason': '按科目和借貸方向查詢',
            'priority': 'MEDIUM',
            'usage': 'balance sheet and income statement services'
        },
        
        # === AccountSubject 表額外索引 ===
        {
            'name': 'idx_subject_code_prefix',
            'table': 'account_subject',
            'columns': ['code'],
            'type': 'prefix',
            'reason': '科目代碼前綴查詢（如 4%、5% 等）',
            'priority': 'HIGH',
            'usage': 'income/expense classification queries'
        }
    ]
    
    print(f"📊 計劃添加 {len(enhanced_indexes)} 個增強索引")
    
    success_count = 0
    error_count = 0
    skip_count = 0
    
    with engine.connect() as conn:
        for idx in enhanced_indexes:
            try:
                # 檢查索引是否已存在
                check_sql = f"""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND name='{idx['name']}'
                """
                result = conn.execute(text(check_sql)).fetchone()
                
                if result:
                    print(f"  ⚠️  索引 {idx['name']} 已存在，跳過")
                    skip_count += 1
                    continue
                
                # 根據索引類型創建SQL
                columns_str = ', '.join(idx['columns'])
                
                create_sql = f"CREATE INDEX {idx['name']} ON {idx['table']}({columns_str})"
                
                # 執行創建
                start_time = time.time()
                conn.execute(text(create_sql))
                conn.commit()
                end_time = time.time()
                
                print(f"  ✅ {idx['name']} - {idx['reason']} ({end_time-start_time:.2f}s)")
                success_count += 1
                
            except Exception as e:
                print(f"  ❌ {idx['name']} 失敗: {str(e)}")
                error_count += 1
    
    print(f"\n📊 增強索引添加完成:")
    print(f"  ✅ 成功: {success_count} 個")
    print(f"  ⚠️  跳過: {skip_count} 個") 
    print(f"  ❌ 失敗: {error_count} 個")
    
    return success_count, error_count, skip_count

def test_query_performance():
    """測試關鍵查詢的性能"""
    
    print("\n🔍 測試查詢性能...")
    
    test_queries = [
        {
            'name': '按科目和時間查詢餘額',
            'sql': """
            SELECT subject_code, SUM(lend) as total_credit, SUM(borrow) as total_debit
            FROM money 
            WHERE subject_code LIKE '1%' AND a_time >= '2024-01-01'
            GROUP BY subject_code
            """,
            'expected_improvement': '50-80%'
        },
        {
            'name': '帳戶交易記錄查詢',
            'sql': """
            SELECT * FROM money 
            WHERE account_code = '1001' 
            AND a_time BETWEEN '2024-01-01' AND '2024-12-31'
            ORDER BY a_time DESC
            LIMIT 50
            """,
            'expected_improvement': '60-90%'
        },
        {
            'name': '收支對象搜索',
            'sql': """
            SELECT * FROM payment_identity 
            WHERE name LIKE '%公司%' OR tax_id LIKE '%123%'
            LIMIT 20
            """,
            'expected_improvement': '30-60%'
        },
        {
            'name': '本期損益計算',
            'sql': """
            SELECT 
                SUM(CASE WHEN subject_code LIKE '4%' THEN lend-borrow ELSE 0 END) as income,
                SUM(CASE WHEN subject_code LIKE '5%' THEN borrow-lend ELSE 0 END) as expense
            FROM money 
            WHERE a_time >= '2024-01-01' AND is_deleted = 0
            """,
            'expected_improvement': '70-95%'
        }
    ]
    
    with engine.connect() as conn:
        for query in test_queries:
            try:
                print(f"\n📈 測試: {query['name']}")
                
                # 執行查詢並計時
                start_time = time.time()
                result = conn.execute(text(query['sql'])).fetchall()
                end_time = time.time()
                
                execution_time = end_time - start_time
                row_count = len(result)
                
                print(f"  ⏱️  執行時間: {execution_time:.3f}s")
                print(f"  📊 返回記錄: {row_count} 筆")
                print(f"  🎯 預期改善: {query['expected_improvement']}")
                
                # 性能評估
                if execution_time < 0.1:
                    status = "🟢 優秀"
                elif execution_time < 0.5:
                    status = "🟡 良好"
                else:
                    status = "🔴 需要優化"
                    
                print(f"  📈 性能評級: {status}")
                
            except Exception as e:
                print(f"  ❌ 查詢失敗: {str(e)}")

def add_slow_query_monitoring():
    """添加慢查詢監控到資料庫連接"""
    
    print("\n📊 配置慢查詢監控...")
    
    monitoring_code = '''
# 添加到 database.py 中的慢查詢監控代碼

import time
import logging
from sqlalchemy import event

# 設置慢查詢日誌
slow_query_logger = logging.getLogger('slow_query')
slow_query_handler = logging.FileHandler('logs/slow_queries.log')
slow_query_formatter = logging.Formatter(
    '%(asctime)s - SLOW QUERY - %(message)s'
)
slow_query_handler.setFormatter(slow_query_formatter)
slow_query_logger.addHandler(slow_query_handler)
slow_query_logger.setLevel(logging.WARNING)

@event.listens_for(engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    context._query_start_time = time.time()

@event.listens_for(engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    total = time.time() - context._query_start_time
    
    # 記錄慢查詢 (超過 200ms)
    if total > 0.2:
        slow_query_logger.warning(
            f"執行時間: {total:.3f}s | 查詢: {statement[:200]}{'...' if len(statement) > 200 else ''}"
        )
        
        # 超過 1 秒的查詢額外記錄到主日誌
        if total > 1.0:
            logger.error(f"超慢查詢 ({total:.3f}s): {statement[:100]}...")
    '''
    
    print("📝 慢查詢監控代碼:")
    print(monitoring_code)
    print("\n💡 請將上述代碼添加到 database.py 檔案中以啟用慢查詢監控")

if __name__ == "__main__":
    print("🚀 開始資料庫索引優化...")
    
    try:
        # 1. 添加增強索引
        success, error, skip = add_enhanced_indexes()
        
        # 2. 測試查詢性能
        if success > 0:
            print("\n" + "="*50)
            test_query_performance()
        
        # 3. 提供慢查詢監控配置
        print("\n" + "="*50)
        add_slow_query_monitoring()
        
        print(f"\n🎉 索引優化完成！")
        print(f"新增 {success} 個索引，預期查詢性能將顯著提升。")
        
    except Exception as e:
        logger.error(f"索引優化失敗: {e}")
        raise