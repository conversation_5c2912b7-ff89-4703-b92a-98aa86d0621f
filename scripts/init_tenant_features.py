"""
初始化租戶功能數據
定義各方案的功能權限
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db
from models.tenant_models import PlanFeature

def init_plan_features():
    """初始化方案功能數據"""
    
    features = [
        # 收支帳簿模組
        {
            'module_name': 'income_expense',
            'feature_name': 'create_transaction',
            'display_name': '新增帳務',
            'description': '建立收入支出記錄',
            'basic_enabled': True,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'income_expense',
            'feature_name': 'view_records',
            'display_name': '檢視紀錄',
            'description': '查看收支記錄列表',
            'basic_enabled': True,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'income_expense',
            'feature_name': 'transaction_details',
            'display_name': '交易明細',
            'description': '查看詳細交易記錄',
            'basic_enabled': True,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'income_expense',
            'feature_name': 'overdue_tracking',
            'display_name': '應收應付逾期',
            'description': '追蹤逾期未收付款',
            'basic_enabled': False,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        
        # 資金管理模組
        {
            'module_name': 'fund_management',
            'feature_name': 'bank_transfer',
            'display_name': '資金移轉',
            'description': '銀行帳戶間資金移轉',
            'basic_enabled': True,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'fund_management',
            'feature_name': 'bank_loan',
            'display_name': '銀行借款',
            'description': '銀行借款管理',
            'basic_enabled': False,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'fund_management',
            'feature_name': 'fund_record',
            'display_name': '資金紀錄',
            'description': '暫收付款、股東往來等',
            'basic_enabled': False,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        
        # 資產管理模組
        {
            'module_name': 'asset_management',
            'feature_name': 'prepaid_expense',
            'display_name': '預付費用',
            'description': '預付費用管理',
            'basic_enabled': False,
            'standard_enabled': False,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'asset_management',
            'feature_name': 'fixed_asset',
            'display_name': '固定資產',
            'description': '固定資產管理',
            'basic_enabled': False,
            'standard_enabled': False,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        
        # 薪資報酬模組
        {
            'module_name': 'payroll',
            'feature_name': 'salary_process',
            'display_name': '發薪作業',
            'description': '員工薪資計算與發放',
            'basic_enabled': False,
            'standard_enabled': False,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'payroll',
            'feature_name': 'employee_management',
            'display_name': '員工管理',
            'description': '員工資料管理',
            'basic_enabled': False,
            'standard_enabled': False,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        
        # 勞務報酬模組
        {
            'module_name': 'service_reward',
            'feature_name': 'create_service_reward',
            'display_name': '建立勞報單',
            'description': '建立勞務報酬單',
            'basic_enabled': False,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        
        # 報表模組
        {
            'module_name': 'reports',
            'feature_name': 'balance_sheet',
            'display_name': '資產負債表',
            'description': '資產負債表報表',
            'basic_enabled': False,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'reports',
            'feature_name': 'income_statement',
            'display_name': '損益表',
            'description': '損益表報表',
            'basic_enabled': False,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'reports',
            'feature_name': 'cash_flow',
            'display_name': '現金流量表',
            'description': '現金流量表報表',
            'basic_enabled': False,
            'standard_enabled': False,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'reports',
            'feature_name': 'department_analysis',
            'display_name': '部門分析',
            'description': '專案/部門別分析報表',
            'basic_enabled': False,
            'standard_enabled': False,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        
        # 會計科目模組
        {
            'module_name': 'accounting',
            'feature_name': 'subject_management',
            'display_name': '科目管理',
            'description': '會計科目管理',
            'basic_enabled': False,
            'standard_enabled': False,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'accounting',
            'feature_name': 'voucher_management',
            'display_name': '傳票管理',
            'description': '會計傳票管理',
            'basic_enabled': False,
            'standard_enabled': False,
            'premium_enabled': False,
            'enterprise_enabled': True,
        },
        
        # 系統管理模組
        {
            'module_name': 'system',
            'feature_name': 'user_management',
            'display_name': '用戶管理',
            'description': '用戶帳號管理',
            'basic_enabled': False,
            'standard_enabled': True,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
        {
            'module_name': 'system',
            'feature_name': 'backup_management',
            'display_name': '備份管理',
            'description': '數據備份與還原',
            'basic_enabled': False,
            'standard_enabled': False,
            'premium_enabled': True,
            'enterprise_enabled': True,
        },
    ]
    
    with get_db() as db:
        # 清空現有功能（僅限開發環境）
        db.query(PlanFeature).delete()
        
        # 插入功能數據
        for feature_data in features:
            feature = PlanFeature(**feature_data)
            db.add(feature)
        
        db.commit()
        print(f"成功初始化 {len(features)} 個功能項目")

if __name__ == '__main__':
    print("開始初始化租戶功能數據...")
    init_plan_features()
    print("初始化完成！")