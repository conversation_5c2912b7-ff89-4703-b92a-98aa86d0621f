#!/usr/bin/env python3
"""
多用戶遷移分析腳本
分析現有資料庫狀況，為遷移做準備
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import engine
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_database_structure():
    """分析資料庫結構"""
    print("📊 分析資料庫結構...")
    
    with engine.connect() as conn:
        # 獲取所有表
        tables_sql = """
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
        ORDER BY name
        """
        
        tables = conn.execute(text(tables_sql)).fetchall()
        
        # 檢查哪些表有 tenant_id
        tables_with_tenant = []
        tables_without_tenant = []
        
        for (table_name,) in tables:
            # 獲取表結構
            pragma_sql = f"PRAGMA table_info({table_name})"
            columns = conn.execute(text(pragma_sql)).fetchall()
            
            # 檢查是否有 tenant_id 欄位
            has_tenant_id = any(col[1] == 'tenant_id' for col in columns)
            
            if has_tenant_id:
                tables_with_tenant.append(table_name)
            else:
                tables_without_tenant.append(table_name)
        
        print(f"\n✅ 已有 tenant_id 的表 ({len(tables_with_tenant)} 個):")
        for table in sorted(tables_with_tenant):
            print(f"  📋 {table}")
        
        print(f"\n❌ 缺少 tenant_id 的表 ({len(tables_without_tenant)} 個):")
        for table in sorted(tables_without_tenant):
            print(f"  📋 {table}")
        
        return tables_with_tenant, tables_without_tenant

def analyze_data_volume():
    """分析資料量"""
    print("\n📈 分析資料量...")
    
    # 重點關注的表
    important_tables = ['money', 'transactions', 'payment_identity', 'department', 'project']
    
    with engine.connect() as conn:
        for table in important_tables:
            try:
                count_sql = f"SELECT COUNT(*) FROM {table}"
                count = conn.execute(text(count_sql)).scalar()
                
                if count > 0:
                    print(f"  📊 {table}: {count:,} 筆記錄")
                    
                    # 如果是 money 表，顯示更多細節
                    if table == 'money':
                        # 檢查資料分布
                        type_sql = """
                        SELECT money_type, COUNT(*) as count 
                        FROM money 
                        GROUP BY money_type
                        """
                        types = conn.execute(text(type_sql)).fetchall()
                        for money_type, type_count in types:
                            print(f"    └─ {money_type}: {type_count:,} 筆")
                else:
                    print(f"  📊 {table}: 無資料")
                    
            except Exception as e:
                print(f"  ❌ {table}: 查詢失敗 - {e}")

def analyze_existing_tenants():
    """分析現有租戶"""
    print("\n🏢 分析現有租戶...")
    
    with engine.connect() as conn:
        try:
            # 檢查租戶表
            tenant_sql = "SELECT id, name, slug, status FROM tenants"
            tenants = conn.execute(text(tenant_sql)).fetchall()
            
            if tenants:
                print(f"  找到 {len(tenants)} 個租戶:")
                for tenant in tenants:
                    print(f"    🏢 ID:{tenant[0]} - {tenant[1]} ({tenant[2]}) - 狀態:{tenant[3]}")
            else:
                print("  ⚠️  沒有找到租戶資料")
                
            # 檢查用戶分布
            user_sql = """
            SELECT tenant_id, COUNT(*) as user_count 
            FROM users 
            GROUP BY tenant_id
            """
            user_dist = conn.execute(text(user_sql)).fetchall()
            
            print("\n👥 用戶分布:")
            for tenant_id, user_count in user_dist:
                print(f"    租戶 {tenant_id}: {user_count} 個用戶")
                
        except Exception as e:
            print(f"  ❌ 租戶分析失敗: {e}")

def check_data_consistency():
    """檢查資料一致性"""
    print("\n🔍 檢查資料一致性...")
    
    with engine.connect() as conn:
        try:
            # 檢查帳戶表的租戶分布
            account_sql = """
            SELECT tenant_id, COUNT(*) as account_count 
            FROM account 
            GROUP BY tenant_id
            """
            account_dist = conn.execute(text(account_sql)).fetchall()
            
            print("  💳 帳戶分布:")
            for tenant_id, account_count in account_dist:
                print(f"    租戶 {tenant_id}: {account_count} 個帳戶")
            
            # 檢查 money 表與 account 表的關聯
            money_account_sql = """
            SELECT a.tenant_id, COUNT(m.id) as money_count
            FROM money m
            LEFT JOIN account a ON m.account_id = a.id
            GROUP BY a.tenant_id
            """
            money_dist = conn.execute(text(money_account_sql)).fetchall()
            
            print("\n  💰 交易記錄分布 (透過帳戶關聯):")
            for tenant_id, money_count in money_dist:
                tenant_name = f"租戶 {tenant_id}" if tenant_id else "無租戶關聯"
                print(f"    {tenant_name}: {money_count} 筆交易")
                
        except Exception as e:
            print(f"  ❌ 一致性檢查失敗: {e}")

def generate_migration_plan():
    """生成遷移計劃"""
    print("\n📋 生成遷移計劃...")
    
    # 需要添加 tenant_id 的關鍵表
    critical_tables = [
        {
            'name': 'money',
            'priority': '🔴 最高',
            'reason': '核心交易資料，必須隔離',
            'strategy': '透過 account 表關聯推導租戶ID'
        },
        {
            'name': 'payment_identity',
            'priority': '🟡 中等',
            'reason': '收支對象資料，需要隔離',
            'strategy': '根據使用該對象的交易推導租戶ID'
        },
        {
            'name': 'department',
            'priority': '🟡 中等',
            'reason': '部門資料，需要隔離',
            'strategy': '根據使用該部門的交易推導租戶ID'
        },
        {
            'name': 'project',
            'priority': '🟡 中等',
            'reason': '專案資料，需要隔離',
            'strategy': '根據使用該專案的交易推導租戶ID'
        }
    ]
    
    print("  需要遷移的表:")
    for table in critical_tables:
        print(f"    📋 {table['name']}")
        print(f"      優先級: {table['priority']}")
        print(f"      原因: {table['reason']}")
        print(f"      策略: {table['strategy']}")
        print()

def main():
    """主執行函數"""
    print("🔍 多用戶資料庫遷移分析")
    print("=" * 50)
    
    try:
        # 分析資料庫結構
        tables_with_tenant, tables_without_tenant = analyze_database_structure()
        
        # 分析資料量
        analyze_data_volume()
        
        # 分析現有租戶
        analyze_existing_tenants()
        
        # 檢查資料一致性
        check_data_consistency()
        
        # 生成遷移計劃
        generate_migration_plan()
        
        print("\n" + "=" * 50)
        print("📊 分析總結:")
        print(f"  ✅ 已有租戶隔離: {len(tables_with_tenant)} 個表")
        print(f"  ❌ 需要添加隔離: {len(tables_without_tenant)} 個表")
        print("  🎯 重點關注: money, payment_identity, department, project 表")
        
        print("\n💡 建議:")
        print("  1. 先備份資料庫")
        print("  2. 確認預設租戶ID")
        print("  3. 從 money 表開始遷移")
        print("  4. 逐步驗證資料一致性")
        
    except Exception as e:
        logger.error(f"分析失敗: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()