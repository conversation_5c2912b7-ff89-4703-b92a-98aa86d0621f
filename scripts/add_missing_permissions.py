#!/usr/bin/env python3
"""
添加缺少的權限
為所有模組建立完整的權限
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db
from sqlalchemy import text

def add_missing_permissions():
    """添加缺少的權限"""
    print("添加缺少的權限...")
    
    # 定義所有模組的權限
    permissions_data = [
        # 資產管理
        ("asset_management.view", "查看資產管理", "asset_management", "view"),
        ("asset_management.create", "建立資產記錄", "asset_management", "create"),
        ("asset_management.edit", "編輯資產記錄", "asset_management", "edit"),
        ("asset_management.delete", "刪除資產記錄", "asset_management", "delete"),
        
        # 薪資報酬
        ("payroll.view", "查看薪資報酬", "payroll", "view"),
        ("payroll.create", "建立薪資記錄", "payroll", "create"),
        ("payroll.edit", "編輯薪資記錄", "payroll", "edit"),
        ("payroll.delete", "刪除薪資記錄", "payroll", "delete"),
        ("payroll.manage", "管理薪資設定", "payroll", "manage"),
        ("payroll.settings", "薪資系統設定", "payroll", "settings"),
        
        # 勞務報酬
        ("service_reward.view", "查看勞務報酬", "service_reward", "view"),
        ("service_reward.create", "建立勞務報酬", "service_reward", "create"),
        ("service_reward.edit", "編輯勞務報酬", "service_reward", "edit"),
        ("service_reward.delete", "刪除勞務報酬", "service_reward", "delete"),
        
        # 會計科目
        ("accounting.view", "查看會計科目", "accounting", "view"),
        ("accounting.create", "建立會計科目", "accounting", "create"),
        ("accounting.edit", "編輯會計科目", "accounting", "edit"),
        ("accounting.delete", "刪除會計科目", "accounting", "delete"),
        ("accounting.manage", "管理會計設定", "accounting", "manage"),
        
        # 扣繳申報
        ("tax_declaration.view", "查看扣繳申報", "tax_declaration", "view"),
        ("tax_declaration.create", "建立扣繳申報", "tax_declaration", "create"),
        ("tax_declaration.edit", "編輯扣繳申報", "tax_declaration", "edit"),
        ("tax_declaration.delete", "刪除扣繳申報", "tax_declaration", "delete"),
        
        # 收支帳簿 - 補充權限
        ("income_expense.edit", "編輯收支記錄", "income_expense", "edit"),
        ("income_expense.delete", "刪除收支記錄", "income_expense", "delete"),
        ("income_expense.share", "分享帳簿", "income_expense", "share"),
        
        # 資金管理 - 補充權限
        ("fund_management.edit", "編輯資金記錄", "fund_management", "edit"),
        ("fund_management.delete", "刪除資金記錄", "fund_management", "delete"),
        
        # 我的報表 - 補充權限
        ("reports.export", "匯出報表", "reports", "export"),
        ("reports.manage", "管理報表設定", "reports", "manage"),
        
        # 設定 - 補充權限
        ("settings.edit", "編輯設定", "settings", "edit"),
        
        # 系統管理權限
        ("admin.system_info", "查看系統資訊", "admin", "system_info"),
        ("admin.log_management", "日誌管理", "admin", "log_management"),
        ("admin.backup_management", "備份管理", "admin", "backup_management"),
    ]
    
    with get_db() as db:
        added_count = 0
        for name, display_name, module, action in permissions_data:
            # 檢查權限是否已存在
            result = db.execute(text("SELECT id FROM permissions WHERE name = :name"), {"name": name}).fetchone()
            if not result:
                db.execute(text("""
                    INSERT INTO permissions (name, display_name, module, action)
                    VALUES (:name, :display_name, :module, :action)
                """), {
                    "name": name,
                    "display_name": display_name,
                    "module": module,
                    "action": action
                })
                added_count += 1
                print(f"  添加權限: {display_name}")
        
        db.commit()
        print(f"權限添加完成，共添加 {added_count} 個權限")

def assign_permissions_to_admin():
    """為管理員分配所有權限"""
    print("為管理員分配所有權限...")
    
    with get_db() as db:
        # 獲取管理員角色ID
        admin_role = db.execute(text("SELECT id FROM roles WHERE name = 'admin'")).fetchone()
        if not admin_role:
            print("  ❌ 找不到管理員角色")
            return
        
        admin_role_id = admin_role[0]
        
        # 獲取所有權限ID
        permissions = db.execute(text("SELECT id FROM permissions")).fetchall()
        
        # 清除現有權限
        db.execute(text("DELETE FROM role_permissions WHERE role_id = :role_id"), {"role_id": admin_role_id})
        
        # 分配所有權限給管理員
        for perm in permissions:
            db.execute(text("""
                INSERT INTO role_permissions (role_id, permission_id)
                VALUES (:role_id, :permission_id)
            """), {"role_id": admin_role_id, "permission_id": perm[0]})
        
        db.commit()
        print(f"  ✅ 為管理員分配了 {len(permissions)} 個權限")

def main():
    """主函數"""
    print("開始添加缺少的權限...")
    
    try:
        # 添加缺少的權限
        add_missing_permissions()
        
        # 為管理員分配所有權限
        assign_permissions_to_admin()
        
        print("\n✅ 權限補充完成！")
        print("現在管理員應該可以看到所有模組了")
        
    except Exception as e:
        print(f"❌ 添加權限失敗: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()