#!/usr/bin/env python3
"""
路由遷移分析腳本
分析現有路由，識別需要租戶隔離的優先級
"""

import sys
import os
import re
from pathlib import Path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def analyze_route_file(file_path):
    """分析單個路由檔案"""
    result = {
        'file': file_path.name,
        'routes': [],
        'models_used': [],
        'tenant_priority': 'low',
        'already_has_tenant': False
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 找到所有路由定義
        route_patterns = re.findall(r'@\w+\.route\([\'"]([^\'"]+)[\'"].*?\)\s*\n(?:@\w+.*?\n)*def\s+(\w+)', content, re.MULTILINE)
        result['routes'] = [(path, func) for path, func in route_patterns]
        
        # 檢查使用的模型
        tenant_models = ['Money', 'PaymentIdentity', 'Department', 'Project']
        for model in tenant_models:
            if model in content:
                result['models_used'].append(model)
        
        # 檢查是否已有租戶相關程式碼
        tenant_keywords = ['tenant_id', 'require_tenant_access', 'add_tenant_filter', 'TenantMixin']
        for keyword in tenant_keywords:
            if keyword in content:
                result['already_has_tenant'] = True
                break
        
        # 判斷優先級
        high_priority_keywords = ['Money', 'money', 'transaction', 'income', 'expense']
        medium_priority_keywords = ['PaymentIdentity', 'Department', 'Project', 'payment', 'department', 'project']
        
        content_lower = content.lower()
        if any(keyword.lower() in content_lower for keyword in high_priority_keywords):
            result['tenant_priority'] = 'high'
        elif any(keyword.lower() in content_lower for keyword in medium_priority_keywords):
            result['tenant_priority'] = 'medium'
        elif any(keyword in ['admin', 'auth', 'login', 'register'] for keyword in file_path.stem.lower()):
            result['tenant_priority'] = 'skip'  # 系統級路由不需要租戶隔離
            
    except Exception as e:
        result['error'] = str(e)
    
    return result

def main():
    """主執行函數"""
    print("🔍 路由遷移需求分析")
    print("=" * 50)
    
    routes_dir = Path("routes")
    
    high_priority = []
    medium_priority = []
    low_priority = []
    skip_routes = []
    already_migrated = []
    
    # 分析所有路由檔案
    for route_file in sorted(routes_dir.glob("*.py")):
        if route_file.name in ['__init__.py', 'base_route.py']:
            continue
            
        result = analyze_route_file(route_file)
        
        if result['already_has_tenant']:
            already_migrated.append(result)
        elif result['tenant_priority'] == 'skip':
            skip_routes.append(result)
        elif result['tenant_priority'] == 'high':
            high_priority.append(result)
        elif result['tenant_priority'] == 'medium':
            medium_priority.append(result)
        else:
            low_priority.append(result)
    
    # 顯示分析結果
    print(f"\n🔴 高優先級路由 ({len(high_priority)} 個):")
    for route in high_priority:
        print(f"  📋 {route['file']}")
        print(f"    路由數: {len(route['routes'])}")
        print(f"    使用模型: {route['models_used']}")
        if route['routes']:
            print(f"    主要路由: {route['routes'][:2]}")
        print()
    
    print(f"\n🟡 中優先級路由 ({len(medium_priority)} 個):")
    for route in medium_priority:
        print(f"  📋 {route['file']}")
        print(f"    路由數: {len(route['routes'])}")
        print(f"    使用模型: {route['models_used']}")
        if route['routes']:
            print(f"    主要路由: {route['routes'][:2]}")
        print()
    
    print(f"\n🟢 低優先級路由 ({len(low_priority)} 個):")
    for route in low_priority:
        print(f"  📋 {route['file']} - {len(route['routes'])} 個路由")
    
    print(f"\n⚪ 跳過的系統路由 ({len(skip_routes)} 個):")
    for route in skip_routes:
        print(f"  📋 {route['file']} - {len(route['routes'])} 個路由")
    
    print(f"\n✅ 已遷移路由 ({len(already_migrated)} 個):")
    for route in already_migrated:
        print(f"  📋 {route['file']} - {len(route['routes'])} 個路由")
    
    print(f"\n📊 遷移計劃總結:")
    print(f"  🔴 立即遷移: {len(high_priority)} 個檔案")
    print(f"  🟡 次要遷移: {len(medium_priority)} 個檔案") 
    print(f"  🟢 可選遷移: {len(low_priority)} 個檔案")
    print(f"  ⚪ 無需遷移: {len(skip_routes)} 個檔案")
    print(f"  ✅ 已完成: {len(already_migrated)} 個檔案")
    
    # 建議遷移順序
    print(f"\n🎯 建議遷移順序:")
    print(f"  第1階段: 核心收支路由 (高優先級)")
    print(f"  第2階段: 基本資料路由 (中優先級)")
    print(f"  第3階段: 報表功能路由 (低優先級)")
    
    return {
        'high': high_priority,
        'medium': medium_priority,
        'low': low_priority,
        'skip': skip_routes,
        'migrated': already_migrated
    }

if __name__ == "__main__":
    main()