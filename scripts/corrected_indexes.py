#!/usr/bin/env python3
"""
修正版資料庫索引腳本
基於實際資料庫結構創建索引
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from database import engine
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_corrected_indexes():
    """基於實際資料庫結構添加索引"""
    
    print("🚀 開始添加修正版資料庫索引...")
    
    # 基於實際欄位的索引設計
    corrected_indexes = [
        
        # === Money 表索引（基於實際欄位）===
        {
            'name': 'idx_money_total_tax',
            'table': 'money',
            'columns': ['total', 'tax'],
            'reason': '總額和稅額查詢優化',
            'priority': 'MEDIUM'
        },
        {
            'name': 'idx_money_subject_total',
            'table': 'money', 
            'columns': ['subject_code', 'total'],
            'reason': '按科目計算總額（最頻繁查詢）',
            'priority': 'HIGH'
        },
        {
            'name': 'idx_money_account_time',
            'table': 'money',
            'columns': ['account_id', 'a_time'],
            'reason': '按帳戶和時間查詢交易記錄',
            'priority': 'HIGH'
        },
        {
            'name': 'idx_money_type_time',
            'table': 'money',
            'columns': ['money_type', 'a_time'],
            'reason': '按收支類型和時間查詢',
            'priority': 'HIGH'
        },
        {
            'name': 'idx_money_payment_identity',
            'table': 'money',
            'columns': ['payment_identity_id', 'a_time'],
            'reason': '按收支對象查詢',
            'priority': 'MEDIUM'
        },
        {
            'name': 'idx_money_is_paid',
            'table': 'money',
            'columns': ['is_paid', 'should_paid_date'],
            'reason': '應收付款查詢',
            'priority': 'MEDIUM'
        },
        {
            'name': 'idx_money_department_project',
            'table': 'money',
            'columns': ['department_id', 'project_id'],
            'reason': '按部門和專案查詢',
            'priority': 'MEDIUM'
        },
        {
            'name': 'idx_money_created_audit',
            'table': 'money',
            'columns': ['created_by', 'created_at'],
            'reason': '建立者審計查詢',
            'priority': 'LOW'
        },
        {
            'name': 'idx_money_updated_audit',
            'table': 'money',
            'columns': ['updated_by', 'updated_at'],
            'reason': '修改者審計查詢',
            'priority': 'LOW'
        },
        {
            'name': 'idx_money_deleted',
            'table': 'money',
            'columns': ['is_deleted', 'deleted_at'],
            'reason': '軟刪除查詢優化',
            'priority': 'HIGH'
        },
        
        # === Account 表索引 ===
        {
            'name': 'idx_account_bank_info',
            'table': 'account',
            'columns': ['bank_name', 'account_number'],
            'reason': '銀行帳戶查詢',
            'priority': 'MEDIUM'
        },
        {
            'name': 'idx_account_category_default',
            'table': 'account',
            'columns': ['category', 'is_default'],
            'reason': '帳戶類別和預設帳戶查詢',
            'priority': 'HIGH'
        },
        {
            'name': 'idx_account_subject_active',
            'table': 'account',
            'columns': ['subject_code', 'is_deleted'],
            'reason': '按科目查詢啟用帳戶',
            'priority': 'HIGH'
        },
        {
            'name': 'idx_account_tenant',
            'table': 'account',
            'columns': ['tenant_id', 'is_deleted'],
            'reason': '多租戶帳戶查詢',
            'priority': 'HIGH'
        },
        
        # === PaymentIdentity 表索引 ===
        {
            'name': 'idx_payment_identity_search',
            'table': 'payment_identity',
            'columns': ['name'],
            'reason': '收支對象名稱搜索',
            'priority': 'MEDIUM'
        },
        {
            'name': 'idx_payment_identity_tax',
            'table': 'payment_identity',
            'columns': ['tax_id'],
            'reason': '統編查詢',
            'priority': 'MEDIUM'
        },
        
        # === Department 表索引 ===
        {
            'name': 'idx_department_active',
            'table': 'department',
            'columns': ['is_deleted'],
            'reason': '啟用部門查詢',
            'priority': 'LOW'
        },
        
        # === Project 表索引 ===
        {
            'name': 'idx_project_status',
            'table': 'project',
            'columns': ['status', 'is_deleted'],
            'reason': '按狀態查詢專案',
            'priority': 'LOW'
        },
        
        # === AccountSubject 表索引 ===
        {
            'name': 'idx_subject_parent_relation',
            'table': 'account_subject',
            'columns': ['parent_id', 'is_expandable'],
            'reason': '父子關係查詢',
            'priority': 'HIGH'
        },
        {
            'name': 'idx_subject_category',
            'table': 'account_subject',
            'columns': ['top_category'],
            'reason': '頂層分類查詢',
            'priority': 'MEDIUM'
        }
    ]
    
    print(f"📊 計劃添加 {len(corrected_indexes)} 個修正索引")
    
    # 按優先級排序
    high_priority = [idx for idx in corrected_indexes if idx['priority'] == 'HIGH']
    medium_priority = [idx for idx in corrected_indexes if idx['priority'] == 'MEDIUM']
    low_priority = [idx for idx in corrected_indexes if idx['priority'] == 'LOW']
    
    print(f"  🔴 高優先級: {len(high_priority)} 個")
    print(f"  🟡 中優先級: {len(medium_priority)} 個")
    print(f"  🟢 低優先級: {len(low_priority)} 個")
    
    success_count = 0
    error_count = 0
    skip_count = 0
    
    with engine.connect() as conn:
        # 依優先級執行
        for idx_group, group_name in [(high_priority, "高優先級"), (medium_priority, "中優先級"), (low_priority, "低優先級")]:
            if not idx_group:
                continue
                
            print(f"\n🔧 添加{group_name}索引...")
            
            for idx in idx_group:
                try:
                    # 檢查索引是否已存在
                    check_sql = f"""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND name='{idx['name']}'
                    """
                    result = conn.execute(text(check_sql)).fetchone()
                    
                    if result:
                        print(f"  ⚠️  索引 {idx['name']} 已存在，跳過")
                        skip_count += 1
                        continue
                    
                    # 創建索引
                    columns_str = ', '.join(idx['columns'])
                    create_sql = f"CREATE INDEX {idx['name']} ON {idx['table']}({columns_str})"
                    
                    start_time = time.time()
                    conn.execute(text(create_sql))
                    conn.commit()
                    end_time = time.time()
                    
                    print(f"  ✅ {idx['name']} - {idx['reason']} ({end_time-start_time:.2f}s)")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ❌ {idx['name']} 失敗: {str(e)}")
                    error_count += 1
    
    print(f"\n📊 修正索引添加完成:")
    print(f"  ✅ 成功: {success_count} 個")
    print(f"  ⚠️  跳過: {skip_count} 個")
    print(f"  ❌ 失敗: {error_count} 個")
    
    return success_count, error_count, skip_count

def test_realistic_queries():
    """測試基於實際欄位的查詢性能"""
    
    print("\n🔍 測試實際查詢性能...")
    
    test_queries = [
        {
            'name': '按科目和時間統計總額',
            'sql': """
            SELECT subject_code, 
                   SUM(CASE WHEN money_type = '收入' THEN total ELSE 0 END) as income,
                   SUM(CASE WHEN money_type = '支出' THEN total ELSE 0 END) as expense
            FROM money 
            WHERE subject_code LIKE '1%' AND a_time >= '2024-01-01' AND is_deleted = 0
            GROUP BY subject_code
            """,
            'expected_improvement': '60-85%'
        },
        {
            'name': '帳戶交易記錄查詢',
            'sql': """
            SELECT id, name, total, money_type, a_time, subject_code 
            FROM money 
            WHERE account_id = 1 
            AND a_time BETWEEN '2024-01-01' AND '2024-12-31'
            AND is_deleted = 0
            ORDER BY a_time DESC
            LIMIT 50
            """,
            'expected_improvement': '70-90%'
        },
        {
            'name': '未收付款查詢',
            'sql': """
            SELECT COUNT(*) as unpaid_count, SUM(total) as unpaid_amount
            FROM money 
            WHERE is_paid = 0 
            AND should_paid_date < date('now')
            AND is_deleted = 0
            """,
            'expected_improvement': '80-95%'
        },
        {
            'name': '按收支類型統計',
            'sql': """
            SELECT money_type, COUNT(*) as count, SUM(total) as amount
            FROM money 
            WHERE a_time >= '2024-01-01' AND is_deleted = 0
            GROUP BY money_type
            """,
            'expected_improvement': '50-75%'
        },
        {
            'name': '部門專案費用統計',
            'sql': """
            SELECT department_id, project_id, SUM(total) as total_expense
            FROM money 
            WHERE money_type = '支出' 
            AND department_id IS NOT NULL 
            AND is_deleted = 0
            GROUP BY department_id, project_id
            """,
            'expected_improvement': '60-85%'
        }
    ]
    
    with engine.connect() as conn:
        for query in test_queries:
            try:
                print(f"\n📈 測試: {query['name']}")
                
                start_time = time.time()
                result = conn.execute(text(query['sql'])).fetchall()
                end_time = time.time()
                
                execution_time = end_time - start_time
                row_count = len(result)
                
                print(f"  ⏱️  執行時間: {execution_time:.3f}s")
                print(f"  📊 返回記錄: {row_count} 筆")
                print(f"  🎯 預期改善: {query['expected_improvement']}")
                
                if execution_time < 0.05:
                    status = "🟢 優秀"
                elif execution_time < 0.2:
                    status = "🟡 良好"  
                else:
                    status = "🔴 需要優化"
                    
                print(f"  📈 性能評級: {status}")
                
            except Exception as e:
                print(f"  ❌ 查詢失敗: {str(e)}")

def show_current_indexes():
    """顯示當前所有索引"""
    
    print("\n📋 當前資料庫索引:")
    
    with engine.connect() as conn:
        indexes_sql = """
        SELECT name, tbl_name, sql 
        FROM sqlite_master 
        WHERE type='index' 
        AND name NOT LIKE 'sqlite_%'
        ORDER BY tbl_name, name
        """
        
        indexes = conn.execute(text(indexes_sql)).fetchall()
        
        if indexes:
            current_table = None
            for idx in indexes:
                if idx[1] != current_table:
                    current_table = idx[1]
                    print(f"\n📄 {current_table} 表:")
                    
                # 判斷是否為我們創建的索引
                if idx[0].startswith('idx_'):
                    status = "🟢"  # 我們創建的索引
                elif idx[0].startswith('ix_'):
                    status = "🔵"  # SQLAlchemy 創建的索引
                else:
                    status = "⚪"  # 其他索引
                    
                print(f"  {status} {idx[0]}")
        else:
            print("⚠️  未發現任何索引")

if __name__ == "__main__":
    print("🚀 開始修正版資料庫索引優化...")
    
    try:
        # 1. 顯示當前索引狀態
        show_current_indexes()
        
        # 2. 添加修正索引  
        print("\n" + "="*60)
        success, error, skip = add_corrected_indexes()
        
        # 3. 測試查詢性能
        if success > 0:
            print("\n" + "="*60)
            test_realistic_queries()
        
        # 4. 顯示最終索引狀態
        print("\n" + "="*60)
        show_current_indexes()
        
        print(f"\n🎉 修正索引優化完成！")
        print(f"✅ 成功新增 {success} 個索引")
        print(f"⚠️  跳過 {skip} 個已存在索引")
        print(f"❌ 失敗 {error} 個索引")
        print(f"\n💡 建議定期監控查詢性能，並根據實際使用情況調整索引策略。")
        
    except Exception as e:
        logger.error(f"索引優化失敗: {e}")
        raise