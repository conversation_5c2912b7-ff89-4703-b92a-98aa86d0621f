#!/usr/bin/env python3
"""
多用戶遷移腳本
安全地為核心表添加 tenant_id 欄位並分配預設租戶
"""

import sys
import os
import shutil
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import engine, get_db
from sqlalchemy import text, inspect
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def backup_database():
    """備份資料庫"""
    try:
        db_path = "app.db"
        if os.path.exists(db_path):
            backup_name = f"app_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            shutil.copy2(db_path, backup_name)
            logger.info(f"✅ 資料庫已備份至: {backup_name}")
            return backup_name
        else:
            logger.warning("⚠️ 找不到 app.db 檔案")
            return None
    except Exception as e:
        logger.error(f"❌ 備份失敗: {e}")
        return None

def get_default_tenant_id():
    """取得預設租戶ID"""
    with engine.connect() as conn:
        # 查看是否已有租戶
        tenant_sql = "SELECT id FROM tenants ORDER BY id LIMIT 1"
        result = conn.execute(text(tenant_sql)).fetchone()
        
        if result:
            tenant_id = result[0]
            logger.info(f"✅ 使用現有租戶 ID: {tenant_id}")
            return tenant_id
        else:
            # 創建預設租戶
            logger.info("📝 創建預設租戶...")
            insert_sql = """
            INSERT INTO tenants (name, slug, contact_email, plan_level, status, created_at)
            VALUES ('預設公司', 'default', '<EMAIL>', 'basic', 'active', datetime('now'))
            """
            result = conn.execute(text(insert_sql))
            conn.commit()
            
            # 取得新創建的租戶ID
            tenant_id = result.lastrowid or 1
            logger.info(f"✅ 創建預設租戶 ID: {tenant_id}")
            return tenant_id

def add_tenant_id_column(table_name, tenant_id):
    """為表添加 tenant_id 欄位"""
    with engine.connect() as conn:
        try:
            # 檢查是否已存在 tenant_id 欄位
            inspector = inspect(engine)
            columns = [col['name'] for col in inspector.get_columns(table_name)]
            
            if 'tenant_id' in columns:
                logger.info(f"⚠️ {table_name} 表已有 tenant_id 欄位")
                return True
            
            # 添加 tenant_id 欄位
            logger.info(f"📝 為 {table_name} 表添加 tenant_id 欄位...")
            alter_sql = f"ALTER TABLE {table_name} ADD COLUMN tenant_id INTEGER"
            conn.execute(text(alter_sql))
            
            # 為所有現有記錄設定預設租戶ID
            update_sql = f"UPDATE {table_name} SET tenant_id = :tenant_id WHERE tenant_id IS NULL"
            result = conn.execute(text(update_sql), {"tenant_id": tenant_id})
            
            # 添加索引
            index_sql = f"CREATE INDEX IF NOT EXISTS idx_{table_name}_tenant_id ON {table_name}(tenant_id)"
            conn.execute(text(index_sql))
            
            conn.commit()
            logger.info(f"✅ {table_name} 表成功添加 tenant_id 欄位，更新了 {result.rowcount} 筆記錄")
            return True
            
        except Exception as e:
            logger.error(f"❌ {table_name} 表遷移失敗: {e}")
            conn.rollback()
            return False

def update_money_table_with_account_relation(tenant_id):
    """特殊處理 money 表 - 透過 account 關聯推導租戶ID"""
    with engine.connect() as conn:
        try:
            logger.info("🔄 根據 account 關聯更新 money 表的 tenant_id...")
            
            # 透過 account 關聯更新 money 表的租戶ID
            update_sql = """
            UPDATE money 
            SET tenant_id = (
                SELECT a.tenant_id 
                FROM account a 
                WHERE a.id = money.account_id
            ) 
            WHERE account_id IS NOT NULL 
            AND EXISTS (SELECT 1 FROM account a WHERE a.id = money.account_id)
            """
            result = conn.execute(text(update_sql))
            
            # 對於沒有 account 關聯的記錄，使用預設租戶ID
            default_update_sql = "UPDATE money SET tenant_id = :tenant_id WHERE tenant_id IS NULL"
            result2 = conn.execute(text(default_update_sql), {"tenant_id": tenant_id})
            
            conn.commit()
            logger.info(f"✅ 透過 account 關聯更新了 {result.rowcount} 筆 money 記錄")
            logger.info(f"✅ 設定預設租戶給 {result2.rowcount} 筆 money 記錄")
            
        except Exception as e:
            logger.error(f"❌ money 表租戶ID更新失敗: {e}")
            conn.rollback()

def migrate_core_tables():
    """遷移核心表"""
    print("🚀 開始多用戶資料庫遷移...")
    print("=" * 60)
    
    # 1. 備份資料庫
    backup_file = backup_database()
    if not backup_file:
        print("❌ 備份失敗，停止遷移")
        return False
    
    try:
        # 2. 取得預設租戶ID
        default_tenant_id = get_default_tenant_id()
        
        # 3. 需要遷移的核心表
        core_tables = [
            'money',              # 最重要 - 交易記錄
            'payment_identity',   # 收支對象
            'department',         # 部門
            'project',           # 專案
        ]
        
        # 4. 遷移每個表
        success_count = 0
        for table in core_tables:
            print(f"\n🔄 處理 {table} 表...")
            if add_tenant_id_column(table, default_tenant_id):
                success_count += 1
            else:
                print(f"❌ {table} 表遷移失敗")
        
        # 5. 特殊處理 money 表的租戶關聯
        if 'money' in core_tables:
            update_money_table_with_account_relation(default_tenant_id)
        
        print(f"\n" + "=" * 60)
        print(f"📊 遷移結果:")
        print(f"  ✅ 成功遷移: {success_count}/{len(core_tables)} 個表")
        print(f"  📁 備份檔案: {backup_file}")
        print(f"  🏢 預設租戶ID: {default_tenant_id}")
        
        if success_count == len(core_tables):
            print(f"\n🎉 多用戶遷移完成！")
            return True
        else:
            print(f"\n⚠️ 部分表遷移失敗，請檢查錯誤訊息")
            return False
            
    except Exception as e:
        logger.error(f"❌ 遷移過程發生錯誤: {e}")
        print(f"\n💡 如遇問題，可從備份檔案 {backup_file} 恢復")
        return False

def verify_migration():
    """驗證遷移結果"""
    print(f"\n🔍 驗證遷移結果...")
    
    with engine.connect() as conn:
        core_tables = ['money', 'payment_identity', 'department', 'project']
        
        for table in core_tables:
            try:
                # 檢查 tenant_id 欄位
                inspector = inspect(engine)
                columns = [col['name'] for col in inspector.get_columns(table)]
                
                if 'tenant_id' in columns:
                    # 檢查資料分布
                    count_sql = f"SELECT tenant_id, COUNT(*) FROM {table} GROUP BY tenant_id"
                    results = conn.execute(text(count_sql)).fetchall()
                    
                    print(f"  📋 {table}:")
                    for tenant_id, count in results:
                        tenant_name = f"租戶{tenant_id}" if tenant_id else "NULL"
                        print(f"    └─ {tenant_name}: {count} 筆記錄")
                else:
                    print(f"  ❌ {table}: 缺少 tenant_id 欄位")
                    
            except Exception as e:
                print(f"  ❌ {table}: 驗證失敗 - {e}")

if __name__ == "__main__":
    success = migrate_core_tables()
    if success:
        verify_migration()
        print(f"\n💡 下一步:")
        print(f"  1. 檢查應用程式是否正常運作")
        print(f"  2. 更新查詢邏輯添加租戶過濾")
        print(f"  3. 實作租戶隔離中介軟體")
    else:
        print(f"\n❌ 遷移失敗，請檢查錯誤並重試")
        sys.exit(1)