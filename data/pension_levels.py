"""
勞工退休金投保薪資分級表資料
114年1月1日起適用
"""

# 勞工退休金投保薪資分級表 (114年1月1日起適用)
# 根據您提供的資料
PENSION_LEVELS = [
    {"level": 1, "salary_range": "1,500元以下", "pension_salary": 1500},
    {"level": 2, "salary_range": "1,501元至3,000元", "pension_salary": 3000},
    {"level": 3, "salary_range": "3,001元至4,500元", "pension_salary": 4500},
    {"level": 4, "salary_range": "4,501元至6,000元", "pension_salary": 6000},
    {"level": 5, "salary_range": "6,001元至7,500元", "pension_salary": 7500},
    {"level": 6, "salary_range": "7,501元至8,700元", "pension_salary": 8700},
    {"level": 7, "salary_range": "8,701元至9,900元", "pension_salary": 9900},
    {"level": 8, "salary_range": "9,901元至11,100元", "pension_salary": 11100},
    {"level": 9, "salary_range": "11,101元至12,540元", "pension_salary": 12540},
    {"level": 10, "salary_range": "12,541元至13,500元", "pension_salary": 13500},
    {"level": 11, "salary_range": "13,501元至15,840元", "pension_salary": 15840},
    {"level": 12, "salary_range": "15,841元至16,500元", "pension_salary": 16500},
    {"level": 13, "salary_range": "16,501元至17,280元", "pension_salary": 17280},
    {"level": 14, "salary_range": "17,281元至17,880元", "pension_salary": 17880},
    {"level": 15, "salary_range": "17,881元至19,047元", "pension_salary": 19047},
    {"level": 16, "salary_range": "19,048元至20,008元", "pension_salary": 20008},
    {"level": 17, "salary_range": "20,009元至21,009元", "pension_salary": 21009},
    {"level": 18, "salary_range": "21,010元至22,000元", "pension_salary": 22000},
    {"level": 19, "salary_range": "22,001元至23,100元", "pension_salary": 23100},
    {"level": 20, "salary_range": "23,101元至24,000元", "pension_salary": 24000},
    {"level": 21, "salary_range": "24,001元至25,250元", "pension_salary": 25250},
    {"level": 22, "salary_range": "25,251元至26,400元", "pension_salary": 26400},
    {"level": 23, "salary_range": "26,401元至27,600元", "pension_salary": 27600},
    {"level": 24, "salary_range": "27,601元至28,590元", "pension_salary": 28590},
    {"level": 25, "salary_range": "28,591元至28,800元", "pension_salary": 28800},
    {"level": 26, "salary_range": "28,801元至30,300元", "pension_salary": 30300},
    {"level": 27, "salary_range": "30,301元至31,800元", "pension_salary": 31800},
    {"level": 28, "salary_range": "31,801元至33,300元", "pension_salary": 33300},
    {"level": 29, "salary_range": "33,301元至34,800元", "pension_salary": 34800},
    {"level": 30, "salary_range": "34,801元至36,300元", "pension_salary": 36300},
    {"level": 31, "salary_range": "36,301元至38,200元", "pension_salary": 38200},
    {"level": 32, "salary_range": "38,201元至40,100元", "pension_salary": 40100},
    {"level": 33, "salary_range": "40,101元至42,000元", "pension_salary": 42000},
    {"level": 34, "salary_range": "42,001元至43,900元", "pension_salary": 43900},
    {"level": 35, "salary_range": "43,901元至45,800元", "pension_salary": 45800},
    {"level": 36, "salary_range": "45,801元至48,200元", "pension_salary": 48200},
    {"level": 37, "salary_range": "48,201元至50,600元", "pension_salary": 50600},
    {"level": 38, "salary_range": "50,601元至53,000元", "pension_salary": 53000},
    {"level": 39, "salary_range": "53,001元至55,400元", "pension_salary": 55400},
    {"level": 40, "salary_range": "55,401元至57,800元", "pension_salary": 57800},
    {"level": 41, "salary_range": "57,801元至60,800元", "pension_salary": 60800},
    {"level": 42, "salary_range": "60,801元至63,800元", "pension_salary": 63800},
    {"level": 43, "salary_range": "63,801元至66,800元", "pension_salary": 66800},
    {"level": 44, "salary_range": "66,801元至69,800元", "pension_salary": 69800},
    {"level": 45, "salary_range": "69,801元至72,800元", "pension_salary": 72800},
    {"level": 46, "salary_range": "72,801元至76,500元", "pension_salary": 76500},
    {"level": 47, "salary_range": "76,501元至80,200元", "pension_salary": 80200},
    {"level": 48, "salary_range": "80,201元至83,900元", "pension_salary": 83900},
    {"level": 49, "salary_range": "83,901元至87,600元", "pension_salary": 87600},
    {"level": 50, "salary_range": "87,601元至92,100元", "pension_salary": 92100},
    {"level": 51, "salary_range": "92,101元至96,600元", "pension_salary": 96600},
    {"level": 52, "salary_range": "96,601元至101,100元", "pension_salary": 101100},
    {"level": 53, "salary_range": "101,101元至105,600元", "pension_salary": 105600},
    {"level": 54, "salary_range": "105,601元至110,100元", "pension_salary": 110100},
    {"level": 55, "salary_range": "110,101元至115,500元", "pension_salary": 115500},
    {"level": 56, "salary_range": "115,501元至120,900元", "pension_salary": 120900},
    {"level": 57, "salary_range": "120,901元至126,300元", "pension_salary": 126300},
    {"level": 58, "salary_range": "126,301元至131,700元", "pension_salary": 131700},
    {"level": 59, "salary_range": "131,701元至137,100元", "pension_salary": 137100},
    {"level": 60, "salary_range": "137,101元至142,500元", "pension_salary": 142500},
    {"level": 61, "salary_range": "142,501元至147,900元", "pension_salary": 147900},
    {"level": 62, "salary_range": "147,901元以上", "pension_salary": 150000},
]

def get_pension_level_by_salary(monthly_salary):
    """
    根據月薪資取得勞退投保級距
    
    Args:
        monthly_salary (int): 月薪資
        
    Returns:
        dict: 勞退級距資料，如果找不到則返回 None
    """
    # 特殊處理最高級距
    if monthly_salary >= 147901:
        return PENSION_LEVELS[-1]  # 第62級
    
    # 根據薪資範圍查找對應級距
    for level_data in PENSION_LEVELS:
        salary_range = level_data["salary_range"]
        
        if "以下" in salary_range:
            # 處理 "1,500元以下"
            max_salary = int(salary_range.replace("元以下", "").replace(",", ""))
            if monthly_salary <= max_salary:
                return level_data
        elif "至" in salary_range:
            # 處理 "1,501元至3,000元"
            parts = salary_range.replace("元", "").split("至")
            min_salary = int(parts[0].replace(",", ""))
            max_salary = int(parts[1].replace(",", ""))
            if min_salary <= monthly_salary <= max_salary:
                return level_data
    
    return None

def get_pension_levels_for_select():
    """
    取得用於下拉選單的勞退級距資料
    
    Returns:
        list: 格式化的級距選項列表
    """
    options = []
    for level_data in PENSION_LEVELS:
        label = f"第{level_data['level']}級 - {level_data['salary_range']} (投保薪資: {level_data['pension_salary']:,}元)"
        
        options.append({
            "value": level_data["level"],
            "label": label,
            "pension_salary": level_data["pension_salary"],
            "salary_range": level_data["salary_range"]
        })
    
    return options

def calculate_pension_contribution(pension_salary, employer_rate=0.06, employee_rate=0.06):
    """
    計算勞退提繳金額
    
    Args:
        pension_salary (int): 投保薪資
        employer_rate (float): 雇主提繳比率 (預設 6%)
        employee_rate (float): 員工自願提繳比率 (預設 6%)
        
    Returns:
        dict: 各項提繳金額
    """
    # 雇主強制提繳 6%
    employer_contribution = int(pension_salary * employer_rate / 100)
    
    # 員工自願提繳 (0% ~ 6%)
    employee_contribution = int(pension_salary * employee_rate / 100)
    
    return {
        "pension_salary": pension_salary,
        "employer_contribution": employer_contribution,
        "employee_contribution": employee_contribution,
        "total_contribution": employer_contribution + employee_contribution,
        "employer_rate": employer_rate,
        "employee_rate": employee_rate
    }
