"""
勞工保險投保薪資分級表資料
114年1月1日起適用
"""

# 勞保投保薪資分級表 (114年1月1日起適用)
# 根據您提供的圖片正確資料
LABOR_INSURANCE_LEVELS = [
    {"level": 1, "monthly_salary_min": 1, "monthly_salary_max": 28500, "insured_salary": 28500},
    {"level": 2, "monthly_salary_min": 28501, "monthly_salary_max": 28800, "insured_salary": 28800},
    {"level": 3, "monthly_salary_min": 28801, "monthly_salary_max": 30300, "insured_salary": 30300},
    {"level": 4, "monthly_salary_min": 30301, "monthly_salary_max": 31800, "insured_salary": 31800},
    {"level": 5, "monthly_salary_min": 31801, "monthly_salary_max": 33300, "insured_salary": 33300},
    {"level": 6, "monthly_salary_min": 33301, "monthly_salary_max": 34800, "insured_salary": 34800},
    {"level": 7, "monthly_salary_min": 34801, "monthly_salary_max": 36300, "insured_salary": 36300},
    {"level": 8, "monthly_salary_min": 36301, "monthly_salary_max": 38200, "insured_salary": 38200},
    {"level": 9, "monthly_salary_min": 38201, "monthly_salary_max": 40100, "insured_salary": 40100},
    {"level": 10, "monthly_salary_min": 40101, "monthly_salary_max": 42000, "insured_salary": 42000},
    {"level": 11, "monthly_salary_min": 42001, "monthly_salary_max": 43900, "insured_salary": 43900},
    {"level": 12, "monthly_salary_min": 43901, "monthly_salary_max": 999999, "insured_salary": 45800},
]

def get_labor_insurance_level_by_salary(monthly_salary):
    """
    根據月薪資取得勞保投保級距
    
    Args:
        monthly_salary (int): 月薪資
        
    Returns:
        dict: 勞保級距資料，如果找不到則返回 None
    """
    for level_data in LABOR_INSURANCE_LEVELS:
        if level_data["monthly_salary_min"] <= monthly_salary <= level_data["monthly_salary_max"]:
            return level_data
    return None

def get_labor_insurance_levels_for_select():
    """
    取得用於下拉選單的勞保級距資料
    
    Returns:
        list: 格式化的級距選項列表
    """
    options = []
    for level_data in LABOR_INSURANCE_LEVELS:
        if level_data["level"] == 45:  # 最高級距
            label = f"第{level_data['level']}級 - {level_data['monthly_salary_min']:,}元以上 (投保薪資: {level_data['insured_salary']:,}元)"
        else:
            label = f"第{level_data['level']}級 - {level_data['monthly_salary_min']:,}~{level_data['monthly_salary_max']:,}元 (投保薪資: {level_data['insured_salary']:,}元)"
        
        options.append({
            "value": level_data["level"],
            "label": label,
            "insured_salary": level_data["insured_salary"],
            "min_salary": level_data["monthly_salary_min"],
            "max_salary": level_data["monthly_salary_max"]
        })
    
    return options

def calculate_labor_insurance_premium(insured_salary, employee_rate=0.12, employer_rate=0.105, employment_insurance_rate=0.01):
    """
    計算勞保費用
    
    Args:
        insured_salary (int): 投保薪資
        employee_rate (float): 員工負擔比率 (預設 12%)
        employer_rate (float): 雇主負擔比率 (預設 10.5%)
        employment_insurance_rate (float): 就業保險費率 (預設 1%)
        
    Returns:
        dict: 各項保險費用
    """
    # 勞保費 (普通事故保險費率 + 職災保險費率)
    labor_insurance_employee = int(insured_salary * employee_rate / 100)
    labor_insurance_employer = int(insured_salary * employer_rate / 100)
    
    # 就業保險費 (員工和雇主各負擔 0.5%)
    employment_insurance_employee = int(insured_salary * (employment_insurance_rate / 2) / 100)
    employment_insurance_employer = int(insured_salary * (employment_insurance_rate / 2) / 100)
    
    return {
        "insured_salary": insured_salary,
        "labor_insurance_employee": labor_insurance_employee,
        "labor_insurance_employer": labor_insurance_employer,
        "employment_insurance_employee": employment_insurance_employee,
        "employment_insurance_employer": employment_insurance_employer,
        "total_employee": labor_insurance_employee + employment_insurance_employee,
        "total_employer": labor_insurance_employer + employment_insurance_employer
    }
