"""
全民健康保險投保金額分級表資料
114年1月1日起適用
"""

# 健保投保金額分級表 (114年1月1日起適用)
# 根據您提供的資料
HEALTH_INSURANCE_LEVELS = [
    {"group": "第一組", "level": 1, "monthly_salary_min": 1, "monthly_salary_max": 28590, "insured_amount": 28590},
    {"group": "第一組", "level": 2, "monthly_salary_min": 28591, "monthly_salary_max": 28800, "insured_amount": 28800},
    {"group": "第二組", "level": 3, "monthly_salary_min": 28801, "monthly_salary_max": 30300, "insured_amount": 30300},
    {"group": "第二組", "level": 4, "monthly_salary_min": 30301, "monthly_salary_max": 31800, "insured_amount": 31800},
    {"group": "第二組", "level": 5, "monthly_salary_min": 31801, "monthly_salary_max": 33300, "insured_amount": 33300},
    {"group": "第二組", "level": 6, "monthly_salary_min": 33301, "monthly_salary_max": 34800, "insured_amount": 34800},
    {"group": "第二組", "level": 7, "monthly_salary_min": 34801, "monthly_salary_max": 36300, "insured_amount": 36300},
    {"group": "第三組", "level": 8, "monthly_salary_min": 36301, "monthly_salary_max": 38200, "insured_amount": 38200},
    {"group": "第三組", "level": 9, "monthly_salary_min": 38201, "monthly_salary_max": 40100, "insured_amount": 40100},
    {"group": "第三組", "level": 10, "monthly_salary_min": 40101, "monthly_salary_max": 42000, "insured_amount": 42000},
    {"group": "第三組", "level": 11, "monthly_salary_min": 42001, "monthly_salary_max": 43900, "insured_amount": 43900},
    {"group": "第三組", "level": 12, "monthly_salary_min": 43901, "monthly_salary_max": 45800, "insured_amount": 45800},
    {"group": "第四組", "level": 13, "monthly_salary_min": 45801, "monthly_salary_max": 48200, "insured_amount": 48200},
    {"group": "第四組", "level": 14, "monthly_salary_min": 48201, "monthly_salary_max": 50600, "insured_amount": 50600},
    {"group": "第四組", "level": 15, "monthly_salary_min": 50601, "monthly_salary_max": 53000, "insured_amount": 53000},
    {"group": "第四組", "level": 16, "monthly_salary_min": 53001, "monthly_salary_max": 55400, "insured_amount": 55400},
    {"group": "第四組", "level": 17, "monthly_salary_min": 55401, "monthly_salary_max": 57800, "insured_amount": 57800},
    {"group": "第五組", "level": 18, "monthly_salary_min": 57801, "monthly_salary_max": 60800, "insured_amount": 60800},
    {"group": "第五組", "level": 19, "monthly_salary_min": 60801, "monthly_salary_max": 63800, "insured_amount": 63800},
    {"group": "第五組", "level": 20, "monthly_salary_min": 63801, "monthly_salary_max": 66800, "insured_amount": 66800},
    {"group": "第五組", "level": 21, "monthly_salary_min": 66801, "monthly_salary_max": 69800, "insured_amount": 69800},
    {"group": "第五組", "level": 22, "monthly_salary_min": 69801, "monthly_salary_max": 72800, "insured_amount": 72800},
    {"group": "第六組", "level": 23, "monthly_salary_min": 72801, "monthly_salary_max": 76500, "insured_amount": 76500},
    {"group": "第六組", "level": 24, "monthly_salary_min": 76501, "monthly_salary_max": 80200, "insured_amount": 80200},
    {"group": "第六組", "level": 25, "monthly_salary_min": 80201, "monthly_salary_max": 83900, "insured_amount": 83900},
    {"group": "第六組", "level": 26, "monthly_salary_min": 83901, "monthly_salary_max": 87600, "insured_amount": 87600},
    {"group": "第七組", "level": 27, "monthly_salary_min": 87601, "monthly_salary_max": 92100, "insured_amount": 92100},
    {"group": "第七組", "level": 28, "monthly_salary_min": 92101, "monthly_salary_max": 96600, "insured_amount": 96600},
    {"group": "第七組", "level": 29, "monthly_salary_min": 96601, "monthly_salary_max": 101100, "insured_amount": 101100},
    {"group": "第七組", "level": 30, "monthly_salary_min": 101101, "monthly_salary_max": 105600, "insured_amount": 105600},
    {"group": "第七組", "level": 31, "monthly_salary_min": 105601, "monthly_salary_max": 110100, "insured_amount": 110100},
    {"group": "第八組", "level": 32, "monthly_salary_min": 110101, "monthly_salary_max": 115500, "insured_amount": 115500},
    {"group": "第八組", "level": 33, "monthly_salary_min": 115501, "monthly_salary_max": 120900, "insured_amount": 120900},
    {"group": "第八組", "level": 34, "monthly_salary_min": 120901, "monthly_salary_max": 126300, "insured_amount": 126300},
    {"group": "第八組", "level": 35, "monthly_salary_min": 126301, "monthly_salary_max": 131700, "insured_amount": 131700},
    {"group": "第八組", "level": 36, "monthly_salary_min": 131701, "monthly_salary_max": 137100, "insured_amount": 137100},
    {"group": "第八組", "level": 37, "monthly_salary_min": 137101, "monthly_salary_max": 142500, "insured_amount": 142500},
    {"group": "第八組", "level": 38, "monthly_salary_min": 142501, "monthly_salary_max": 147900, "insured_amount": 147900},
    {"group": "第八組", "level": 39, "monthly_salary_min": 147901, "monthly_salary_max": 150000, "insured_amount": 150000},
    {"group": "第九組", "level": 40, "monthly_salary_min": 150001, "monthly_salary_max": 156400, "insured_amount": 156400},
    {"group": "第九組", "level": 41, "monthly_salary_min": 156401, "monthly_salary_max": 162800, "insured_amount": 162800},
    {"group": "第九組", "level": 42, "monthly_salary_min": 162801, "monthly_salary_max": 169200, "insured_amount": 169200},
    {"group": "第九組", "level": 43, "monthly_salary_min": 169201, "monthly_salary_max": 175600, "insured_amount": 175600},
    {"group": "第九組", "level": 44, "monthly_salary_min": 175601, "monthly_salary_max": 182000, "insured_amount": 182000},
    {"group": "第十組", "level": 45, "monthly_salary_min": 182001, "monthly_salary_max": 189500, "insured_amount": 189500},
    {"group": "第十組", "level": 46, "monthly_salary_min": 189501, "monthly_salary_max": 197000, "insured_amount": 197000},
    {"group": "第十組", "level": 47, "monthly_salary_min": 197001, "monthly_salary_max": 204500, "insured_amount": 204500},
    {"group": "第十組", "level": 48, "monthly_salary_min": 204501, "monthly_salary_max": 212000, "insured_amount": 212000},
    {"group": "第十組", "level": 49, "monthly_salary_min": 212001, "monthly_salary_max": 219500, "insured_amount": 219500},
    {"group": "第十一組", "level": 50, "monthly_salary_min": 219501, "monthly_salary_max": 228200, "insured_amount": 228200},
    {"group": "第十一組", "level": 51, "monthly_salary_min": 228201, "monthly_salary_max": 236900, "insured_amount": 236900},
    {"group": "第十一組", "level": 52, "monthly_salary_min": 236901, "monthly_salary_max": 245600, "insured_amount": 245600},
    {"group": "第十一組", "level": 53, "monthly_salary_min": 245601, "monthly_salary_max": 254300, "insured_amount": 254300},
    {"group": "第十一組", "level": 54, "monthly_salary_min": 254301, "monthly_salary_max": 263000, "insured_amount": 263000},
    {"group": "第十二組", "level": 55, "monthly_salary_min": 263001, "monthly_salary_max": 273000, "insured_amount": 273000},
    {"group": "第十二組", "level": 56, "monthly_salary_min": 273001, "monthly_salary_max": 283000, "insured_amount": 283000},
    {"group": "第十二組", "level": 57, "monthly_salary_min": 283001, "monthly_salary_max": 293000, "insured_amount": 293000},
    {"group": "第十二組", "level": 58, "monthly_salary_min": 293001, "monthly_salary_max": 303000, "insured_amount": 303000},
    {"group": "第十二組", "level": 59, "monthly_salary_min": 303001, "monthly_salary_max": 999999, "insured_amount": 313000},
]

def get_health_insurance_level_by_salary(monthly_salary):
    """
    根據月薪資取得健保投保級距
    
    Args:
        monthly_salary (int): 月薪資
        
    Returns:
        dict: 健保級距資料，如果找不到則返回 None
    """
    for level_data in HEALTH_INSURANCE_LEVELS:
        if level_data["monthly_salary_min"] <= monthly_salary <= level_data["monthly_salary_max"]:
            return level_data
    return None

def get_health_insurance_levels_for_select():
    """
    取得用於下拉選單的健保級距資料
    
    Returns:
        list: 格式化的級距選項列表
    """
    options = []
    for level_data in HEALTH_INSURANCE_LEVELS:
        if level_data["level"] == 59:  # 最高級距
            label = f"{level_data['group']}第{level_data['level']}級 - {level_data['monthly_salary_min']:,}元以上 (投保金額: {level_data['insured_amount']:,}元)"
        else:
            label = f"{level_data['group']}第{level_data['level']}級 - {level_data['monthly_salary_min']:,}~{level_data['monthly_salary_max']:,}元 (投保金額: {level_data['insured_amount']:,}元)"
        
        options.append({
            "value": level_data["level"],
            "label": label,
            "group": level_data["group"],
            "insured_amount": level_data["insured_amount"],
            "min_salary": level_data["monthly_salary_min"],
            "max_salary": level_data["monthly_salary_max"]
        })
    
    return options

def calculate_health_insurance_premium(insured_amount):
    """
    計算健保費用

    Args:
        insured_amount (int): 投保金額

    Returns:
        dict: 各項健保費用
    """
    # 健保費計算 (一般保險對象)
    # 健保費率 5.17%
    # 員工負擔 30%，雇主負擔 60%，政府負擔 10%
    health_rate = 5.17
    total_premium = int(insured_amount * health_rate / 100)

    employee_premium = int(total_premium * 0.3)  # 員工負擔 30%
    employer_premium = int(total_premium * 0.6)  # 雇主負擔 60%
    government_premium = int(total_premium * 0.1)  # 政府負擔 10%

    return {
        "insured_amount": insured_amount,
        "total_premium": total_premium,
        "employee_premium": employee_premium,
        "employer_premium": employer_premium,
        "government_premium": government_premium
    }
