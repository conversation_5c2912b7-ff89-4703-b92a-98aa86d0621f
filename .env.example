# 環境變數範例文件
# 複製此文件為 .env 並設置實際值

# Flask 安全密鑰（必須設置！）
# 生成方式：python -c "import secrets; print(secrets.token_hex(32))"
SECRET_KEY=508b1c2350b36dc9e4d02d128f717d1c8e8cc67e34135c5bd8b6d497ee548cd7

# 管理員密碼（必須設置！）
ADMIN_PASSWORD=Admin@2024#Secure

# 資料庫設定
DATABASE_URL=sqlite:///app.db

# 應用程式環境
FLASK_ENV=development
FLASK_DEBUG=True

# 日誌設定
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 安全設定
SESSION_COOKIE_SECURE=False
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

# 上傳設定
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# 備份設定
BACKUP_FOLDER=backups
AUTO_BACKUP=True
BACKUP_RETENTION_DAYS=30
