"""
租戶隔離中介軟體
自動為所有請求添加租戶上下文和隔離檢查
"""

from flask import Flask, request, g, session, abort, current_app
from functools import wraps
from typing import Optional, Callable, Any
import logging

logger = logging.getLogger(__name__)

class TenantMiddleware:
    """租戶隔離中介軟體"""
    
    def __init__(self, app: Optional[Flask] = None):
        self.app = app
        if app is not None:
            self.init_app(app)
    
    def init_app(self, app: Flask):
        """初始化應用程式"""
        app.before_request(self.load_tenant_context)
        app.teardown_appcontext(self.clear_tenant_context)
        
        # 註冊錯誤處理器
        app.register_error_handler(403, self.handle_tenant_access_denied)
    
    def load_tenant_context(self):
        """
        在每個請求開始時載入租戶上下文
        """
        try:
            # 從session取得租戶ID
            tenant_id = session.get('tenant_id')
            
            if tenant_id:
                # 設定全域租戶上下文
                g.tenant_id = tenant_id
                logger.debug(f"設定租戶上下文: {tenant_id}")
            else:
                # 對於需要租戶存取權限的路由，這將在裝飾器中處理
                g.tenant_id = None
                
                # 檢查是否為需要租戶權限的端點
                if self._requires_tenant_access():
                    logger.warning(f"需要租戶權限但未找到租戶ID - 路由: {request.endpoint}")
        
        except Exception as e:
            logger.error(f"載入租戶上下文失敗: {e}")
            g.tenant_id = None
    
    def clear_tenant_context(self, exception):
        """清除租戶上下文"""
        if hasattr(g, 'tenant_id'):
            delattr(g, 'tenant_id')
    
    def _requires_tenant_access(self) -> bool:
        """
        檢查當前端點是否需要租戶存取權限
        """
        if not request.endpoint:
            return False
        
        # 不需要租戶權限的端點
        public_endpoints = {
            'auth.login',
            'auth.logout', 
            'main.index',
            'static',
            'auth.register',
            'api.health'
        }
        
        # 管理員端點（超級用戶權限）
        admin_endpoints = {
            'tenant_admin.',
            'admin.',
            'permission_admin.'
        }
        
        # 檢查是否為公開端點
        if request.endpoint in public_endpoints:
            return False
            
        # 檢查是否為管理員端點
        for admin_prefix in admin_endpoints:
            if request.endpoint.startswith(admin_prefix):
                return False  # 管理員端點有自己的權限檢查
        
        return True
    
    def handle_tenant_access_denied(self, error):
        """處理租戶存取被拒絕的錯誤"""
        logger.warning(f"租戶存取被拒絕: {request.endpoint}, 用戶: {session.get('username', 'anonymous')}")
        
        if request.is_json:
            return {'error': '無租戶存取權限', 'code': 'TENANT_ACCESS_DENIED'}, 403
        else:
            return render_template('errors/403_tenant.html'), 403

def tenant_required(f: Callable) -> Callable:
    """
    裝飾器：強制要求租戶存取權限
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(g, 'tenant_id') or g.tenant_id is None:
            logger.warning(f"租戶存取被拒絕 - 路由: {request.endpoint}")
            abort(403, "需要租戶存取權限")
        
        return f(*args, **kwargs)
    
    return decorated_function

def get_current_tenant_id() -> Optional[int]:
    """
    取得當前請求的租戶ID
    
    Returns:
        int: 租戶ID，如果沒有則返回None
    """
    return getattr(g, 'tenant_id', None)

def validate_tenant_access(resource_tenant_id: int) -> bool:
    """
    驗證當前用戶是否可以存取指定租戶的資源
    
    Args:
        resource_tenant_id: 資源所屬的租戶ID
        
    Returns:
        bool: 是否有存取權限
    """
    current_tenant = get_current_tenant_id()
    
    if current_tenant is None:
        return False
        
    # 基本權限檢查：只能存取自己租戶的資源
    return current_tenant == resource_tenant_id

def tenant_isolation_check(model_class, record_id: int) -> bool:
    """
    檢查記錄是否屬於當前租戶
    
    Args:
        model_class: 資料模型類別
        record_id: 記錄ID
        
    Returns:
        bool: 是否通過隔離檢查
    """
    from database import get_db
    
    current_tenant = get_current_tenant_id()
    if not current_tenant:
        return False
    
    # 如果模型沒有tenant_id欄位，視為通過檢查
    if not hasattr(model_class, 'tenant_id'):
        logger.warning(f"模型 {model_class.__name__} 缺少 tenant_id 欄位")
        return True
    
    try:
        with get_db() as db:
            record = db.query(model_class).filter(
                model_class.id == record_id,
                model_class.tenant_id == current_tenant
            ).first()
            
            return record is not None
            
    except Exception as e:
        logger.error(f"租戶隔離檢查失敗: {e}")
        return False

class TenantAwareQueryMixin:
    """
    提供租戶感知查詢的混合類別
    可以被服務類別繼承使用
    """
    
    def get_tenant_query(self, db_session, model_class):
        """
        取得有租戶過濾的查詢物件
        
        Args:
            db_session: 資料庫會話
            model_class: 模型類別
            
        Returns:
            過濾後的查詢物件
        """
        tenant_id = get_current_tenant_id()
        
        base_query = db_session.query(model_class)
        
        # 如果有租戶ID且模型支持租戶隔離
        if tenant_id and hasattr(model_class, 'tenant_id'):
            return base_query.filter(model_class.tenant_id == tenant_id)
        
        return base_query
    
    def get_by_id_safe(self, db_session, model_class, record_id: int):
        """
        安全地根據ID取得記錄（含租戶隔離）
        
        Args:
            db_session: 資料庫會話
            model_class: 模型類別
            record_id: 記錄ID
            
        Returns:
            記錄物件或None
        """
        query = self.get_tenant_query(db_session, model_class)
        return query.filter(model_class.id == record_id).first()

# 建立全域中介軟體實例
tenant_middleware = TenantMiddleware()