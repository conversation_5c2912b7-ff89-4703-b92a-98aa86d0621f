#!/usr/bin/env python3
"""
檢查開帳記錄的腳本
"""
import os
import sys
from datetime import date

# 添加項目根目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_opening_records():
    """檢查開帳記錄"""
    try:
        print("=== 檢查開帳記錄 ===")
        
        from database import get_db
        from model import Money, Account
        
        with get_db() as db:
            # 檢查所有Money記錄
            all_money = db.query(Money).all()
            print(f"總Money記錄數: {len(all_money)}")
            
            # 檢查開帳相關記錄
            opening_records = db.query(Money).filter(
                Money.name.like('%開帳%')
            ).all()
            
            print(f"開帳相關記錄數: {len(opening_records)}")
            
            for record in opening_records:
                print(f"- ID: {record.id}")
                print(f"  名稱: {record.name}")
                print(f"  日期: {record.a_time}")
                print(f"  金額: {record.total}")
                print(f"  類型: {record.money_type}")
                print(f"  帳戶ID: {record.account_id}")
                print()
            
            # 檢查今日記錄
            today = date.today()
            today_records = db.query(Money).filter(
                Money.a_time == today
            ).all()
            
            print(f"今日({today})記錄數: {len(today_records)}")
            
            for record in today_records:
                print(f"- {record.name}: {record.total}")
            
            # 檢查有期初金額的帳戶
            accounts_with_init = db.query(Account).filter(
                Account.init_amount.isnot(None), 
                Account.init_amount > 0
            ).all()
            
            print(f"\n有期初金額的帳戶數: {len(accounts_with_init)}")
            for acc in accounts_with_init:
                print(f"- {acc.name}: NT$ {acc.init_amount:,}")
                
    except Exception as e:
        print(f"錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_opening_records()
