#!/usr/bin/env python3
"""
修正帳戶的科目代碼，將簡短代碼轉換為完整的會計科目代碼
"""
import os
import sys

# 添加項目根目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_account_subject_codes():
    """修正帳戶的科目代碼"""
    try:
        print("=== 修正帳戶科目代碼 ===")
        
        from database import get_db
        from model import Account
        
        with get_db() as db:
            # 查找所有需要修正的帳戶
            accounts = db.query(Account).all()
            
            updated_count = 0
            
            for account in accounts:
                old_code = account.subject_code
                new_code = None
                
                # 檢查是否需要修正
                if account.category == '現金' and account.subject_code:
                    # 如果不是以1105開頭，則需要修正
                    if not account.subject_code.startswith('1105'):
                        new_code = f'1105{str(account.subject_code).zfill(3)}'
                        
                elif account.category == '銀行帳戶' and account.subject_code:
                    # 如果不是以1110開頭，則需要修正
                    if not account.subject_code.startswith('1110'):
                        new_code = f'1110{str(account.subject_code).zfill(3)}'
                
                # 執行修正
                if new_code:
                    print(f"修正帳戶: {account.name}")
                    print(f"  類別: {account.category}")
                    print(f"  舊代碼: {old_code}")
                    print(f"  新代碼: {new_code}")
                    
                    account.subject_code = new_code
                    updated_count += 1
                    print("  ✅ 已修正")
                    print()
            
            if updated_count > 0:
                db.commit()
                print(f"✅ 成功修正 {updated_count} 個帳戶的科目代碼")
            else:
                print("ℹ️  沒有需要修正的帳戶")
                
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    fix_account_subject_codes()
