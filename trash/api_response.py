"""
API 響應標準化工具
統一 API 響應格式，提升前後端協作效率
"""

from flask import jsonify, Response
from datetime import datetime
from typing import Any, Dict, List, Optional
import logging

logger = logging.getLogger(__name__)


class APIResponse:
    """標準化 API 響應類"""

    @staticmethod
    def success(data: Any = None, message: str = "操作成功", meta: Optional[Dict] = None) -> Response:
        """成功響應"""
        response = {
            "success": True,
            "message": message,
            "data": data,
            "timestamp": datetime.now().isoformat(),
            "error": None,
        }

        if meta:
            response["meta"] = meta

        return jsonify(response)

    @staticmethod
    def error(
        message: str = "操作失敗",
        error_code: Optional[str] = None,
        details: Optional[Dict] = None,
        status_code: int = 400,
    ) -> tuple[Response, int]:
        """錯誤響應"""
        response = {
            "success": False,
            "message": message,
            "data": None,
            "timestamp": datetime.now().isoformat(),
            "error": {"code": error_code, "details": details or {}},
        }

        return jsonify(response), status_code

    @staticmethod
    def paginated(
        data: List, page: int, per_page: int, total: int, message: str = "查詢成功"
    ) -> Response:
        """分頁響應"""
        total_pages = (total + per_page - 1) // per_page

        meta = {
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1,
            }
        }

        return APIResponse.success(data=data, message=message, meta=meta)

    @staticmethod
    def created(data: Any = None, message: str = "創建成功") -> tuple[Response, int]:
        """創建成功響應"""
        response = APIResponse.success(data=data, message=message)
        return response, 201

    @staticmethod
    def no_content(message: str = "操作成功") -> tuple[Response, int]:
        """無內容響應"""
        response = {
            "success": True,
            "message": message,
            "timestamp": datetime.now().isoformat(),
        }
        return jsonify(response), 204

    @staticmethod
    def validation_error(errors: Dict, message: str = "資料驗證失敗") -> tuple[Response, int]:
        """驗證錯誤響應"""
        return APIResponse.error(
            message=message,
            error_code="VALIDATION_ERROR",
            details={"validation_errors": errors},
            status_code=422,
        )

    @staticmethod
    def not_found(resource: str = "資源", message: Optional[str] = None) -> tuple:
        """找不到資源響應"""
        if not message:
            message = f"找不到指定的{resource}"

        return APIResponse.error(
            message=message, error_code="NOT_FOUND", status_code=404
        )

    @staticmethod
    def unauthorized(message: str = "未授權訪問") -> tuple:
        """未授權響應"""
        return APIResponse.error(
            message=message, error_code="UNAUTHORIZED", status_code=401
        )

    @staticmethod
    def forbidden(message: str = "權限不足") -> tuple:
        """禁止訪問響應"""
        return APIResponse.error(
            message=message, error_code="FORBIDDEN", status_code=403
        )


def api_response(func):
    """API 響應裝飾器 - 自動處理異常並返回標準格式"""
    from functools import wraps
    from utils.error_handler import (
        ValidationError,
        DatabaseError,
        BusinessLogicError,
        AuthenticationError,
        PermissionError,
    )

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)

            # 如果函數已經返回了 Flask Response 對象，直接返回
            if hasattr(result, "status_code"):
                return result

            # 如果返回的是元組（通常是 jsonify 結果和狀態碼）
            if isinstance(result, tuple):
                return result

            # 否則包裝為成功響應
            return APIResponse.success(data=result)

        except ValidationError as e:
            logger.warning(f"API 驗證錯誤: {str(e)}")
            return APIResponse.validation_error(
                errors={e.error_code: e.message}, message=e.message
            )

        except DatabaseError as e:
            logger.error(f"API 資料庫錯誤: {str(e)}")
            return APIResponse.error(
                message="資料庫操作失敗", error_code=e.error_code, status_code=500
            )

        except BusinessLogicError as e:
            logger.warning(f"API 業務邏輯錯誤: {str(e)}")
            return APIResponse.error(
                message=e.message, error_code=e.error_code, status_code=422
            )

        except AuthenticationError as e:
            logger.warning(f"API 認證錯誤: {str(e)}")
            return APIResponse.unauthorized(message=e.message)

        except PermissionError as e:
            logger.warning(f"API 權限錯誤: {str(e)}")
            return APIResponse.forbidden(message=e.message)

        except Exception as e:
            logger.error(f"API 未預期錯誤: {str(e)}")
            return APIResponse.error(
                message="系統內部錯誤", error_code="INTERNAL_ERROR", status_code=500
            )

    return wrapper


class APIValidator:
    """API 參數驗證器"""

    @staticmethod
    def validate_pagination(page: Any, per_page: Any, max_per_page: int = 100) -> tuple:
        """驗證分頁參數"""
        from utils.error_handler import ValidationError

        try:
            page = int(page) if page else 1
            per_page = int(per_page) if per_page else 20
        except (ValueError, TypeError):
            raise ValidationError("分頁參數必須為整數")

        if page < 1:
            raise ValidationError("頁碼必須大於0")

        if per_page < 1:
            raise ValidationError("每頁數量必須大於0")

        if per_page > max_per_page:
            raise ValidationError(f"每頁數量不能超過{max_per_page}")

        return page, per_page

    @staticmethod
    def validate_date_range(start_date: str, end_date: str) -> tuple:
        """驗證日期範圍"""
        from datetime import datetime
        from utils.error_handler import ValidationError

        try:
            start = (
                datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
            )
            end = datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else None
        except ValueError:
            raise ValidationError("日期格式錯誤，請使用 YYYY-MM-DD 格式")

        if start and end and start > end:
            raise ValidationError("開始日期不能晚於結束日期")

        return start, end

    @staticmethod
    def validate_required_fields(data: Dict, required_fields: List[str]) -> None:
        """驗證必填欄位"""
        from utils.error_handler import ValidationError

        missing_fields = []

        for field in required_fields:
            if (
                field not in data
                or data[field] is None
                or str(data[field]).strip() == ""
            ):
                missing_fields.append(field)

        if missing_fields:
            raise ValidationError(
                f"缺少必填欄位: {', '.join(missing_fields)}",
                error_code="MISSING_REQUIRED_FIELDS",
            )
