#!/usr/bin/env python3
"""
測試開帳功能的腳本
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from model import Account, Money
from datetime import date

def test_opening():
    """測試開帳功能"""
    try:
        with get_db() as db:
            # 檢查帳戶
            accounts = db.query(Account).filter(Account.init_amount.isnot(None), Account.init_amount > 0).all()
            print(f"找到 {len(accounts)} 個有期初金額的帳戶:")
            
            for acc in accounts:
                print(f"- {acc.name}: NT$ {acc.init_amount:,}")
                print(f"  科目代碼: {acc.subject_code}")
                print(f"  帳戶ID: {acc.id}")
            
            # 檢查是否已有開帳記錄
            today = date.today()
            existing = db.query(Money).filter(
                Money.name.like('%開帳%'),
                Money.a_time == today
            ).all()
            
            print(f"\n今日已有 {len(existing)} 筆開帳記錄:")
            for record in existing:
                print(f"- {record.name}: NT$ {record.total:,}")
            
            return True
            
    except Exception as e:
        print(f"測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_opening()
