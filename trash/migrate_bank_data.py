#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
銀行資料遷移腳本
將 bank_data.py 中的資料匯入到資料庫
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from model import BankHeadOffice, BankBranch
from data.bank_data import HEAD_OFFICES, BRANCHES

def migrate_bank_data():
    """遷移銀行資料到資料庫"""
    print("開始遷移銀行資料...")
    
    with get_db() as db:
        # 清空現有資料
        print("清空現有銀行資料...")
        # db.query(BankBranch).delete()
        # db.query(BankHeadOffice).delete()
        # db.commit()
        
        # 匯入總行資料
        print("匯入總行資料...")
        head_offices = []
        for code, name in HEAD_OFFICES.items():
            head_office = BankHeadOffice(code=code, name=name)
            head_offices.append(head_office)
            db.add_all(head_offices)
            db.commit()
        print(f"已匯入 {len(head_offices)} 筆總行資料")
        
        # 匯入分行資料
        print("匯入分行資料...")
        branches = []
        for code, info in BRANCHES.items():
            branch = BankBranch(
                code=code,
                name=info["name"],
                head_office_code=info["head_office"]
            )
            branches.append(branch)
        
            db.add_all(branches)
            db.commit()
        print(f"已匯入 {len(branches)} 筆分行資料")
        
        # 驗證資料
        total_head_offices = db.query(BankHeadOffice).count()
        total_branches = db.query(BankBranch).count()
        
        print("\n遷移完成！")
        print(f"總行數量: {total_head_offices}")
        print(f"分行數量: {total_branches}")
        
        # 顯示一些範例資料
        print("\n範例總行資料:")
        sample_head_offices = db.query(BankHeadOffice).limit(5).all()
        for ho in sample_head_offices:
            branch_count = db.query(BankBranch).filter_by(head_office_code=ho.code).count()
            print(f"  {ho.code}: {ho.name} ({branch_count} 個分行)")
        
        print("\n範例分行資料:")
        sample_branches = db.query(BankBranch).limit(5).all()
        for branch in sample_branches:
            print(f"  {branch.code}: {branch.name} (總行: {branch.head_office_code})")

if __name__ == "__main__":
    try:
        migrate_bank_data()
        print("\n✅ 銀行資料遷移成功！")
    except Exception as e:
        print(f"\n❌ 遷移失敗: {e}")
        import traceback
        traceback.print_exc() 