#!/usr/bin/env python3
"""
修復側邊欄顏色問題的腳本
"""

import re

def fix_sidebar_html():
    """修復側邊欄 HTML 中的顏色問題"""
    
    # 讀取當前的側邊欄文件
    with open('templates/sidebar.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修復標題顏色
    content = re.sub(
        r'<h1 class="title is-4 has-text-white mb-0">',
        r'<h1 class="title is-4 has-text-white mb-0" style="color: white !important;">',
        content
    )
    
    # 修復所有選單項目
    # 匹配 <a class="..." 的模式
    def fix_menu_item(match):
        full_match = match.group(0)
        if 'style=' not in full_match:
            # 在 > 之前添加樣式
            return full_match.replace('>', ' style="color: rgba(255, 255, 255, 0.8) !important;">')
        return full_match
    
    # 修復所有的 <a> 標籤
    content = re.sub(
        r'<a[^>]*class="[^"]*(?:has-text-white-ter|sidebar-menu-item)[^"]*"[^>]*>',
        fix_menu_item,
        content
    )
    
    # 修復所有的 <span> 和 <i> 標籤
    content = re.sub(
        r'<span class="icon"[^>]*>',
        r'<span class="icon" style="color: inherit !important;">',
        content
    )
    
    content = re.sub(
        r'<i class="fas[^"]*"[^>]*>',
        lambda m: m.group(0).replace('>', ' style="color: inherit !important;">') if 'style=' not in m.group(0) else m.group(0),
        content
    )
    
    content = re.sub(
        r'<span>([^<]+)</span>',
        r'<span style="color: inherit !important;">\1</span>',
        content
    )
    
    # 寫回文件
    with open('templates/sidebar.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 側邊欄 HTML 顏色修復完成")

def create_simple_sidebar():
    """創建一個簡化的側邊欄，確保顏色正確"""
    
    sidebar_content = '''<!-- 基於 Bulma 的現代化側邊欄 - 顏色修復版 -->
<aside class="custom-sidebar" style="background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%) !important; color: white !important;">
    <!-- 側邊欄品牌區域 -->
    <div class="p-5 has-text-centered" style="border-bottom: 1px solid rgba(255, 255, 255, 0.1); color: white !important;">
        <h1 class="title is-4 has-text-white mb-0" style="color: white !important;">
            <i class="fas fa-calculator mr-2" style="color: white !important;"></i>
            印錢大師
        </h1>
    </div>

    <!-- 側邊欄選單 -->
    <div class="menu p-4" style="color: white !important;">
        <ul class="menu-list">
            <li>
                <a href="/" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-tachometer-alt" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">總覽</span>
                </a>
            </li>
            
            <li>
                <a class="sidebar-menu-item" data-menu="income-expense" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none; cursor: pointer;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-book" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">收支帳簿</span>
                    <span class="icon is-small ml-auto expand-icon" style="color: inherit !important;">
                        <i class="fas fa-chevron-right" style="color: inherit !important;"></i>
                    </span>
                </a>
            </li>
            
            <li>
                <a class="sidebar-menu-item" data-menu="fund-management" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none; cursor: pointer;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-money-bill-wave" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">資金管理</span>
                    <span class="icon is-small ml-auto expand-icon" style="color: inherit !important;">
                        <i class="fas fa-chevron-right" style="color: inherit !important;"></i>
                    </span>
                </a>
            </li>
            
            <li>
                <a class="sidebar-menu-item" data-menu="asset-management" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none; cursor: pointer;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-building" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">資產管理</span>
                    <span class="icon is-small ml-auto expand-icon" style="color: inherit !important;">
                        <i class="fas fa-chevron-right" style="color: inherit !important;"></i>
                    </span>
                </a>
            </li>
            
            <li>
                <a class="sidebar-menu-item" data-menu="salary" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none; cursor: pointer;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-user-tie" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">薪資報酬</span>
                    <span class="icon is-small ml-auto expand-icon" style="color: inherit !important;">
                        <i class="fas fa-chevron-right" style="color: inherit !important;"></i>
                    </span>
                </a>
            </li>
            
            <li>
                <a class="sidebar-menu-item" data-menu="labor-compensation" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none; cursor: pointer;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-file-invoice-dollar" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">勞務報酬</span>
                    <span class="icon is-small ml-auto expand-icon" style="color: inherit !important;">
                        <i class="fas fa-chevron-right" style="color: inherit !important;"></i>
                    </span>
                </a>
            </li>
            
            <li>
                <a class="sidebar-menu-item" data-menu="reports" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none; cursor: pointer;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-chart-bar" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">我的報表</span>
                    <span class="icon is-small ml-auto expand-icon" style="color: inherit !important;">
                        <i class="fas fa-chevron-right" style="color: inherit !important;"></i>
                    </span>
                </a>
            </li>
            
            <li>
                <a class="sidebar-menu-item" data-menu="accounting-subjects" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none; cursor: pointer;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-list-alt" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">會計科目</span>
                    <span class="icon is-small ml-auto expand-icon" style="color: inherit !important;">
                        <i class="fas fa-chevron-right" style="color: inherit !important;"></i>
                    </span>
                </a>
            </li>
            
            <li>
                <a class="sidebar-menu-item" data-menu="tax-filing" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none; cursor: pointer;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-file-alt" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">扣繳申報</span>
                    <span class="icon is-small ml-auto expand-icon" style="color: inherit !important;">
                        <i class="fas fa-chevron-right" style="color: inherit !important;"></i>
                    </span>
                </a>
            </li>
            
            <li>
                <a class="sidebar-menu-item" data-menu="settings" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none; cursor: pointer;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-cog" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">設定</span>
                    <span class="icon is-small ml-auto expand-icon" style="color: inherit !important;">
                        <i class="fas fa-chevron-right" style="color: inherit !important;"></i>
                    </span>
                </a>
            </li>
            
            <li>
                <a class="sidebar-menu-item" data-menu="system" style="color: rgba(255, 255, 255, 0.8) !important; text-decoration: none; cursor: pointer;">
                    <span class="icon" style="color: inherit !important;">
                        <i class="fas fa-tools" style="color: inherit !important;"></i>
                    </span>
                    <span style="color: inherit !important;">系統管理</span>
                    <span class="icon is-small ml-auto expand-icon" style="color: inherit !important;">
                        <i class="fas fa-chevron-right" style="color: inherit !important;"></i>
                    </span>
                </a>
            </li>
        </ul>
    </div>
</aside>'''
    
    # 讀取原始文件以保留子選單部分
    with open('templates/sidebar.html', 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    # 找到子選單部分的開始
    submenu_start = original_content.find('<!-- 收支帳簿子選單 -->')
    if submenu_start != -1:
        submenu_content = original_content[submenu_start:]
        sidebar_content += '\n\n' + submenu_content
    
    # 寫入新的側邊欄
    with open('templates/sidebar.html', 'w', encoding='utf-8') as f:
        f.write(sidebar_content)
    
    print("✅ 創建了新的側邊欄，確保顏色正確")

if __name__ == "__main__":
    print("🔧 修復側邊欄顏色問題...")
    create_simple_sidebar()
    print("✅ 修復完成！現在側邊欄應該有正確的顏色了。")