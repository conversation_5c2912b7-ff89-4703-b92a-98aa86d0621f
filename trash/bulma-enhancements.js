/**
 * Bulma 組件增強腳本
 * Bulma Components Enhancement Script
 */

(function () {
    'use strict';

    // Bulma 增強器
    const BulmaEnhancer = {
        init: function () {
            this.initNavbar();
            this.initDropdowns();
            this.initModals();
            this.initTabs();
            this.initAccordions();
            this.initTooltips();

            if (window.App && window.App.logger) {
                window.App.logger.log('Bulma 增強器已初始化', 'success');
            }
        },

        // 導航欄增強
        initNavbar: function () {
            // 漢堡選單切換
            const navbarBurgers = document.querySelectorAll('.navbar-burger');
            navbarBurgers.forEach(burger => {
                burger.addEventListener('click', function () {
                    const target = document.getElementById(this.dataset.target) ||
                        document.querySelector('.navbar-menu');

                    if (target) {
                        this.classList.toggle('is-active');
                        target.classList.toggle('is-active');
                    }
                });
            });
        },

        // 下拉選單增強
        initDropdowns: function () {
            const dropdowns = document.querySelectorAll('.dropdown');
            dropdowns.forEach(dropdown => {
                const trigger = dropdown.querySelector('.dropdown-trigger');
                if (trigger) {
                    trigger.addEventListener('click', function (e) {
                        e.stopPropagation();
                        dropdown.classList.toggle('is-active');
                    });
                }
            });

            // 點擊外部關閉下拉選單
            document.addEventListener('click', function () {
                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('is-active');
                });
            });
        },

        // 模態框增強
        initModals: function () {
            // 開啟模態框
            const modalTriggers = document.querySelectorAll('[data-modal-target]');
            modalTriggers.forEach(trigger => {
                trigger.addEventListener('click', function () {
                    const targetId = this.getAttribute('data-modal-target');
                    const modal = document.getElementById(targetId);
                    if (modal) {
                        modal.classList.add('is-active');
                        document.documentElement.classList.add('is-clipped');
                    }
                });
            });

            // 關閉模態框
            const modalCloses = document.querySelectorAll('.modal-background, .modal-close, .modal-card-head .delete');
            modalCloses.forEach(close => {
                close.addEventListener('click', function () {
                    const modal = this.closest('.modal');
                    if (modal) {
                        modal.classList.remove('is-active');
                        document.documentElement.classList.remove('is-clipped');
                    }
                });
            });

            // ESC 鍵關閉模態框
            document.addEventListener('keydown', function (e) {
                if (e.key === 'Escape') {
                    const activeModal = document.querySelector('.modal.is-active');
                    if (activeModal) {
                        activeModal.classList.remove('is-active');
                        document.documentElement.classList.remove('is-clipped');
                    }
                }
            });
        },

        // 標籤頁增強
        initTabs: function () {
            const tabsContainers = document.querySelectorAll('.tabs');
            tabsContainers.forEach(container => {
                const tabs = container.querySelectorAll('li');
                const contents = document.querySelectorAll('.tab-content');

                tabs.forEach((tab, index) => {
                    tab.addEventListener('click', function (e) {
                        e.preventDefault();

                        // 移除所有活動狀態
                        tabs.forEach(t => t.classList.remove('is-active'));
                        contents.forEach(c => c.classList.remove('is-active'));

                        // 添加活動狀態
                        this.classList.add('is-active');
                        if (contents[index]) {
                            contents[index].classList.add('is-active');
                        }
                    });
                });
            });
        },

        // 手風琴增強
        initAccordions: function () {
            const accordions = document.querySelectorAll('.accordion-item');
            accordions.forEach(item => {
                const header = item.querySelector('.accordion-header');
                const content = item.querySelector('.accordion-content');

                if (header && content) {
                    header.addEventListener('click', function () {
                        const isActive = item.classList.contains('is-active');

                        // 關閉其他手風琴項目（可選）
                        accordions.forEach(otherItem => {
                            if (otherItem !== item) {
                                otherItem.classList.remove('is-active');
                            }
                        });

                        // 切換當前項目
                        item.classList.toggle('is-active', !isActive);
                    });
                }
            });
        },

        // 工具提示增強
        initTooltips: function () {
            const tooltips = document.querySelectorAll('[data-tooltip]');
            tooltips.forEach(element => {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                tooltip.textContent = element.getAttribute('data-tooltip');

                element.addEventListener('mouseenter', function () {
                    document.body.appendChild(tooltip);
                    const rect = this.getBoundingClientRect();
                    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
                    tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
                    tooltip.classList.add('is-active');
                });

                element.addEventListener('mouseleave', function () {
                    tooltip.classList.remove('is-active');
                    setTimeout(() => {
                        if (tooltip.parentNode) {
                            tooltip.parentNode.removeChild(tooltip);
                        }
                    }, 200);
                });
            });
        }
    };

    // 等待 DOM 載入完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function () {
            BulmaEnhancer.init();
        });
    } else {
        BulmaEnhancer.init();
    }

    // 暴露到全局
    window.BulmaEnhancer = BulmaEnhancer;

})();