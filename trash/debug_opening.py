#!/usr/bin/env python3
"""
調試開帳功能的腳本
"""
import os
import sys
from datetime import date, datetime

# 添加項目根目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_opening_debug():
    """調試開帳功能"""
    try:
        print("=== 開始調試開帳功能 ===")
        
        # 1. 測試導入
        print("1. 測試導入模組...")
        from database import get_db
        from model import Account, Money, AccountSubject
        print("   ✓ 模組導入成功")
        
        # 2. 測試資料庫連接
        print("2. 測試資料庫連接...")
        with get_db() as db:
            print("   ✓ 資料庫連接成功")
            
            # 3. 查詢帳戶資料
            print("3. 查詢帳戶資料...")
            all_accounts = db.query(Account).all()
            print(f"   總帳戶數: {len(all_accounts)}")
            
            accounts_with_init = db.query(Account).filter(
                Account.init_amount.isnot(None), 
                Account.init_amount > 0
            ).all()
            print(f"   有期初金額的帳戶數: {len(accounts_with_init)}")
            
            for acc in accounts_with_init:
                print(f"   - {acc.name}: NT$ {acc.init_amount:,} (科目: {acc.subject_code})")
            
            if not accounts_with_init:
                print("   ⚠️  沒有找到有期初金額的帳戶")
                return False
            
            # 4. 測試創建Money記錄
            print("4. 測試創建Money記錄...")
            test_account = accounts_with_init[0]
            opening_date = date.today()
            
            # 檢查是否已有開帳記錄
            existing = db.query(Money).filter(
                Money.name.like('%開帳%'),
                Money.a_time == opening_date
            ).all()
            
            if existing:
                print(f"   已有 {len(existing)} 筆今日開帳記錄，先清除...")
                for record in existing:
                    db.delete(record)
                db.commit()
                print("   ✓ 舊記錄已清除")
            
            # 創建測試記錄
            test_record = Money(
                money_type='收入',
                a_time=opening_date,
                name=f'測試開帳-{test_account.name}',
                total=test_account.init_amount,
                extra_fee=0,
                subject_code=test_account.subject_code,
                account_id=test_account.id,
                note='測試開帳記錄',
                number=f'TEST-{opening_date.strftime("%Y%m%d")}-001',
                is_paper=True,
                tax_type='免稅'
            )
            
            print(f"   創建測試記錄: {test_record.name}")
            db.add(test_record)
            db.commit()
            print("   ✓ 測試記錄創建成功")
            
            # 驗證記錄
            created_record = db.query(Money).filter_by(id=test_record.id).first()
            if created_record:
                print(f"   ✓ 記錄驗證成功: ID={created_record.id}")
            else:
                print("   ❌ 記錄驗證失敗")
                return False
            
            # 清除測試記錄
            db.delete(test_record)
            db.commit()
            print("   ✓ 測試記錄已清除")
            
        print("\n=== 調試完成：所有測試通過 ===")
        return True
        
    except Exception as e:
        print(f"\n❌ 調試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_opening_debug()
    if success:
        print("\n🎉 開帳功能基礎測試通過，問題可能在Flask路由層面")
    else:
        print("\n💥 開帳功能基礎測試失敗，需要修復底層問題")
