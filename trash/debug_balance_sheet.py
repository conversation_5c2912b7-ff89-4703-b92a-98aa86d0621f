"""
調試資產負債表的路由
"""
from flask import Blueprint, jsonify
from sqlalchemy.orm import sessionmaker
from datetime import date

from model import engine, JournalEntry, Transaction, AccountSubject
from services.new_balance_sheet_service import NewBalanceSheetService

Session = sessionmaker(bind=engine)
debug_balance_sheet_bp = Blueprint('debug_balance_sheet', __name__)


@debug_balance_sheet_bp.route('/debug/balance_sheet_data')
def debug_balance_sheet_data():
    """調試資產負債表數據"""
    db = Session()
    try:
        service = NewBalanceSheetService(db)
        
        # 獲取所有分錄數據
        all_entries = db.query(JournalEntry).join(Transaction).all()
        
        # 按科目分組
        subject_data = {}
        for entry in all_entries:
            code = entry.subject_code
            if code not in subject_data:
                subject_data[code] = {
                    'subject_code': code,
                    'total_debit': 0,
                    'total_credit': 0,
                    'entries': []
                }
            
            subject_data[code]['total_debit'] += entry.debit_amount or 0
            subject_data[code]['total_credit'] += entry.credit_amount or 0
            subject_data[code]['entries'].append({
                'transaction_id': entry.transaction_id,
                'debit': entry.debit_amount or 0,
                'credit': entry.credit_amount or 0,
                'description': entry.description
            })
        
        # 計算餘額
        for code, data in subject_data.items():
            if code.startswith('1'):  # 資產
                data['balance'] = data['total_debit'] - data['total_credit']
                data['type'] = 'Asset'
            elif code.startswith('2'):  # 負債
                data['balance'] = data['total_credit'] - data['total_debit']
                data['type'] = 'Liability'
            elif code.startswith('3'):  # 權益
                data['balance'] = data['total_credit'] - data['total_debit']
                data['type'] = 'Equity'
            elif code.startswith('4'):  # 收入
                data['balance'] = data['total_credit'] - data['total_debit']
                data['type'] = 'Revenue'
            elif code.startswith(('5', '6')):  # 費用
                data['balance'] = data['total_debit'] - data['total_credit']
                data['type'] = 'Expense'
        
        # 計算本期損益
        revenue_total = sum(data['balance'] for code, data in subject_data.items() if code.startswith('4'))
        expense_total = sum(data['balance'] for code, data in subject_data.items() if code.startswith(('5', '6')))
        net_income = revenue_total - expense_total
        
        # 計算資產負債表總計
        asset_total = sum(data['balance'] for code, data in subject_data.items() if code.startswith('1'))
        liability_total = sum(data['balance'] for code, data in subject_data.items() if code.startswith('2'))
        equity_total = sum(data['balance'] for code, data in subject_data.items() if code.startswith('3'))
        
        # 加上本期損益
        equity_with_income = equity_total + net_income
        
        return jsonify({
            'subject_data': subject_data,
            'summary': {
                'revenue_total': revenue_total,
                'expense_total': expense_total,
                'net_income': net_income,
                'asset_total': asset_total,
                'liability_total': liability_total,
                'equity_total': equity_total,
                'equity_with_income': equity_with_income,
                'is_balanced': abs(asset_total - (liability_total + equity_with_income)) < 1
            }
        })
    
    finally:
        db.close()


@debug_balance_sheet_bp.route('/debug/service_test')
def debug_service_test():
    """測試服務方法"""
    db = Session()
    try:
        service = NewBalanceSheetService(db)
        
        # 測試資產負債表科目餘額
        balance_sheet_balances = service._calculate_balance_sheet_balances(date.today())
        
        # 測試本期損益
        net_income = service._calculate_net_income(date.today())
        
        # 測試完整資產負債表
        full_balance_sheet = service.generate_balance_sheet(date.today())
        
        return jsonify({
            'balance_sheet_balances': balance_sheet_balances,
            'net_income': net_income,
            'full_balance_sheet': {
                'totals': full_balance_sheet['totals'],
                'assets_count': sum(len(items) for items in full_balance_sheet['assets'].values()),
                'liabilities_count': sum(len(items) for items in full_balance_sheet['liabilities'].values()),
                'equity_count': sum(len(items) for items in full_balance_sheet['equity'].values())
            }
        })
    
    finally:
        db.close()
