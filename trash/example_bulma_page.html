{% extends "admin/base.html" %}

{% block title %}Bulma 示例頁面{% endblock %}
{% block page_icon %}fa-star{% endblock %}
{% block page_title %}Bulma 示例頁面{% endblock %}
{% block page_subtitle %}
<p class="subtitle is-6 has-text-grey">展示基於 Bulma 的現代化界面組件</p>
{% endblock %}

{% block breadcrumb %}
<li><a href="/example">示例</a></li>
<li class="is-active"><a href="#" aria-current="page">Bulma 示例</a></li>
{% endblock %}

{% block content %}
<!-- 統計卡片區域 -->
<div class="columns is-multiline mb-6">
    <div class="column is-3">
        <div class="card">
            <div class="card-content has-text-centered">
                <div class="icon-text">
                    <span class="icon has-text-primary is-large">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </span>
                </div>
                <p class="title is-4 text-gradient-primary">$125,430</p>
                <p class="subtitle is-6">總收入</p>
            </div>
        </div>
    </div>

    <div class="column is-3">
        <div class="card">
            <div class="card-content has-text-centered">
                <div class="icon-text">
                    <span class="icon has-text-success is-large">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </span>
                </div>
                <p class="title is-4 has-text-success">+12.5%</p>
                <p class="subtitle is-6">成長率</p>
            </div>
        </div>
    </div>

    <div class="column is-3">
        <div class="card">
            <div class="card-content has-text-centered">
                <div class="icon-text">
                    <span class="icon has-text-warning is-large">
                        <i class="fas fa-users fa-2x"></i>
                    </span>
                </div>
                <p class="title is-4 has-text-warning">1,234</p>
                <p class="subtitle is-6">用戶數</p>
            </div>
        </div>
    </div>

    <div class="column is-3">
        <div class="card">
            <div class="card-content has-text-centered">
                <div class="icon-text">
                    <span class="icon has-text-danger is-large">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </span>
                </div>
                <p class="title is-4 has-text-danger">5</p>
                <p class="subtitle is-6">待處理</p>
            </div>
        </div>
    </div>
</div>

<!-- 按鈕示例 -->
<div class="box mb-6">
    <h2 class="title is-4">按鈕樣式</h2>
    <div class="buttons">
        <button class="button is-primary">
            <span class="icon">
                <i class="fas fa-save"></i>
            </span>
            <span>儲存</span>
        </button>
        <button class="button is-success">
            <span class="icon">
                <i class="fas fa-check"></i>
            </span>
            <span>確認</span>
        </button>
        <button class="button is-warning">
            <span class="icon">
                <i class="fas fa-exclamation"></i>
            </span>
            <span>警告</span>
        </button>
        <button class="button is-danger">
            <span class="icon">
                <i class="fas fa-trash"></i>
            </span>
            <span>刪除</span>
        </button>
        <button class="button is-info">
            <span class="icon">
                <i class="fas fa-info"></i>
            </span>
            <span>資訊</span>
        </button>
        <button class="button is-light">
            <span class="icon">
                <i class="fas fa-cancel"></i>
            </span>
            <span>取消</span>
        </button>
    </div>
</div>

<!-- 表單示例 -->
<div class="columns">
    <div class="column is-half">
        <div class="box">
            <h2 class="title is-4">表單示例</h2>
            <form>
                <div class="field">
                    <label class="label">姓名</label>
                    <div class="control has-icons-left">
                        <input class="input" type="text" placeholder="請輸入姓名">
                        <span class="icon is-small is-left">
                            <i class="fas fa-user"></i>
                        </span>
                    </div>
                </div>

                <div class="field">
                    <label class="label">電子郵件</label>
                    <div class="control has-icons-left has-icons-right">
                        <input class="input" type="email" placeholder="請輸入電子郵件">
                        <span class="icon is-small is-left">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <span class="icon is-small is-right">
                            <i class="fas fa-check"></i>
                        </span>
                    </div>
                </div>

                <div class="field">
                    <label class="label">類別</label>
                    <div class="control">
                        <div class="select is-fullwidth">
                            <select>
                                <option>請選擇類別</option>
                                <option>收入</option>
                                <option>支出</option>
                                <option>轉帳</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="field">
                    <label class="label">備註</label>
                    <div class="control">
                        <textarea class="textarea" placeholder="請輸入備註"></textarea>
                    </div>
                </div>

                <div class="field is-grouped">
                    <div class="control">
                        <button class="button is-primary">提交</button>
                    </div>
                    <div class="control">
                        <button class="button is-light">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="column is-half">
        <div class="box">
            <h2 class="title is-4">通知示例</h2>

            <div class="notification is-success">
                <button class="delete"></button>
                <strong>成功！</strong> 資料已成功儲存。
            </div>

            <div class="notification is-warning">
                <button class="delete"></button>
                <strong>注意！</strong> 請檢查輸入的資料。
            </div>

            <div class="notification is-danger">
                <button class="delete"></button>
                <strong>錯誤！</strong> 發生了一個錯誤。
            </div>

            <div class="notification is-info">
                <button class="delete"></button>
                <strong>資訊：</strong> 這是一個資訊通知。
            </div>
        </div>

        <div class="box">
            <h2 class="title is-4">標籤示例</h2>
            <div class="tags">
                <span class="tag is-primary">主要</span>
                <span class="tag is-success">成功</span>
                <span class="tag is-warning">警告</span>
                <span class="tag is-danger">危險</span>
                <span class="tag is-info">資訊</span>
                <span class="tag is-light">淺色</span>
            </div>
        </div>
    </div>
</div>

<!-- 表格示例 -->
<div class="box">
    <h2 class="title is-4">表格示例</h2>
    <div class="table-container">
        <table class="table is-fullwidth is-striped is-hoverable">
            <thead>
                <tr>
                    <th>日期</th>
                    <th>項目</th>
                    <th>類別</th>
                    <th>金額</th>
                    <th>狀態</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2024-01-15</td>
                    <td>辦公用品採購</td>
                    <td>支出</td>
                    <td class="has-text-danger">-$1,250</td>
                    <td><span class="tag is-success">已完成</span></td>
                    <td>
                        <div class="buttons are-small">
                            <button class="button is-primary is-small">
                                <span class="icon">
                                    <i class="fas fa-edit"></i>
                                </span>
                            </button>
                            <button class="button is-danger is-small">
                                <span class="icon">
                                    <i class="fas fa-trash"></i>
                                </span>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>2024-01-14</td>
                    <td>客戶付款</td>
                    <td>收入</td>
                    <td class="has-text-success">+$5,000</td>
                    <td><span class="tag is-warning">處理中</span></td>
                    <td>
                        <div class="buttons are-small">
                            <button class="button is-primary is-small">
                                <span class="icon">
                                    <i class="fas fa-edit"></i>
                                </span>
                            </button>
                            <button class="button is-danger is-small">
                                <span class="icon">
                                    <i class="fas fa-trash"></i>
                                </span>
                            </button>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>2024-01-13</td>
                    <td>租金支付</td>
                    <td>支出</td>
                    <td class="has-text-danger">-$2,500</td>
                    <td><span class="tag is-danger">逾期</span></td>
                    <td>
                        <div class="buttons are-small">
                            <button class="button is-primary is-small">
                                <span class="icon">
                                    <i class="fas fa-edit"></i>
                                </span>
                            </button>
                            <button class="button is-danger is-small">
                                <span class="icon">
                                    <i class="fas fa-trash"></i>
                                </span>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- 進度條示例 -->
<div class="box">
    <h2 class="title is-4">進度條示例</h2>
    <div class="field">
        <label class="label">專案進度</label>
        <progress class="progress is-primary" value="75" max="100">75%</progress>
    </div>
    <div class="field">
        <label class="label">預算使用</label>
        <progress class="progress is-success" value="60" max="100">60%</progress>
    </div>
    <div class="field">
        <label class="label">時間進度</label>
        <progress class="progress is-warning" value="90" max="100">90%</progress>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', () => {
        // 自動關閉通知
        (document.querySelectorAll('.notification .delete') || []).forEach(($delete) => {
            const $notification = $delete.parentNode;
            $delete.addEventListener('click', () => {
                $notification.parentNode.removeChild($notification);
            });
        });
    });
</script>
{% endblock %}