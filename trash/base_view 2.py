"""
基礎視圖類別 - 提供通用的選單和模板渲染功能
"""
from flask import render_template
from data.menu_data import menu

class BaseView:
    """基礎視圖類別，提供通用功能"""
    
    def __init__(self, selected_menu=None):
        self.selected_menu = selected_menu
        self.sidebar_items = list(menu.keys())
        self.submenus = menu.get(selected_menu, {}) if selected_menu else {}
    
    def render(self, template_name, **context):
        """
        渲染模板，自動注入選單資料
        
        Args:
            template_name (str): 模板名稱
            **context: 其他模板上下文資料
            
        Returns:
            渲染後的模板
        """
        # 合併選單資料和其他上下文
        full_context = {
            'sidebar_items': self.sidebar_items,
            'selected': self.selected_menu,
            'submenus': self.submenus,
            **context
        }
        
        return render_template(template_name, **full_context)
    
    def get_context(self, **additional_context):
        """
        獲取完整的模板上下文
        
        Args:
            **additional_context: 額外的上下文資料
            
        Returns:
            dict: 完整的模板上下文
        """
        return {
            'sidebar_items': self.sidebar_items,
            'selected': self.selected_menu,
            'submenus': self.submenus,
            **additional_context
        }

# 預定義的視圖類別
class FundManagementView(BaseView):
    def __init__(self):
        super().__init__('資金管理')

class IncomeExpenseView(BaseView):
    def __init__(self):
        super().__init__('收支帳簿')

class PayrollView(BaseView):
    def __init__(self):
        super().__init__('薪資報酬')

class ServiceRewardView(BaseView):
    def __init__(self):
        super().__init__('勞務報酬')

class AssetManagementView(BaseView):
    def __init__(self):
        super().__init__('資產管理')

class SettingsView(BaseView):
    def __init__(self):
        super().__init__('設定')

class ReportsView(BaseView):
    def __init__(self):
        super().__init__('我的報表')

class AccountingView(BaseView):
    def __init__(self):
        super().__init__('會計科目')