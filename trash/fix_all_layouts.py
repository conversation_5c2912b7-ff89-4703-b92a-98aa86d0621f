#!/usr/bin/env python3
"""
修復所有模板的布局問題
"""

import os
import glob
import re

def fix_template_layout(file_path):
    """修復單個模板文件的布局"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否需要修復
        if 'main-content' in content and 'margin-left: 280px' not in content:
            # 添加內聯樣式
            content = re.sub(
                r'<div class="main-content"([^>]*)>',
                r'<div class="main-content"\1 style="margin-left: 280px !important; padding: 2rem !important; min-height: 100vh !important; position: relative !important; z-index: 1 !important;">',
                content
            )
            
            # 寫回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 修復了 {file_path}")
            return True
        else:
            print(f"⏭️  跳過 {file_path} (不需要修復)")
            return False
            
    except Exception as e:
        print(f"❌ 修復 {file_path} 時出錯: {e}")
        return False

def main():
    """主函數"""
    print("🔧 開始修復所有模板的布局問題...")
    
    # 查找所有 HTML 模板文件
    template_files = []
    for pattern in ['templates/*.html', 'templates/**/*.html']:
        template_files.extend(glob.glob(pattern, recursive=True))
    
    fixed_count = 0
    total_count = len(template_files)
    
    for template_file in template_files:
        if fix_template_layout(template_file):
            fixed_count += 1
    
    print(f"\n📊 修復完成:")
    print(f"   總文件數: {total_count}")
    print(f"   修復文件數: {fixed_count}")
    print(f"   跳過文件數: {total_count - fixed_count}")
    
    # 創建測試頁面
    create_test_page()
    
    print("\n🎉 所有布局問題已修復!")
    print("請重新啟動應用程序並測試布局效果。")

def create_test_page():
    """創建一個測試頁面"""
    test_content = '''<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局修復測試</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-modern-theme.css') }}">
</head>
<body class="has-background-light">
    <!-- 側邊欄 -->
    {% include 'sidebar.html' %}
    
    <!-- 主內容區域 -->
    <div class="main-content" style="margin-left: 280px !important; padding: 2rem !important; min-height: 100vh !important; position: relative !important; z-index: 1 !important;">
        <div class="container is-fluid">
            <div class="box">
                <h1 class="title">🎯 布局修復測試成功！</h1>
                <div class="content">
                    <div class="notification is-success">
                        <h2 class="subtitle">✅ 修復完成</h2>
                        <p>如果你能看到這個頁面正確顯示，說明布局問題已經解決：</p>
                        <ul>
                            <li>✅ 側邊欄在左側，深色背景，白色文字</li>
                            <li>✅ 主內容在右側，有適當的左邊距</li>
                            <li>✅ 內容不被側邊欄覆蓋</li>
                            <li>✅ 布局類似期望的效果</li>
                        </ul>
                    </div>
                    
                    <div class="columns">
                        <div class="column">
                            <div class="card">
                                <div class="card-header">
                                    <p class="card-header-title">測試卡片 1</p>
                                </div>
                                <div class="card-content">
                                    <p>這個卡片應該正確顯示在主內容區域中。</p>
                                </div>
                            </div>
                        </div>
                        <div class="column">
                            <div class="card">
                                <div class="card-header">
                                    <p class="card-header-title">測試卡片 2</p>
                                </div>
                                <div class="card-content">
                                    <p>側邊欄和主內容應該並排顯示，不重疊。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/sidebar-menu.js') }}"></script>
</body>
</html>'''
    
    with open('templates/layout_test.html', 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print("✅ 創建了測試頁面: templates/layout_test.html")

if __name__ == "__main__":
    main()