"""
資料庫會話管理輔助工具
統一處理資料庫會話，確保一致性和正確的資源管理
"""

from database import get_db
from functools import wraps
import logging

logger = logging.getLogger(__name__)

def with_db_session(func):
    """
    裝飾器：自動管理資料庫會話
    
    使用方式：
    @with_db_session
    def my_function(db, other_params):
        # 使用 db 進行資料庫操作
        pass
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        with get_db() as db:
            try:
                return func(db, *args, **kwargs)
            except Exception as e:
                logger.error(f"Database operation failed in {func.__name__}: {str(e)}")
                db.rollback()
                raise
    return wrapper

def standardize_session_usage():
    """
    標準化資料庫會話使用的指引
    
    正確用法：
    1. 使用 with get_db() as db:
    2. 使用 @with_db_session 裝飾器
    
    避免：
    - 直接使用 Session()
    - 忘記關閉會話
    - 不處理異常時的回滾
    """
    pass

class DBSessionManager:
    """
    資料庫會話管理器
    提供更高級的會話管理功能
    """
    
    @staticmethod
    def batch_operation(operations):
        """
        批量執行資料庫操作
        
        Args:
            operations: 要執行的操作列表（函數）
        
        Returns:
            操作結果列表
        """
        results = []
        with get_db() as db:
            try:
                for operation in operations:
                    result = operation(db)
                    results.append(result)
                db.commit()
                return results
            except Exception as e:
                logger.error(f"Batch operation failed: {str(e)}")
                db.rollback()
                raise
    
    @staticmethod
    def execute_with_retry(operation, max_retries=3):
        """
        執行操作並在失敗時重試
        
        Args:
            operation: 要執行的操作（接受 db 參數的函數）
            max_retries: 最大重試次數
        
        Returns:
            操作結果
        """
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                with get_db() as db:
                    result = operation(db)
                    db.commit()
                    return result
            except Exception as e:
                last_exception = e
                logger.warning(f"Operation failed (attempt {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(0.5 * (attempt + 1))  # 指數退避
        
        raise last_exception