/**
 * 簡化版模態框管理器
 * Simple Modal Manager
 */

(function () {
    'use strict';

    console.log('🔄 載入簡化版模態框管理器...');

    // 簡單的模態框管理器
    class SimpleModalManager {
        constructor() {
            this.activeModal = null;
            this.init();
            console.log('✅ SimpleModalManager 初始化完成');
        }

        init() {
            // 添加基本樣式
            this.addStyles();

            // 綁定 ESC 鍵關閉
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.activeModal) {
                    this.close();
                }
            });
        }

        addStyles() {
            if (document.getElementById('simple-modal-styles')) return;

            const style = document.createElement('style');
            style.id = 'simple-modal-styles';
            style.textContent = `
                .simple-modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                }
                
                .simple-modal-overlay.is-active {
                    opacity: 1;
                    visibility: visible;
                }
                
                .simple-modal-content {
                    background: white;
                    border-radius: 12px;
                    padding: 30px;
                    max-width: 500px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                    transform: scale(0.9);
                    transition: transform 0.3s ease;
                }
                
                .simple-modal-overlay.is-active .simple-modal-content {
                    transform: scale(1);
                }
                
                .simple-modal-header {
                    margin-bottom: 20px;
                    padding-bottom: 15px;
                    border-bottom: 1px solid #eee;
                }
                
                .simple-modal-title {
                    margin: 0;
                    font-size: 1.5rem;
                    color: #333;
                }
                
                .simple-modal-body {
                    margin-bottom: 20px;
                }
                
                .simple-modal-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                }
                
                .simple-modal-button {
                    padding: 10px 20px;
                    border: none;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.2s ease;
                }
                
                .simple-modal-button-primary {
                    background: #007bff;
                    color: white;
                }
                
                .simple-modal-button-primary:hover {
                    background: #0056b3;
                }
                
                .simple-modal-button-secondary {
                    background: #6c757d;
                    color: white;
                }
                
                .simple-modal-button-secondary:hover {
                    background: #545b62;
                }
            `;
            document.head.appendChild(style);
        }

        show(options) {
            const {
                title = '提示',
                content = '',
                buttons = [{ text: '關閉', type: 'secondary', handler: () => this.close() }]
            } = options;

            // 移除現有模態框
            this.close();

            // 創建模態框
            const overlay = document.createElement('div');
            overlay.className = 'simple-modal-overlay';

            const buttonsHtml = buttons.map(btn =>
                `<button class="simple-modal-button simple-modal-button-${btn.type || 'secondary'}" 
                         data-action="${btn.action || ''}">${btn.text}</button>`
            ).join('');

            overlay.innerHTML = `
                <div class="simple-modal-content">
                    <div class="simple-modal-header">
                        <h3 class="simple-modal-title">${title}</h3>
                    </div>
                    <div class="simple-modal-body">
                        ${content}
                    </div>
                    <div class="simple-modal-footer">
                        ${buttonsHtml}
                    </div>
                </div>
            `;

            // 綁定按鈕事件
            buttons.forEach((btn, index) => {
                const button = overlay.querySelectorAll('.simple-modal-button')[index];
                if (button && btn.handler) {
                    button.addEventListener('click', btn.handler);
                }
            });

            // 點擊背景關閉
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.close();
                }
            });

            // 添加到頁面
            document.body.appendChild(overlay);
            this.activeModal = overlay;

            // 顯示動畫
            setTimeout(() => {
                overlay.classList.add('is-active');
            }, 10);

            console.log('✅ 模態框已顯示');
            return overlay;
        }

        close() {
            if (!this.activeModal) return;

            this.activeModal.classList.remove('is-active');

            setTimeout(() => {
                if (this.activeModal && this.activeModal.parentNode) {
                    this.activeModal.parentNode.removeChild(this.activeModal);
                }
                this.activeModal = null;
            }, 300);

            console.log('✅ 模態框已關閉');
        }

        confirm(options) {
            const {
                title = '確認',
                message = '確定要執行此操作嗎？',
                confirmText = '確認',
                cancelText = '取消',
                onConfirm = () => { },
                onCancel = () => { }
            } = options;

            return this.show({
                title,
                content: `<p>${message}</p>`,
                buttons: [
                    {
                        text: cancelText,
                        type: 'secondary',
                        handler: () => {
                            this.close();
                            onCancel();
                        }
                    },
                    {
                        text: confirmText,
                        type: 'primary',
                        handler: () => {
                            this.close();
                            onConfirm();
                        }
                    }
                ]
            });
        }
    }

    // 創建全局實例
    window.simpleModalManager = new SimpleModalManager();

    // 如果原始的 ModalManager 不存在，使用簡化版本
    if (typeof ModalManager === 'undefined') {
        console.log('⚠️ 使用簡化版模態框管理器');

        // 提供兼容的全局函數
        window.showModal = (options) => window.simpleModalManager.show(options);
        window.showConfirm = (options) => window.simpleModalManager.confirm(options);
        window.closeModal = () => window.simpleModalManager.close();

        // 創建一個簡單的 ModalManager 類以保持兼容性
        window.ModalManager = SimpleModalManager;
        window.modalManager = window.simpleModalManager;
    }

    console.log('✅ 簡化版模態框管理器載入完成');

})();