{% extends "admin/base.html" %}

{% block title %}彈出視窗範例{% endblock %}
{% block page_icon %}fa-window-maximize{% endblock %}
{% block page_title %}彈出視窗範例{% endblock %}

{% block breadcrumb %}
<li><a href="#">系統管理</a></li>
<li class="is-active"><a href="#" aria-current="page">彈出視窗範例</a></li>
{% endblock %}

{% block content %}
<div class="modern-card fade-in-up">
    <h2 class="subtitle">
        <i class="fas fa-desktop mr-2"></i>
        彈出視窗功能展示
    </h2>
    
    <div class="columns">
        <div class="column">
            <div class="card">
                <div class="card-content">
                    <h3 class="title is-5">基本彈出視窗</h3>
                    <p class="subtitle is-6">展示基本的彈出視窗功能</p>
                    <button class="btn-gradient-primary" onclick="showBasicModal()">
                        <i class="fas fa-window-maximize"></i>
                        <span>顯示基本視窗</span>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="column">
            <div class="card">
                <div class="card-content">
                    <h3 class="title is-5">確認對話框</h3>
                    <p class="subtitle is-6">需要用戶確認的操作</p>
                    <button class="btn-gradient-warning" onclick="showConfirmModal()">
                        <i class="fas fa-question-circle"></i>
                        <span>顯示確認視窗</span>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="column">
            <div class="card">
                <div class="card-content">
                    <h3 class="title is-5">表單視窗</h3>
                    <p class="subtitle is-6">包含表單輸入的視窗</p>
                    <button class="btn-gradient-success" onclick="showFormModal()">
                        <i class="fas fa-edit"></i>
                        <span>顯示表單視窗</span>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="column">
            <div class="card">
                <div class="card-content">
                    <h3 class="title is-5">載入視窗</h3>
                    <p class="subtitle is-6">處理中的載入視窗</p>
                    <button class="btn-gradient-info" onclick="showLoadingModal()">
                        <i class="fas fa-spinner"></i>
                        <span>顯示載入視窗</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modern-card fade-in-up">
    <h2 class="subtitle">
        <i class="fas fa-code mr-2"></i>
        使用方法
    </h2>
    
    <div class="content">
        <h4>基本用法：</h4>
        <pre><code>// 基本彈出視窗
showModal({
    title: '標題',
    content: '內容',
    actions: [
        { text: '確定', type: 'primary', handler: () => console.log('確定') }
    ]
});

// 確認對話框
showConfirm({
    title: '確認操作',
    message: '您確定要執行此操作嗎？',
    onConfirm: () => console.log('已確認'),
    onCancel: () => console.log('已取消')
});

// 表單視窗
showForm({
    title: '新增資料',
    fields: [
        { name: 'name', label: '姓名', type: 'text', required: true },
        { name: 'email', label: '電子郵件', type: 'email' }
    ],
    onSubmit: (data) => console.log('提交資料:', data)
});</code></pre>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showBasicModal() {
    showModal({
        title: '歡迎使用彈出視窗',
        subtitle: '這是一個現代化的彈出視窗範例',
        icon: 'info-circle',
        content: `
            <div style="padding: 20px 0;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <i class="fas fa-rocket" style="font-size: 4rem; color: #6366f1; margin-bottom: 20px;"></i>
                    <h3 style="font-size: 1.5rem; font-weight: 700; color: #1e293b; margin-bottom: 10px;">
                        功能強大的彈出視窗
                    </h3>
                    <p style="color: #64748b; font-size: 1rem; line-height: 1.6;">
                        支援多種內容格式，美觀的動畫效果，以及靈活的自訂選項
                    </p>
                </div>
                
                <div style="background: #f8fafc; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                    <h4 style="color: #334155; font-weight: 600; margin-bottom: 12px;">
                        <i class="fas fa-star" style="color: #f59e0b; margin-right: 8px;"></i>
                        主要特點
                    </h4>
                    <ul style="color: #64748b; line-height: 1.8; margin-left: 20px;">
                        <li>🎨 現代化設計風格</li>
                        <li>📱 響應式佈局</li>
                        <li>⚡ 流暢的動畫效果</li>
                        <li>🔧 易於自訂和擴展</li>
                    </ul>
                </div>
                
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                            color: white; 
                            border-radius: 12px; 
                            padding: 16px 20px; 
                            text-align: center;">
                    <i class="fas fa-lightbulb" style="margin-right: 8px;"></i>
                    <strong>提示：</strong> 按 ESC 鍵或點擊背景可以關閉視窗
                </div>
            </div>
        `,
        size: 'medium',
        actions: [
            {
                text: '了解',
                type: 'primary',
                icon: 'check',
                handler: () => closeModal()
            }
        ]
    });
}

function showConfirmModal() {
    showConfirm({
        title: '確認刪除',
        message: '您確定要刪除這個項目嗎？此操作無法復原。',
        confirmText: '確定刪除',
        cancelText: '取消',
        onConfirm: () => {
            // 模擬刪除操作
            showLoading('處理中', '正在刪除項目...');
            setTimeout(() => {
                closeModal();
                // 可以在這裡顯示成功訊息
                setTimeout(() => {
                    showModal({
                        title: '操作成功',
                        content: `
                            <div class="has-text-centered">
                                <i class="fas fa-check-circle has-text-success" style="font-size: 3rem; margin-bottom: 16px;"></i>
                                <p>項目已成功刪除！</p>
                            </div>
                        `,
                        actions: [{
                            text: '確定',
                            type: 'success',
                            handler: () => closeModal()
                        }]
                    });
                }, 100);
            }, 2000);
        },
        onCancel: () => {
            console.log('取消刪除操作');
        }
    });
}

function showFormModal() {
    showForm({
        title: '新增用戶',
        subtitle: '請填寫以下資訊來創建新用戶',
        fields: [
            {
                name: 'name',
                label: '姓名',
                type: 'text',
                placeholder: '請輸入完整姓名',
                required: true
            },
            {
                name: 'email',
                label: '電子郵件',
                type: 'email',
                placeholder: '<EMAIL>',
                required: true
            },
            {
                name: 'role',
                label: '角色權限',
                type: 'select',
                options: [
                    { value: 'user', text: '一般用戶' },
                    { value: 'admin', text: '系統管理員' },
                    { value: 'editor', text: '內容編輯者' }
                ],
                required: true
            },
            {
                name: 'department',
                label: '部門',
                type: 'select',
                options: [
                    { value: 'sales', text: '業務部' },
                    { value: 'tech', text: '技術部' },
                    { value: 'hr', text: '人資部' },
                    { value: 'finance', text: '財務部' }
                ]
            },
            {
                name: 'phone',
                label: '聯絡電話',
                type: 'tel',
                placeholder: '0912-345-678'
            },
            {
                name: 'description',
                label: '備註說明',
                type: 'textarea',
                placeholder: '請輸入其他相關資訊...',
                rows: 3
            }
        ],
        onSubmit: (data) => {
            console.log('表單資料:', data);
            
            // 驗證資料
            if (!data.name.trim()) {
                alert('請輸入姓名');
                return false; // 阻止關閉視窗
            }
            
            if (!data.email.trim()) {
                alert('請輸入電子郵件');
                return false;
            }
            
            // 顯示載入視窗
            closeModal();
            showLoading('處理中', '正在創建用戶...');
            
            // 模擬提交
            setTimeout(() => {
                closeModal();
                showModal({
                    title: '創建成功',
                    content: `
                        <div class="has-text-centered">
                            <i class="fas fa-user-plus has-text-success" style="font-size: 3rem; margin-bottom: 16px;"></i>
                            <h3 class="title is-4">用戶創建成功</h3>
                            <p>用戶 "${data.name}" 已成功創建。</p>
                            <div class="box" style="margin-top: 20px; text-align: left;">
                                <strong>詳細資訊：</strong><br>
                                姓名：${data.name}<br>
                                電子郵件：${data.email}<br>
                                角色：${data.role}<br>
                                ${data.description ? `描述：${data.description}` : ''}
                            </div>
                        </div>
                    `,
                    actions: [{
                        text: '確定',
                        type: 'success',
                        handler: () => closeModal()
                    }]
                });
            }, 1500);
        },
        onCancel: () => {
            console.log('取消表單提交');
        }
    });
}

function showLoadingModal() {
    showLoading('數據處理中', '正在處理您的請求，請稍候...');
    
    // 3秒後自動關閉
    setTimeout(() => {
        closeModal();
    }, 3000);
}
</script>
{% endblock %}