/**
 * 應用程序初始化腳本
 * App Initialization Script
 */

(function () {
    'use strict';

    // 應用程序配置
    const AppConfig = {
        debug: true,
        version: '1.0.0',
        name: '印錢大師',
        features: {
            sidebar: true,
            modals: true,
            notifications: true,
            responsive: true
        }
    };

    // 日誌工具
    const Logger = {
        log: function (message, type = 'info') {
            if (AppConfig.debug) {
                const timestamp = new Date().toLocaleTimeString();
                const prefix = `[${AppConfig.name}] ${timestamp}`;

                switch (type) {
                    case 'error':
                        console.error(`${prefix} ❌`, message);
                        break;
                    case 'warn':
                        console.warn(`${prefix} ⚠️`, message);
                        break;
                    case 'success':
                        console.log(`${prefix} ✅`, message);
                        break;
                    default:
                        console.log(`${prefix} ℹ️`, message);
                }
            }
        }
    };

    // 通知管理器
    const NotificationManager = {
        init: function () {
            // 自動關閉通知按鈕
            this.bindDeleteButtons();

            // 自動隱藏通知
            this.autoHideNotifications();

            Logger.log('通知管理器已初始化', 'success');
        },

        bindDeleteButtons: function () {
            document.querySelectorAll('.notification .delete').forEach(deleteBtn => {
                deleteBtn.addEventListener('click', function () {
                    const notification = this.closest('.notification');
                    if (notification) {
                        notification.style.opacity = '0';
                        setTimeout(() => notification.remove(), 300);
                    }
                });
            });
        },

        autoHideNotifications: function () {
            setTimeout(() => {
                document.querySelectorAll('.notification').forEach(notification => {
                    // 只自動隱藏沒有被懸停的通知
                    if (!notification.matches(':hover')) {
                        notification.style.opacity = '0';
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.remove();
                            }
                        }, 300);
                    }
                });
            }, 5000);
        },

        show: function (message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `notification is-${type}`;
            notification.innerHTML = `
                <button class="delete"></button>
                ${message}
            `;

            // 添加到頁面
            const container = document.querySelector('.main-content .container') || document.body;
            container.insertBefore(notification, container.firstChild);

            // 綁定關閉事件
            const deleteBtn = notification.querySelector('.delete');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => {
                    notification.style.opacity = '0';
                    setTimeout(() => notification.remove(), 300);
                });
            }

            // 自動隱藏
            if (duration > 0) {
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.style.opacity = '0';
                        setTimeout(() => notification.remove(), 300);
                    }
                }, duration);
            }
        }
    };

    // 響應式管理器
    const ResponsiveManager = {
        init: function () {
            this.handleResize();
            window.addEventListener('resize', this.handleResize.bind(this));
            Logger.log('響應式管理器已初始化', 'success');
        },

        handleResize: function () {
            const width = window.innerWidth;
            const sidebar = document.querySelector('.custom-sidebar');
            const mainContent = document.querySelector('.main-content');

            if (width <= 1024) {
                // 移動端
                if (sidebar) sidebar.classList.add('is-mobile');
                if (mainContent) mainContent.classList.add('is-mobile');
            } else {
                // 桌面端
                if (sidebar) sidebar.classList.remove('is-mobile');
                if (mainContent) mainContent.classList.remove('is-mobile');
            }
        }
    };

    // 表單增強器
    const FormEnhancer = {
        init: function () {
            this.enhanceForms();
            Logger.log('表單增強器已初始化', 'success');
        },

        enhanceForms: function () {
            // 為所有表單添加提交確認
            document.querySelectorAll('form[data-confirm]').forEach(form => {
                form.addEventListener('submit', function (e) {
                    const message = this.getAttribute('data-confirm') || '確定要提交嗎？';
                    if (!confirm(message)) {
                        e.preventDefault();
                    }
                });
            });

            // 自動聚焦第一個輸入框
            const firstInput = document.querySelector('form input:not([type="hidden"]):not([readonly])');
            if (firstInput) {
                firstInput.focus();
            }
        }
    };

    // 主初始化函數
    function initializeApp() {
        Logger.log(`正在初始化 ${AppConfig.name} v${AppConfig.version}...`);

        try {
            // 初始化各個模組
            if (AppConfig.features.notifications) {
                NotificationManager.init();
            }

            if (AppConfig.features.responsive) {
                ResponsiveManager.init();
            }

            if (AppConfig.features.modals && typeof ModalManager !== 'undefined') {
                window.modalManager = new ModalManager();
                Logger.log('模態框管理器已初始化', 'success');
            }

            // 表單增強
            FormEnhancer.init();

            // 全局錯誤處理
            window.addEventListener('error', function (e) {
                Logger.log(`JavaScript 錯誤: ${e.message}`, 'error');
            });

            Logger.log('應用程序初始化完成！', 'success');

        } catch (error) {
            Logger.log(`初始化失敗: ${error.message}`, 'error');
        }
    }

    // 等待 DOM 載入完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
        initializeApp();
    }

    // 暴露全局 API
    window.App = {
        config: AppConfig,
        logger: Logger,
        notifications: NotificationManager,
        responsive: ResponsiveManager
    };

})();