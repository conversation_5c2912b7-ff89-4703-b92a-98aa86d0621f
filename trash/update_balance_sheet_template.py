#!/usr/bin/env python3
"""
批量更新資產負債表模板，添加百分比欄位
"""

import re

def update_balance_sheet_template():
    """更新資產負債表模板"""
    
    # 讀取模板文件
    with open('templates/balance_sheet.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. 更新所有 colspan="2" 為 colspan="3"
    content = re.sub(r'colspan="2"', 'colspan="3"', content)
    
    # 2. 在所有金額欄位後添加百分比欄位
    # 匹配模式：<td class="has-text-right">{{ "{:,}".format(item.balance) }}</td>
    pattern = r'(<td class="has-text-right">\{\{ "\{:,\}"\.format\(item\.balance\) \}\}</td>)'
    replacement = r'\1\n                                                <td class="has-text-right">{{ item.percentage }}%</td>'
    content = re.sub(pattern, replacement, content)
    
    # 3. 更新合計行的百分比欄位
    # 流動資產合計
    content = re.sub(
        r'(<td class="has-text-right has-text-weight-bold">\s*\{\{ "\{:,\}"\.format\(balance_sheet\[\'assets\'\]\[\'current_assets\'\]\[\'total\'\]\) \}\}\s*</td>)',
        r'\1\n                                                <td class="has-text-right has-text-weight-bold">{{ "%.2f"|format((balance_sheet[\'assets\'][\'current_assets\'][\'total\'] / balance_sheet[\'assets\'][\'total_assets\'] * 100) if balance_sheet[\'assets\'][\'total_assets\'] > 0 else 0) }}%</td>',
        content
    )
    
    # 非流動資產合計
    content = re.sub(
        r'(<td class="has-text-right has-text-weight-bold">\s*\{\{ "\{:,\}"\.format\(balance_sheet\[\'assets\'\]\[\'non_current_assets\'\]\[\'total\'\]\) \}\}\s*</td>)',
        r'\1\n                                                <td class="has-text-right has-text-weight-bold">{{ "%.2f"|format((balance_sheet[\'assets\'][\'non_current_assets\'][\'total\'] / balance_sheet[\'assets\'][\'total_assets\'] * 100) if balance_sheet[\'assets\'][\'total_assets\'] > 0 else 0) }}%</td>',
        content
    )
    
    # 資產總計
    content = re.sub(
        r'(<h3 class="title is-5 has-text-primary">\s*\{\{ "\{:,\}"\.format\(balance_sheet\[\'assets\'\]\[\'total_assets\'\]\) \}\}\s*</h3>)',
        r'\1\n                                            <h3 class="title is-5 has-text-primary">100.00%</h3>',
        content
    )
    
    # 流動負債合計
    content = re.sub(
        r'(<td class="has-text-right has-text-weight-bold">\s*\{\{ "\{:,\}"\.format\(balance_sheet\[\'liabilities\'\]\[\'current_liabilities\'\]\[\'total\'\]\) \}\}\s*</td>)',
        r'\1\n                                                <td class="has-text-right has-text-weight-bold">{{ "%.2f"|format((balance_sheet[\'liabilities\'][\'current_liabilities\'][\'total\'] / balance_sheet[\'assets\'][\'total_assets\'] * 100) if balance_sheet[\'assets\'][\'total_assets\'] > 0 else 0) }}%</td>',
        content
    )
    
    # 非流動負債合計
    content = re.sub(
        r'(<td class="has-text-right has-text-weight-bold">\s*\{\{ "\{:,\}"\.format\(balance_sheet\[\'liabilities\'\]\[\'non_current_liabilities\'\]\[\'total\'\]\) \}\}\s*</td>)',
        r'\1\n                                                <td class="has-text-right has-text-weight-bold">{{ "%.2f"|format((balance_sheet[\'liabilities\'][\'non_current_liabilities\'][\'total\'] / balance_sheet[\'assets\'][\'total_assets\'] * 100) if balance_sheet[\'assets\'][\'total_assets\'] > 0 else 0) }}%</td>',
        content
    )
    
    # 負債總計
    content = re.sub(
        r'(<h3 class="title is-5 has-text-danger">\s*\{\{ "\{:,\}"\.format\(balance_sheet\[\'liabilities\'\]\[\'total_liabilities\'\]\) \}\}\s*</h3>)',
        r'\1\n                                            <h3 class="title is-5 has-text-danger">{{ "%.2f"|format((balance_sheet[\'liabilities\'][\'total_liabilities\'] / balance_sheet[\'assets\'][\'total_assets\'] * 100) if balance_sheet[\'assets\'][\'total_assets\'] > 0 else 0) }}%</h3>',
        content
    )
    
    # 權益總計
    content = re.sub(
        r'(<h3 class="title is-5 has-text-success">\s*\{\{ "\{:,\}"\.format\(balance_sheet\[\'equity\'\]\[\'total_equity\'\]\) \}\}\s*</h3>)',
        r'\1\n                                            <h3 class="title is-5 has-text-success">{{ "%.2f"|format((balance_sheet[\'equity\'][\'total_equity\'] / balance_sheet[\'assets\'][\'total_assets\'] * 100) if balance_sheet[\'assets\'][\'total_assets\'] > 0 else 0) }}%</h3>',
        content
    )
    
    # 寫回文件
    with open('templates/balance_sheet.html', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 資產負債表模板更新完成！已添加百分比欄位。")

if __name__ == '__main__':
    update_balance_sheet_template()
