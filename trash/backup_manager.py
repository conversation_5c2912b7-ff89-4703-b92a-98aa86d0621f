"""
資料備份與恢復管理系統
確保資料安全，提供災難恢復能力
"""
import shutil
import sqlite3
import gzip
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class BackupManager:
    """備份管理器"""
    
    def __init__(self, db_path: str = "app.db", backup_dir: str = "backups"):
        self.db_path = db_path
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
    
    def create_backup(self, backup_name: Optional[str] = None) -> str:
        """創建資料庫備份"""
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}"
        
        backup_file = self.backup_dir / f"{backup_name}.db"
        
        try:
            # 使用 SQLite 的備份 API
            source_conn = sqlite3.connect(self.db_path)
            backup_conn = sqlite3.connect(str(backup_file))
            
            source_conn.backup(backup_conn)
            
            source_conn.close()
            backup_conn.close()
            
            # 壓縮備份文件
            compressed_file = self.backup_dir / f"{backup_name}.db.gz"
            with open(backup_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # 刪除未壓縮的備份文件
            backup_file.unlink()
            
            # 記錄備份信息
            self._save_backup_info(backup_name, compressed_file)
            
            logger.info(f"備份創建成功: {compressed_file}")
            return str(compressed_file)
            
        except Exception as e:
            logger.error(f"創建備份失敗: {str(e)}")
            raise
    
    def restore_backup(self, backup_file: str) -> bool:
        """恢復備份"""
        try:
            backup_path = Path(backup_file)
            if not backup_path.exists():
                raise FileNotFoundError(f"備份文件不存在: {backup_file}")
            
            # 創建當前資料庫的備份
            current_backup = self.create_backup("before_restore")
            logger.info(f"恢復前已創建當前資料庫備份: {current_backup}")
            
            # 解壓縮備份文件
            temp_db = self.backup_dir / "temp_restore.db"
            with gzip.open(backup_path, 'rb') as f_in:
                with open(temp_db, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # 替換當前資料庫
            shutil.copy2(temp_db, self.db_path)
            temp_db.unlink()
            
            logger.info(f"資料庫恢復成功: {backup_file}")
            return True
            
        except Exception as e:
            logger.error(f"恢復備份失敗: {str(e)}")
            return False
    
    def list_backups(self) -> List[Dict]:
        """列出所有備份"""
        backups = []
        info_file = self.backup_dir / "backup_info.json"
        
        if info_file.exists():
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
                
                for backup in backup_info:
                    backup_path = Path(backup['file_path'])
                    if backup_path.exists():
                        backup['file_size'] = backup_path.stat().st_size
                        backup['exists'] = True
                    else:
                        backup['exists'] = False
                    
                    backups.append(backup)
                        
            except Exception as e:
                logger.error(f"讀取備份信息失敗: {str(e)}")
        
        # 掃描備份目錄中的其他備份文件
        for backup_file in self.backup_dir.glob("*.db.gz"):
            if not any(b['file_path'] == str(backup_file) for b in backups):
                backups.append({
                    'name': backup_file.stem.replace('.db', ''),
                    'file_path': str(backup_file),
                    'created_at': datetime.fromtimestamp(backup_file.stat().st_mtime).isoformat(),
                    'file_size': backup_file.stat().st_size,
                    'exists': True,
                    'description': '未記錄的備份'
                })
        
        return sorted(backups, key=lambda x: x['created_at'], reverse=True)
    
    def delete_backup(self, backup_name: str) -> bool:
        """刪除備份"""
        try:
            backup_file = self.backup_dir / f"{backup_name}.db.gz"
            if backup_file.exists():
                backup_file.unlink()
                
                # 從備份信息中移除
                self._remove_backup_info(backup_name)
                
                logger.info(f"備份刪除成功: {backup_name}")
                return True
            else:
                logger.warning(f"備份文件不存在: {backup_name}")
                return False
                
        except Exception as e:
            logger.error(f"刪除備份失敗: {str(e)}")
            return False
    
    def cleanup_old_backups(self, keep_days: int = 30, keep_count: int = 10) -> int:
        """清理舊備份"""
        backups = self.list_backups()
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        
        # 按時間排序，保留最新的 keep_count 個
        backups.sort(key=lambda x: x['created_at'], reverse=True)
        
        deleted_count = 0
        for i, backup in enumerate(backups):
            backup_date = datetime.fromisoformat(backup['created_at'])
            
            # 保留最新的 keep_count 個備份，或者在保留期內的備份
            if i >= keep_count and backup_date < cutoff_date:
                if self.delete_backup(backup['name']):
                    deleted_count += 1
        
        logger.info(f"清理舊備份完成，刪除了 {deleted_count} 個備份")
        return deleted_count
    
    def _save_backup_info(self, backup_name: str, backup_file: Path):
        """保存備份信息"""
        info_file = self.backup_dir / "backup_info.json"
        
        backup_info = []
        if info_file.exists():
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
            except Exception:
                backup_info = []
        
        backup_info.append({
            'name': backup_name,
            'file_path': str(backup_file),
            'created_at': datetime.now().isoformat(),
            'description': f'自動備份 - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
        })
        
        try:
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存備份信息失敗: {str(e)}")
    
    def _remove_backup_info(self, backup_name: str):
        """從備份信息中移除指定備份"""
        info_file = self.backup_dir / "backup_info.json"
        
        if not info_file.exists():
            return
        
        try:
            with open(info_file, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
            
            backup_info = [b for b in backup_info if b['name'] != backup_name]
            
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"移除備份信息失敗: {str(e)}")

class AutoBackupScheduler:
    """自動備份排程器"""
    
    def __init__(self, backup_manager: BackupManager):
        self.backup_manager = backup_manager
        self.last_backup_time = None
    
    def should_backup(self, interval_hours: int = 24) -> bool:
        """檢查是否需要備份"""
        if self.last_backup_time is None:
            return True
        
        time_since_backup = datetime.now() - self.last_backup_time
        return time_since_backup.total_seconds() >= interval_hours * 3600
    
    def run_auto_backup(self, interval_hours: int = 24) -> Optional[str]:
        """執行自動備份"""
        if self.should_backup(interval_hours):
            try:
                backup_file = self.backup_manager.create_backup()
                self.last_backup_time = datetime.now()
                
                # 清理舊備份
                self.backup_manager.cleanup_old_backups()
                
                logger.info(f"自動備份完成: {backup_file}")
                return backup_file
                
            except Exception as e:
                logger.error(f"自動備份失敗: {str(e)}")
                return None
        
        return None
    
    def setup_background_backup(self, interval_hours: int = 24):
        """設置背景自動備份"""
        import threading
        import time
        
        def backup_worker():
            while True:
                try:
                    self.run_auto_backup(interval_hours)
                    time.sleep(3600)  # 每小時檢查一次
                except Exception as e:
                    logger.error(f"背景備份任務錯誤: {str(e)}")
                    time.sleep(3600)
        
        backup_thread = threading.Thread(target=backup_worker, daemon=True)
        backup_thread.start()
        logger.info(f"背景自動備份已啟動，間隔: {interval_hours} 小時")

# 全域備份管理器實例
backup_manager = BackupManager()
auto_backup_scheduler = AutoBackupScheduler(backup_manager)