/**
 * Modern Modal Manager
 * 現代化彈出視窗管理器
 */

class ModalManager {
    constructor() {
        this.activeModal = null;
        this.init();
    }

    init() {
        // 添加樣式到頭部
        this.addModalStyles();
        
        // 綁定全局事件監聽器
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.closeModal();
            }
        });

        // 點擊背景關閉模態框
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay') && this.activeModal) {
                this.closeModal();
            }
        });
    }

    addModalStyles() {
        if (document.getElementById('modal-styles')) return;

        const style = document.createElement('style');
        style.id = 'modal-styles';
        style.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(15, 23, 42, 0.75);
                backdrop-filter: blur(10px);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                padding: 20px;
            }

            .modal-overlay.is-active {
                opacity: 1;
                visibility: visible;
            }

            .modal-container {
                background: #ffffff;
                border-radius: 24px;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 
                            0 0 0 1px rgba(0, 0, 0, 0.03);
                max-width: 90vw;
                max-height: 90vh;
                min-width: 480px;
                overflow: hidden;
                transform: scale(0.95) translateY(10px);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                display: flex;
                flex-direction: column;
            }

            .modal-container.modal-large {
                min-width: 800px;
                max-width: 1200px;
            }

            .modal-container.modal-small {
                min-width: 400px;
                max-width: 500px;
            }

            .modal-overlay.is-active .modal-container {
                transform: scale(1) translateY(0);
            }

            @keyframes modalSlideIn {
                from {
                    opacity: 0;
                    transform: scale(0.95) translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: scale(1) translateY(0);
                }
            }

            .modal-header {
                padding: 28px 32px 20px;
                border-bottom: 1px solid rgba(229, 231, 235, 0.8);
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                background: linear-gradient(to bottom, #ffffff, #fafbfc);
            }

            .modal-title-wrapper {
                flex: 1;
            }

            .modal-title {
                font-size: 1.75rem;
                font-weight: 700;
                margin: 0 0 4px 0;
                color: #1e293b;
                display: flex;
                align-items: center;
                gap: 12px;
                letter-spacing: -0.025em;
            }

            .modal-subtitle {
                font-size: 0.95rem;
                color: #64748b;
                margin: 0;
                font-weight: 400;
            }

            .modal-close {
                background: transparent;
                border: none;
                border-radius: 12px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                color: #64748b;
                font-size: 20px;
                transition: all 0.2s ease;
                margin: -4px -8px 0 0;
            }

            .modal-close:hover {
                background: rgba(241, 245, 249, 0.8);
                color: #334155;
                transform: rotate(90deg);
            }

            .modal-body {
                padding: 32px;
                flex: 1;
                overflow-y: auto;
                background: #ffffff;
            }

            .modal-body::-webkit-scrollbar {
                width: 8px;
            }

            .modal-body::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 4px;
            }

            .modal-body::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 4px;
            }

            .modal-body::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }

            .modal-footer {
                padding: 20px 32px 28px;
                border-top: 1px solid rgba(229, 231, 235, 0.8);
                display: flex;
                justify-content: flex-end;
                gap: 12px;
                background: linear-gradient(to top, #ffffff, #fafbfc);
            }

            .modal-form-group {
                margin-bottom: 24px;
            }

            .modal-form-group label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #334155;
                font-size: 0.9rem;
                letter-spacing: -0.01em;
            }

            .modal-form-group label .required {
                color: #ef4444;
                margin-left: 2px;
            }

            .modal-form-group input,
            .modal-form-group select,
            .modal-form-group textarea {
                width: 100%;
                padding: 12px 16px;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                font-size: 15px;
                transition: all 0.2s ease;
                background: #ffffff;
                font-weight: 400;
                color: #1e293b;
            }

            .modal-form-group input::placeholder,
            .modal-form-group textarea::placeholder {
                color: #94a3b8;
            }

            .modal-form-group input:focus,
            .modal-form-group select:focus,
            .modal-form-group textarea:focus {
                outline: none;
                border-color: #6366f1;
                box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
                background: #ffffff;
            }

            .modal-form-group input:hover,
            .modal-form-group select:hover,
            .modal-form-group textarea:hover {
                border-color: #cbd5e1;
            }

            .modal-button {
                padding: 12px 24px;
                border: none;
                border-radius: 12px;
                font-size: 15px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                display: inline-flex;
                align-items: center;
                gap: 8px;
                min-width: 120px;
                justify-content: center;
                letter-spacing: -0.01em;
                position: relative;
                overflow: hidden;
            }

            .modal-button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .modal-button-primary {
                background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
                color: white;
                box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.3);
            }

            .modal-button-primary:hover:not(:disabled) {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(99, 102, 241, 0.4);
                background: linear-gradient(135deg, #5558e3 0%, #7c3aed 100%);
            }

            .modal-button-secondary {
                background: #f1f5f9;
                color: #475569;
                border: 2px solid #e2e8f0;
            }

            .modal-button-secondary:hover:not(:disabled) {
                background: #e2e8f0;
                transform: translateY(-1px);
                border-color: #cbd5e1;
            }

            .modal-button-danger {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                color: white;
                box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.3);
            }

            .modal-button-danger:hover:not(:disabled) {
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
            }

            .modal-button-success {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.3);
            }

            .modal-button-success:hover:not(:disabled) {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
            }

            .modal-loading {
                display: none;
                justify-content: center;
                align-items: center;
                padding: 40px;
            }

            .modal-spinner {
                width: 40px;
                height: 40px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #667eea;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            @media (max-width: 768px) {
                .modal-container {
                    min-width: 95vw;
                    max-width: 95vw;
                    margin: 20px;
                    max-height: 90vh;
                }

                .modal-header {
                    padding: 20px 16px 12px;
                }

                .modal-body {
                    padding: 16px;
                    max-height: calc(90vh - 140px);
                }

                .modal-footer {
                    padding: 12px 16px 20px;
                    flex-direction: column;
                }

                .modal-button {
                    width: 100%;
                    margin-bottom: 8px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    createModal({ title, subtitle = '', content, size = 'medium', actions = [], onClose = null, icon = null }) {
        // 移除現有的模態框
        this.closeModal();

        // 創建模態框結構
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';
        overlay.innerHTML = `
            <div class="modal-container modal-${size}">
                <div class="modal-header">
                    <div class="modal-title-wrapper">
                        <h2 class="modal-title">
                            ${icon ? `<i class="fas fa-${icon}"></i>` : ''}
                            ${title}
                        </h2>
                        ${subtitle ? `<p class="modal-subtitle">${subtitle}</p>` : ''}
                    </div>
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                ${actions.length > 0 ? `
                    <div class="modal-footer">
                        ${actions.map(action => `
                            <button class="modal-button modal-button-${action.type || 'secondary'}" 
                                    data-action="${action.action || ''}" 
                                    ${action.disabled ? 'disabled' : ''}>
                                ${action.icon ? `<i class="fas fa-${action.icon}"></i>` : ''}
                                ${action.text}
                            </button>
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        `;

        // 添加到 DOM
        document.body.appendChild(overlay);
        this.activeModal = overlay;

        // 綁定關閉按鈕事件
        overlay.querySelector('.modal-close').addEventListener('click', () => {
            this.closeModal();
            if (onClose) onClose();
        });

        // 綁定動作按鈕事件
        actions.forEach(action => {
            if (action.handler) {
                const button = overlay.querySelector(`[data-action="${action.action}"]`);
                if (button) {
                    button.addEventListener('click', action.handler);
                }
            }
        });

        // 顯示模態框
        setTimeout(() => {
            overlay.classList.add('is-active');
        }, 10);

        return overlay;
    }

    closeModal() {
        if (!this.activeModal) return;

        this.activeModal.classList.remove('is-active');
        
        setTimeout(() => {
            if (this.activeModal && this.activeModal.parentNode) {
                this.activeModal.parentNode.removeChild(this.activeModal);
            }
            this.activeModal = null;
        }, 300);
    }

    showConfirmModal({ title, message, confirmText = '確認', cancelText = '取消', onConfirm, onCancel }) {
        return this.createModal({
            title,
            content: `<div class="has-text-centered">
                <i class="fas fa-question-circle has-text-warning" style="font-size: 3rem; margin-bottom: 16px;"></i>
                <p style="font-size: 1.1rem; margin-bottom: 0;">${message}</p>
            </div>`,
            actions: [
                {
                    text: cancelText,
                    type: 'secondary',
                    icon: 'times',
                    action: 'cancel',
                    handler: () => {
                        this.closeModal();
                        if (onCancel) onCancel();
                    }
                },
                {
                    text: confirmText,
                    type: 'primary',
                    icon: 'check',
                    action: 'confirm',
                    handler: () => {
                        this.closeModal();
                        if (onConfirm) onConfirm();
                    }
                }
            ]
        });
    }

    showFormModal({ title, subtitle = '', fields, onSubmit, onCancel }) {
        let formContent = '<form class="modal-form">';
        
        fields.forEach(field => {
            formContent += `
                <div class="modal-form-group">
                    <label for="modal-${field.name}">
                        ${field.label}${field.required ? '<span class="required">*</span>' : ''}
                    </label>
                    ${field.type === 'textarea' ? 
                        `<textarea id="modal-${field.name}" name="${field.name}" 
                                  placeholder="${field.placeholder || ''}" 
                                  ${field.required ? 'required' : ''}
                                  rows="${field.rows || 3}"></textarea>` :
                        field.type === 'select' ? 
                        `<select id="modal-${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>
                            ${field.options.map(opt => `<option value="${opt.value}">${opt.text}</option>`).join('')}
                         </select>` :
                        `<input type="${field.type || 'text'}" 
                               id="modal-${field.name}" 
                               name="${field.name}" 
                               placeholder="${field.placeholder || ''}" 
                               value="${field.value || ''}"
                               ${field.required ? 'required' : ''}>`
                    }
                </div>
            `;
        });
        
        formContent += '</form>';

        return this.createModal({
            title,
            subtitle,
            icon: 'edit',
            content: formContent,
            actions: [
                {
                    text: '取消',
                    type: 'secondary',
                    icon: 'times',
                    action: 'cancel',
                    handler: () => {
                        this.closeModal();
                        if (onCancel) onCancel();
                    }
                },
                {
                    text: '提交',
                    type: 'primary',
                    icon: 'check',
                    action: 'submit',
                    handler: () => {
                        const form = this.activeModal.querySelector('.modal-form');
                        const formData = new FormData(form);
                        const data = Object.fromEntries(formData.entries());
                        
                        if (onSubmit) {
                            const result = onSubmit(data);
                            if (result !== false) {
                                this.closeModal();
                            }
                        } else {
                            this.closeModal();
                        }
                    }
                }
            ]
        });
    }

    showLoadingModal(title = '處理中...', message = '請稍候') {
        return this.createModal({
            title,
            content: `
                <div class="modal-loading" style="display: flex;">
                    <div class="modal-spinner"></div>
                </div>
                <div class="has-text-centered">
                    <p>${message}</p>
                </div>
            `
        });
    }
}

// 創建全局實例
window.modalManager = new ModalManager();

// 便捷方法
window.showModal = (options) => window.modalManager.createModal(options);
window.showConfirm = (options) => window.modalManager.showConfirmModal(options);
window.showForm = (options) => window.modalManager.showFormModal(options);
window.showLoading = (title, message) => window.modalManager.showLoadingModal(title, message);
window.closeModal = () => window.modalManager.closeModal();