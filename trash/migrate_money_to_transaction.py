#!/usr/bin/env python3
"""
資料遷移腳本：將 Money 表的資料轉換成 Transaction 表
"""

import sys
import os
from datetime import datetime
from collections import defaultdict

# 添加專案路徑
sys.path.append(os.path.dirname(__file__))

from sqlalchemy.orm import sessionmaker
from model import engine, Money, Transaction, JournalEntry

Session = sessionmaker(bind=engine)

def migrate_money_to_transaction():
    """將 Money 表的資料遷移到 Transaction 表"""
    db = Session()
    
    try:
        print("開始資料遷移...")
        
        # 詢問是否清空現有資料
        clear_existing = input("是否清空現有的 Transaction 和 JournalEntry 資料？(y/N): ")
        
        if clear_existing.lower() == 'y':
            try:
                print("清空現有的 Transaction 和 JournalEntry 資料...")
                db.query(JournalEntry).delete()
                db.query(Transaction).delete()
                db.commit()
                print("清空完成")
            except Exception as e:
                print(f"清空資料失敗：{str(e)}")
                print("可能是因為資料庫被其他程序使用")
                print("請先關閉 Flask 應用程式，然後重新執行此腳本")
                return
        else:
            print("保留現有資料，只新增不存在的交易...")
        
        # 2. 從 Money 表讀取所有資料，按 journal_reference 分組
        print("讀取 Money 表資料...")
        money_records = db.query(Money).filter(Money.is_deleted == False).all()
        
        if not money_records:
            print("Money 表中沒有資料需要遷移")
            return
        
        # 按 journal_reference 分組，如果沒有 journal_reference 則按其他欄位組合
        grouped_records = defaultdict(list)
        
        for record in money_records:
            # 如果有 journal_reference，用它分組
            if record.journal_reference:
                group_key = record.journal_reference
            else:
                # 如果沒有，用日期+名稱+金額組合作為分組依據
                group_key = f"{record.a_time}_{record.name}_{record.total}_{record.account_id}"
            
            grouped_records[group_key].append(record)
        
        print(f"找到 {len(grouped_records)} 個交易群組")
        
        # 3. 為每個群組創建一筆 Transaction 記錄
        transaction_count = 0
        skipped_count = 0
        
        for group_key, records in grouped_records.items():
            # 檢查是否已經存在相同的交易
            main_record = find_main_record(records)
            if not main_record:
                print(f"警告：群組 {group_key} 找不到主要記錄，跳過")
                skipped_count += 1
                continue
            
            # 檢查是否已存在相同的交易
            existing_transaction = db.query(Transaction).filter(
                Transaction.transaction_date == main_record.a_time,
                Transaction.description == (main_record.name or ''),
                Transaction.total_amount == calculate_total_amount(records),
                Transaction.account_id == main_record.account_id
            ).first()
            
            if existing_transaction and clear_existing.lower() != 'y':
                print(f"跳過已存在的交易：{main_record.name}")
                skipped_count += 1
                continue
            
            # 判斷交易類型
            transaction_type = determine_transaction_type(records)
            
            # 計算總金額（含稅）
            total_amount = calculate_total_amount(records)
            
            # 計算稅額
            tax_amount = calculate_tax_amount(records)
            
            # 創建 Transaction 記錄
            transaction = Transaction(
                transaction_date=main_record.a_time,
                description=main_record.name or '',
                total_amount=total_amount,
                tax_amount=tax_amount,
                extra_fee=main_record.extra_fee or 0,
                transaction_type=transaction_type,
                account_id=main_record.account_id,
                payment_identity_id=main_record.payment_identity_id,
                department_id=main_record.department_id,
                project_id=main_record.project_id,
                is_paper=main_record.is_paper or False,
                invoice_number=main_record.number,
                tax_type=main_record.tax_type,
                buyer_tax_id=main_record.buyer_tax_id,
                seller_tax_id=main_record.seller_tax_id,
                invoice_date=main_record.date,
                is_paid=main_record.is_paid or False,
                should_paid_date=main_record.should_paid_date,
                paid_date=main_record.paid_date,
                note=main_record.note,
                tags=main_record.tags,
                image_path=main_record.image_path,
                created_at=main_record.created_at,
                updated_at=main_record.updated_at,
                created_by=main_record.created_by,
                updated_by=main_record.updated_by
            )
            
            db.add(transaction)
            db.flush()  # 取得 transaction.id
            
            # 為每筆 Money 記錄創建對應的 JournalEntry
            for record in records:
                # 判斷借貸方向和金額
                if record.entry_side == 'DEBIT':
                    debit_amount = record.total
                    credit_amount = 0
                else:
                    debit_amount = 0
                    credit_amount = record.total
                
                # 判斷分錄類型
                entry_type = determine_entry_type(record, records)
                
                journal_entry = JournalEntry(
                    transaction_id=transaction.id,
                    subject_code=record.subject_code,
                    debit_amount=debit_amount,
                    credit_amount=credit_amount,
                    description=record.name,
                    entry_type=entry_type,
                    created_at=record.created_at
                )
                
                db.add(journal_entry)
            
            transaction_count += 1
            print(f"已處理交易 {transaction_count}: {main_record.name}")
        
        # 4. 提交所有變更
        db.commit()
        print(f"\n遷移完成！")
        print(f"共創建了 {transaction_count} 筆交易記錄")
        print(f"跳過了 {skipped_count} 筆記錄")
        
        # 5. 驗證遷移結果
        verify_migration(db)
        
    except Exception as e:
        db.rollback()
        print(f"遷移失敗：{str(e)}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        db.close()

def find_main_record(records):
    """找出主要記錄"""
    # 優先順序：
    # 1. 收入：找貸方分錄且不是稅額科目
    # 2. 支出：找借方分錄且不是稅額科目
    
    for record in records:
        # 排除稅額科目
        if record.subject_code in ['1290', '2290']:  # 進項稅額、銷項稅額
            continue
        
        # 收入交易：找貸方分錄
        if record.money_type == '收入' and record.entry_side == 'CREDIT':
            return record
        
        # 支出交易：找借方分錄
        if record.money_type == '支出' and record.entry_side == 'DEBIT':
            return record
    
    # 如果找不到，返回第一筆記錄
    return records[0] if records else None

def determine_transaction_type(records):
    """判斷交易類型"""
    # 根據 money_type 判斷
    money_types = set(record.money_type for record in records)
    
    if '收入' in money_types:
        return 'income'
    elif '支出' in money_types:
        return 'expense'
    elif '轉帳' in money_types:
        return 'transfer'
    else:
        return 'expense'  # 預設為支出

def calculate_total_amount(records):
    """計算總金額（含稅）"""
    # 找出銀行存款或現金科目的金額（這通常是含稅總額）
    for record in records:
        if record.subject_code and (
            record.subject_code.startswith('1110') or  # 銀行存款
            record.subject_code.startswith('1105')     # 現金
        ):
            return abs(record.total)
    
    # 如果找不到，使用主要分錄的金額
    main_record = find_main_record(records)
    return main_record.total if main_record else 0

def calculate_tax_amount(records):
    """計算稅額"""
    tax_amount = 0
    for record in records:
        if record.subject_code in ['1290', '2290']:  # 進項稅額、銷項稅額
            tax_amount += abs(record.total)
    return tax_amount

def determine_entry_type(record, all_records):
    """判斷分錄類型"""
    # 稅額分錄
    if record.subject_code in ['1290', '2290']:
        return 'balance'
    
    # 銀行存款/現金分錄
    if record.subject_code and (
        record.subject_code.startswith('1110') or
        record.subject_code.startswith('1105')
    ):
        return 'balance'
    
    # 其他視為主要分錄
    return 'primary'

def verify_migration(db):
    """驗證遷移結果"""
    print("\n=== 驗證遷移結果 ===")
    
    # 統計 Money 表記錄數
    money_count = db.query(Money).filter(Money.is_deleted == False).count()
    print(f"Money 表記錄數：{money_count}")
    
    # 統計 Transaction 表記錄數
    transaction_count = db.query(Transaction).count()
    print(f"Transaction 表記錄數：{transaction_count}")
    
    # 統計 JournalEntry 表記錄數
    journal_count = db.query(JournalEntry).count()
    print(f"JournalEntry 表記錄數：{journal_count}")
    
    # 檢查金額是否平衡
    transactions = db.query(Transaction).all()
    for transaction in transactions:
        entries = db.query(JournalEntry).filter(JournalEntry.transaction_id == transaction.id).all()
        total_debit = sum(entry.debit_amount for entry in entries)
        total_credit = sum(entry.credit_amount for entry in entries)
        
        if total_debit != total_credit:
            print(f"警告：交易 {transaction.id} 借貸不平衡 - 借方：{total_debit}, 貸方：{total_credit}")

if __name__ == '__main__':
    print("資料遷移腳本")
    print("這將把 Money 表的資料轉換成 Transaction 表格式")
    
    confirm = input("確定要執行遷移嗎？(y/N): ")
    if confirm.lower() == 'y':
        migrate_money_to_transaction()
    else:
        print("取消遷移") 