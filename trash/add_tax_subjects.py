#!/usr/bin/env python3
"""
添加稅額科目
"""
from sqlalchemy.orm import sessionmaker
from model import engine, AccountSubject

Session = sessionmaker(bind=engine)

def add_tax_subjects():
    """添加進項稅額和銷項稅額科目"""
    db = Session()
    try:
        # 檢查是否已存在
        existing_1290 = db.query(AccountSubject).filter_by(code='1290').first()
        existing_2290 = db.query(AccountSubject).filter_by(code='2290').first()
        
        if not existing_1290:
            input_tax = AccountSubject(
                code='1290',
                name='進項稅額',
                top_category='資產',
                parent_code='12',
                is_active=True
            )
            db.add(input_tax)
            print("添加進項稅額科目 1290")
        else:
            print("進項稅額科目 1290 已存在")
        
        if not existing_2290:
            output_tax = AccountSubject(
                code='2290',
                name='銷項稅額',
                top_category='負債',
                parent_code='22',
                is_active=True
            )
            db.add(output_tax)
            print("添加銷項稅額科目 2290")
        else:
            print("銷項稅額科目 2290 已存在")
        
        db.commit()
        print("稅額科目添加完成")
        
        # 驗證
        subjects = db.query(AccountSubject).filter(
            AccountSubject.code.in_(['1290', '2290'])
        ).all()
        
        print("\n現有稅額科目：")
        for subject in subjects:
            print(f"  {subject.code} - {subject.name}")
            
    except Exception as e:
        db.rollback()
        print(f"錯誤：{e}")
    finally:
        db.close()

if __name__ == "__main__":
    add_tax_subjects()
