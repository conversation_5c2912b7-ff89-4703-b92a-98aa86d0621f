#!/usr/bin/env python3
from database import get_db
from model import Account

def check_accounts():
    with get_db() as db:
        # 檢查所有帳戶
        all_accounts = db.query(Account).all()
        print(f'總帳戶數量: {len(all_accounts)}')
        
        # 檢查現金帳戶
        cash_accounts = db.query(Account).filter_by(category='現金').all()
        print(f'現金帳戶數量: {len(cash_accounts)}')
        
        for acc in cash_accounts:
            print(f'ID: {acc.id}, 名稱: {acc.name}, 期初金額: {acc.init_amount}')
        
        # 檢查銀行帳戶
        bank_accounts = db.query(Account).filter_by(category='銀行帳戶').all()
        print(f'銀行帳戶數量: {len(bank_accounts)}')
        
        for acc in bank_accounts:
            print(f'ID: {acc.id}, 名稱: {acc.name}, 銀行: {acc.bank_name}')

if __name__ == '__main__':
    check_accounts()
