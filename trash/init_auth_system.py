#!/usr/bin/env python3
"""
簡化版權限系統初始化腳本
直接在主目錄執行，建立基本的權限系統
"""

from database import get_db
from werkzeug.security import generate_password_hash
from sqlalchemy import text

def create_tables():
    """建立權限相關資料表"""
    print("建立權限系統資料表...")
    
    with get_db() as db:
        # 建立角色表
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY,
                name VARCHAR(50) UNIQUE NOT NULL,
                display_name VARCHAR(100) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME,
                updated_at DATETIME
            )
        """))
        
        # 建立權限表
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY,
                name VARCHAR(100) UNIQUE NOT NULL,
                display_name VARCHAR(100) NOT NULL,
                module VARCHAR(50) NOT NULL,
                action VARCHAR(50) NOT NULL,
                description TEXT
            )
        """))
        
        # 建立用戶角色關聯表
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS user_roles (
                user_id INTEGER,
                role_id INTEGER,
                PRIMARY KEY (user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (role_id) REFERENCES roles (id)
            )
        """))
        
        # 建立角色權限關聯表
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS role_permissions (
                role_id INTEGER,
                permission_id INTEGER,
                PRIMARY KEY (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES roles (id),
                FOREIGN KEY (permission_id) REFERENCES permissions (id)
            )
        """))
        
        # 建立用戶會話表
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY,
                user_id INTEGER NOT NULL,
                session_token VARCHAR(255) UNIQUE NOT NULL,
                expires_at DATETIME NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME,
                last_accessed DATETIME,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        """))
        
        db.commit()
        print("資料表建立完成")

def create_basic_permissions():
    """建立基本權限"""
    print("建立基本權限...")
    
    permissions_data = [
        # 收支帳簿
        ("income_expense.view", "查看收支帳簿", "income_expense", "view"),
        ("income_expense.create", "建立收支記錄", "income_expense", "create"),
        
        # 資金管理
        ("fund_management.view", "查看資金管理", "fund_management", "view"),
        ("fund_management.create", "建立資金記錄", "fund_management", "create"),
        
        # 我的報表
        ("reports.view", "查看報表", "reports", "view"),
        
        # 設定
        ("settings.view", "查看設定", "settings", "view"),
        ("settings.manage", "管理系統設定", "settings", "manage"),
        
        # 系統管理
        ("admin.view", "查看管理介面", "admin", "view"),
        ("admin.user_management", "用戶管理", "admin", "user_management"),
    ]
    
    with get_db() as db:
        for name, display_name, module, action in permissions_data:
            # 檢查權限是否已存在
            result = db.execute(text("SELECT id FROM permissions WHERE name = :name"), {"name": name}).fetchone()
            if not result:
                db.execute(text("""
                    INSERT INTO permissions (name, display_name, module, action)
                    VALUES (:name, :display_name, :module, :action)
                """), {
                    "name": name,
                    "display_name": display_name,
                    "module": module,
                    "action": action
                })
                print(f"  建立權限: {display_name}")
        
        db.commit()

def create_basic_roles():
    """建立基本角色"""
    print("建立基本角色...")
    
    roles_data = [
        ("admin", "系統管理員", "擁有所有權限的系統管理員"),
        ("user", "一般用戶", "基本使用權限的用戶"),
    ]
    
    with get_db() as db:
        for name, display_name, description in roles_data:
            # 檢查角色是否已存在
            result = db.execute(text("SELECT id FROM roles WHERE name = :name"), {"name": name}).fetchone()
            if not result:
                db.execute(text("""
                    INSERT INTO roles (name, display_name, description, is_active, created_at)
                    VALUES (:name, :display_name, :description, 1, datetime('now'))
                """), {
                    "name": name,
                    "display_name": display_name,
                    "description": description
                })
                print(f"  建立角色: {display_name}")
        
        db.commit()

def assign_permissions_to_roles():
    """為角色分配權限"""
    print("分配角色權限...")
    
    with get_db() as db:
        # 獲取所有權限ID
        permissions = db.execute(text("SELECT id, name FROM permissions")).fetchall()
        permission_map = {perm[1]: perm[0] for perm in permissions}
        
        # 獲取角色ID
        admin_role = db.execute(text("SELECT id FROM roles WHERE name = 'admin'")).fetchone()
        user_role = db.execute(text("SELECT id FROM roles WHERE name = 'user'")).fetchone()
        
        if admin_role and user_role:
            admin_role_id = admin_role[0]
            user_role_id = user_role[0]
            
            # 管理員獲得所有權限
            for perm_id in permission_map.values():
                db.execute(text("""
                    INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
                    VALUES (:role_id, :permission_id)
                """), {"role_id": admin_role_id, "permission_id": perm_id})
            
            # 一般用戶獲得基本權限
            basic_permissions = [
                "income_expense.view", "income_expense.create",
                "fund_management.view", "reports.view", "settings.view"
            ]
            
            for perm_name in basic_permissions:
                if perm_name in permission_map:
                    db.execute(text("""
                        INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
                        VALUES (:role_id, :permission_id)
                    """), {"role_id": user_role_id, "permission_id": permission_map[perm_name]})
            
            db.commit()
            print("  權限分配完成")

def create_admin_user():
    """建立管理員用戶"""
    print("建立管理員用戶...")
    
    with get_db() as db:
        # 檢查是否已有管理員
        existing_admin = db.execute(text("SELECT id FROM users WHERE username = 'admin'")).fetchone()
        if existing_admin:
            print("  管理員用戶已存在")
            return existing_admin[0]
        
        # 建立管理員用戶
        password_hash = generate_password_hash('admin123')
        
        result = db.execute(text("""
            INSERT INTO users (username, email, password_hash, full_name, is_active, created_at)
            VALUES ('admin', '<EMAIL>', :password_hash, '系統管理員', 1, datetime('now'))
        """), {"password_hash": password_hash})
        
        user_id = result.lastrowid
        
        # 分配管理員角色
        admin_role = db.execute(text("SELECT id FROM roles WHERE name = 'admin'")).fetchone()
        if admin_role:
            db.execute(text("""
                INSERT INTO user_roles (user_id, role_id)
                VALUES (:user_id, :role_id)
            """), {"user_id": user_id, "role_id": admin_role[0]})
        
        db.commit()
        print("  管理員用戶建立成功")
        print("  用戶名: admin")
        print("  密碼: admin123")
        
        return user_id

def main():
    """主函數"""
    print("開始初始化權限系統...")
    
    try:
        # 建立資料表
        create_tables()
        
        # 建立基本權限
        create_basic_permissions()
        
        # 建立基本角色
        create_basic_roles()
        
        # 分配角色權限
        assign_permissions_to_roles()
        
        # 建立管理員用戶
        create_admin_user()
        
        print("\n✅ 權限系統初始化完成！")
        print("請使用以下帳號登入：")
        print("用戶名: admin")
        print("密碼: admin123")
        print("\n現在可以啟動應用程式並訪問 /auth/login 進行登入")
        
    except Exception as e:
        print(f"❌ 初始化失敗: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()