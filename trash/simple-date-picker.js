// 簡單的日期選擇器實現
class SimpleDateRangePicker {
    constructor(containerId) {
        this.container = document.getElementById(containerId);

        // 日期狀態
        this.startDate = null;
        this.endDate = null;

        // 選擇狀態：0=未選擇，1=已選擇起始日期
        this.selectionStep = 0;

        // 日曆顯示月份
        this.leftMonth = new Date();
        this.rightMonth = new Date();
        this.rightMonth.setMonth(this.rightMonth.getMonth() + 1);

        this.init();
    }

    init() {
        this.bindEvents();
        this.renderCalendars();
        this.setDefaultRange();
    }

    bindEvents() {
        // 快速篩選按鈕
        this.container.querySelectorAll('.quick-filters .button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleQuickFilter(e.target.dataset.range);
            });
        });

        // 日曆切換按鈕
        const calendarToggle = this.container.querySelector('#calendar-toggle');
        if (calendarToggle) {
            calendarToggle.addEventListener('click', () => this.toggleCalendar());
        }

        // 關閉日曆按鈕
        const closeCalendar = this.container.querySelector('#close-calendar');
        if (closeCalendar) {
            closeCalendar.addEventListener('click', () => this.hideCalendar());
        }

        // 月份導航按鈕
        const prevMonthStart = this.container.querySelector('#prev-month-start');
        const nextMonthStart = this.container.querySelector('#next-month-start');
        const prevMonthEnd = this.container.querySelector('#prev-month-end');
        const nextMonthEnd = this.container.querySelector('#next-month-end');

        if (prevMonthStart) prevMonthStart.addEventListener('click', () => this.changeMonth('left', -1));
        if (nextMonthStart) nextMonthStart.addEventListener('click', () => this.changeMonth('left', 1));
        if (prevMonthEnd) prevMonthEnd.addEventListener('click', () => this.changeMonth('right', -1));
        if (nextMonthEnd) nextMonthEnd.addEventListener('click', () => this.changeMonth('right', 1));

        // 確定和取消按鈕
        const applyBtn = this.container.querySelector('#apply-date-range');
        const cancelBtn = this.container.querySelector('#cancel-date-range');

        if (applyBtn) applyBtn.addEventListener('click', () => this.applyDateRange());
        if (cancelBtn) cancelBtn.addEventListener('click', () => this.hideCalendar());

        // 點擊外部關閉日曆
        document.addEventListener('click', (e) => {
            const calendarContainer = this.container.querySelector('#calendar-container');
            const calendarToggle = this.container.querySelector('#calendar-toggle');

            if (calendarContainer &&
                calendarContainer.style.display !== 'none' &&
                !calendarContainer.contains(e.target) &&
                !calendarToggle.contains(e.target)) {
                this.hideCalendar();
            }
        });
    }

    handleQuickFilter(range) {
        const today = new Date();
        let start, end;

        if (range === 'custom') {
            // 清空日期，顯示日曆讓用戶選擇
            this.startDate = null;
            this.endDate = null;
            this.selectionStep = 0;
            this.showCalendar();
            return;
        }

        switch (range) {
            case '7':
                // 最近7天
                start = new Date(today);
                start.setDate(today.getDate() - 6);
                end = new Date(today);
                break;
            case '30':
                // 最近30天
                start = new Date(today);
                start.setDate(today.getDate() - 29);
                end = new Date(today);
                break;
            case 'this_month':
                // 本月
                start = new Date(today.getFullYear(), today.getMonth(), 1);
                end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                break;
            case 'last_month':
                // 上月
                start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                end = new Date(today.getFullYear(), today.getMonth(), 0);
                break;
            case 'this_quarter':
                // 本季度
                const quarter = Math.floor(today.getMonth() / 3);
                start = new Date(today.getFullYear(), quarter * 3, 1);
                end = new Date(today.getFullYear(), (quarter + 1) * 3, 0);
                break;
            case 'last_quarter':
                // 上季度
                const lastQuarter = Math.floor(today.getMonth() / 3) - 1;
                const lastQuarterYear = lastQuarter < 0 ? today.getFullYear() - 1 : today.getFullYear();
                const lastQuarterMonth = lastQuarter < 0 ? 9 : lastQuarter * 3;
                start = new Date(lastQuarterYear, lastQuarterMonth, 1);
                end = new Date(lastQuarterYear, lastQuarterMonth + 3, 0);
                break;
            case 'this_year':
                // 今年
                start = new Date(today.getFullYear(), 0, 1);
                end = new Date(today.getFullYear(), 11, 31);
                break;
            case 'last_year':
                // 去年
                start = new Date(today.getFullYear() - 1, 0, 1);
                end = new Date(today.getFullYear() - 1, 11, 31);
                break;
        }

        // 設置日期範圍
        this.startDate = start;
        this.endDate = end;
        this.selectionStep = 0; // 重置選擇狀態

        // 更新顯示並觸發變更事件
        this.updateDisplay();
        this.triggerChange();
        this.hideCalendar();
    }

    setDefaultRange() {
        // 預設為本月
        this.handleQuickFilter('this_month');
    }

    updateDisplay() {
        const display = this.container.querySelector('#date-display');
        const startHidden = this.container.querySelector('#date-start-hidden');
        const endHidden = this.container.querySelector('#date-end-hidden');

        if (this.startDate) {
            const startStr = this.formatDate(this.startDate);

            if (this.endDate) {
                // 完整日期範圍
                const endStr = this.formatDate(this.endDate);
                display.value = `${startStr} ~ ${endStr}`;
                startHidden.value = startStr;
                endHidden.value = endStr;
            } else {
                // 只有起始日期
                display.value = `${startStr} ~ 請選擇結束日期`;
                startHidden.value = startStr;
                endHidden.value = '';
            }
        } else {
            // 未選擇日期
            display.value = '';
            startHidden.value = '';
            endHidden.value = '';
        }
    }

    formatDate(date) {
        if (!date) return '';
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    toggleCalendar() {
        const container = this.container.querySelector('#calendar-container');
        if (container.style.display === 'none' || !container.style.display) {
            this.showCalendar();
        } else {
            this.hideCalendar();
        }
    }

    showCalendar() {
        const container = this.container.querySelector('#calendar-container');
        container.style.display = 'block';
        this.renderCalendars();
    }

    hideCalendar() {
        const container = this.container.querySelector('#calendar-container');
        container.style.display = 'none';
    }

    changeMonth(side, delta) {
        if (side === 'left') {
            this.leftMonth.setMonth(this.leftMonth.getMonth() + delta);
        } else {
            this.rightMonth.setMonth(this.rightMonth.getMonth() + delta);
        }
        this.renderCalendars();
    }

    renderCalendars() {
        this.renderCalendar('start', this.leftMonth);
        this.renderCalendar('end', this.rightMonth);
    }

    renderCalendar(type, date) {
        const daysContainer = this.container.querySelector(`#${type}-days`);
        const monthYearElement = this.container.querySelector(`#${type}-month-year`);

        // 更新月份年份顯示
        monthYearElement.textContent = `${date.getFullYear()}年${date.getMonth() + 1}月`;

        // 清空日期容器
        daysContainer.innerHTML = '';

        // 獲取月份的第一天和最後一天
        const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
        const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);

        // 獲取第一天是星期幾
        const firstDayOfWeek = firstDay.getDay();

        // 添加上個月的日期
        for (let i = 0; i < firstDayOfWeek; i++) {
            const prevDate = new Date(firstDay);
            prevDate.setDate(prevDate.getDate() - (firstDayOfWeek - i));
            this.addDayElement(daysContainer, prevDate, 'other-month');
        }

        // 添加當月的日期
        for (let day = 1; day <= lastDay.getDate(); day++) {
            const currentDate = new Date(date.getFullYear(), date.getMonth(), day);
            this.addDayElement(daysContainer, currentDate, 'current-month');
        }

        // 添加下個月的日期
        const daysInGrid = 42; // 6行7列
        const daysAdded = firstDayOfWeek + lastDay.getDate();
        const remainingDays = daysInGrid - daysAdded;

        for (let day = 1; day <= remainingDays; day++) {
            const nextDate = new Date(lastDay);
            nextDate.setDate(nextDate.getDate() + day);
            this.addDayElement(daysContainer, nextDate, 'other-month');
        }
    }

    addDayElement(container, date, className) {
        const dayElement = document.createElement('button');
        dayElement.className = `calendar-day ${className}`;
        dayElement.textContent = date.getDate();
        dayElement.dataset.date = this.formatDate(date);

        // 檢查是否為今天
        const today = new Date();
        if (date.toDateString() === today.toDateString()) {
            dayElement.classList.add('today');
        }

        // 檢查是否為選中日期
        if (this.startDate && this.isSameDay(date, this.startDate)) {
            dayElement.classList.add('selected');
        }
        if (this.endDate && this.isSameDay(date, this.endDate)) {
            dayElement.classList.add('selected');
        }

        // 檢查是否在範圍內
        if (this.startDate && this.endDate &&
            date >= this.startDate && date <= this.endDate) {
            dayElement.classList.add('in-range');
        }

        // 點擊事件
        dayElement.addEventListener('click', () => this.onDateClick(date));

        container.appendChild(dayElement);
    }

    isSameDay(date1, date2) {
        return date1.getFullYear() === date2.getFullYear() &&
            date1.getMonth() === date2.getMonth() &&
            date1.getDate() === date2.getDate();
    }

    onDateClick(date) {
        console.log(`日期點擊: ${this.formatDate(date)}, 當前步驟: ${this.selectionStep}`);

        // 創建新的日期對象，避免引用問題
        const clickedDate = new Date(date);

        if (this.selectionStep === 0) {
            // 第一步：選擇起始日期
            console.log("第一步：設置起始日期");
            this.startDate = clickedDate;
            this.endDate = null;
            this.selectionStep = 1;
        } else {
            // 第二步：選擇結束日期
            console.log("第二步：設置結束日期");

            // 確保開始日期早於結束日期
            if (clickedDate < this.startDate) {
                this.endDate = new Date(this.startDate);
                this.startDate = clickedDate;
            } else {
                this.endDate = clickedDate;
            }

            this.selectionStep = 0; // 重置為第一步，準備下一次選擇
        }

        console.log(`選擇後：起始=${this.formatDate(this.startDate)}, 結束=${this.endDate ? this.formatDate(this.endDate) : 'null'}`);

        this.renderCalendars();
        this.updateDisplay();
        this.triggerChange();
    }

    applyDateRange() {
        this.hideCalendar();
        this.triggerChange();
    }

    triggerChange() {
        const event = new CustomEvent('dateRangeChanged', {
            detail: {
                startDate: this.startDate,
                endDate: this.endDate,
                startDateStr: this.startDate ? this.formatDate(this.startDate) : '',
                endDateStr: this.endDate ? this.formatDate(this.endDate) : ''
            }
        });
        this.container.dispatchEvent(event);
    }
}

// 初始化日期選擇器
document.addEventListener('DOMContentLoaded', function () {
    const datePicker = new SimpleDateRangePicker('date-range-picker');

    // 監聽日期變化事件
    document.getElementById('date-range-picker').addEventListener('dateRangeChanged', function (e) {
        document.getElementById('date-start-hidden').value = e.detail.startDateStr;
        document.getElementById('date-end-hidden').value = e.detail.endDateStr;
    });
});