<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分錄驗證管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .sidebar {
            width: 200px;
            font-size: 20px;
        }

        .main-content {
            margin-left: 220px;
            padding: 20px;
        }

        .info-box {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .info-box-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            float: left;
            margin-right: 15px;
        }

        .info-box-content {
            overflow: hidden;
        }

        .info-box-text {
            display: block;
            font-size: 14px;
            color: #666;
        }

        .info-box-number {
            display: block;
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .bg-info {
            background-color: #3498db;
        }

        .bg-success {
            background-color: #2ecc71;
        }

        .bg-warning {
            background-color: #f39c12;
        }

        .bg-primary {
            background-color: #9b59b6;
        }

        .table-warning {
            background-color: #fff3cd;
        }

        .badge {
            display: inline-block;
            padding: 0.25em 0.6em;
            font-size: 0.75em;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }

        .badge-info {
            background-color: #17a2b8;
            color: white;
        }

        .badge-success {
            background-color: #28a745;
            color: white;
        }

        .badge-danger {
            background-color: #dc3545;
            color: white;
        }

        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
    </style>
</head>

<body>
    <!-- 側邊欄 -->
    {% include 'sidebar.html' %}

    <!-- 主要內容 -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="fas fa-check-circle"></i> 會計分錄驗證管理
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- 統計資訊 -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info">
                                            <i class="fas fa-list"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">總分錄數</span>
                                            <span class="info-box-number">{{ stats.total_journals }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-success">
                                            <i class="fas fa-check"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">平衡分錄</span>
                                            <span class="info-box-number">{{ stats.balanced_journals }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">不平衡分錄</span>
                                            <span class="info-box-number">{{ stats.unbalanced_journals }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-primary">
                                            <i class="fas fa-percentage"></i>
                                        </span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">平衡率</span>
                                            <span class="info-box-number">{{ "%.1f"|format(stats.balance_rate)
                                                }}%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 驗證結果表格 -->
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>分錄參考號</th>
                                            <th>分錄數量</th>
                                            <th>借方總額</th>
                                            <th>貸方總額</th>
                                            <th>平衡狀態</th>
                                            <th>錯誤/警告</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for result in validation_results %}
                                        <tr class="{% if not result.is_balanced %}table-warning{% endif %}">
                                            <td>
                                                <code>{{ result.journal_reference }}</code>
                                            </td>
                                            <td>
                                                <span class="badge badge-info">{{ result.entry_count }}</span>
                                            </td>
                                            <td class="text-right">
                                                <strong>{{ "{:,}".format(result.debit_total) }}</strong>
                                            </td>
                                            <td class="text-right">
                                                <strong>{{ "{:,}".format(result.credit_total) }}</strong>
                                            </td>
                                            <td>
                                                {% if result.is_balanced %}
                                                <span class="badge badge-success">
                                                    <i class="fas fa-check"></i> 平衡
                                                </span>
                                                {% else %}
                                                <span class="badge badge-danger">
                                                    <i class="fas fa-times"></i> 不平衡
                                                </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if result.errors %}
                                                <span class="badge badge-danger">{{ result.errors|length }} 錯誤</span>
                                                {% endif %}
                                                {% if result.warnings %}
                                                <span class="badge badge-warning">{{ result.warnings|length }} 警告</span>
                                                {% endif %}
                                                {% if not result.errors and not result.warnings %}
                                                <span class="text-muted">無</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('journal_validation.journal_detail', journal_reference=result.journal_reference) }}"
                                                    class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> 詳細
                                                </a>
                                                <button class="btn btn-sm btn-outline-info"
                                                    onclick="validateJournal('{{ result.journal_reference }}')">
                                                    <i class="fas fa-sync"></i> 重新驗證
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            {% if not validation_results %}
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle"></i>
                                目前沒有需要驗證的分錄記錄
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 驗證結果模態框 -->
        <div class="modal fade" id="validationModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">分錄驗證結果</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" id="validationResult">
                        <!-- 驗證結果將在這裡顯示 -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">關閉</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            function validateJournal(journalReference) {
                fetch(`/api/validate_journal/${journalReference}`)
                    .then(response => response.json())
                    .then(data => {
                        let html = `
                <div class="card">
                    <div class="card-header">
                        <h6>分錄參考號: <code>${data.journal_reference}</code></h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>分錄數量:</strong> ${data.entry_count}</p>
                                <p><strong>借方總額:</strong> ${data.debit_total.toLocaleString()}</p>
                                <p><strong>貸方總額:</strong> ${data.credit_total.toLocaleString()}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>平衡狀態:</strong> 
                                    ${data.is_balanced ?
                                '<span class="badge badge-success">平衡</span>' :
                                '<span class="badge badge-danger">不平衡</span>'
                            }
                                </p>
                            </div>
                        </div>
            `;

                        if (data.errors && data.errors.length > 0) {
                            html += `
                    <div class="alert alert-danger">
                        <h6>錯誤:</h6>
                        <ul class="mb-0">
                            ${data.errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                `;
                        }

                        if (data.warnings && data.warnings.length > 0) {
                            html += `
                    <div class="alert alert-warning">
                        <h6>警告:</h6>
                        <ul class="mb-0">
                            ${data.warnings.map(warning => `<li>${warning}</li>`).join('')}
                        </ul>
                    </div>
                `;
                        }

                        html += `
                    </div>
                </div>
            `;

                        document.getElementById('validationResult').innerHTML = html;
                        $('#validationModal').modal('show');
                    })
                    .catch(error => {
                        console.error('驗證失敗:', error);
                        document.getElementById('validationResult').innerHTML = `
                <div class="alert alert-danger">
                    驗證失敗: ${error.message}
                </div>
            `;
                        $('#validationModal').modal('show');
                    });
            }
        </script>
    </div>
</body>

</html>