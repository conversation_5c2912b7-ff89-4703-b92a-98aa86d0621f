"""
批量操作工具
提供高效的批量資料庫操作功能
"""

import logging
import gc
import psutil
from typing import List, Dict, Any, Callable, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
from database import get_db
import time

logger = logging.getLogger(__name__)

class BatchOperationManager:
    """
    批量操作管理器
    提供高效的批量插入、更新、刪除功能
    """
    
    def __init__(self, batch_size: int = 1000):
        """
        初始化批量操作管理器
        
        Args:
            batch_size: 批次大小
        """
        self.batch_size = batch_size
        self.stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'total_time': 0
        }
    
    def bulk_insert(self, model_class: Any, data: List[Dict[str, Any]], 
                   return_ids: bool = False) -> Optional[List[int]]:
        """
        批量插入數據
        
        Args:
            model_class: 模型類
            data: 要插入的數據列表
            return_ids: 是否返回插入的 ID
        
        Returns:
            如果 return_ids=True，返回插入的 ID 列表
        """
        if not data:
            return [] if return_ids else None
        
        start_time = time.time()
        inserted_ids = []
        
        try:
            with get_db() as db:
                # 分批處理
                for i in range(0, len(data), self.batch_size):
                    batch = data[i:i + self.batch_size]
                    
                    if return_ids:
                        # 需要返回 ID 時，使用常規插入
                        objects = [model_class(**item) for item in batch]
                        db.add_all(objects)
                        db.flush()  # 獲取 ID
                        inserted_ids.extend([obj.id for obj in objects])
                    else:
                        # 不需要返回 ID 時，使用更快的 bulk_insert_mappings
                        db.bulk_insert_mappings(model_class, batch)
                    
                    logger.debug(f"Inserted batch {i//self.batch_size + 1}: {len(batch)} records")
                
                db.commit()
                
                # 更新統計
                self.stats['total_operations'] += len(data)
                self.stats['successful_operations'] += len(data)
                self.stats['total_time'] += time.time() - start_time
                
                logger.info(f"Bulk insert completed: {len(data)} records in {time.time() - start_time:.2f}s")
                
                return inserted_ids if return_ids else None
                
        except Exception as e:
            self.stats['failed_operations'] += len(data)
            logger.error(f"Bulk insert failed: {str(e)}")
            raise
    
    def bulk_update(self, model_class: Any, updates: List[Dict[str, Any]],
                   id_field: str = 'id') -> int:
        """
        批量更新數據
        
        Args:
            model_class: 模型類
            updates: 更新數據列表，每個字典必須包含 id_field
            id_field: ID 欄位名稱
        
        Returns:
            更新的記錄數
        """
        if not updates:
            return 0
        
        start_time = time.time()
        total_updated = 0
        
        try:
            with get_db() as db:
                # 分批處理
                for i in range(0, len(updates), self.batch_size):
                    batch = updates[i:i + self.batch_size]
                    
                    # 使用 bulk_update_mappings（需要包含主鍵）
                    db.bulk_update_mappings(model_class, batch)
                    total_updated += len(batch)
                    
                    logger.debug(f"Updated batch {i//self.batch_size + 1}: {len(batch)} records")
                
                db.commit()
                
                # 更新統計
                self.stats['total_operations'] += len(updates)
                self.stats['successful_operations'] += len(updates)
                self.stats['total_time'] += time.time() - start_time
                
                logger.info(f"Bulk update completed: {total_updated} records in {time.time() - start_time:.2f}s")
                
                return total_updated
                
        except Exception as e:
            self.stats['failed_operations'] += len(updates)
            logger.error(f"Bulk update failed: {str(e)}")
            raise
    
    def bulk_delete(self, model_class: Any, ids: List[int]) -> int:
        """
        批量刪除數據
        
        Args:
            model_class: 模型類
            ids: 要刪除的 ID 列表
        
        Returns:
            刪除的記錄數
        """
        if not ids:
            return 0
        
        start_time = time.time()
        total_deleted = 0
        
        try:
            with get_db() as db:
                # 分批處理
                for i in range(0, len(ids), self.batch_size):
                    batch_ids = ids[i:i + self.batch_size]
                    
                    # 使用 IN 查詢批量刪除
                    deleted = db.query(model_class).filter(
                        model_class.id.in_(batch_ids)
                    ).delete(synchronize_session=False)
                    
                    total_deleted += deleted
                    logger.debug(f"Deleted batch {i//self.batch_size + 1}: {deleted} records")
                
                db.commit()
                
                # 更新統計
                self.stats['total_operations'] += len(ids)
                self.stats['successful_operations'] += len(ids)
                self.stats['total_time'] += time.time() - start_time
                
                logger.info(f"Bulk delete completed: {total_deleted} records in {time.time() - start_time:.2f}s")
                
                return total_deleted
                
        except Exception as e:
            self.stats['failed_operations'] += len(ids)
            logger.error(f"Bulk delete failed: {str(e)}")
            raise
    
    def execute_batch_sql(self, sql: str, params_list: List[Dict[str, Any]]) -> int:
        """
        執行批量 SQL 語句
        
        Args:
            sql: SQL 語句模板
            params_list: 參數列表
        
        Returns:
            影響的記錄數
        """
        if not params_list:
            return 0
        
        start_time = time.time()
        total_affected = 0
        
        try:
            with get_db() as db:
                # 分批執行
                for i in range(0, len(params_list), self.batch_size):
                    batch_params = params_list[i:i + self.batch_size]
                    
                    for params in batch_params:
                        result = db.execute(text(sql), params)
                        total_affected += result.rowcount
                    
                    logger.debug(f"Executed batch {i//self.batch_size + 1}: {len(batch_params)} queries")
                
                db.commit()
                
                logger.info(f"Batch SQL completed: {total_affected} records affected in {time.time() - start_time:.2f}s")
                
                return total_affected
                
        except Exception as e:
            logger.error(f"Batch SQL execution failed: {str(e)}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        獲取批量操作統計信息
        
        Returns:
            統計信息字典
        """
        if self.stats['total_operations'] > 0:
            success_rate = (
                self.stats['successful_operations'] / 
                self.stats['total_operations'] * 100
            )
            avg_time = self.stats['total_time'] / self.stats['total_operations']
        else:
            success_rate = 0
            avg_time = 0
        
        return {
            **self.stats,
            'success_rate': round(success_rate, 2),
            'avg_operation_time': round(avg_time, 4)
        }

class TransactionBatchProcessor:
    """
    交易記錄批量處理器
    專門優化交易相關的批量操作
    """
    
    @staticmethod
    def batch_create_transactions(transactions: List[Dict[str, Any]]) -> List[int]:
        """
        批量創建交易記錄
        
        Args:
            transactions: 交易數據列表
        
        Returns:
            創建的交易 ID 列表
        """
        from model import Transaction, Money
        
        batch_manager = BatchOperationManager()
        
        # 準備交易數據
        transaction_ids = []
        money_records = []
        
        with get_db() as db:
            # 批量插入交易
            for trans_data in transactions:
                # 分離 Money 相關數據
                money_data = {
                    'transaction_type': trans_data.pop('transaction_type'),
                    'amount': trans_data.pop('amount'),
                    'account_id': trans_data.pop('account_id'),
                    'date': trans_data.pop('date'),
                    'description': trans_data.get('description', '')
                }
                
                # 創建 Transaction
                trans = Transaction(**trans_data)
                db.add(trans)
                db.flush()  # 獲取 ID
                
                transaction_ids.append(trans.id)
                
                # 準備 Money 記錄
                money_data['transaction_id'] = trans.id
                money_records.append(money_data)
            
            # 批量插入 Money 記錄
            if money_records:
                batch_manager.bulk_insert(Money, money_records)
            
            db.commit()
            
            logger.info(f"Batch created {len(transactions)} transactions")
            
            return transaction_ids
    
    @staticmethod
    def batch_update_balances(account_updates: Dict[int, float]) -> int:
        """
        批量更新帳戶餘額
        
        Args:
            account_updates: {account_id: balance_change} 字典
        
        Returns:
            更新的帳戶數
        """
        if not account_updates:
            return 0
        
        with get_db() as db:
            updated_count = 0
            
            for account_id, balance_change in account_updates.items():
                # 使用原生 SQL 進行原子更新
                result = db.execute(
                    text("""
                        UPDATE accounts 
                        SET balance = balance + :balance_change,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = :account_id
                    """),
                    {
                        'balance_change': balance_change,
                        'account_id': account_id
                    }
                )
                updated_count += result.rowcount
            
            db.commit()
            
            logger.info(f"Batch updated {updated_count} account balances")
            
            return updated_count
    
    @staticmethod
    def batch_calculate_period_totals(
        start_date: str, 
        end_date: str,
        account_ids: List[int] = None
    ) -> Dict[int, Dict[str, float]]:
        """
        批量計算期間總額
        
        Args:
            start_date: 開始日期
            end_date: 結束日期
            account_ids: 帳戶 ID 列表（可選）
        
        Returns:
            {account_id: {'income': 金額, 'expense': 金額}} 字典
        """
        with get_db() as db:
            # 構建查詢
            query = """
                SELECT 
                    account_id,
                    transaction_type,
                    SUM(amount) as total
                FROM money
                WHERE date BETWEEN :start_date AND :end_date
            """
            
            params = {
                'start_date': start_date,
                'end_date': end_date
            }
            
            if account_ids:
                query += " AND account_id IN :account_ids"
                params['account_ids'] = tuple(account_ids)
            
            query += " GROUP BY account_id, transaction_type"
            
            # 執行查詢
            result = db.execute(text(query), params)
            
            # 整理結果
            totals = {}
            for row in result:
                account_id = row[0]
                trans_type = row[1]
                total = row[2]
                
                if account_id not in totals:
                    totals[account_id] = {'income': 0, 'expense': 0}
                
                totals[account_id][trans_type] = float(total)
            
            return totals

# 全局批量操作管理器實例
batch_manager = BatchOperationManager()

def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    將列表分割成指定大小的塊
    
    Args:
        lst: 要分割的列表
        chunk_size: 塊大小
    
    Returns:
        分割後的列表列表
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]