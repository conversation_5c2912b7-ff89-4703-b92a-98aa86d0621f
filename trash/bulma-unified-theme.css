/* 印錢大師 - 統一Bulma主題 */

/* ===== CSS變數定義 ===== */
:root {
  /* 主要色彩 */
  --primary-color: #3273dc;
  --primary-dark: #2366d1;
  --primary-light: #4285f4;
  
  /* 輔助色彩 */
  --success-color: #48c774;
  --warning-color: #ffdd57;
  --danger-color: #f14668;
  --info-color: #3298dc;
  
  /* 灰階色彩 */
  --grey-darker: #363636;
  --grey-dark: #4a4a4a;
  --grey: #7a7a7a;
  --grey-light: #b5b5b5;
  --grey-lighter: #dbdbdb;
  --grey-lightest: #f5f5f5;
  
  /* 背景色彩 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-sidebar: #2d3748;
  --bg-sidebar-hover: #3a4556;
  
  /* 陰影效果 */
  --shadow-light: 0 2px 3px rgba(10, 10, 10, 0.1);
  --shadow-medium: 0 4px 8px rgba(10, 10, 10, 0.1);
  --shadow-heavy: 0 8px 16px rgba(10, 10, 10, 0.15);
  
  /* 邊框半徑 */
  --radius-small: 4px;
  --radius-medium: 6px;
  --radius-large: 8px;
  
  /* 動畫時間 */
  --transition-fast: 0.15s ease;
  --transition-medium: 0.25s ease;
  --transition-slow: 0.35s ease;
}

/* ===== 全域樣式重置 ===== */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--bg-secondary);
  color: var(--grey-darker);
  line-height: 1.6;
}

/* ===== 容器樣式 ===== */
.main-container {
  min-height: 100vh;
  display: flex;
}

.content-wrapper {
  flex: 1;
  padding: 1.5rem;
  background-color: var(--bg-secondary);
}

/* ===== 側邊欄統一樣式 ===== */
.sidebar {
  background-color: var(--bg-sidebar);
  color: white;
  min-height: 100vh;
  width: 250px;
  padding: 0;
  box-shadow: var(--shadow-medium);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  overflow-y: auto;
}

.sidebar-brand {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.sidebar-brand h1 {
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.sidebar-menu {
  list-style: none;
  padding: 1rem 0;
  margin: 0;
}

.sidebar-item {
  padding: 0.75rem 1.25rem;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  border: none;
  background: none;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  width: 100%;
}

.sidebar-item:hover {
  background-color: var(--bg-sidebar-hover);
  color: white;
}

.sidebar-item.is-active {
  background-color: var(--primary-color);
  color: white;
}

.sidebar-icon {
  margin-right: 0.75rem;
  width: 20px;
  text-align: center;
  font-size: 1rem;
}

.expand-icon {
  margin-left: auto;
  transition: transform var(--transition-fast);
}

.sidebar-item:hover .expand-icon {
  transform: rotate(90deg);
}

/* ===== 主內容區域 ===== */
.main-content {
  margin-left: 250px;
  padding: 2rem;
  background-color: var(--bg-secondary);
  min-height: 100vh;
}

/* ===== 卡片樣式統一 ===== */
.card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--grey-lightest);
  transition: box-shadow var(--transition-medium);
}

.card:hover {
  box-shadow: var(--shadow-medium);
}

.card-header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--grey-lightest);
  border-radius: var(--radius-large) var(--radius-large) 0 0;
}

.card-header-title {
  color: var(--grey-darker);
  font-weight: 600;
}

/* ===== 按鈕樣式統一 ===== */
.button {
  border-radius: var(--radius-medium);
  font-weight: 500;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
}

.button.is-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.button.is-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

.button.is-success:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

.button.is-warning:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

.button.is-danger:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

/* ===== 表格樣式統一 ===== */
.table {
  background-color: var(--bg-primary);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.table thead th {
  background-color: var(--primary-color);
  color: white;
  border: none;
  font-weight: 600;
  padding: 1rem;
}

.table tbody tr {
  transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
  background-color: rgba(50, 115, 220, 0.05);
}

.table tbody td {
  padding: 0.75rem 1rem;
  border: none;
  border-bottom: 1px solid var(--grey-lightest);
}

/* 特殊表格樣式 */
.balance-sheet-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--bg-primary);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.balance-sheet-table th,
.balance-sheet-table td {
  border: 1px solid var(--grey-lightest);
  padding: 0.75rem;
  text-align: left;
}

.balance-sheet-table th {
  background-color: var(--grey-lightest);
  font-weight: 600;
  color: var(--grey-darker);
}

.assets-header {
  background-color: var(--primary-color) !important;
  color: white !important;
  text-align: center;
  font-weight: 600;
}

.liabilities-header {
  background-color: var(--warning-color) !important;
  color: var(--grey-darker) !important;
  text-align: center;
  font-weight: 600;
}

.amount-cell {
  text-align: right;
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

.percentage-cell {
  text-align: right;
  font-size: 0.9rem;
  color: var(--grey);
}

/* ===== 表單樣式統一 ===== */
.input, .textarea, .select select {
  border-radius: var(--radius-medium);
  border: 1px solid var(--grey-lighter);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.input:focus, .textarea:focus, .select select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.125em rgba(50, 115, 220, 0.25);
}

.field.has-addons .control .button,
.field.has-addons .control .input,
.field.has-addons .control .select select {
  border-radius: 0;
}

.field.has-addons .control:first-child .button,
.field.has-addons .control:first-child .input,
.field.has-addons .control:first-child .select select {
  border-radius: var(--radius-medium) 0 0 var(--radius-medium);
}

.field.has-addons .control:last-child .button,
.field.has-addons .control:last-child .input,
.field.has-addons .control:last-child .select select {
  border-radius: 0 var(--radius-medium) var(--radius-medium) 0;
}

/* ===== 通知樣式 ===== */
.notification {
  border-radius: var(--radius-large);
  border: none;
  box-shadow: var(--shadow-light);
}

.notification.is-success {
  background-color: var(--success-color);
  color: white;
}

.notification.is-warning {
  background-color: var(--warning-color);
  color: var(--grey-darker);
}

.notification.is-danger {
  background-color: var(--danger-color);
  color: white;
}

.notification.is-info {
  background-color: var(--info-color);
  color: white;
}

/* ===== 標籤樣式 ===== */
.tag {
  border-radius: var(--radius-small);
  font-weight: 500;
}

.tag.is-success {
  background-color: var(--success-color);
  color: white;
}

.tag.is-warning {
  background-color: var(--warning-color);
  color: var(--grey-darker);
}

.tag.is-danger {
  background-color: var(--danger-color);
  color: white;
}

.tag.is-info {
  background-color: var(--info-color);
  color: white;
}

/* ===== 子選單樣式 ===== */
.submenu-container {
  position: absolute;
  left: 250px;
  background-color: var(--bg-primary);
  border: 1px solid var(--grey-lightest);
  box-shadow: var(--shadow-heavy);
  border-radius: var(--radius-large);
  min-width: 700px;
  min-height: 400px;
  max-height: 80vh;
  display: none;
  padding: 1.5rem;
  z-index: 200;
  overflow-y: auto;
  opacity: 0;
  transform: translateX(-10px);
  transition: opacity var(--transition-medium), transform var(--transition-medium);
}

.submenu-container.show {
  opacity: 1;
  transform: translateX(0);
}

.submenu-container::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid var(--bg-primary);
  filter: drop-shadow(-2px 0 2px rgba(0, 0, 0, 0.1));
}

.submenu-close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 24px;
  height: 24px;
  background: var(--grey-lighter);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color var(--transition-fast);
}

.submenu-close-btn:hover {
  background: var(--grey-light);
}

.submenu-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--grey-darker);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-color);
}

.submenu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.submenu-item {
  background: var(--bg-primary);
  border: 1px solid var(--grey-lightest);
  border-radius: var(--radius-medium);
  padding: 1rem;
  text-decoration: none;
  color: var(--grey-darker);
  transition: all var(--transition-fast);
  display: block;
}

.submenu-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-light);
  transform: translateY(-2px);
  color: var(--primary-color);
}

/* ===== 響應式設計 ===== */
@media screen and (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform var(--transition-medium);
  }
  
  .sidebar.is-active {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .submenu-container {
    left: 0;
    right: 0;
    min-width: auto;
    margin: 1rem;
  }
}

@media screen and (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }
  
  .card {
    margin-bottom: 1rem;
  }
  
  .table {
    font-size: 0.875rem;
  }
  
  .balance-sheet-table th,
  .balance-sheet-table td {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
  
  .submenu-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== 動畫效果 ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn var(--transition-medium) ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp var(--transition-slow) ease-out forwards;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft var(--transition-medium) ease-out;
}

/* ===== 工具類別 ===== */
.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.font-mono {
  font-family: 'Courier New', monospace;
}

.font-weight-bold {
  font-weight: 600;
}

.border-radius-large {
  border-radius: var(--radius-large);
}

.shadow-light {
  box-shadow: var(--shadow-light);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.bg-primary {
  background-color: var(--bg-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

/* ===== 特殊組件樣式 ===== */
.summary-card {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  padding: 1.5rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--grey-lightest);
  transition: all var(--transition-medium);
}

.summary-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.summary-card-title {
  font-size: 0.875rem;
  color: var(--grey);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.summary-card-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--grey-darker);
  font-family: 'Courier New', monospace;
}

.chart-container {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  padding: 1.5rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--grey-lightest);
}

/* ===== 頁面標題樣式 ===== */
.page-header {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--grey-lightest);
}

.page-title {
  color: var(--grey-darker);
  font-weight: 600;
  margin: 0;
}

.breadcrumb {
  background: transparent;
  padding: 0;
  margin-top: 0.5rem;
}

.breadcrumb a {
  color: var(--primary-color);
  text-decoration: none;
}

.breadcrumb a:hover {
  color: var(--primary-dark);
}

/* ===== 載入狀態 ===== */
.is-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.6;
}

.is-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--grey-lighter);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== 向後兼容的舊類名 ===== */
.modern-card {
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  padding: 1.5rem;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--grey-lightest);
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.modern-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  opacity: 0;
  transition: opacity var(--transition-medium);
}

.modern-card:hover::before {
  opacity: 1;
}

.modern-table {
  background-color: var(--bg-primary);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  border: none;
  width: 100%;
  border-collapse: collapse;
}

.modern-table thead th {
  background-color: var(--primary-color);
  color: white;
  border: none;
  font-weight: 600;
  padding: 1rem;
}

.modern-table tbody tr {
  transition: background-color var(--transition-fast);
}

.modern-table tbody tr:hover {
  background-color: rgba(50, 115, 220, 0.05);
  transform: scale(1.01);
}

.modern-table tbody td {
  padding: 0.75rem 1rem;
  border: none;
  border-bottom: 1px solid var(--grey-lightest);
}

/* 現代化側邊欄 */
.modern-sidebar {
  background: var(--bg-sidebar);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--glass-border);
  box-shadow: var(--shadow-medium);
}

/* 漸層按鈕 */
.btn-gradient-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: none;
  color: white;
  border-radius: var(--radius-medium);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-gradient-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn-gradient-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  color: white;
}

.btn-gradient-primary:hover::before {
  left: 100%;
}

.btn-gradient-success {
  background: linear-gradient(135deg, var(--success-color) 0%, #38c172 100%);
}

.btn-gradient-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #e53e3e 100%);
}

.btn-gradient-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #f6ad55 100%);
}