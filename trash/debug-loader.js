/**
 * JavaScript 載入診斷工具
 * Debug Loader for JavaScript Files
 */

(function () {
    'use strict';

    console.log('🔍 JavaScript 載入診斷工具啟動');

    // 檢查文件是否存在的函數
    function checkFileExists(url) {
        return fetch(url, { method: 'HEAD' })
            .then(response => response.ok)
            .catch(() => false);
    }

    // 診斷結果
    const diagnostics = {
        files: [],
        globals: [],
        errors: []
    };

    // 要檢查的文件列表
    const jsFiles = [
        'static/js/sidebar-menu.js',
        'static/js/modal-manager.js',
        'static/js/modal-manager-simple.js',
        'static/js/bulma-enhancements.js',
        'static/js/app-init.js'
    ];

    // 要檢查的全局變數/函數
    const globalChecks = [
        { name: 'closeSubmenu', type: 'function', source: 'sidebar-menu.js' },
        { name: 'ModalManager', type: 'function', source: 'modal-manager.js' },
        { name: 'modalManager', type: 'object', source: 'modal-manager.js' },
        { name: 'simpleModalManager', type: 'object', source: 'modal-manager-simple.js' },
        { name: 'showModal', type: 'function', source: 'modal-manager.js' },
        { name: 'showConfirm', type: 'function', source: 'modal-manager.js' },
        { name: 'closeModal', type: 'function', source: 'modal-manager.js' },
        { name: 'BulmaEnhancer', type: 'object', source: 'bulma-enhancements.js' },
        { name: 'App', type: 'object', source: 'app-init.js' }
    ];

    // 檢查文件存在性
    async function checkFiles() {
        console.log('📁 檢查 JavaScript 文件存在性...');

        for (const file of jsFiles) {
            try {
                const exists = await checkFileExists(file);
                diagnostics.files.push({
                    file,
                    exists,
                    status: exists ? '✅ 存在' : '❌ 不存在'
                });
                console.log(`${exists ? '✅' : '❌'} ${file}`);
            } catch (error) {
                diagnostics.files.push({
                    file,
                    exists: false,
                    status: '❌ 檢查失敗',
                    error: error.message
                });
                console.error(`❌ ${file} - 檢查失敗:`, error.message);
            }
        }
    }

    // 檢查全局變數/函數
    function checkGlobals() {
        console.log('🌐 檢查全局變數和函數...');

        globalChecks.forEach(check => {
            const exists = typeof window[check.name] === check.type;
            const actualType = typeof window[check.name];

            diagnostics.globals.push({
                name: check.name,
                expectedType: check.type,
                actualType,
                exists,
                source: check.source,
                status: exists ? '✅ 可用' : `❌ 不可用 (${actualType})`
            });

            console.log(`${exists ? '✅' : '❌'} ${check.name} (${check.source}): 期望 ${check.type}, 實際 ${actualType}`);
        });
    }

    // 生成診斷報告
    function generateReport() {
        console.log('\n📊 JavaScript 載入診斷報告');
        console.log('='.repeat(50));

        console.log('\n📁 文件檢查結果:');
        diagnostics.files.forEach(file => {
            console.log(`  ${file.status} ${file.file}`);
            if (file.error) {
                console.log(`    錯誤: ${file.error}`);
            }
        });

        console.log('\n🌐 全局變數檢查結果:');
        diagnostics.globals.forEach(global => {
            console.log(`  ${global.status} ${global.name} (來源: ${global.source})`);
        });

        // 統計
        const filesOk = diagnostics.files.filter(f => f.exists).length;
        const globalsOk = diagnostics.globals.filter(g => g.exists).length;

        console.log('\n📈 統計結果:');
        console.log(`  文件: ${filesOk}/${diagnostics.files.length} 可用`);
        console.log(`  全局變數: ${globalsOk}/${diagnostics.globals.length} 可用`);

        // 建議
        console.log('\n💡 建議:');
        if (filesOk < diagnostics.files.length) {
            console.log('  - 檢查文件路徑是否正確');
            console.log('  - 確認 Flask 靜態文件配置');
        }
        if (globalsOk < diagnostics.globals.length) {
            console.log('  - 檢查 JavaScript 文件載入順序');
            console.log('  - 查看瀏覽器控制台是否有語法錯誤');
        }

        return diagnostics;
    }

    // 主診斷函數
    async function runDiagnostics() {
        try {
            await checkFiles();
            checkGlobals();
            const report = generateReport();

            // 將報告暴露到全局
            window.jsLoadDiagnostics = report;

            console.log('\n✅ 診斷完成！可以通過 window.jsLoadDiagnostics 查看詳細報告');

        } catch (error) {
            console.error('❌ 診斷過程中發生錯誤:', error);
        }
    }

    // 等待 DOM 載入後執行診斷
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runDiagnostics);
    } else {
        // 延遲執行，確保其他腳本有時間載入
        setTimeout(runDiagnostics, 1000);
    }

    // 暴露診斷函數到全局
    window.runJSDiagnostics = runDiagnostics;

})();