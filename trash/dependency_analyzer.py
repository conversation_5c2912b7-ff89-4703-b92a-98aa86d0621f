#!/usr/bin/env python3
"""
會計系統 Python 檔案依賴分析工具
分析所有 Python 檔案的依賴關係，找出未被使用的檔案
"""

import os
import ast
import re
from pathlib import Path
from collections import defaultdict
from typing import Set, Dict, List, Tuple

class DependencyAnalyzer:
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.python_files = []
        self.imports = defaultdict(set)
        self.used_files = set()
        self.unused_files = set()
        
    def find_python_files(self) -> List[Path]:
        """找到所有 Python 檔案（排除 trash 目錄和虛擬環境）"""
        python_files = []
        exclude_dirs = ['trash', '.venv', 'venv', '__pycache__', '.pytest_cache', '.git']
        
        for root, dirs, files in os.walk(self.root_path):
            # 排除不需要的目錄
            root_parts = Path(root).parts
            if any(exclude_dir in root_parts for exclude_dir in exclude_dirs):
                continue
            
            for file in files:
                if file.endswith('.py') and not file.startswith('.'):
                    python_files.append(Path(root) / file)
        return python_files
    
    def extract_imports(self, file_path: Path) -> Set[str]:
        """從 Python 檔案中提取所有 import"""
        imports = set()
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析 AST
            try:
                tree = ast.parse(content)
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.add(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        if node.module:
                            imports.add(node.module)
                            for alias in node.names:
                                imports.add(f"{node.module}.{alias.name}")
            except SyntaxError:
                # AST 解析失敗，使用正則表達式
                import_patterns = [
                    r'from\s+([a-zA-Z_][a-zA-Z0-9_\.]*)\s+import',
                    r'import\s+([a-zA-Z_][a-zA-Z0-9_\.]*)'
                ]
                for pattern in import_patterns:
                    matches = re.findall(pattern, content)
                    imports.update(matches)
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
        
        return imports
    
    def normalize_path(self, file_path: Path) -> str:
        """將檔案路徑標準化為模組名稱"""
        relative_path = file_path.relative_to(self.root_path)
        # 移除 .py 擴展名
        if relative_path.name == '__init__.py':
            # __init__.py 檔案代表包
            return str(relative_path.parent).replace('/', '.')
        else:
            return str(relative_path.with_suffix('')).replace('/', '.')
    
    def analyze_dependencies(self):
        """分析所有檔案的依賴關係"""
        self.python_files = self.find_python_files()
        print(f"Found {len(self.python_files)} Python files")
        
        # 建立檔案到模組名稱的映射
        file_to_module = {}
        module_to_file = {}
        
        for file_path in self.python_files:
            module_name = self.normalize_path(file_path)
            file_to_module[file_path] = module_name
            module_to_file[module_name] = file_path
        
        # 提取每個檔案的 import
        for file_path in self.python_files:
            imports = self.extract_imports(file_path)
            self.imports[file_path] = imports
        
        # 分析使用情況
        for file_path, imports in self.imports.items():
            for import_name in imports:
                # 檢查是否為本專案內的模組
                if import_name in module_to_file:
                    self.used_files.add(module_to_file[import_name])
                # 檢查子模組
                for module_name in module_to_file:
                    if import_name.startswith(module_name + '.'):
                        self.used_files.add(module_to_file[module_name])
        
        # 從 main.py 開始的特殊處理
        main_py = self.root_path / 'main.py'
        if main_py.exists():
            self.used_files.add(main_py)
            # main.py 引入的所有模組都被標記為使用
            main_imports = self.imports.get(main_py, set())
            for import_name in main_imports:
                if import_name in module_to_file:
                    self.used_files.add(module_to_file[import_name])
        
        # 找出未使用的檔案
        self.unused_files = set(self.python_files) - self.used_files
        
        return file_to_module, module_to_file
    
    def generate_report(self) -> str:
        """生成分析報告"""
        report = []
        report.append("=== 會計系統 Python 檔案依賴分析報告 ===\n")
        
        report.append(f"總檔案數: {len(self.python_files)}")
        report.append(f"已使用檔案數: {len(self.used_files)}")
        report.append(f"疑似未使用檔案數: {len(self.unused_files)}\n")
        
        if self.unused_files:
            report.append("=== 疑似未使用的檔案 ===")
            for file_path in sorted(self.unused_files):
                relative_path = file_path.relative_to(self.root_path)
                report.append(f"  {relative_path}")
        
        report.append("\n=== 按目錄分析 ===")
        
        # 按目錄分組
        by_directory = defaultdict(lambda: {'used': [], 'unused': []})
        
        for file_path in self.python_files:
            relative_path = file_path.relative_to(self.root_path)
            directory = str(relative_path.parent) if relative_path.parent != Path('.') else 'root'
            
            if file_path in self.used_files:
                by_directory[directory]['used'].append(relative_path.name)
            else:
                by_directory[directory]['unused'].append(relative_path.name)
        
        for directory in sorted(by_directory.keys()):
            info = by_directory[directory]
            report.append(f"\n目錄: {directory}")
            report.append(f"  已使用: {len(info['used'])} 個檔案")
            if info['unused']:
                report.append(f"  未使用: {len(info['unused'])} 個檔案")
                for filename in sorted(info['unused']):
                    report.append(f"    - {filename}")
        
        return '\n'.join(report)

if __name__ == '__main__':
    analyzer = DependencyAnalyzer('/Users/<USER>/Library/CloudStorage/Dropbox/Code/python/accounting')
    analyzer.analyze_dependencies()
    
    report = analyzer.generate_report()
    
    # 輸出到控制台
    print(report)
    
    # 保存到檔案
    with open('/Users/<USER>/Library/CloudStorage/Dropbox/Code/python/accounting/dependency_analysis_detailed.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n詳細報告已保存到: dependency_analysis_detailed.txt")