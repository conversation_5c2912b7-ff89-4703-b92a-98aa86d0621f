#!/usr/bin/env python3
"""
清除開帳記錄的腳本
"""
import os
import sys
from datetime import date

# 添加項目根目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def clear_opening_records():
    """清除所有開帳記錄"""
    try:
        print("=== 清除開帳記錄 ===")
        
        from database import get_db
        from model import Money
        
        with get_db() as db:
            # 查找所有開帳相關記錄
            opening_records = db.query(Money).filter(
                Money.name.like('%開帳%')
            ).all()
            
            print(f"找到 {len(opening_records)} 筆開帳記錄:")
            
            for record in opening_records:
                print(f"- ID: {record.id}")
                print(f"  名稱: {record.name}")
                print(f"  日期: {record.a_time}")
                print(f"  金額: {record.total}")
                print(f"  類型: {record.money_type}")
                print(f"  科目: {record.subject_code}")
                print()
            
            if opening_records:
                confirm = input(f"確定要刪除這 {len(opening_records)} 筆記錄嗎？(y/N): ")
                
                if confirm.lower() == 'y':
                    # 刪除所有開帳記錄
                    for record in opening_records:
                        db.delete(record)
                    
                    db.commit()
                    print(f"✅ 已成功刪除 {len(opening_records)} 筆開帳記錄")
                else:
                    print("❌ 取消刪除操作")
            else:
                print("ℹ️  沒有找到開帳記錄")
                
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    clear_opening_records()
