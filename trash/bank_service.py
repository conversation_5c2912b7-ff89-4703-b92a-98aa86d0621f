#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
銀行服務模組
提供銀行資料查詢功能
"""

from database import get_db
from model import BankHeadOffice, BankBranch

class BankService:
    """銀行服務類別"""
    
    @staticmethod
    def get_all_head_offices():
        """取得所有總行資料"""
        with get_db() as db:
            head_offices = db.query(BankHeadOffice).order_by(BankHeadOffice.code).all()
            return [
                {
                    'code': ho.code,
                    'name': ho.name
                }
                for ho in head_offices
            ]
    
    @staticmethod
    def get_head_office_by_code(code):
        """根據代碼取得總行資料"""
        with get_db() as db:
            head_office = db.query(BankHeadOffice).filter_by(code=code).first()
            if head_office:
                return {
                    'code': head_office.code,
                    'name': head_office.name
                }
            return None
    
    @staticmethod
    def get_branches_by_head_office(head_office_code):
        """根據總行代碼取得所有分行"""
        with get_db() as db:
            branches = db.query(BankBranch).filter_by(
                head_office_code=head_office_code
            ).order_by(BankBranch.code).all()
            
            return [
                {
                    'code': branch.code,
                    'name': branch.name,
                    'head_office_code': branch.head_office_code
                }
                for branch in branches
            ]
    
    @staticmethod
    def get_branch_by_code(code):
        """根據代碼取得分行資料"""
        with get_db() as db:
            branch = db.query(BankBranch).filter_by(code=code).first()
            if branch:
                head_office = db.query(BankHeadOffice).filter_by(
                    code=branch.head_office_code
                ).first()
                
                return {
                    'code': branch.code,
                    'name': branch.name,
                    'head_office_code': branch.head_office_code,
                    'head_office_name': head_office.name if head_office else '未知總行'
                }
            return None
    
    @staticmethod
    def search_banks(keyword):
        """搜尋銀行（總行或分行）"""
        with get_db() as db:
            # 搜尋總行
            head_offices = db.query(BankHeadOffice).filter(
                BankHeadOffice.name.contains(keyword) |
                BankHeadOffice.code.contains(keyword)
            ).all()
            
            # 搜尋分行
            branches = db.query(BankBranch).filter(
                BankBranch.name.contains(keyword) |
                BankBranch.code.contains(keyword)
            ).all()
            
            results = []
            
            # 加入總行結果
            for ho in head_offices:
                results.append({
                    'type': 'head_office',
                    'code': ho.code,
                    'name': ho.name,
                    'display_name': f"{ho.code} - {ho.name} (總行)"
                })
            
            # 加入分行結果
            for branch in branches:
                head_office = db.query(BankHeadOffice).filter_by(
                    code=branch.head_office_code
                ).first()
                head_office_name = head_office.name if head_office else '未知總行'
                
                results.append({
                    'type': 'branch',
                    'code': branch.code,
                    'name': branch.name,
                    'head_office_code': branch.head_office_code,
                    'head_office_name': head_office_name,
                    'display_name': f"{branch.code} - {branch.name} ({head_office_name})"
                })
            
            return results
    
    @staticmethod
    def get_bank_statistics():
        """取得銀行資料統計"""
        with get_db() as db:
            total_head_offices = db.query(BankHeadOffice).count()
            total_branches = db.query(BankBranch).count()
            
            # 統計各總行的分行數量
            head_office_stats = []
            head_offices = db.query(BankHeadOffice).all()
            
            for ho in head_offices:
                branch_count = db.query(BankBranch).filter_by(
                    head_office_code=ho.code
                ).count()
                
                head_office_stats.append({
                    'code': ho.code,
                    'name': ho.name,
                    'branch_count': branch_count
                })
            
            return {
                'total_head_offices': total_head_offices,
                'total_branches': total_branches,
                'head_office_stats': sorted(head_office_stats, key=lambda x: x['branch_count'], reverse=True)
            } 