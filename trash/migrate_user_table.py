#!/usr/bin/env python3
"""
遷移用戶資料表，添加權限系統需要的欄位
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db
from sqlalchemy import text

def migrate_user_table():
    """遷移用戶資料表"""
    print("開始遷移用戶資料表...")
    
    with get_db() as db:
        # 檢查現有的 users 表結構
        result = db.execute(text("PRAGMA table_info(users)")).fetchall()
        existing_columns = [row[1] for row in result]
        print(f"現有欄位: {existing_columns}")
        
        # 需要添加的欄位
        new_columns = [
            ("password_hash", "VARCHAR(255)"),
            ("full_name", "VARCHAR(100)"),
            ("is_active", "BOOLEAN DEFAULT 1"),
            ("last_login", "DATETIME"),
            ("created_at", "DATETIME"),
            ("updated_at", "DATETIME")
        ]
        
        # 添加缺少的欄位
        for column_name, column_type in new_columns:
            if column_name not in existing_columns:
                try:
                    db.execute(text(f"ALTER TABLE users ADD COLUMN {column_name} {column_type}"))
                    print(f"  添加欄位: {column_name}")
                except Exception as e:
                    print(f"  添加欄位 {column_name} 失敗: {e}")
        
        db.commit()
        print("用戶資料表遷移完成")

def main():
    """主函數"""
    try:
        migrate_user_table()
        print("\n✅ 資料表遷移成功！")
        print("現在可以執行 python init_auth_system.py 來初始化權限系統")
    except Exception as e:
        print(f"❌ 遷移失敗: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()