#!/usr/bin/env python3
"""
依賴關係分析工具
從 main.py 開始追蹤所有被使用的 Python 檔案
"""

import os
import re
import ast
import sys
from pathlib import Path
from collections import defaultdict, deque

class DependencyAnalyzer:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.analyzed_files = set()
        self.dependencies = defaultdict(list)
        self.imports = defaultdict(list)
        self.used_files = set()
        self.all_python_files = set()
        
        # 收集所有Python檔案
        self._collect_all_python_files()
        
    def _collect_all_python_files(self):
        """收集專案中所有的Python檔案"""
        for root, dirs, files in os.walk(self.project_root):
            # 跳過一些不需要的目錄
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', '.pytest_cache', 'venv', '.venv']]
            
            for file in files:
                if file.endswith('.py'):
                    full_path = Path(root) / file
                    self.all_python_files.add(full_path.relative_to(self.project_root))
    
    def _parse_imports(self, file_path):
        """解析Python檔案中的import語句"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用AST解析
            try:
                tree = ast.parse(content)
                imports = []
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(('import', alias.name, None))
                    
                    elif isinstance(node, ast.ImportFrom):
                        module = node.module if node.module else ''
                        for alias in node.names:
                            imports.append(('from', module, alias.name))
                
                return imports
            except SyntaxError:
                # 如果AST解析失敗，使用正則表達式
                return self._parse_imports_regex(content)
                
        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            return []
    
    def _parse_imports_regex(self, content):
        """使用正則表達式解析import語句（備用方案）"""
        imports = []
        
        # 匹配 from ... import ...
        from_pattern = r'from\s+([\w.]+)\s+import\s+([\w\s,*]+)'
        for match in re.finditer(from_pattern, content):
            module = match.group(1)
            names = [name.strip() for name in match.group(2).split(',')]
            for name in names:
                imports.append(('from', module, name))
        
        # 匹配 import ...
        import_pattern = r'import\s+([\w.]+)'
        for match in re.finditer(import_pattern, content):
            module = match.group(1)
            imports.append(('import', module, None))
        
        return imports
    
    def _resolve_local_import(self, module_path, current_file):
        """解析本地模組的實際檔案路徑"""
        current_dir = current_file.parent
        
        # 處理相對導入
        if module_path.startswith('.'):
            # 相對導入
            level = 0
            for char in module_path:
                if char == '.':
                    level += 1
                else:
                    break
            
            module_name = module_path[level:]
            target_dir = current_dir
            
            for _ in range(level - 1):
                target_dir = target_dir.parent
            
            if module_name:
                possible_paths = [
                    target_dir / f"{module_name.replace('.', '/')}.py",
                    target_dir / module_name.replace('.', '/') / "__init__.py"
                ]
            else:
                possible_paths = [target_dir / "__init__.py"]
        
        else:
            # 絕對導入
            possible_paths = [
                self.project_root / f"{module_path.replace('.', '/')}.py",
                self.project_root / module_path.replace('.', '/') / "__init__.py"
            ]
        
        for path in possible_paths:
            if path.exists():
                return path.relative_to(self.project_root)
        
        return None
    
    def analyze_file(self, file_path):
        """分析單個檔案的依賴關係"""
        if file_path in self.analyzed_files:
            return
        
        self.analyzed_files.add(file_path)
        full_path = self.project_root / file_path
        
        if not full_path.exists():
            return
        
        imports = self._parse_imports(full_path)
        
        for import_type, module, name in imports:
            if import_type == 'from' and module:
                # 檢查是否為本地模組
                resolved_path = self._resolve_local_import(module, file_path.parent if isinstance(file_path, Path) else Path(file_path).parent)
                
                if resolved_path:
                    self.dependencies[file_path].append(resolved_path)
                    self.imports[file_path].append(f"from {module} import {name}")
                    self.used_files.add(resolved_path)
                    
                    # 遞迴分析依賴
                    self.analyze_file(resolved_path)
                else:
                    # 外部模組
                    self.imports[file_path].append(f"from {module} import {name}")
            
            elif import_type == 'import':
                # 檢查是否為本地模組
                resolved_path = self._resolve_local_import(module, file_path.parent if isinstance(file_path, Path) else Path(file_path).parent)
                
                if resolved_path:
                    self.dependencies[file_path].append(resolved_path)
                    self.imports[file_path].append(f"import {module}")
                    self.used_files.add(resolved_path)
                    
                    # 遞迴分析依賴
                    self.analyze_file(resolved_path)
                else:
                    # 外部模組
                    self.imports[file_path].append(f"import {module}")
    
    def get_unused_files(self):
        """獲取未被使用的檔案"""
        return self.all_python_files - self.used_files - {Path('main.py')}
    
    def generate_report(self):
        """生成分析報告"""
        report = []
        report.append("=" * 80)
        report.append("Python 專案依賴關係分析報告")
        report.append("=" * 80)
        report.append("")
        
        # 核心檔案
        report.append("1. 核心檔案 (main.py 和直接依賴)")
        report.append("-" * 40)
        core_files = [Path('main.py')]
        if Path('main.py') in self.dependencies:
            core_files.extend(self.dependencies[Path('main.py')])
        
        for file_path in sorted(core_files):
            report.append(f"  ✓ {file_path}")
        report.append("")
        
        # 依類型分類的已使用檔案
        categories = {
            'routes': [],
            'utils': [],
            'services': [],
            'models': [],
            'config': [],
            'others': []
        }
        
        for file_path in sorted(self.used_files):
            if file_path.parts and file_path.parts[0] in categories:
                categories[file_path.parts[0]].append(file_path)
            else:
                categories['others'].append(file_path)
        
        for category, files in categories.items():
            if files:
                report.append(f"2. {category.upper()} 檔案 (共 {len(files)} 個)")
                report.append("-" * 40)
                for file_path in files:
                    report.append(f"  ✓ {file_path}")
                report.append("")
        
        # 未使用的檔案
        unused_files = self.get_unused_files()
        report.append(f"3. 可能未使用的檔案 (共 {len(unused_files)} 個)")
        report.append("-" * 40)
        
        # 按目錄分組
        unused_by_dir = defaultdict(list)
        for file_path in unused_files:
            if file_path.parts:
                unused_by_dir[file_path.parts[0]].append(file_path)
            else:
                unused_by_dir['root'].append(file_path)
        
        for directory, files in sorted(unused_by_dir.items()):
            if files:
                report.append(f"  {directory}/ 目錄:")
                for file_path in sorted(files):
                    report.append(f"    ? {file_path}")
        report.append("")
        
        # 統計摘要
        report.append("4. 統計摘要")
        report.append("-" * 40)
        report.append(f"  總 Python 檔案數: {len(self.all_python_files)}")
        report.append(f"  已分析檔案數: {len(self.analyzed_files)}")
        report.append(f"  實際使用檔案數: {len(self.used_files) + 1}  # +1 for main.py")
        report.append(f"  可能未使用檔案數: {len(unused_files)}")
        report.append(f"  使用率: {((len(self.used_files) + 1) / len(self.all_python_files)) * 100:.1f}%")
        
        return "\n".join(report)

def main():
    project_root = "/Users/<USER>/Library/CloudStorage/Dropbox/Code/python/accounting"
    analyzer = DependencyAnalyzer(project_root)
    
    # 從 main.py 開始分析
    main_file = Path('main.py')
    analyzer.analyze_file(main_file)
    analyzer.used_files.add(main_file)  # 確保 main.py 被標記為已使用
    
    # 生成報告
    report = analyzer.generate_report()
    print(report)
    
    # 也保存到檔案
    with open(os.path.join(project_root, 'dependency_analysis_report.txt'), 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n報告已保存至: {project_root}/dependency_analysis_report.txt")

if __name__ == '__main__':
    main()