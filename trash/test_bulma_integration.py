#!/usr/bin/env python3
"""
測試 Bulma 集成的簡單腳本
"""

import os
import sys

def test_files_exist():
    """測試必要的文件是否存在"""
    required_files = [
        'static/css/unified-modern-theme.css',
        'templates/sidebar.html',
        'templates/admin/base.html',
        'templates/example_bulma_page.html',
        'static/js/sidebar-menu.js',
        'BULMA_INTEGRATION.md'
    ]
    
    print("🔍 檢查必要文件...")
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少 {len(missing_files)} 個文件")
        return False
    else:
        print(f"\n✅ 所有 {len(required_files)} 個文件都存在")
        return True

def test_css_content():
    """測試 CSS 文件內容"""
    print("\n🎨 檢查 CSS 內容...")
    
    css_file = 'static/css/unified-modern-theme.css'
    if not os.path.exists(css_file):
        print("❌ CSS 文件不存在")
        return False
    
    with open(css_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_patterns = [
        '--bulma-primary',
        '--primary-gradient',
        '.custom-sidebar',
        '.submenu-container',
        '@media screen and (max-width: 1024px)'
    ]
    
    missing_patterns = []
    for pattern in required_patterns:
        if pattern in content:
            print(f"✅ 找到: {pattern}")
        else:
            print(f"❌ 缺少: {pattern}")
            missing_patterns.append(pattern)
    
    if missing_patterns:
        print(f"\n❌ CSS 文件缺少 {len(missing_patterns)} 個必要模式")
        return False
    else:
        print(f"\n✅ CSS 文件包含所有必要模式")
        return True

def test_html_templates():
    """測試 HTML 模板"""
    print("\n📄 檢查 HTML 模板...")
    
    # 檢查基礎模板
    base_template = 'templates/admin/base.html'
    if os.path.exists(base_template):
        with open(base_template, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'bulma@0.9.4' in content and 'unified-modern-theme.css' in content:
            print("✅ 基礎模板正確引用 Bulma 和自定義 CSS")
        else:
            print("❌ 基礎模板缺少必要的 CSS 引用")
            return False
    
    # 檢查側邊欄模板
    sidebar_template = 'templates/sidebar.html'
    if os.path.exists(sidebar_template):
        with open(sidebar_template, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'custom-sidebar' in content and 'menu-list' in content:
            print("✅ 側邊欄模板使用正確的 Bulma 類別")
        else:
            print("❌ 側邊欄模板缺少必要的 Bulma 類別")
            return False
    
    print("✅ HTML 模板檢查通過")
    return True

def test_javascript():
    """測試 JavaScript 文件"""
    print("\n🔧 檢查 JavaScript...")
    
    js_file = 'static/js/sidebar-menu.js'
    if not os.path.exists(js_file):
        print("❌ JavaScript 文件不存在")
        return False
    
    with open(js_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    required_functions = [
        'sidebar-menu-item',
        'custom-sidebar',
        'mobile-menu-toggle'
    ]
    
    missing_functions = []
    for func in required_functions:
        if func in content:
            print(f"✅ 找到: {func}")
        else:
            print(f"❌ 缺少: {func}")
            missing_functions.append(func)
    
    if missing_functions:
        print(f"\n❌ JavaScript 文件缺少 {len(missing_functions)} 個必要功能")
        return False
    else:
        print(f"\n✅ JavaScript 文件包含所有必要功能")
        return True

def test_route_import():
    """測試路由導入"""
    print("\n🛣️  測試路由導入...")
    
    try:
        sys.path.append(os.path.dirname(__file__))
        from routes.main import main_bp
        print("✅ 成功導入 main_bp")
        
        # 檢查是否有 example-bulma 路由
        # Blueprint 沒有 url_map，我們只檢查函數是否存在
        if hasattr(main_bp, 'deferred_functions'):
            print("✅ Blueprint 結構正常")
        else:
            print("❌ Blueprint 結構異常")
            return False
        
        # 檢查 example_bulma 函數是否存在
        try:
            from routes.main import example_bulma
            print("✅ 找到 example_bulma 函數")
        except ImportError:
            print("❌ 缺少 example_bulma 函數")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 導入失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試 Bulma 集成...")
    print("=" * 50)
    
    tests = [
        ("文件存在性", test_files_exist),
        ("CSS 內容", test_css_content),
        ("HTML 模板", test_html_templates),
        ("JavaScript", test_javascript),
        ("路由導入", test_route_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 測試: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 測試通過")
            else:
                print(f"❌ {test_name} 測試失敗")
        except Exception as e:
            print(f"❌ {test_name} 測試出錯: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！Bulma 集成成功！")
        print("\n📝 下一步:")
        print("1. 啟動 Flask 應用")
        print("2. 訪問 /example-bulma 查看示例頁面")
        print("3. 測試響應式設計和移動端功能")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查上述錯誤")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)