"""
新的收支記錄路由 - 使用重構後的 Transaction 和 JournalEntry 模型
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash
from sqlalchemy.orm import sessionmaker
from datetime import datetime

from model import Account, PaymentIdentity, AccountSubject, engine, Transaction, JournalEntry
from services.transaction_service import TransactionService
from services.journal_validator import JournalValidationError
from utils.audit_helper import get_current_user
from data.menu_data import menu

Session = sessionmaker(bind=engine)
new_income_expense_bp = Blueprint('new_income_expense', __name__)


def parse_date(date_str):
    """解析日期字符串"""
    if not date_str:
        return None
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return None


def safe_int(value, default=0):
    """安全的整數轉換"""
    if not value:
        return default
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


@new_income_expense_bp.route('/new_expense_record', methods=['GET', 'POST'])
def new_expense_record():
    """新的支出記錄頁面"""
    db = Session()
    try:
        if request.method == 'POST':
            form = request.form
            
            # 準備交易資料
            transaction_data = {
                'transaction_date': parse_date(form.get('a_time')),
                'description': form.get('name'),
                'amount': safe_int(form.get('total')),
                'tax_amount': safe_int(form.get('tax')),
                'extra_fee': safe_int(form.get('extra_fee')),
                'expense_subject_code': form.get('subject_code'),
                'account_id': safe_int(form.get('account_id')) if form.get('account_id') else None,
                'payment_identity_id': safe_int(form.get('payment_identity_id')) if form.get('payment_identity_id') else None,
                'department_id': safe_int(form.get('department_id')) if form.get('department_id') else None,
                'project_id': safe_int(form.get('project_id')) if form.get('project_id') else None,
                'is_paper': bool(form.get('is_paper')),
                'invoice_number': form.get('number'),
                'tax_type': form.get('tax_type'),
                'buyer_tax_id': form.get('buyer_tax_id'),
                'seller_tax_id': form.get('seller_tax_id'),
                'invoice_date': form.get('invoice_date'),
                'is_paid': form.get('is_paid') == '1',
                'should_paid_date': parse_date(form.get('should_paid_date')),
                'paid_date': parse_date(form.get('paid_date')),
                'note': form.get('note'),
                'tags': form.get('tag'),
                'created_by': get_current_user()
            }
            
            try:
                # 調試：打印交易資料
                print(f"Transaction data: {transaction_data}")

                # 使用交易服務創建支出記錄
                service = TransactionService(db)
                transaction = service.create_expense_transaction(transaction_data)

                db.commit()
                flash('支出記錄創建成功！', 'success')
                return redirect(url_for('new_income_expense.new_expense_list'))

            except JournalValidationError as e:
                db.rollback()
                print(f"Validation error: {str(e)}")
                flash(f'分錄驗證失敗：{str(e)}', 'error')
            except Exception as e:
                db.rollback()
                print(f"General error: {str(e)}")
                import traceback
                traceback.print_exc()
                flash(f'創建失敗：{str(e)}', 'error')
        
        # GET 請求：顯示表單
        accounts = db.query(Account).filter_by(is_deleted=False).all()
        subjects = db.query(AccountSubject).filter(
            AccountSubject.code.like('5%') | AccountSubject.code.like('6%')
        ).all()  # 成本和費用科目
        payment_identities = db.query(PaymentIdentity).filter_by(is_active=True).all()
        
        return render_template('new_expense_record.html',
                             sidebar_items=menu,
                             selected='新支出記錄',
                             accounts=accounts,
                             subjects=subjects,
                             payment_identities=payment_identities)
    
    finally:
        db.close()


@new_income_expense_bp.route('/new_income_record', methods=['GET', 'POST'])
def new_income_record():
    """新的收入記錄頁面"""
    db = Session()
    try:
        if request.method == 'POST':
            form = request.form
            
            # 準備交易資料
            transaction_data = {
                'transaction_date': parse_date(form.get('a_time')),
                'description': form.get('name'),
                'amount': safe_int(form.get('total')),
                'tax_amount': safe_int(form.get('tax')),
                'extra_fee': safe_int(form.get('extra_fee')),
                'income_subject_code': form.get('subject_code'),
                'account_id': safe_int(form.get('account_id')) if form.get('account_id') else None,
                'payment_identity_id': safe_int(form.get('payment_identity_id')) if form.get('payment_identity_id') else None,
                'department_id': safe_int(form.get('department_id')) if form.get('department_id') else None,
                'project_id': safe_int(form.get('project_id')) if form.get('project_id') else None,
                'is_paper': bool(form.get('is_paper')),
                'invoice_number': form.get('number'),
                'tax_type': form.get('tax_type'),
                'buyer_tax_id': form.get('buyer_tax_id'),
                'seller_tax_id': form.get('seller_tax_id'),
                'invoice_date': form.get('invoice_date'),
                'is_paid': form.get('is_paid') == '1',
                'should_paid_date': parse_date(form.get('should_paid_date')),
                'paid_date': parse_date(form.get('paid_date')),
                'note': form.get('note'),
                'tags': form.get('tag'),
                'created_by': get_current_user()
            }
            
            try:
                # 使用交易服務創建收入記錄
                service = TransactionService(db)
                transaction = service.create_income_transaction(transaction_data)
                
                db.commit()
                flash('收入記錄創建成功！', 'success')
                return redirect(url_for('new_income_expense.new_income_list'))
                
            except JournalValidationError as e:
                db.rollback()
                flash(f'分錄驗證失敗：{str(e)}', 'error')
            except Exception as e:
                db.rollback()
                flash(f'創建失敗：{str(e)}', 'error')
        
        # GET 請求：顯示表單
        accounts = db.query(Account).filter_by(is_deleted=False).all()
        subjects = db.query(AccountSubject).filter(
            AccountSubject.code.like('4%')
        ).all()  # 收入科目
        payment_identities = db.query(PaymentIdentity).filter_by(is_active=True).all()
        
        return render_template('new_income_record.html',
                             sidebar_items=menu,
                             selected='新收入記錄',
                             accounts=accounts,
                             subjects=subjects,
                             payment_identities=payment_identities)
    
    finally:
        db.close()


@new_income_expense_bp.route('/new_expense_list')
def new_expense_list():
    """新的支出列表頁面"""
    db = Session()
    try:
        # 獲取所有交易及其主要分錄
        transactions = db.query(Transaction).join(JournalEntry).filter(
            JournalEntry.debit_amount > 0,  # 借方分錄
            JournalEntry.subject_code.like('5%') | JournalEntry.subject_code.like('6%'),  # 成本/費用科目
            JournalEntry.entry_type == 'primary'  # 只顯示主要分錄
        ).order_by(Transaction.transaction_date.desc()).all()
        
        return render_template('new_expense_list.html',
                             sidebar_items=menu,
                             selected='新支出列表',
                             transactions=transactions)
    
    finally:
        db.close()


@new_income_expense_bp.route('/new_income_list')
def new_income_list():
    """新的收入列表頁面"""
    db = Session()
    try:
        # 獲取所有收入交易
        transactions = db.query(Transaction).join(JournalEntry).filter(
            JournalEntry.credit_amount > 0,  # 貸方分錄
            JournalEntry.subject_code.like('4%'),  # 收入科目
            JournalEntry.entry_type == 'primary'  # 只顯示主要分錄
        ).order_by(Transaction.transaction_date.desc()).all()
        
        return render_template('new_income_list.html',
                             sidebar_items=menu,
                             selected='新收入列表',
                             transactions=transactions)
    
    finally:
        db.close()


@new_income_expense_bp.route('/transaction_detail/<int:transaction_id>')
def transaction_detail(transaction_id):
    """交易詳細頁面"""
    db = Session()
    try:
        service = TransactionService(db)
        transaction = service.get_transaction_with_entries(transaction_id)
        
        if not transaction:
            flash('找不到指定的交易記錄', 'error')
            return redirect(url_for('new_income_expense.new_expense_list'))
        
        # 區分主要分錄和平衡分錄
        primary_entries = []
        balance_entries = []
        
        for entry in transaction.journal_entries:
            if entry.entry_type == 'primary':
                primary_entries.append(entry)
            else:
                balance_entries.append(entry)
        
        return render_template('transaction_detail.html',
                             sidebar_items=menu,
                             selected='交易詳細',
                             transaction=transaction,
                             primary_entries=primary_entries,
                             balance_entries=balance_entries)
    
    finally:
        db.close()
