"""Add soft delete fields to PaymentIdentity, Department, Project, and PaymentIdentityType

Revision ID: 2401f6dab479
Revises: 84cba4485354
Create Date: 2025-07-22 15:29:58.005902

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2401f6dab479'
down_revision: Union[str, Sequence[str], None] = '84cba4485354'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_sessions')
    op.drop_table('user_roles')
    op.drop_table('role_permissions')
    op.drop_table('_alembic_tmp_money')
    op.drop_table('roles')
    op.drop_table('permissions')
    with op.batch_alter_table('account', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_account_subject_code'), ['subject_code'], unique=False)

    with op.batch_alter_table('department', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_deleted', sa.Boolean(), nullable=True, comment='是否已刪除'))
        batch_op.add_column(sa.Column('deleted_at', sa.DateTime(), nullable=True, comment='刪除時間'))
        batch_op.add_column(sa.Column('deleted_by', sa.String(length=100), nullable=True, comment='刪除者'))
        batch_op.create_index(batch_op.f('ix_department_is_deleted'), ['is_deleted'], unique=False)

    with op.batch_alter_table('journal_entries', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_journal_entry_type'))
        batch_op.create_index(batch_op.f('ix_journal_entries_entry_type'), ['entry_type'], unique=False)

    with op.batch_alter_table('money', schema=None) as batch_op:
        batch_op.alter_column('entry_side',
               existing_type=sa.TEXT(),
               type_=sa.String(length=10),
               existing_nullable=True)
        batch_op.alter_column('journal_reference',
               existing_type=sa.TEXT(),
               type_=sa.String(length=50),
               existing_nullable=True)
        batch_op.drop_index(batch_op.f('idx_money_journal_reference'))
        batch_op.create_index(batch_op.f('ix_money_journal_reference'), ['journal_reference'], unique=False)

    with op.batch_alter_table('payment_identity', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_deleted', sa.Boolean(), nullable=True, comment='是否已刪除'))
        batch_op.add_column(sa.Column('deleted_at', sa.DateTime(), nullable=True, comment='刪除時間'))
        batch_op.add_column(sa.Column('deleted_by', sa.String(length=100), nullable=True, comment='刪除者'))
        batch_op.create_index(batch_op.f('ix_payment_identity_is_deleted'), ['is_deleted'], unique=False)

    with op.batch_alter_table('payment_identity_types', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_deleted', sa.Boolean(), nullable=True, comment='是否已刪除'))
        batch_op.add_column(sa.Column('deleted_at', sa.DateTime(), nullable=True, comment='刪除時間'))
        batch_op.add_column(sa.Column('deleted_by', sa.String(length=100), nullable=True, comment='刪除者'))
        batch_op.create_index(batch_op.f('ix_payment_identity_types_is_deleted'), ['is_deleted'], unique=False)

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_deleted', sa.Boolean(), nullable=True, comment='是否已刪除'))
        batch_op.add_column(sa.Column('deleted_at', sa.DateTime(), nullable=True, comment='刪除時間'))
        batch_op.add_column(sa.Column('deleted_by', sa.String(length=100), nullable=True, comment='刪除者'))
        batch_op.create_index(batch_op.f('ix_project_is_deleted'), ['is_deleted'], unique=False)

    with op.batch_alter_table('transactions', schema=None) as batch_op:
        batch_op.alter_column('transaction_type',
               existing_type=sa.VARCHAR(length=20),
               nullable=False,
               existing_server_default=sa.text("'expense'"))
        batch_op.drop_index(batch_op.f('ix_transaction_type'))
        batch_op.create_index(batch_op.f('ix_transactions_transaction_type'), ['transaction_type'], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('transactions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_transactions_transaction_type'))
        batch_op.create_index(batch_op.f('ix_transaction_type'), ['transaction_type'], unique=False)
        batch_op.alter_column('transaction_type',
               existing_type=sa.VARCHAR(length=20),
               nullable=True,
               existing_server_default=sa.text("'expense'"))

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_project_is_deleted'))
        batch_op.drop_column('deleted_by')
        batch_op.drop_column('deleted_at')
        batch_op.drop_column('is_deleted')

    with op.batch_alter_table('payment_identity_types', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_payment_identity_types_is_deleted'))
        batch_op.drop_column('deleted_by')
        batch_op.drop_column('deleted_at')
        batch_op.drop_column('is_deleted')

    with op.batch_alter_table('payment_identity', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_payment_identity_is_deleted'))
        batch_op.drop_column('deleted_by')
        batch_op.drop_column('deleted_at')
        batch_op.drop_column('is_deleted')

    with op.batch_alter_table('money', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_money_journal_reference'))
        batch_op.create_index(batch_op.f('idx_money_journal_reference'), ['journal_reference'], unique=False)
        batch_op.alter_column('journal_reference',
               existing_type=sa.String(length=50),
               type_=sa.TEXT(),
               existing_nullable=True)
        batch_op.alter_column('entry_side',
               existing_type=sa.String(length=10),
               type_=sa.TEXT(),
               existing_nullable=True)

    with op.batch_alter_table('journal_entries', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_journal_entries_entry_type'))
        batch_op.create_index(batch_op.f('ix_journal_entry_type'), ['entry_type'], unique=False)

    with op.batch_alter_table('department', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_department_is_deleted'))
        batch_op.drop_column('deleted_by')
        batch_op.drop_column('deleted_at')
        batch_op.drop_column('is_deleted')

    with op.batch_alter_table('account', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_account_subject_code'))

    op.create_table('permissions',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('display_name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('module', sa.VARCHAR(length=50), nullable=False),
    sa.Column('action', sa.VARCHAR(length=50), nullable=False),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('roles',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=50), nullable=False),
    sa.Column('display_name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('_alembic_tmp_money',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('money_type', sa.VARCHAR(length=50), nullable=True),
    sa.Column('a_time', sa.DATE(), nullable=True),
    sa.Column('name', sa.VARCHAR(length=100), nullable=True),
    sa.Column('total', sa.INTEGER(), nullable=True),
    sa.Column('extra_fee', sa.INTEGER(), nullable=True),
    sa.Column('subject_code', sa.VARCHAR(length=50), nullable=True),
    sa.Column('account_id', sa.INTEGER(), nullable=True),
    sa.Column('payment_identity_id', sa.INTEGER(), nullable=True),
    sa.Column('is_paper', sa.BOOLEAN(), nullable=True),
    sa.Column('number', sa.VARCHAR(length=50), nullable=True),
    sa.Column('tax_type', sa.VARCHAR(length=50), nullable=True),
    sa.Column('buyer_tax_id', sa.VARCHAR(length=50), nullable=True),
    sa.Column('seller_tax_id', sa.VARCHAR(length=50), nullable=True),
    sa.Column('date', sa.VARCHAR(length=50), nullable=True),
    sa.Column('is_paid', sa.BOOLEAN(), nullable=True),
    sa.Column('should_paid_date', sa.DATETIME(), nullable=True),
    sa.Column('paid_date', sa.DATETIME(), nullable=True),
    sa.Column('note', sa.TEXT(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.Column('created_by', sa.VARCHAR(length=100), nullable=True),
    sa.Column('updated_by', sa.VARCHAR(length=100), nullable=True),
    sa.Column('version', sa.INTEGER(), nullable=True),
    sa.Column('is_deleted', sa.BOOLEAN(), nullable=True),
    sa.Column('deleted_at', sa.DATETIME(), nullable=True),
    sa.Column('deleted_by', sa.VARCHAR(length=100), nullable=True),
    sa.Column('department_id', sa.INTEGER(), nullable=True),
    sa.Column('project_id', sa.INTEGER(), nullable=True),
    sa.Column('tags', sa.VARCHAR(length=200), nullable=True),
    sa.Column('image_path', sa.VARCHAR(length=300), nullable=True),
    sa.Column('tax', sa.INTEGER(), nullable=True),
    sa.Column('entry_side', sa.VARCHAR(length=10), nullable=True),
    sa.Column('journal_reference', sa.VARCHAR(length=50), nullable=True),
    sa.CheckConstraint('should_paid_date IS NULL OR paid_date IS NULL OR paid_date >= should_paid_date', name=op.f('check_payment_dates')),
    sa.CheckConstraint('tax >= 0', name=op.f('check_tax_non_negative')),
    sa.ForeignKeyConstraint(['account_id'], ['account.id'], ),
    sa.ForeignKeyConstraint(['department_id'], ['department.id'], ),
    sa.ForeignKeyConstraint(['payment_identity_id'], ['payment_identity.id'], ),
    sa.ForeignKeyConstraint(['project_id'], ['project.id'], ),
    sa.ForeignKeyConstraint(['subject_code'], ['account_subject.code'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('role_permissions',
    sa.Column('role_id', sa.INTEGER(), nullable=False),
    sa.Column('permission_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.PrimaryKeyConstraint('role_id', 'permission_id')
    )
    op.create_table('user_roles',
    sa.Column('user_id', sa.INTEGER(), nullable=False),
    sa.Column('role_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'role_id')
    )
    op.create_table('user_sessions',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('user_id', sa.INTEGER(), nullable=False),
    sa.Column('session_token', sa.VARCHAR(length=255), nullable=False),
    sa.Column('expires_at', sa.DATETIME(), nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('last_accessed', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('session_token')
    )
    # ### end Alembic commands ###
