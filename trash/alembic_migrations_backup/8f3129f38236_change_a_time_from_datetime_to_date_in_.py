"""Change a_time from DateTime to Date in Money table

Revision ID: 8f3129f38236
Revises: 
Create Date: 2025-07-07 23:02:46.786637

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8f3129f38236'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    with op.batch_alter_table('money', schema=None) as batch_op:
        batch_op.alter_column('a_time',
               existing_type=sa.DATETIME(),
               type_=sa.Date(),
               existing_nullable=True)


def downgrade() -> None:
    """Downgrade schema."""
    with op.batch_alter_table('money', schema=None) as batch_op:
        batch_op.alter_column('a_time',
               existing_type=sa.Date(),
               type_=sa.DATETIME(),
               existing_nullable=True)
