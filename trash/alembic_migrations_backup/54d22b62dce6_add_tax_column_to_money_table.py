"""Add tax column to money table

Revision ID: 54d22b62dce6
Revises: cca36410de1e
Create Date: 2025-07-17 22:13:57.103084

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '54d22b62dce6'
down_revision: Union[str, Sequence[str], None] = 'cca36410de1e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 只新增 tax 欄位到 money 資料表
    with op.batch_alter_table('money', schema=None) as batch_op:
        batch_op.add_column(sa.Column('tax', sa.Integer(), nullable=True, default=0, comment='營業稅'))
        batch_op.create_check_constraint('check_tax_non_negative', 'tax >= 0')


def downgrade() -> None:
    """Downgrade schema."""
    # 移除 tax 欄位和相關約束
    with op.batch_alter_table('money', schema=None) as batch_op:
        batch_op.drop_constraint('check_tax_non_negative', type_='check')
        batch_op.drop_column('tax')
