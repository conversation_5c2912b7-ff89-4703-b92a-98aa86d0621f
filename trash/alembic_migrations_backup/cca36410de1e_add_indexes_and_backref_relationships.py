"""Add indexes and backref relationships

Revision ID: cca36410de1e
Revises: 8f3129f38236
Create Date: 2025-07-08 20:38:39.852018

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cca36410de1e'
down_revision: Union[str, Sequence[str], None] = '8f3129f38236'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('account', schema=None) as batch_op:
        batch_op.create_foreign_key('fk_account_subject_code', 'account_subject', ['subject_code'], ['code'])

    with op.batch_alter_table('account_subject', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_account_subject_code'), ['code'], unique=True)
        batch_op.create_index(batch_op.f('ix_account_subject_is_expandable'), ['is_expandable'], unique=False)

    with op.batch_alter_table('bank_branches', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('idx_bank_branches_code'))
        batch_op.drop_index(batch_op.f('idx_bank_branches_head_office_code'))
        batch_op.drop_index(batch_op.f('idx_bank_branches_name'))
        batch_op.create_index(batch_op.f('ix_bank_branches_head_office_code'), ['head_office_code'], unique=False)

    with op.batch_alter_table('bank_head_offices', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('idx_bank_head_offices_code'))

    with op.batch_alter_table('money', schema=None) as batch_op:
        batch_op.create_index('ix_money_account_date', ['account_id', 'a_time'], unique=False)
        batch_op.create_index('ix_money_date_type', ['a_time', 'money_type'], unique=False)
        batch_op.create_index(batch_op.f('ix_money_department_id'), ['department_id'], unique=False)
        batch_op.create_index('ix_money_dept_project', ['department_id', 'project_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_money_project_id'), ['project_id'], unique=False)
        batch_op.create_index('ix_money_subject_date', ['subject_code', 'a_time'], unique=False)
        batch_op.create_foreign_key('fk_money_project_id', 'project', ['project_id'], ['id'])
        batch_op.create_foreign_key('fk_money_department_id', 'department', ['department_id'], ['id'])

    with op.batch_alter_table('payment_identity', schema=None) as batch_op:
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.alter_column('code',
               existing_type=sa.VARCHAR(length=20),
               nullable=False)

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('idx_users_username'))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('idx_users_username'), ['username'], unique=1)

    with op.batch_alter_table('project', schema=None) as batch_op:
        batch_op.alter_column('code',
               existing_type=sa.VARCHAR(length=20),
               nullable=True)

    with op.batch_alter_table('payment_identity', schema=None) as batch_op:
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)

    with op.batch_alter_table('money', schema=None) as batch_op:
        batch_op.drop_constraint('fk_money_project_id', type_='foreignkey')
        batch_op.drop_constraint('fk_money_department_id', type_='foreignkey')
        batch_op.drop_index('ix_money_subject_date')
        batch_op.drop_index(batch_op.f('ix_money_project_id'))
        batch_op.drop_index('ix_money_dept_project')
        batch_op.drop_index(batch_op.f('ix_money_department_id'))
        batch_op.drop_index('ix_money_date_type')
        batch_op.drop_index('ix_money_account_date')

    with op.batch_alter_table('bank_head_offices', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('idx_bank_head_offices_code'), ['code'], unique=False)

    with op.batch_alter_table('bank_branches', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_bank_branches_head_office_code'))
        batch_op.create_index(batch_op.f('idx_bank_branches_name'), ['name'], unique=False)
        batch_op.create_index(batch_op.f('idx_bank_branches_head_office_code'), ['head_office_code'], unique=False)
        batch_op.create_index(batch_op.f('idx_bank_branches_code'), ['code'], unique=False)

    with op.batch_alter_table('account_subject', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_account_subject_is_expandable'))
        batch_op.drop_index(batch_op.f('ix_account_subject_code'))

    with op.batch_alter_table('account', schema=None) as batch_op:
        batch_op.drop_constraint('fk_account_subject_code', type_='foreignkey')

    # ### end Alembic commands ###
