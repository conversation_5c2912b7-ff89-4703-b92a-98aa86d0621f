"""Add payment identity type model

Revision ID: 84cba4485354
Revises: 54d22b62dce6
Create Date: 2025-07-19 14:35:06.135542

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '84cba4485354'
down_revision: Union[str, Sequence[str], None] = '54d22b62dce6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 移除刪除表的操作，因為這些表可能不存在
    # op.drop_table('roles')
    # op.drop_table('role_permissions')
    # op.drop_table('user_sessions')
    # op.drop_table('permissions')
    # op.drop_table('user_roles')
    # 註釋掉創建已存在索引的操作
    # with op.batch_alter_table('account', schema=None) as batch_op:
    #     batch_op.create_index(batch_op.f('ix_account_subject_code'), ['subject_code'], unique=False)

    # 註釋掉刪除不存在索引的操作
    # with op.batch_alter_table('journal_entries', schema=None) as batch_op:
    #     batch_op.drop_index(batch_op.f('idx_journal_entries_subject'))
    #     batch_op.drop_index(batch_op.f('idx_journal_entries_transaction'))

    # 註釋掉修改 money 表的操作
    # with op.batch_alter_table('money', schema=None) as batch_op:
    #     batch_op.alter_column('entry_side',
    #            existing_type=sa.TEXT(),
    #            type_=sa.String(length=10),
    #            existing_nullable=True)
    #     batch_op.alter_column('journal_reference',
    #            existing_type=sa.TEXT(),
    #            type_=sa.String(length=50),
    #            existing_nullable=True)
    #     batch_op.drop_index(batch_op.f('idx_money_journal_reference'))
    #     batch_op.create_index(batch_op.f('ix_money_journal_reference'), ['journal_reference'], unique=False)

    with op.batch_alter_table('payment_identity', schema=None) as batch_op:
        batch_op.add_column(sa.Column('type_id', sa.Integer(), nullable=True, comment='對象類別ID'))
        batch_op.create_index(batch_op.f('ix_payment_identity_type_id'), ['type_id'], unique=False)
        batch_op.create_index('ix_payment_type_id', ['type_id'], unique=False)
        batch_op.create_foreign_key('fk_payment_identity_type_id', 'payment_identity_types', ['type_id'], ['id'])

    with op.batch_alter_table('transactions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('idx_transactions_account'))
        batch_op.drop_index(batch_op.f('idx_transactions_date'))
        batch_op.drop_index(batch_op.f('idx_transactions_invoice_number'))
        batch_op.drop_index(batch_op.f('idx_transactions_payment_identity'))

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_users_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_email'), ['email'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_username'), ['username'], unique=True)

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_username'))
        batch_op.drop_index(batch_op.f('ix_users_email'))
        batch_op.drop_index(batch_op.f('ix_users_created_at'))

    with op.batch_alter_table('transactions', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('idx_transactions_payment_identity'), ['payment_identity_id'], unique=False)
        batch_op.create_index(batch_op.f('idx_transactions_invoice_number'), ['invoice_number'], unique=False)
        batch_op.create_index(batch_op.f('idx_transactions_date'), ['transaction_date'], unique=False)
        batch_op.create_index(batch_op.f('idx_transactions_account'), ['account_id'], unique=False)

    with op.batch_alter_table('payment_identity', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_index('ix_payment_type_id')
        batch_op.drop_index(batch_op.f('ix_payment_identity_type_id'))
        batch_op.drop_column('type_id')

    with op.batch_alter_table('money', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_money_journal_reference'))
        batch_op.create_index(batch_op.f('idx_money_journal_reference'), ['journal_reference'], unique=False)
        batch_op.alter_column('journal_reference',
               existing_type=sa.String(length=50),
               type_=sa.TEXT(),
               existing_nullable=True)
        batch_op.alter_column('entry_side',
               existing_type=sa.String(length=10),
               type_=sa.TEXT(),
               existing_nullable=True)

    with op.batch_alter_table('journal_entries', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('idx_journal_entries_transaction'), ['transaction_id'], unique=False)
        batch_op.create_index(batch_op.f('idx_journal_entries_subject'), ['subject_code'], unique=False)

    with op.batch_alter_table('account', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_account_subject_code'))

    op.create_table('user_roles',
    sa.Column('user_id', sa.INTEGER(), nullable=False),
    sa.Column('role_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'role_id')
    )
    op.create_table('permissions',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('display_name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('module', sa.VARCHAR(length=50), nullable=False),
    sa.Column('action', sa.VARCHAR(length=50), nullable=False),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('user_sessions',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('user_id', sa.INTEGER(), nullable=False),
    sa.Column('session_token', sa.VARCHAR(length=255), nullable=False),
    sa.Column('expires_at', sa.DATETIME(), nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('last_accessed', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('session_token')
    )
    op.create_table('role_permissions',
    sa.Column('role_id', sa.INTEGER(), nullable=False),
    sa.Column('permission_id', sa.INTEGER(), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.PrimaryKeyConstraint('role_id', 'permission_id')
    )
    op.create_table('roles',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=50), nullable=False),
    sa.Column('display_name', sa.VARCHAR(length=100), nullable=False),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    # ### end Alembic commands ###
