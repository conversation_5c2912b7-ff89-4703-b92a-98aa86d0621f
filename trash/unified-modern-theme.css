/* 印錢大師 - 基於 Bulma 的現代化主題 CSS */

/* ===== Bulma 變數覆蓋 ===== */
:root {
    /* Bulma 主色彩覆蓋 */
    --bulma-primary: #667eea;
    --bulma-primary-invert: #ffffff;
    --bulma-success: #48c774;
    --bulma-warning: #ffdd57;
    --bulma-danger: #f14668;
    --bulma-info: #3298dc;

    /* 自定義色彩系統 */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #48c774 0%, #38a169 100%);
    --warning-gradient: linear-gradient(135deg, #ffdd57 0%, #f6ad55 100%);
    --danger-gradient: linear-gradient(135deg, #f14668 0%, #e53e3e 100%);
    --info-gradient: linear-gradient(135deg, #3298dc 0%, #2b77e6 100%);

    /* 背景色彩 */
    --bg-sidebar: linear-gradient(180deg, #2d3748 0%, #1a202c 100%);
    --bg-sidebar-hover: rgba(255, 255, 255, 0.1);
    --bg-glass: rgba(255, 255, 255, 0.25);

    /* 陰影效果 */
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* 動畫時間 */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.35s cubic-bezier(0.4, 0, 0.2, 1);

    /* 字體 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* ===== Bulma 全域樣式擴展 ===== */
body {
    font-family: var(--font-family) !important;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Bulma 組件增強 */
.has-background-light {
    background-color: #f8fafc !important;
}

/* ===== 自定義側邊欄樣式 (基於 Bulma) ===== */
.custom-sidebar {
    background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%) !important;
    color: white !important;
    min-height: 100vh;
    width: 280px;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
    overflow-y: auto;
    backdrop-filter: blur(20px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.custom-sidebar .menu-list a {
    color: rgba(255, 255, 255, 0.8) !important;
    border-radius: 8px;
    margin-bottom: 4px;
    padding: 12px 16px;
    transition: all var(--transition-fast);
    position: relative;
    display: flex;
    align-items: center;
}

.custom-sidebar .menu-list a * {
    color: inherit !important;
}

.custom-sidebar .menu-list a:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    transform: translateX(4px);
}

.custom-sidebar .menu-list a:hover * {
    color: white !important;
}

.custom-sidebar .menu-list a.is-active {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

.custom-sidebar .menu-list a.is-active * {
    color: white !important;
}

.custom-sidebar .menu-list a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform var(--transition-fast);
    border-radius: 0 4px 4px 0;
}

.custom-sidebar .menu-list a:hover::before,
.custom-sidebar .menu-list a.is-active::before {
    transform: scaleY(1);
}

.custom-sidebar .expand-icon {
    transition: transform var(--transition-fast);
}

.custom-sidebar .sidebar-menu-item:hover .expand-icon {
    transform: rotate(90deg);
}

/* 子選單容器樣式 - 強制右側顯示 */
.submenu-container {
    position: fixed !important;
    left: 280px !important;
    top: 0 !important;
    min-width: 800px !important;
    min-height: 500px !important;
    max-height: 80vh !important;
    display: none !important;
    z-index: 9999 !important;
    overflow-y: auto !important;
    opacity: 0 !important;
    transform: translateX(-20px) !important;
    transition: all var(--transition-normal) !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    background-color: white !important;
    border: 1px solid #e5e7eb !important;
    border-radius: 12px !important;
    padding: 2rem !important;
}

.submenu-container.show {
    opacity: 1;
    transform: translateX(0);
}

.submenu-container::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 30px;
    width: 0;
    height: 0;
    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;
    border-right: 12px solid white;
    filter: drop-shadow(-2px 0 4px rgba(0, 0, 0, 0.1));
}

.submenu-close-btn {
    position: absolute !important;
    top: 1rem !important;
    right: 1rem !important;
    z-index: 10;
}

/* Bulma 按鈕增強 */
.button.is-light:hover {
    background-color: #f5f5f5;
    border-color: #dbdbdb;
    color: #363636;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Bulma 主色彩按鈕增強 */
.button.is-primary {
    background: var(--primary-gradient);
    border: none;
}

.button.is-primary:hover {
    background: var(--primary-gradient);
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(102, 126, 234, 0.3);
}

.button.is-success {
    background: var(--success-gradient);
    border: none;
}

.button.is-success:hover {
    background: var(--success-gradient);
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(72, 199, 116, 0.3);
}

.button.is-warning {
    background: var(--warning-gradient);
    border: none;
}

.button.is-warning:hover {
    background: var(--warning-gradient);
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(255, 221, 87, 0.3);
}

.button.is-danger {
    background: var(--danger-gradient);
    border: none;
}

.button.is-danger:hover {
    background: var(--danger-gradient);
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(241, 70, 104, 0.3);
}

.button.is-info {
    background: var(--info-gradient);
    border: none;
}

.button.is-info:hover {
    background: var(--info-gradient);
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(50, 152, 220, 0.3);
}

/* ===== 側邊欄統一樣式 ===== */
.sidebar {
    background: var(--bg-sidebar);
    color: white;
    min-height: 100vh;
    width: 280px;
    padding: 0;
    box-shadow: var(--shadow-xl);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
    overflow-y: auto;
    backdrop-filter: blur(20px);
}

.sidebar-brand {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
}

.sidebar-brand h1 {
    color: white;
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-menu {
    list-style: none;
    padding: 1.5rem 0;
    margin: 0;
}

.sidebar-item {
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    background: none;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    width: 100%;
    font-size: 0.95rem;
    font-weight: 500;
    position: relative;
}

.sidebar-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform var(--transition-fast);
}

.sidebar-item:hover {
    background: var(--bg-sidebar-hover);
    color: white;
    transform: translateX(4px);
}

.sidebar-item:hover::before {
    transform: scaleY(1);
}

.sidebar-item.is-active {
    background: var(--bg-sidebar-hover);
    color: white;
}

.sidebar-item.is-active::before {
    transform: scaleY(1);
}

.sidebar-icon {
    margin-right: 1rem;
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

.expand-icon {
    margin-left: auto;
    transition: transform var(--transition-fast);
    font-size: 0.875rem;
}

.sidebar-item:hover .expand-icon {
    transform: rotate(90deg);
}

/* ===== 主內容區域 ===== */
.main-content {
    margin-left: 280px;
    padding: 2rem;
    background: var(--bg-secondary);
    min-height: 100vh;
    transition: margin-left var(--transition-normal);
}

/* ===== 頁面標題區域 ===== */
.page-header {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--grey-lightest);
}

.page-title {
    color: var(--grey-darker);
    font-weight: 700;
    margin: 0;
    font-size: 2rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    color: var(--grey);
    font-size: 1.125rem;
    margin-top: 0.5rem;
    font-weight: 400;
}

/* ===== 卡片樣式統一 ===== */
.card,
.modern-card {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card::before,
.modern-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.card:hover,
.modern-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.card:hover::before,
.modern-card:hover::before {
    opacity: 1;
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    border-bottom: none;
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    padding: 1.5rem;
}

.card-header-title {
    color: white;
    font-weight: 600;
    font-size: 1.25rem;
}

.card-content {
    padding: 1.5rem;
}

/* ===== 按鈕樣式統一 ===== */
.button {
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: all var(--transition-fast);
    border: 1px solid transparent;
    font-family: var(--font-family);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.button:hover::before {
    left: 100%;
}

.button.is-primary {
    background: var(--primary-gradient);
    border-color: transparent;
    color: white;
}

.button.is-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.button.is-success {
    background: var(--success-gradient);
    border-color: transparent;
    color: white;
}

.button.is-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.button.is-warning {
    background: var(--warning-gradient);
    border-color: transparent;
    color: var(--grey-darker);
}

.button.is-warning:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--grey-darker);
}

.button.is-danger {
    background: var(--danger-gradient);
    border-color: transparent;
    color: white;
}

.button.is-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.button.is-info {
    background: var(--info-gradient);
    border-color: transparent;
    color: white;
}

.button.is-info:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.button.is-light {
    background: var(--bg-primary);
    border-color: var(--grey-lighter);
    color: var(--grey-darker);
}

.button.is-light:hover {
    background: var(--grey-lightest);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* ===== 表格樣式統一 ===== */
.table,
.modern-table,
.balance-sheet-table,
.income-statement-table {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 100%;
    border-collapse: collapse;
    backdrop-filter: blur(20px);
}

.table thead th,
.modern-table thead th,
.balance-sheet-table th,
.income-statement-table th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1.25rem 1rem;
    text-align: left;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table tbody tr,
.modern-table tbody tr,
.balance-sheet-table tr,
.income-statement-table tr {
    transition: all var(--transition-fast);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table tbody tr:hover,
.modern-table tbody tr:hover,
.balance-sheet-table tr:hover,
.income-statement-table tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table tbody td,
.modern-table tbody td,
.balance-sheet-table td,
.income-statement-table td {
    padding: 1rem;
    border: none;
    vertical-align: middle;
}

.table tbody tr:last-child,
.modern-table tbody tr:last-child,
.balance-sheet-table tr:last-child,
.income-statement-table tr:last-child {
    border-bottom: none;
}

/* 特殊表格樣式 */
.assets-header {
    background: var(--primary-gradient) !important;
    color: white !important;
    text-align: center;
    font-weight: 600;
}

.liabilities-header {
    background: var(--warning-gradient) !important;
    color: var(--grey-darker) !important;
    text-align: center;
    font-weight: 600;
}

.amount-cell {
    text-align: right;
    font-family: var(--font-mono);
    font-weight: 600;
    font-size: 0.9rem;
}

.percentage-cell {
    text-align: right;
    font-size: 0.875rem;
    color: var(--grey);
    font-family: var(--font-mono);
}

.positive-amount {
    color: var(--success-color);
    font-weight: 700;
}

.negative-amount {
    color: var(--danger-color);
    font-weight: 700;
}

/* ===== 表單樣式統一 ===== */
.input,
.textarea,
.select select {
    border-radius: var(--radius-md);
    border: 2px solid var(--grey-lighter);
    transition: all var(--transition-fast);
    padding: 0.75rem 1rem;
    font-family: var(--font-family);
    font-size: 0.875rem;
    background: var(--bg-primary);
    color: var(--grey-darker);
}

.input:focus,
.textarea:focus,
.select select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.field.has-addons .control .button,
.field.has-addons .control .input,
.field.has-addons .control .select select {
    border-radius: 0;
}

.field.has-addons .control:first-child .button,
.field.has-addons .control:first-child .input,
.field.has-addons .control:first-child .select select {
    border-radius: var(--radius-md) 0 0 var(--radius-md);
}

.field.has-addons .control:last-child .button,
.field.has-addons .control:last-child .input,
.field.has-addons .control:last-child .select select {
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.label {
    color: var(--grey-darker);
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

/* ===== 通知樣式 ===== */
.notification {
    border-radius: var(--radius-lg);
    border: none;
    box-shadow: var(--shadow-md);
    padding: 1.25rem;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
}

.notification.is-success {
    background: linear-gradient(135deg, rgba(72, 199, 116, 0.1) 0%, rgba(56, 161, 105, 0.1) 100%);
    color: var(--success-color);
    border: 1px solid rgba(72, 199, 116, 0.2);
}

.notification.is-warning {
    background: linear-gradient(135deg, rgba(255, 221, 87, 0.1) 0%, rgba(246, 173, 85, 0.1) 100%);
    color: var(--grey-darker);
    border: 1px solid rgba(255, 221, 87, 0.2);
}

.notification.is-danger {
    background: linear-gradient(135deg, rgba(241, 70, 104, 0.1) 0%, rgba(229, 62, 62, 0.1) 100%);
    color: var(--danger-color);
    border: 1px solid rgba(241, 70, 104, 0.2);
}

.notification.is-info {
    background: linear-gradient(135deg, rgba(50, 152, 220, 0.1) 0%, rgba(43, 119, 230, 0.1) 100%);
    color: var(--info-color);
    border: 1px solid rgba(50, 152, 220, 0.2);
}

/* ===== 標籤樣式 ===== */
.tag {
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
    display: inline-flex;
    align-items: center;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.tag.is-success {
    background: var(--success-gradient);
    color: white;
}

.tag.is-warning {
    background: var(--warning-gradient);
    color: var(--grey-darker);
}

.tag.is-danger {
    background: var(--danger-gradient);
    color: white;
}

.tag.is-info {
    background: var(--info-gradient);
    color: white;
}

/* ===== 子選單樣式 (重複定義已移除) ===== */

.submenu-container::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 30px;
    width: 0;
    height: 0;
    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;
    border-right: 12px solid white;
    filter: drop-shadow(-2px 0 4px rgba(0, 0, 0, 0.1));
}

.submenu-close-btn {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 32px;
    height: 32px;
    background: var(--grey-lighter);
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    color: var(--grey-dark);
}

.submenu-close-btn:hover {
    background: var(--danger-color);
    color: white;
    transform: scale(1.1);
}

.submenu-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--grey-darker);
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 3px solid;
    border-image: var(--primary-gradient) 1;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.submenu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.submenu-item {
    background: var(--bg-primary);
    border: 2px solid var(--grey-lightest);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-decoration: none;
    color: var(--grey-darker);
    transition: all var(--transition-fast);
    display: block;
    position: relative;
    overflow: hidden;
}

.submenu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-fast);
}

.submenu-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
    color: var(--primary-color);
}

.submenu-item:hover::before {
    transform: scaleX(1);
}

/* ===== 摘要卡片樣式 ===== */
.summary-card {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.summary-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--transition-normal);
}

.summary-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl);
}

.summary-card:hover::after {
    transform: scaleX(1);
}

.summary-card-title {
    font-size: 0.875rem;
    color: var(--grey);
    font-weight: 600;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.summary-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--grey-darker);
    font-family: var(--font-mono);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== 圖表容器 ===== */
.chart-container {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0.02;
    z-index: 0;
}

.chart-container>* {
    position: relative;
    z-index: 1;
}

/* ===== 特殊組件樣式 ===== */
.collapsible-header {
    cursor: pointer;
    user-select: none;
    transition: all var(--transition-fast);
}

.collapsible-header:hover {
    background: rgba(102, 126, 234, 0.05);
}

.collapse-icon {
    margin-right: 0.75rem;
    transition: transform var(--transition-fast);
    font-size: 0.875rem;
}

.collapsed .collapse-icon {
    transform: rotate(-90deg);
}

.subtotal-row {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    font-weight: 600;
    border-top: 2px solid var(--primary-color);
}

.total-row {
    background: var(--success-gradient);
    color: white;
    font-weight: 700;
    border-top: 3px solid var(--success-color);
    font-size: 1.1em;
}

.category-header {
    background: var(--primary-gradient);
    color: white;
    font-weight: 600;
    transition: all var(--transition-fast);
}

.category-header:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* ===== 響應式設計 ===== */
@media screen and (max-width: 1024px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
    }

    .sidebar.is-active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .submenu-container {
        left: 0;
        right: 0;
        min-width: auto;
        margin: 1rem;
        position: fixed;
        top: 0;
        bottom: 0;
        max-height: 100vh;
    }
}

@media screen and (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }

    .content-wrapper {
        padding: 1rem;
    }

    .card,
    .modern-card {
        margin-bottom: 1rem;
    }

    .table,
    .modern-table,
    .balance-sheet-table,
    .income-statement-table {
        font-size: 0.875rem;
    }

    .table thead th,
    .modern-table thead th,
    .balance-sheet-table th,
    .income-statement-table th,
    .table tbody td,
    .modern-table tbody td,
    .balance-sheet-table td,
    .income-statement-table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .submenu-grid {
        grid-template-columns: 1fr;
    }

    .page-header {
        padding: 1.5rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .summary-card {
        padding: 1.5rem;
    }

    .summary-card-value {
        font-size: 1.5rem;
    }
}

@media screen and (max-width: 480px) {
    .sidebar {
        width: 100%;
    }

    .main-content {
        padding: 0.75rem;
    }

    .content-wrapper {
        padding: 0.75rem;
    }

    .page-header {
        padding: 1rem;
    }

    .page-title {
        font-size: 1.25rem;
    }

    .card-content,
    .summary-card {
        padding: 1rem;
    }

    .button {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}

/* ===== 動畫效果 ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn var(--transition-normal) ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp var(--transition-slow) ease-out forwards;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft var(--transition-normal) ease-out;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.is-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.is-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--grey-lighter);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

/* ===== 工具類別 ===== */
.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.font-mono {
    font-family: var(--font-mono);
}

.font-weight-bold {
    font-weight: 700;
}

.font-weight-semibold {
    font-weight: 600;
}

.font-weight-medium {
    font-weight: 500;
}

.text-primary {
    color: var(--primary-color);
}

.text-success {
    color: var(--success-color);
}

.text-warning {
    color: var(--warning-color);
}

.text-danger {
    color: var(--danger-color);
}

.text-info {
    color: var(--info-color);
}

.text-grey {
    color: var(--grey);
}

.text-grey-light {
    color: var(--grey-light);
}

.text-grey-dark {
    color: var(--grey-dark);
}

.bg-primary {
    background-color: var(--bg-primary);
}

.bg-secondary {
    background-color: var(--bg-secondary);
}

.bg-tertiary {
    background-color: var(--bg-tertiary);
}

.shadow-sm {
    box-shadow: var(--shadow-sm);
}

.shadow-md {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

.shadow-xl {
    box-shadow: var(--shadow-xl);
}

.rounded-sm {
    border-radius: var(--radius-sm);
}

.rounded-md {
    border-radius: var(--radius-md);
}

.rounded-lg {
    border-radius: var(--radius-lg);
}

.rounded-xl {
    border-radius: var(--radius-xl);
}

.rounded-2xl {
    border-radius: var(--radius-2xl);
}

.rounded-full {
    border-radius: var(--radius-full);
}

/* ===== 深色模式支援 ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1a202c;
        --bg-secondary: #2d3748;
        --bg-tertiary: #4a5568;
        --bg-card: rgba(26, 32, 44, 0.95);
        --grey-darker: #f7fafc;
        --grey-dark: #e2e8f0;
        --grey: #a0aec0;
        --grey-light: #718096;
        --grey-lighter: #4a5568;
        --grey-lightest: #2d3748;
    }
}

/* ===== 列印樣式 ===== */
@media print {

    .sidebar,
    .admin-panel,
    .submenu-container,
    .button,
    .notification {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .card,
    .modern-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .table,
    .modern-table,
    .balance-sheet-table,
    .income-statement-table {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* ====
= Bulma 表格增強 ===== */
.table {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

.table tbody tr {
    transition: all var(--transition-fast);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table tbody tr:last-child {
    border-bottom: none;
}

/* ===== Bulma 卡片增強 ===== */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    border-bottom: none;
    border-radius: 16px 16px 0 0;
}

.card-header-title {
    color: white;
    font-weight: 600;
}

/* ===== Bulma 表單增強 ===== */
.input,
.textarea,
.select select {
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    transition: all var(--transition-fast);
    font-family: var(--font-family);
}

.input:focus,
.textarea:focus,
.select select:focus {
    border-color: var(--bulma-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.label {
    color: #2d3748;
    font-weight: 600;
    font-size: 0.875rem;
}

/* ===== Bulma 通知增強 ===== */
.notification {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
}

.notification.is-success {
    background: linear-gradient(135deg, rgba(72, 199, 116, 0.1) 0%, rgba(56, 161, 105, 0.1) 100%);
    color: #48c774;
    border: 1px solid rgba(72, 199, 116, 0.2);
}

.notification.is-warning {
    background: linear-gradient(135deg, rgba(255, 221, 87, 0.1) 0%, rgba(246, 173, 85, 0.1) 100%);
    color: #2d3748;
    border: 1px solid rgba(255, 221, 87, 0.2);
}

.notification.is-danger {
    background: linear-gradient(135deg, rgba(241, 70, 104, 0.1) 0%, rgba(229, 62, 62, 0.1) 100%);
    color: #f14668;
    border: 1px solid rgba(241, 70, 104, 0.2);
}

.notification.is-info {
    background: linear-gradient(135deg, rgba(50, 152, 220, 0.1) 0%, rgba(43, 119, 230, 0.1) 100%);
    color: #3298dc;
    border: 1px solid rgba(50, 152, 220, 0.2);
}

/* ===== Bulma 標籤增強 ===== */
.tag {
    border-radius: 9999px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.tag.is-success {
    background: var(--success-gradient);
    color: white;
}

.tag.is-warning {
    background: var(--warning-gradient);
    color: #2d3748;
}

.tag.is-danger {
    background: var(--danger-gradient);
    color: white;
}

.tag.is-info {
    background: var(--info-gradient);
    color: white;
}

/* ===== 移動端導航欄 ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 150;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-burger {
    cursor: pointer;
}

.navbar-burger:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-burger.is-active span:nth-child(1) {
    transform: translateY(5px) rotate(45deg);
}

.navbar-burger.is-active span:nth-child(2) {
    opacity: 0;
}

.navbar-burger.is-active span:nth-child(3) {
    transform: translateY(-5px) rotate(-45deg);
}

/* ===== 響應式設計 ===== */
@media screen and (max-width: 1024px) {
    .custom-sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
        z-index: 120;
    }

    .custom-sidebar.is-active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0 !important;
        padding-top: 3.25rem;
        /* 為移動端導航欄留出空間 */
    }

    .submenu-container {
        left: 0;
        right: 0;
        min-width: auto;
        margin: 1rem;
        top: 3.25rem;
    }
}

@media screen and (min-width: 1025px) {
    .main-content {
        padding-top: 0;
    }

    .submenu-container {
        left: 280px !important;
        top: 0 !important;
        position: absolute !important;
        min-width: 800px !important;
        min-height: 500px !important;
        margin: 0 !important;
    }
}

@media screen and (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }

    .box {
        margin-bottom: 1rem;
    }

    .table {
        font-size: 0.875rem;
    }

    .table th,
    .table td {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .submenu-container .columns {
        margin: 0;
    }

    .submenu-container .column {
        padding: 0.25rem;
    }

    .title.is-3 {
        font-size: 1.5rem;
    }
}

@media screen and (max-width: 480px) {
    .custom-sidebar {
        width: 100%;
    }

    .main-content {
        padding: 0.75rem;
    }

    .box {
        padding: 1rem;
    }

    .title.is-3 {
        font-size: 1.25rem;
    }

    .button {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}

/* ===== 動畫效果 ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn var(--transition-normal) ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp var(--transition-slow) ease-out forwards;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft var(--transition-normal) ease-out;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.is-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.is-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #e2e8f0;
    border-top-color: var(--bulma-primary);
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

/* ===== 工具類別 ===== */
.text-gradient-primary {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-glass {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
}

.shadow-glass {
    box-shadow: var(--shadow-glass);
}

/* ===== 深色模式支援 ===== */
@media (prefers-color-scheme: dark) {
    .has-background-light {
        background-color: #1a202c !important;
    }

    .box {
        background-color: rgba(26, 32, 44, 0.95) !important;
        color: #f7fafc;
    }

    .table {
        background: rgba(26, 32, 44, 0.95) !important;
        color: #f7fafc;
    }

    .card {
        background: rgba(26, 32, 44, 0.95) !important;
        color: #f7fafc;
    }

    .input,
    .textarea,
    .select select {
        background-color: #2d3748;
        color: #f7fafc;
        border-color: #4a5568;
    }

    .label {
        color: #f7fafc;
    }
}

/* ===== 列印樣式 ===== */
@media print {

    .custom-sidebar,
    .submenu-container,
    .button,
    .notification {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .card,
    .box {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .table {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/*
 ===== 側邊欄額外樣式修復 ===== */
.custom-sidebar .title {
    color: white !important;
}

.custom-sidebar .has-text-white {
    color: white !important;
}

.custom-sidebar .has-text-white-ter {
    color: rgba(255, 255, 255, 0.8) !important;
}

.custom-sidebar .icon {
    color: inherit !important;
}

.custom-sidebar .fas {
    color: inherit !important;
}

/* 確保所有側邊欄內的元素都有正確的顏色 */
.custom-sidebar * {
    border-color: rgba(255, 255, 255, 0.1) !important;
}

.custom-sidebar .menu-list a:hover .icon,
.custom-sidebar .menu-list a:hover .fas {
    color: white !important;
}

/* ==
=== 強制修復側邊欄顏色問題 ===== */
.custom-sidebar {
    background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%) !important;
}

.custom-sidebar .title,
.custom-sidebar .title.is-4 {
    color: white !important;
}

.custom-sidebar .has-text-white,
.custom-sidebar .has-text-white * {
    color: white !important;
}

.custom-sidebar .has-text-white-ter,
.custom-sidebar .has-text-white-ter *,
.custom-sidebar .menu-list a,
.custom-sidebar .menu-list a * {
    color: rgba(255, 255, 255, 0.8) !important;
}

.custom-sidebar .icon,
.custom-sidebar .icon *,
.custom-sidebar .fas,
.custom-sidebar .fa,
.custom-sidebar span {
    color: inherit !important;
}

/* 覆蓋 Bulma 的默認樣式 */
.custom-sidebar .menu-list a {
    background-color: transparent !important;
}

.custom-sidebar .menu-list a:hover,
.custom-sidebar .menu-list a:hover *,
.custom-sidebar .menu-list a:hover .icon,
.custom-sidebar .menu-list a:hover .fas,
.custom-sidebar .menu-list a:hover span {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* ====
= 主內容區域布局修復 ===== */
.main-content {
    margin-left: 280px !important;
    padding: 2rem !important;
    min-height: 100vh;
    transition: margin-left var(--transition-normal);
}

/* 確保主內容不被側邊欄覆蓋 */
body {
    overflow-x: hidden;
}

.custom-sidebar {
    position: fixed !important;
    left: 0 !important;
    top: 0 !important;
    width: 280px !important;
    height: 100vh !important;
    z-index: 100 !important;
}

/* ===
== 最終布局修復 - 最高優先級 ===== */
html body .main-content {
    margin-left: 280px !important;
    padding: 2rem !important;
    min-height: 100vh !important;
    position: relative !important;
    z-index: 1 !important;
}

html body .custom-sidebar {
    position: fixed !important;
    left: 0 !important;
    top: 0 !important;
    width: 280px !important;
    height: 100vh !important;
    z-index: 100 !important;
    background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%) !important;
    color: white !important;
    overflow-y: auto !important;
}

/* 確保容器不會重置邊距 */
html body .main-content .container,
html body .main-content .container.is-fluid {
    margin-left: 0 !important;
    padding-left: 0 !important;
    max-width: none !important;
}

/* 響應式修復 */
@media screen and (max-width: 1024px) {
    html body .main-content {
        margin-left: 0 !important;
        padding-top: 3.25rem !important;
    }

    html body .custom-sidebar {
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease !important;
    }

    html body .custom-sidebar.is-active {
        transform: translateX(0) !important;
    }
}