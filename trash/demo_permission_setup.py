#!/usr/bin/env python3
"""
權限管理系統示例
展示如何為不同用戶設定不同的權限等級
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.auth_service import AuthService, RoleService
from services.menu_service import MenuService
from database import get_db
from model import User
from models.auth_models import Role
from werkzeug.security import generate_password_hash

def create_demo_users():
    """建立示例用戶"""
    print("=== 建立示例用戶 ===")
    
    demo_users = [
        {
            'username': 'accountant',
            'email': '<EMAIL>',
            'full_name': '會計師',
            'role': 'user'  # 一般用戶角色
        },
        {
            'username': 'manager',
            'email': '<EMAIL>', 
            'full_name': '部門經理',
            'role': 'user'
        },
        {
            'username': 'employee',
            'email': '<EMAIL>',
            'full_name': '一般員工',
            'role': 'user'
        }
    ]
    
    with get_db() as db:
        for user_data in demo_users:
            # 檢查用戶是否已存在
            existing_user = db.query(User).filter(User.username == user_data['username']).first()
            if existing_user:
                print(f"  用戶 {user_data['username']} 已存在")
                continue
            
            # 建立新用戶
            new_user = User(
                username=user_data['username'],
                email=user_data['email'],
                password_hash=generate_password_hash('123456'),  # 預設密碼
                full_name=user_data['full_name'],
                is_active=True
            )
            
            db.add(new_user)
            db.commit()
            db.refresh(new_user)
            
            # 分配角色
            role = db.query(Role).filter(Role.name == user_data['role']).first()
            if role:
                RoleService.assign_role_to_user(new_user.id, [role.id])
            
            print(f"  ✅ 建立用戶: {user_data['full_name']} ({user_data['username']})")

def demo_user_permissions():
    """展示不同用戶的權限差異"""
    print("\n=== 用戶權限對比 ===")
    
    users = ['admin', 'accountant', 'manager', 'employee']
    
    with get_db() as db:
        for username in users:
            user = db.query(User).filter(User.username == username).first()
            if not user:
                print(f"  ❌ 找不到用戶: {username}")
                continue
            
            print(f"\n👤 {user.full_name} ({username}):")
            
            # 獲取權限
            permissions = AuthService.get_user_permissions(user.id)
            print(f"   權限數量: {len(permissions)}")
            
            # 獲取可訪問模組
            modules = AuthService.get_user_modules(user.id)
            print(f"   可訪問模組: {modules}")
            
            # 獲取可訪問的選單
            user_menu = MenuService.get_user_menu(user.id)
            main_menus = list(user_menu.keys())
            print(f"   可見主選單: {main_menus}")
            
            # 測試特定功能權限
            can_create_income = MenuService.user_can_access_function(user.id, '新增帳務')
            can_view_reports = MenuService.user_can_access_function(user.id, '資產負債表')
            print(f"   可新增帳務: {can_create_income}")
            print(f"   可查看報表: {can_view_reports}")

def demo_custom_role():
    """示例：建立自訂角色"""
    print("\n=== 建立自訂角色示例 ===")
    
    # 建立「查看者」角色 - 只能查看報表
    try:
        viewer_role = RoleService.create_role(
            name="viewer",
            display_name="報表查看者",
            description="只能查看報表的用戶"
        )
        print(f"✅ 建立角色: {viewer_role.display_name}")
        
        # 為角色分配權限（只有查看報表的權限）
        with get_db() as db:
            from models.auth_models import Permission
            view_permissions = db.query(Permission).filter(
                Permission.name.in_(['reports.view'])
            ).all()
            
            permission_ids = [p.id for p in view_permissions]
            RoleService.assign_permissions_to_role(viewer_role.id, permission_ids)
            print(f"✅ 為角色分配 {len(permission_ids)} 個權限")
        
    except Exception as e:
        print(f"❌ 建立角色失敗: {e}")

def demo_menu_comparison():
    """展示不同用戶看到的選單差異"""
    print("\n=== 選單差異對比 ===")
    
    users_to_compare = ['admin', 'accountant']
    
    with get_db() as db:
        for username in users_to_compare:
            user = db.query(User).filter(User.username == username).first()
            if not user:
                continue
            
            print(f"\n📋 {user.full_name} 的選單:")
            user_menu = MenuService.get_user_menu(user.id)
            
            for main_menu, submenus in user_menu.items():
                print(f"  📁 {main_menu}")
                for submenu in submenus:
                    print(f"    📂 {submenu['title']}")
                    for button in submenu['buttons']:
                        print(f"      🔘 {button['label']}")
                        for child in button.get('children', []):
                            print(f"        - {child}")

def main():
    """主函數"""
    print("🚀 權限管理系統示例")
    print("=" * 50)
    
    try:
        # 建立示例用戶
        create_demo_users()
        
        # 展示用戶權限差異
        demo_user_permissions()
        
        # 建立自訂角色示例
        demo_custom_role()
        
        # 展示選單差異
        demo_menu_comparison()
        
        print("\n" + "=" * 50)
        print("🎉 示例完成！")
        print("\n💡 使用說明:")
        print("1. 啟動應用程式: python main.py")
        print("2. 訪問登入頁面: http://localhost:5001/auth/login")
        print("3. 使用不同帳號登入查看差異:")
        print("   - admin/admin123 (系統管理員)")
        print("   - accountant/123456 (會計師)")
        print("   - manager/123456 (部門經理)")
        print("   - employee/123456 (一般員工)")
        
    except Exception as e:
        print(f"❌ 示例執行失敗: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()