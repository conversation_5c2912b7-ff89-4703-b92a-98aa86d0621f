#!/usr/bin/env python3
"""
用戶角色測試腳本
測試管理員和一般用戶的不同存取權限
"""
import sys
import os

# 添加專案路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from model import User
from models.auth_models import Role, Permission, user_roles, role_permissions
from services.auth_service import AuthService
from services.tenant_service import TenantService

def test_user_roles():
    """測試用戶角色功能"""
    print("=== 用戶角色測試 ===\n")
    
    with get_db() as db:
        # 1. 檢查現有用戶
        print("1. 現有用戶列表：")
        users = db.query(User).filter(User.is_active == True).all()
        if users:
            for user in users:
                roles = user.get_roles()
                role_names = [role.name for role in roles] if roles else ['無角色']
                tenant_admin_status = "租戶管理員" if user.is_tenant_admin else "一般用戶"
                print(f"   - {user.username} ({user.full_name or '未設定姓名'}) - {tenant_admin_status} - 角色: {', '.join(role_names)}")
                print(f"     Email: {user.email}, 租戶ID: {user.tenant_id}")
        else:
            print("   沒有找到任何用戶")
        
        print()
        
        # 2. 檢查現有角色
        print("2. 現有角色列表：")
        roles = db.query(Role).filter(Role.is_active == True).all()
        if roles:
            for role in roles:
                permissions = role.permissions
                perm_names = [f"{p.module}.{p.action}" for p in permissions] if permissions else ['無權限']
                print(f"   - {role.name} ({role.display_name}) - 權限: {', '.join(perm_names[:5])}")
                if len(perm_names) > 5:
                    print(f"     ... 還有 {len(perm_names) - 5} 個權限")
        else:
            print("   沒有找到任何角色")
        
        print()
        
        # 3. 檢查現有權限
        print("3. 現有權限列表（按模組分組）：")
        permissions = db.query(Permission).all()
        if permissions:
            modules = {}
            for perm in permissions:
                if perm.module not in modules:
                    modules[perm.module] = []
                modules[perm.module].append(f"{perm.action} ({perm.display_name})")
            
            for module, actions in modules.items():
                print(f"   - {module}: {', '.join(actions)}")
        else:
            print("   沒有找到任何權限")

def test_admin_access():
    """測試管理員存取權限"""
    print("\n=== 管理員存取權限測試 ===\n")
    
    with get_db() as db:
        # 尋找管理員用戶
        admin_users = db.query(User).filter(
            User.is_active == True,
            User.is_tenant_admin == True
        ).all()
        
        if admin_users:
            for admin_user in admin_users:
                print(f"測試管理員用戶: {admin_user.username}")
                
                # 檢查角色
                roles = admin_user.get_roles()
                print(f"   角色: {[role.name for role in roles]}")
                
                # 檢查權限
                permissions = AuthService.get_user_permissions(admin_user.id)
                print(f"   權限數量: {len(permissions)}")
                if permissions:
                    print(f"   前5個權限: {permissions[:5]}")
                
                # 檢查可存取模組
                modules = AuthService.get_user_modules(admin_user.id)
                print(f"   可存取模組: {modules}")
                
                print()
        else:
            print("沒有找到管理員用戶")

def test_regular_user_access():
    """測試一般用戶存取權限"""
    print("\n=== 一般用戶存取權限測試 ===\n")
    
    with get_db() as db:
        # 尋找一般用戶
        regular_users = db.query(User).filter(
            User.is_active == True,
            User.is_tenant_admin == False
        ).all()
        
        if regular_users:
            for user in regular_users[:3]:  # 只測試前3個
                print(f"測試一般用戶: {user.username}")
                
                # 檢查角色
                roles = user.get_roles()
                print(f"   角色: {[role.name for role in roles]}")
                
                # 檢查權限
                permissions = AuthService.get_user_permissions(user.id)
                print(f"   權限數量: {len(permissions)}")
                if permissions:
                    print(f"   前5個權限: {permissions[:5]}")
                
                # 檢查可存取模組
                modules = AuthService.get_user_modules(user.id)
                print(f"   可存取模組: {modules}")
                
                print()
        else:
            print("沒有找到一般用戶")

def create_test_admin_user():
    """創建測試管理員用戶"""
    print("\n=== 創建測試管理員用戶 ===\n")
    
    with get_db() as db:
        # 檢查是否已存在測試管理員
        existing_admin = db.query(User).filter(User.username == 'test_admin').first()
        if existing_admin:
            print("測試管理員用戶已存在，跳過創建")
            return
        
        from werkzeug.security import generate_password_hash
        
        # 創建測試管理員用戶
        admin_user = User(
            username='test_admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin123'),
            full_name='測試管理員',
            is_active=True,
            is_tenant_admin=True,
            tenant_id=1  # 假設租戶ID為1
        )
        
        db.add(admin_user)
        db.commit()
        
        print(f"成功創建測試管理員用戶: {admin_user.username}")
        print("登入資訊: test_admin / admin123")

def main():
    """主要測試函式"""
    try:
        test_user_roles()
        test_admin_access()
        test_regular_user_access()
        
        # 如果沒有管理員用戶，詢問是否創建測試用戶
        with get_db() as db:
            admin_count = db.query(User).filter(
                User.is_active == True,
                User.is_tenant_admin == True
            ).count()
            
            if admin_count == 0:
                print("\n>>> 沒有發現管理員用戶，建議創建測試管理員用戶以進行測試")
                response = input("是否要創建測試管理員用戶？(y/n): ").lower()
                if response in ['y', 'yes', '是']:
                    create_test_admin_user()
        
        print("\n=== 測試完成 ===")
        
    except Exception as e:
        print(f"測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()