#!/usr/bin/env python3
"""
資料庫遷移腳本：添加分錄類型和交易類型欄位
"""

import sys
import os

# 添加項目根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from database import get_db

def migrate():
    """執行遷移"""
    print("開始遷移：添加分錄類型和交易類型欄位...")
    
    with get_db() as db:
        # 檢查 journal_entries 表是否存在
        result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='journal_entries'")).fetchone()
        if result:
            # 檢查 entry_type 欄位是否存在
            result = db.execute(text("PRAGMA table_info(journal_entries)")).fetchall()
            column_names = [row[1] for row in result]
            
            if 'entry_type' not in column_names:
                print("添加 entry_type 欄位到 journal_entries 表...")
                db.execute(text("ALTER TABLE journal_entries ADD COLUMN entry_type VARCHAR(20) DEFAULT 'primary'"))
                # 為現有記錄設置默認值
                db.execute(text("UPDATE journal_entries SET entry_type = 'primary'"))
                print("創建 entry_type 索引...")
                db.execute(text("CREATE INDEX ix_journal_entry_type ON journal_entries (entry_type)"))
        else:
            print("journal_entries 表不存在，跳過...")
        
        # 檢查 transactions 表是否存在
        result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='transactions'")).fetchone()
        if result:
            # 檢查 transaction_type 欄位是否存在
            result = db.execute(text("PRAGMA table_info(transactions)")).fetchall()
            column_names = [row[1] for row in result]
            
            if 'transaction_type' not in column_names:
                print("添加 transaction_type 欄位到 transactions 表...")
                db.execute(text("ALTER TABLE transactions ADD COLUMN transaction_type VARCHAR(20) DEFAULT 'expense'"))
                # 為現有記錄設置默認值
                db.execute(text("UPDATE transactions SET transaction_type = 'expense'"))
                print("創建 transaction_type 索引...")
                db.execute(text("CREATE INDEX ix_transaction_type ON transactions (transaction_type)"))
        else:
            print("transactions 表不存在，跳過...")
        
        db.commit()
    
    print("遷移完成！")

if __name__ == "__main__":
    migrate()