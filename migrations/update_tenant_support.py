"""
更新多租戶支援的資料庫遷移腳本（適應現有結構）
"""
import sqlite3
import os
from datetime import datetime

def migrate():
    """執行資料庫遷移"""
    
    # 獲取資料庫路徑
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 找不到資料庫檔案: {db_path}")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("🔄 開始執行多租戶資料庫遷移...")
        
        # 1. 檢查並創建租戶表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tenants'")
        if not cursor.fetchone():
            print("📦 創建 tenants 表...")
            cursor.execute("""
                CREATE TABLE tenants (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(255) NOT NULL,
                    slug VARCHAR(100) UNIQUE NOT NULL,
                    domain VARCHAR(255),
                    plan_level VARCHAR(20) DEFAULT 'BASIC',
                    status VARCHAR(20) DEFAULT 'TRIAL',
                    contact_email VARCHAR(255),
                    contact_person VARCHAR(100),
                    contact_phone VARCHAR(50),
                    trial_start_date DATE,
                    trial_end_date DATE,
                    subscription_start_date DATE,
                    subscription_end_date DATE,
                    max_users INTEGER DEFAULT 3,
                    max_storage_mb INTEGER DEFAULT 500,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            """)
        else:
            print("✓ tenants 表已存在")
        
        # 2. 檢查並創建租戶方案功能表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tenant_plan_features'")
        if not cursor.fetchone():
            print("📦 創建 tenant_plan_features 表...")
            cursor.execute("""
                CREATE TABLE tenant_plan_features (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    tenant_id INTEGER NOT NULL,
                    feature_code VARCHAR(100) NOT NULL,
                    is_enabled BOOLEAN DEFAULT 1,
                    custom_limit INTEGER,
                    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
                    UNIQUE(tenant_id, feature_code)
                )
            """)
        else:
            print("✓ tenant_plan_features 表已存在")
        
        # 3. 檢查並創建使用記錄表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tenant_usage_logs'")
        if not cursor.fetchone():
            print("📦 創建 tenant_usage_logs 表...")
            cursor.execute("""
                CREATE TABLE tenant_usage_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    tenant_id INTEGER NOT NULL,
                    user_id INTEGER,
                    action VARCHAR(100) NOT NULL,
                    resource_type VARCHAR(50),
                    resource_id INTEGER,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
                )
            """)
        else:
            print("✓ tenant_usage_logs 表已存在")
        
        # 4. 為現有表添加 tenant_id 欄位
        tables_to_update = [
            'account',
            'transaction',
            'assets',
            'accounting_subjects',
            'payroll',
            'service_reward',
            'bankloan',
            'fund_record',
            'share_account',
            'accounting_journal',
            'audit_log'
        ]
        
        for table in tables_to_update:
            # 檢查表是否存在
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                # 檢查欄位是否已存在
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [row[1] for row in cursor.fetchall()]
                
                if 'tenant_id' not in columns:
                    print(f"➕ 為 {table} 表添加 tenant_id 欄位...")
                    cursor.execute(f"ALTER TABLE {table} ADD COLUMN tenant_id INTEGER")
                else:
                    print(f"✓ {table} 表已有 tenant_id 欄位")
        
        # 5. 為 users 表添加租戶相關欄位
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if cursor.fetchone():
            cursor.execute("PRAGMA table_info(users)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'tenant_id' not in columns:
                print("➕ 為 users 表添加 tenant_id 欄位...")
                cursor.execute("ALTER TABLE users ADD COLUMN tenant_id INTEGER")
            else:
                print("✓ users 表已有 tenant_id 欄位")
            
            if 'is_tenant_admin' not in columns:
                print("➕ 為 users 表添加 is_tenant_admin 欄位...")
                cursor.execute("ALTER TABLE users ADD COLUMN is_tenant_admin BOOLEAN DEFAULT 0")
            else:
                print("✓ users 表已有 is_tenant_admin 欄位")
        
        # 6. 檢查是否已有預設租戶
        cursor.execute("SELECT id FROM tenants WHERE slug = 'default'")
        default_tenant = cursor.fetchone()
        
        if not default_tenant:
            print("🏢 創建預設租戶...")
            cursor.execute("""
                INSERT INTO tenants (
                    name, slug, plan_level, status, 
                    contact_email, contact_person,
                    max_users, max_storage_mb
                ) VALUES (
                    '預設公司', 'default', 'ENTERPRISE', 'ACTIVE',
                    '<EMAIL>', '系統管理員',
                    999, 99999
                )
            """)
            # 獲取新建立的租戶ID
            cursor.execute("SELECT id FROM tenants WHERE slug = 'default'")
            default_tenant_id = cursor.fetchone()[0]
        else:
            default_tenant_id = default_tenant[0]
            print(f"✓ 預設租戶已存在 (ID: {default_tenant_id})")
        
        # 7. 更新現有資料的 tenant_id
        for table in tables_to_update:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                # 檢查是否有未分配租戶的資料
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE tenant_id IS NULL")
                count = cursor.fetchone()[0]
                if count > 0:
                    print(f"🔄 更新 {table} 表的 {count} 筆現有資料...")
                    cursor.execute(f"UPDATE {table} SET tenant_id = ? WHERE tenant_id IS NULL", (default_tenant_id,))
                else:
                    print(f"✓ {table} 表的資料已分配租戶")
        
        # 更新 users 表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if cursor.fetchone():
            cursor.execute("SELECT COUNT(*) FROM users WHERE tenant_id IS NULL")
            count = cursor.fetchone()[0]
            if count > 0:
                print(f"🔄 更新 users 表的 {count} 筆現有資料...")
                cursor.execute("UPDATE users SET tenant_id = ? WHERE tenant_id IS NULL", (default_tenant_id,))
                # 將第一個用戶設為租戶管理員
                cursor.execute("UPDATE users SET is_tenant_admin = 1 WHERE id = 1 AND is_tenant_admin = 0")
            else:
                print("✓ users 表的資料已分配租戶")
        
        conn.commit()
        print("✅ 資料庫遷移成功完成！")
        
        # 顯示摘要
        cursor.execute("SELECT COUNT(*) FROM tenants")
        tenant_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM users WHERE tenant_id IS NOT NULL")
        user_count = cursor.fetchone()[0]
        
        print(f"\n📊 遷移摘要:")
        print(f"   租戶數量: {tenant_count}")
        print(f"   用戶數量: {user_count}")
        
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"❌ 遷移失敗: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    migrate()