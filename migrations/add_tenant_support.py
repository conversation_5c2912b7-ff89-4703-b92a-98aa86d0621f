"""
添加多租戶支援的資料庫遷移腳本
"""
import sqlite3
import os
from datetime import datetime

def migrate():
    """執行資料庫遷移"""
    
    # 獲取資料庫路徑
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 找不到資料庫檔案: {db_path}")
        return False
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        print("🔄 開始執行多租戶資料庫遷移...")
        
        # 1. 創建租戶表
        print("📦 創建 tenants 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tenants (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(255) NOT NULL,
                slug VARCHAR(100) UNIQUE NOT NULL,
                domain VARCHAR(255),
                plan_level VARCHAR(20) DEFAULT 'BASIC',
                status VARCHAR(20) DEFAULT 'TRIAL',
                contact_email VARCHAR(255),
                contact_person VARCHAR(100),
                contact_phone VARCHAR(50),
                trial_start_date DATE,
                trial_end_date DATE,
                subscription_start_date DATE,
                subscription_end_date DATE,
                max_users INTEGER DEFAULT 3,
                max_storage_mb INTEGER DEFAULT 500,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        """)
        
        # 2. 創建方案功能表
        print("📦 創建 plan_features 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS plan_features (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plan_level VARCHAR(20) NOT NULL,
                feature_code VARCHAR(100) NOT NULL,
                feature_name VARCHAR(255) NOT NULL,
                description TEXT,
                is_enabled BOOLEAN DEFAULT 1,
                limit_value INTEGER,
                UNIQUE(plan_level, feature_code)
            )
        """)
        
        # 3. 創建租戶方案功能表
        print("📦 創建 tenant_plan_features 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tenant_plan_features (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tenant_id INTEGER NOT NULL,
                feature_code VARCHAR(100) NOT NULL,
                is_enabled BOOLEAN DEFAULT 1,
                custom_limit INTEGER,
                FOREIGN KEY (tenant_id) REFERENCES tenants(id),
                UNIQUE(tenant_id, feature_code)
            )
        """)
        
        # 4. 創建使用記錄表
        print("📦 創建 tenant_usage_logs 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tenant_usage_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tenant_id INTEGER NOT NULL,
                user_id INTEGER,
                action VARCHAR(100) NOT NULL,
                resource_type VARCHAR(50),
                resource_id INTEGER,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (tenant_id) REFERENCES tenants(id)
            )
        """)
        
        # 5. 為現有表添加 tenant_id 欄位
        tables_to_update = [
            'account',
            'transaction',
            'assets',
            'accounting_subjects',
            'payroll',
            'service_reward',
            'bankloan',
            'fund_record',
            'share_account',
            'accounting_journal',
            'audit_log'
        ]
        
        for table in tables_to_update:
            # 檢查表是否存在
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                # 檢查欄位是否已存在
                cursor.execute(f"PRAGMA table_info({table})")
                columns = [row[1] for row in cursor.fetchall()]
                
                if 'tenant_id' not in columns:
                    print(f"➕ 為 {table} 表添加 tenant_id 欄位...")
                    cursor.execute(f"ALTER TABLE {table} ADD COLUMN tenant_id INTEGER")
                else:
                    print(f"✓ {table} 表已有 tenant_id 欄位")
        
        # 6. 為 users 表添加租戶相關欄位
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if cursor.fetchone():
            cursor.execute("PRAGMA table_info(users)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'tenant_id' not in columns:
                print("➕ 為 users 表添加 tenant_id 欄位...")
                cursor.execute("ALTER TABLE users ADD COLUMN tenant_id INTEGER")
            
            if 'is_tenant_admin' not in columns:
                print("➕ 為 users 表添加 is_tenant_admin 欄位...")
                cursor.execute("ALTER TABLE users ADD COLUMN is_tenant_admin BOOLEAN DEFAULT 0")
        
        # 7. 創建預設租戶（為現有資料）
        print("🏢 創建預設租戶...")
        cursor.execute("""
            INSERT OR IGNORE INTO tenants (
                name, slug, plan_level, status, 
                contact_email, contact_person,
                max_users, max_storage_mb
            ) VALUES (
                '預設公司', 'default', 'ENTERPRISE', 'ACTIVE',
                '<EMAIL>', '系統管理員',
                999, 99999
            )
        """)
        
        # 獲取預設租戶ID
        cursor.execute("SELECT id FROM tenants WHERE slug = 'default'")
        default_tenant_id = cursor.fetchone()[0]
        
        # 8. 更新現有資料的 tenant_id
        for table in tables_to_update:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                print(f"🔄 更新 {table} 表的現有資料...")
                cursor.execute(f"UPDATE {table} SET tenant_id = ? WHERE tenant_id IS NULL", (default_tenant_id,))
        
        # 更新 users 表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if cursor.fetchone():
            print("🔄 更新 users 表的現有資料...")
            cursor.execute("UPDATE users SET tenant_id = ? WHERE tenant_id IS NULL", (default_tenant_id,))
            # 將第一個用戶設為租戶管理員
            cursor.execute("UPDATE users SET is_tenant_admin = 1 WHERE id = 1")
        
        # 9. 插入預設方案功能
        print("📝 插入預設方案功能...")
        plan_features = [
            # BASIC
            ('BASIC', 'income_expense', '收支記錄', '基本收入支出記錄功能', 1, None),
            ('BASIC', 'account_management', '帳戶管理', '銀行帳戶管理', 1, None),
            ('BASIC', 'transaction_list', '交易明細', '查看交易明細', 1, None),
            
            # STANDARD (包含BASIC)
            ('STANDARD', 'income_expense', '收支記錄', '基本收入支出記錄功能', 1, None),
            ('STANDARD', 'account_management', '帳戶管理', '銀行帳戶管理', 1, None),
            ('STANDARD', 'transaction_list', '交易明細', '查看交易明細', 1, None),
            ('STANDARD', 'balance_sheet', '資產負債表', '財務報表', 1, None),
            ('STANDARD', 'income_statement', '損益表', '財務報表', 1, None),
            ('STANDARD', 'overdue_tracking', '逾期追蹤', '應收應付逾期追蹤', 1, None),
            ('STANDARD', 'bank_loan', '銀行借款', '借款管理', 1, None),
            ('STANDARD', 'service_reward', '勞務報酬', '勞務報酬管理', 1, None),
            
            # PREMIUM (包含STANDARD)
            ('PREMIUM', 'income_expense', '收支記錄', '基本收入支出記錄功能', 1, None),
            ('PREMIUM', 'account_management', '帳戶管理', '銀行帳戶管理', 1, None),
            ('PREMIUM', 'transaction_list', '交易明細', '查看交易明細', 1, None),
            ('PREMIUM', 'balance_sheet', '資產負債表', '財務報表', 1, None),
            ('PREMIUM', 'income_statement', '損益表', '財務報表', 1, None),
            ('PREMIUM', 'overdue_tracking', '逾期追蹤', '應收應付逾期追蹤', 1, None),
            ('PREMIUM', 'bank_loan', '銀行借款', '借款管理', 1, None),
            ('PREMIUM', 'service_reward', '勞務報酬', '勞務報酬管理', 1, None),
            ('PREMIUM', 'cash_flow', '現金流量表', '現金流量分析', 1, None),
            ('PREMIUM', 'department_analysis', '部門分析', '部門別損益分析', 1, None),
            ('PREMIUM', 'asset_management', '資產管理', '固定資產管理', 1, None),
            ('PREMIUM', 'payroll', '薪資管理', '薪資系統', 1, None),
            ('PREMIUM', 'accounting_subjects', '會計科目', '會計科目管理', 1, None),
            ('PREMIUM', 'backup', '資料備份', '資料備份還原', 1, None),
            
            # ENTERPRISE (全功能)
            ('ENTERPRISE', 'income_expense', '收支記錄', '基本收入支出記錄功能', 1, None),
            ('ENTERPRISE', 'account_management', '帳戶管理', '銀行帳戶管理', 1, None),
            ('ENTERPRISE', 'transaction_list', '交易明細', '查看交易明細', 1, None),
            ('ENTERPRISE', 'balance_sheet', '資產負債表', '財務報表', 1, None),
            ('ENTERPRISE', 'income_statement', '損益表', '財務報表', 1, None),
            ('ENTERPRISE', 'overdue_tracking', '逾期追蹤', '應收應付逾期追蹤', 1, None),
            ('ENTERPRISE', 'bank_loan', '銀行借款', '借款管理', 1, None),
            ('ENTERPRISE', 'service_reward', '勞務報酬', '勞務報酬管理', 1, None),
            ('ENTERPRISE', 'cash_flow', '現金流量表', '現金流量分析', 1, None),
            ('ENTERPRISE', 'department_analysis', '部門分析', '部門別損益分析', 1, None),
            ('ENTERPRISE', 'asset_management', '資產管理', '固定資產管理', 1, None),
            ('ENTERPRISE', 'payroll', '薪資管理', '薪資系統', 1, None),
            ('ENTERPRISE', 'accounting_subjects', '會計科目', '會計科目管理', 1, None),
            ('ENTERPRISE', 'backup', '資料備份', '資料備份還原', 1, None),
            ('ENTERPRISE', 'journal_entry', '傳票管理', '會計傳票', 1, None),
            ('ENTERPRISE', 'multi_company', '多公司', '多公司管理', 1, None),
            ('ENTERPRISE', 'api_access', 'API存取', 'API整合', 1, None),
            ('ENTERPRISE', 'custom_reports', '客製報表', '客製化報表', 1, None),
        ]
        
        for feature in plan_features:
            cursor.execute("""
                INSERT OR IGNORE INTO plan_features 
                (plan_level, feature_code, feature_name, description, is_enabled, limit_value)
                VALUES (?, ?, ?, ?, ?, ?)
            """, feature)
        
        conn.commit()
        print("✅ 資料庫遷移成功完成！")
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"❌ 遷移失敗: {str(e)}")
        return False
        
    finally:
        conn.close()

if __name__ == "__main__":
    migrate()