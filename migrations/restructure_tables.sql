-- 重構資料表：將 money 表分離為 transactions 和 journal_entries
-- 執行日期: 2025-01-17
-- 階段 1：創建新表結構

-- 1. 創建新的交易主表
CREATE TABLE IF NOT EXISTS transactions (
    id INTEGER PRIMARY KEY,
    transaction_date DATE NOT NULL,
    description TEXT NOT NULL,
    total_amount INTEGER NOT NULL DEFAULT 0,
    tax_amount INTEGER DEFAULT 0,
    extra_fee INTEGER DEFAULT 0,
    
    -- 關聯資訊
    account_id INTEGER,
    payment_identity_id INTEGER,
    department_id INTEGER,
    project_id INTEGER,
    
    -- 發票相關
    is_paper BOOLEAN DEFAULT FALSE,
    invoice_number VARCHAR(50),
    tax_type VARCHAR(50),
    buyer_tax_id VARCHAR(50),
    seller_tax_id VARCHAR(50),
    invoice_date VARCHAR(50),
    
    -- 付款狀態
    is_paid BOOLEAN DEFAULT FALSE,
    should_paid_date DATETIME,
    paid_date DATETIME,
    
    -- 其他
    note TEXT,
    tags TEXT,
    image_path TEXT,
    
    -- 審計欄位
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER,
    
    -- 外鍵約束
    FOREIGN KEY (account_id) REFERENCES account(id),
    FOREIGN KEY (payment_identity_id) REFERENCES payment_identity(id),
    FOREIGN KEY (department_id) REFERENCES department(id),
    FOREIGN KEY (project_id) REFERENCES project(id)
);

-- 2. 創建會計分錄表
CREATE TABLE IF NOT EXISTS journal_entries (
    id INTEGER PRIMARY KEY,
    transaction_id INTEGER NOT NULL,
    subject_code VARCHAR(50) NOT NULL,
    debit_amount INTEGER DEFAULT 0,
    credit_amount INTEGER DEFAULT 0,
    description TEXT,
    
    -- 審計欄位
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 外鍵約束
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_code) REFERENCES account_subject(code),
    
    -- 檢查約束：借方和貸方金額不能同時為0
    CHECK ((debit_amount > 0 AND credit_amount = 0) OR (debit_amount = 0 AND credit_amount > 0))
);

-- 3. 創建索引
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_transactions_account ON transactions(account_id);
CREATE INDEX IF NOT EXISTS idx_transactions_payment_identity ON transactions(payment_identity_id);
CREATE INDEX IF NOT EXISTS idx_transactions_invoice_number ON transactions(invoice_number);

CREATE INDEX IF NOT EXISTS idx_journal_entries_transaction ON journal_entries(transaction_id);
CREATE INDEX IF NOT EXISTS idx_journal_entries_subject ON journal_entries(subject_code);

-- 4. 遷移現有資料的策略
-- 由於需要處理複雜的資料關聯，我們分步驟進行

-- 步驟 4.1：創建臨時表來處理分組
CREATE TEMPORARY TABLE temp_transaction_groups AS
SELECT
    journal_reference,
    MIN(id) as first_id,
    a_time,
    name,
    account_id,
    payment_identity_id,
    department_id,
    project_id,
    is_paper,
    number,
    tax_type,
    buyer_tax_id,
    seller_tax_id,
    date,
    is_paid,
    should_paid_date,
    paid_date,
    note,
    tags,
    image_path,
    created_at,
    created_by,
    SUM(CASE WHEN entry_side = 'DEBIT' THEN total ELSE 0 END) as total_amount,
    COALESCE(MAX(tax), 0) as tax_amount,
    COALESCE(MAX(extra_fee), 0) as extra_fee
FROM money
WHERE journal_reference IS NOT NULL
GROUP BY journal_reference;

-- 步驟 4.2：插入交易記錄
INSERT INTO transactions (
    transaction_date, description, total_amount, tax_amount, extra_fee,
    account_id, payment_identity_id, department_id, project_id,
    is_paper, invoice_number, tax_type, buyer_tax_id, seller_tax_id, invoice_date,
    is_paid, should_paid_date, paid_date, note, tags, image_path,
    created_at, created_by
)
SELECT
    a_time, name, total_amount, tax_amount, extra_fee,
    account_id, payment_identity_id, department_id, project_id,
    COALESCE(is_paper, 0), number, tax_type, buyer_tax_id, seller_tax_id, date,
    COALESCE(is_paid, 0), should_paid_date, paid_date, note, tags, image_path,
    created_at, created_by
FROM temp_transaction_groups;

-- 步驟 4.3：插入會計分錄
INSERT INTO journal_entries (
    transaction_id, subject_code, debit_amount, credit_amount, description
)
SELECT
    t.id,
    m.subject_code,
    CASE WHEN m.entry_side = 'DEBIT' THEN m.total ELSE 0 END,
    CASE WHEN m.entry_side = 'CREDIT' THEN m.total ELSE 0 END,
    m.name
FROM money m
JOIN temp_transaction_groups tg ON tg.journal_reference = m.journal_reference
JOIN transactions t ON t.created_at = tg.created_at AND t.description = tg.name
WHERE m.journal_reference IS NOT NULL
ORDER BY m.journal_reference, m.entry_side DESC; -- DEBIT 在前

-- 5. 驗證遷移結果
-- 檢查交易數量
SELECT
    'Transactions' as table_name,
    COUNT(*) as record_count
FROM transactions
UNION ALL
SELECT
    'Journal Entries' as table_name,
    COUNT(*) as record_count
FROM journal_entries
UNION ALL
SELECT
    'Original Money (with journal_ref)' as table_name,
    COUNT(*) as record_count
FROM money
WHERE journal_reference IS NOT NULL;

-- 檢查借貸平衡
SELECT
    t.id,
    t.description,
    SUM(CASE WHEN je.debit_amount > 0 THEN je.debit_amount ELSE 0 END) as total_debit,
    SUM(CASE WHEN je.credit_amount > 0 THEN je.credit_amount ELSE 0 END) as total_credit,
    SUM(CASE WHEN je.debit_amount > 0 THEN je.debit_amount ELSE 0 END) -
    SUM(CASE WHEN je.credit_amount > 0 THEN je.credit_amount ELSE 0 END) as balance
FROM transactions t
JOIN journal_entries je ON je.transaction_id = t.id
GROUP BY t.id, t.description
HAVING balance != 0;

-- 如果上面的查詢沒有結果，表示所有交易都平衡

-- 6. 備份原始 money 表（可選）
-- CREATE TABLE money_backup AS SELECT * FROM money;

-- 7. 清理臨時表
DROP TABLE temp_transaction_groups;
