<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>分享帳簿</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    .main-title {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 1.5rem;
    }

    .share-card {
      background: #fff;
      border-radius: 18px;
      box-shadow: 0 2px 8px #eee;
      padding: 2.5rem 2rem;
      margin: 2rem auto;
    }

    .table thead th {
      background: #2563eb !important;
      color: #fff !important;
      font-weight: 600 !important;
      border-color: #2563eb !important;
    }

    .table th,
    .table td {
      vertical-align: middle;
      text-align: center;
    }

    .is-disabled {
      color: #aaa;
      pointer-events: none;
    }
  </style>
</head>

<body style="background:#f5f6fa;">
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <div class="main-title mt-5 mb-4">
          <a href="javascript:history.back()"
            style="color:#222;text-decoration:none;font-size:1.5rem;vertical-align:middle;">←</a>
          分享帳簿
        </div>
        <div class="share-card">
          <form class="mb-4" method="get">
            <div class="field is-grouped">
              <div class="control">
                <div class="select">
                  <select name="type">
                    <option value="">記帳日期</option>
                    <option value="other">發票日期</option>
                  </select>
                </div>
              </div>
              <div class="control">
                <input class="input" type="date" name="start_date" placeholder="起始日期">
              </div>
              <div class="control">
                <input class="input" type="date" name="end_date" placeholder="結束日期">
              </div>
              <div class="control">
                <button class="button is-link" type="submit">查詢</button>
              </div>
            </div>
          </form>
          <table class="table is-fullwidth is-hoverable">
            <thead>
              <tr>
                <th>產生日期</th>
                <th>產生類別</th>
                <th>日期起訖</th>
                <th>產生人員</th>
                <th>複製連結</th>
                <th>下載檔案</th>
                <th>到期日</th>
              </tr>
            </thead>
            <tbody>
              {% for row in share_books %}
              <tr>
                <td>{{ row.date }}</td>
                <td>{{ row.type }}</td>
                <td>{{ row.range }}</td>
                <td>{{ row.user }}</td>
                <td>
                  <a class="button is-small is-link" href="{{ row.copy_url }}" target="_blank">複製連結</a>
                </td>
                <td>
                  <a class="button is-small is-info" href="{{ row.download_url }}" target="_blank">下載檔案</a>
                </td>
                <td>
                  {% if row.expired %}
                  <span class="tag is-light">已失效</span>
                  {% else %}
                  <span class="tag is-success">有效</span>
                  {% endif %}
                </td>
              </tr>
              {% else %}
              <tr>
                <td colspan="7" class="has-text-centered">目前沒有分享帳簿紀錄</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
          <nav class="pagination is-centered" role="navigation" aria-label="pagination">
            <ul class="pagination-list">
              <li><a class="pagination-link is-current">1</a></li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>
</body>

</html>