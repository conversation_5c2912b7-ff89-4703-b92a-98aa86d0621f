<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帳戶對帳單</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 導航 -->
                <div class="box mb-5">
                    <h2 class="subtitle">
                        <a href="/?main=我的報表" style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        <i class="fas fa-university"></i> 帳戶對帳單
                    </h2>
                    
                    <!-- 查詢表單 -->
                    <form method="GET" class="field is-grouped">
                        <div class="control">
                            <div class="select">
                                <select name="account_id" required>
                                    <option value="">選擇帳戶</option>
                                    {% for account in accounts %}
                                    <option value="{{ account.id }}" {% if account.id == selected_account_id %}selected{% endif %}>
                                        {{ account.name }} ({{ account.category }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="control">
                            <input class="input" type="date" name="start_date" value="{{ start_date }}" required>
                        </div>
                        <div class="control">
                            <input class="input" type="date" name="end_date" value="{{ end_date }}" required>
                        </div>
                        <div class="control">
                            <button type="submit" class="button is-primary">
                                <span class="icon"><i class="fas fa-search"></i></span>
                                <span>查詢</span>
                            </button>
                        </div>
                    </form>
                </div>

                {% if statement %}
                <!-- 帳戶資訊 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-info-circle"></i> 帳戶資訊
                    </h3>
                    <div class="columns">
                        <div class="column">
                            <table class="table is-fullwidth">
                                <tbody>
                                    <tr>
                                        <th>帳戶名稱</th>
                                        <td>{{ statement.account.name }}</td>
                                    </tr>
                                    <tr>
                                        <th>帳戶類別</th>
                                        <td><span class="tag is-info">{{ statement.account.category }}</span></td>
                                    </tr>
                                    {% if statement.account.bank_name %}
                                    <tr>
                                        <th>銀行名稱</th>
                                        <td>{{ statement.account.bank_name }}</td>
                                    </tr>
                                    {% endif %}
                                    {% if statement.account.account_number %}
                                    <tr>
                                        <th>帳號</th>
                                        <td><code>{{ statement.account.account_number }}</code></td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                        <div class="column">
                            <table class="table is-fullwidth">
                                <tbody>
                                    <tr>
                                        <th>查詢期間</th>
                                        <td>{{ statement.period.start_date }} 至 {{ statement.period.end_date }}</td>
                                    </tr>
                                    <tr>
                                        <th>期初餘額</th>
                                        <td class="has-text-right">{{ "{:,}".format(statement.balances.opening) }}</td>
                                    </tr>
                                    <tr>
                                        <th>期末餘額</th>
                                        <td class="has-text-right has-text-weight-bold">{{ "{:,}".format(statement.balances.closing) }}</td>
                                    </tr>
                                    <tr>
                                        <th>餘額變動</th>
                                        <td class="has-text-right">
                                            <span class="has-text-{{ 'success' if statement.balances.change >= 0 else 'danger' }}">
                                                {{ "{:,}".format(statement.balances.change) }}
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 期間統計 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-chart-bar"></i> 期間統計
                    </h3>
                    <div class="columns">
                        <div class="column">
                            <div class="notification is-success">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ "{:,}".format(statement.period_summary.income) }}</p>
                                                <p class="subtitle">期間收入</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-arrow-up fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="column">
                            <div class="notification is-danger">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ "{:,}".format(statement.period_summary.expense) }}</p>
                                                <p class="subtitle">期間支出</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-arrow-down fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="column">
                            <div class="notification is-info">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ statement.period_summary.transaction_count }}</p>
                                                <p class="subtitle">交易筆數</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-list fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 交易明細 -->
                {% if statement.transactions %}
                <div class="box">
                    <h3 class="subtitle">
                        <i class="fas fa-list-alt"></i> 交易明細
                    </h3>
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>交易名稱</th>
                                    <th>類型</th>
                                    <th>金額</th>
                                    <th>餘額</th>
                                    <th>狀態</th>
                                    <th>備註</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in statement.transactions %}
                                <tr>
                                    <td>{{ transaction.date }}</td>
                                    <td>{{ transaction.name }}</td>
                                    <td>
                                        <span class="tag is-{{ 'success' if transaction.money_type == '收入' else 'danger' }}">
                                            {{ transaction.money_type }}
                                        </span>
                                    </td>
                                    <td class="has-text-right">
                                        <span class="has-text-{{ 'success' if transaction.amount > 0 else 'danger' }}">
                                            {{ "{:,}".format(transaction.amount) }}
                                        </span>
                                    </td>
                                    <td class="has-text-right has-text-weight-bold">
                                        {{ "{:,}".format(transaction.balance) }}
                                    </td>
                                    <td>
                                        <span class="tag is-{{ 'success' if transaction.is_paid else 'warning' }}">
                                            {{ '已付款' if transaction.is_paid else '未付款' }}
                                        </span>
                                    </td>
                                    <td>
                                        <small>{{ transaction.note or '' }}</small>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% else %}
                <div class="box">
                    <div class="notification is-info">
                        <p>所選期間內沒有交易記錄。</p>
                    </div>
                </div>
                {% endif %}

                {% else %}
                <!-- 選擇提示 -->
                <div class="box">
                    <div class="notification is-info">
                        <h4 class="title is-4">
                            <i class="fas fa-info-circle"></i> 請選擇查詢條件
                        </h4>
                        <p>請選擇帳戶和日期範圍來查看對帳單。</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

</body>
</html>