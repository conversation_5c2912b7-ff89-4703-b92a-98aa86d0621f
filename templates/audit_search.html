<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>審計搜尋</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 審計系統導航 -->
                <div class="box mb-5">
                    <h2 class="subtitle">
                        <a href="/" style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        <i class="fas fa-shield-alt"></i> 審計系統
                    </h2>
                    <div class="buttons">
                        <a class="button is-info is-light" href="/audit_dashboard">
                            <span class="icon"><i class="fas fa-tachometer-alt"></i></span>
                            <span>儀表板</span>
                        </a>
                        <a class="button is-primary" href="/audit_search">
                            <span class="icon"><i class="fas fa-search"></i></span>
                            <span>審計搜尋</span>
                        </a>
                        <a class="button is-warning is-light" href="/deleted_records">
                            <span class="icon"><i class="fas fa-trash-restore"></i></span>
                            <span>已刪除記錄</span>
                        </a>
                    </div>
                </div>
                <!-- 搜尋表單 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-search"></i> 搜尋條件
                    </h3>
                    <form id="searchForm">
                        <div class="columns">
                            <div class="column">
                                <div class="field">
                                    <label class="label">表格</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="table" name="table">
                                                <option value="money">收支記錄</option>
                                                <option value="account">帳戶</option>
                                                <option value="transfer">轉帳</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column">
                                <div class="field">
                                    <label class="label">動作類型</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="action_type" name="action_type">
                                                <option value="">全部</option>
                                                <option value="create">建立</option>
                                                <option value="update">修改</option>
                                                <option value="delete">刪除</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column">
                                <div class="field">
                                    <label class="label">用戶名稱</label>
                                    <div class="control">
                                        <input class="input" type="text" id="user" name="user" placeholder="輸入用戶名稱">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column">
                                <div class="field">
                                    <label class="label">&nbsp;</label>
                                    <div class="control">
                                        <button type="submit" class="button is-primary is-fullwidth">
                                            <span class="icon"><i class="fas fa-search"></i></span>
                                            <span>搜尋</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="columns">
                            <div class="column is-half">
                                <div class="field">
                                    <label class="label">開始日期</label>
                                    <div class="control">
                                        <input class="input" type="date" id="start_date" name="start_date">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="column is-half">
                                <div class="field">
                                    <label class="label">結束日期</label>
                                    <div class="control">
                                        <input class="input" type="date" id="end_date" name="end_date">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- 搜尋結果 -->
                <div class="box">
                    <div class="level">
                        <div class="level-left">
                            <div class="level-item">
                                <h3 class="subtitle">
                                    <i class="fas fa-list"></i> 搜尋結果
                                </h3>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <span id="resultCount" class="tag is-info">0 筆記錄</span>
                            </div>
                        </div>
                    </div>
                    
                    <div id="loadingSpinner" class="has-text-centered is-hidden">
                        <button class="button is-loading is-large is-white">載入中</button>
                    </div>
                    
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped" id="resultsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>名稱</th>
                                    <th>類型</th>
                                    <th>金額</th>
                                    <th>建立者</th>
                                    <th>建立時間</th>
                                    <th>修改者</th>
                                    <th>修改時間</th>
                                    <th>版本</th>
                                    <th>狀態</th>
                                </tr>
                            </thead>
                            <tbody id="resultsBody">
                                <tr>
                                    <td colspan="10" class="has-text-centered has-text-grey">請輸入搜尋條件並點擊搜尋</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const searchData = Object.fromEntries(formData);
            
            // 顯示載入動畫
            document.getElementById('loadingSpinner').classList.remove('is-hidden');
            document.getElementById('resultsBody').innerHTML = '';
            
            fetch('/api/audit_search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(searchData)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loadingSpinner').classList.add('is-hidden');
                
                if (data.success) {
                    displayResults(data.records);
                    document.getElementById('resultCount').textContent = `${data.total} 筆記錄`;
                } else {
                    alert('搜尋失敗: ' + data.message);
                }
            })
            .catch(error => {
                document.getElementById('loadingSpinner').classList.add('is-hidden');
                console.error('Error:', error);
                alert('搜尋時發生錯誤');
            });
        });
        
        function displayResults(records) {
            const tbody = document.getElementById('resultsBody');
            
            if (records.length === 0) {
                tbody.innerHTML = '<tr><td colspan="10" class="has-text-centered has-text-grey">沒有找到符合條件的記錄</td></tr>';
                return;
            }
            
            tbody.innerHTML = records.map(record => `
                <tr>
                    <td>${record.id}</td>
                    <td>${record.name || ''}</td>
                    <td>${record.money_type || ''}</td>
                    <td>${record.total ? record.total.toLocaleString() : ''}</td>
                    <td>${record.created_by || ''}</td>
                    <td>${record.created_at ? new Date(record.created_at).toLocaleString() : ''}</td>
                    <td>${record.updated_by || ''}</td>
                    <td>${record.updated_at ? new Date(record.updated_at).toLocaleString() : ''}</td>
                    <td>${record.version || ''}</td>
                    <td>
                        ${record.is_deleted ? 
                            '<span class="tag is-danger">已刪除</span>' : 
                            '<span class="tag is-success">正常</span>'
                        }
                    </td>
                </tr>
            `).join('');
        }
    </script>
</body>
</html>