<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>編輯收支對象</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/payment_identity_list"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        編輯收支對象
                    </h1>
                </div>
                <div class="box">
                    <h2 class="subtitle is-5">收支對象資訊</h2>
                    <form method="post">
                        <div class="field">
                            <label class="label">對象類別</label>
                            <div class="control">
                                <div class="select is-fullwidth">
                                    <select name="type_id">
                                        <option value="">-- 請選擇對象類別（可選） --</option>
                                        {% for type in types %}
                                        <option value="{{ type.id }}" {% if identity.type_id==type.id %}selected{% endif %}>{{ type.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">公司名稱</label>
                            <div class="control">
                                <input class="input" type="text" name="name" value="{{ identity.name }}">
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">公司統編</label>
                            <div class="control">
                                <input class="input" type="text" name="tax_id" value="{{ identity.tax_id }}">
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">銀行代碼</label>
                            <div class="control">
                                <input class="input" type="text" name="bank_code" value="{{ identity.bank_code }}">
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">銀行帳號</label>
                            <div class="control">
                                <input class="input" type="text" name="bank_account"
                                    value="{{ identity.bank_account }}">
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">聯絡人姓名</label>
                            <div class="control">
                                <input class="input" type="text" name="contact" value="{{ identity.contact }}">
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">手機</label>
                            <div class="control">
                                <input class="input" type="text" name="mobile" value="{{ identity.mobile }}">
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">Line ID</label>
                            <div class="control">
                                <input class="input" type="text" name="line" value="{{ identity.line }}">
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">備註</label>
                            <div class="control">
                                <input class="input" type="text" name="note" value="{{ identity.note }}">
                            </div>
                        </div>
                        <div class="field">
                            <button class="button is-primary" type="submit">儲存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>

</html>