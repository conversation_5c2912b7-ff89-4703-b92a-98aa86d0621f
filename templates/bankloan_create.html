{# 新增銀行借款表單頁 #}
<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <title>新增銀行借款</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }


    .main-box {
      max-width: 1100px;
      margin: 2rem auto;
    }

    .upload-box {
      border: 2px dashed #b5b5b5;
      border-radius: 6px;
      text-align: center;
      padding: 1.5rem 0.5rem;
      color: #888;
      cursor: pointer;
    }

    .action-bar {
      display: flex;
      gap: 1rem;
      align-items: center;
      margin-bottom: 1rem;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <!-- 頁面標題 -->
        <div class="mb-4">
          <h1 class="title is-4">
            <a href="/?main=資金管理"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            新增銀行借款
          </h1>
        </div>
        <div class="main-box box">
          <div class="is-flex is-align-items-center mb-4">

            <a href="/?main=資金管理" class="mr-3" style="color:#222;text-decoration:none;">&larr; {{ '編輯銀行借款' if edit_mode
              else '新增銀行借款' }}</a>

            <button class="button is-info is-light ml-auto">頁面指南</button>
          </div>
          <form method="POST">
            <div class="columns">
              <!-- 左側表單區域 (2/3) -->
              <div class="column is-two-thirds">
                <!-- 第一行：銀行名稱、本金、手續費 -->
                <div class="columns">
                  <div class="column">
                    <div class="field">
                      <label class="label">銀行 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="text" name="bank_name" placeholder="請輸入銀行名稱"
                          value="{{ loan_record.bank_name if edit_mode and loan_record else '' }}" required>
                      </div>
                    </div>
                  </div>
                  <div class="column">
                    <div class="field">
                      <label class="label">本金</label>
                      <div class="control">
                        <input class="input" type="number" name="principal"
                          value="{{ loan_record.principal if edit_mode and loan_record else 0 }}" id="principal">
                      </div>
                    </div>
                  </div>
                  <div class="column">
                    <div class="field">
                      <label class="label">手續費</label>
                      <div class="control">
                        <input class="input" type="number" name="fee"
                          value="{{ loan_record.fee if edit_mode and loan_record else 0 }}" id="fee">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 第二行：借款會計項目和借/還款帳戶 -->
                <div class="columns">
                  <div class="column">
                    <div class="field">
                      <label class="label">借款會計項目 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <div class="select is-fullwidth">
                          <select name="subject_code" required>
                            <option value="">請選擇會計項目</option>
                            <option value="2115" {{ 'selected' if edit_mode and loan_record and
                              loan_record.subject_code=='2115' else '' }}>2115 銀行借款</option>
                            <option value="2420" {{ 'selected' if edit_mode and loan_record and
                              loan_record.subject_code=='2420' else '' }}>2420 長期借款</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="column">
                    <div class="field">
                      <label class="label">借/還款帳戶</label>
                      <div class="control">
                        <div class="select is-fullwidth">
                          <select name="repayment_account" id="repayment-account">
                            <option value="">請選擇還款帳戶</option>
                            {% for account in accounts %}
                            <option value="{{ account.id }}" {{ 'selected' if edit_mode and loan_record and
                              loan_record.repayment_account_id==account.id else '' }}>{{ account.display_name }}
                            </option>
                            {% endfor %}
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 第三行：入帳金額、分期數、固定還款月 -->
                <div class="columns">
                  <div class="column">
                    <div class="field">
                      <label class="label">入帳金額</label>
                      <div class="control">
                        <input class="input" type="number"
                          value="{{ loan_record.deposit_amount if edit_mode and loan_record else 0 }}"
                          id="deposit-amount" disabled>
                      </div>
                    </div>
                  </div>
                  <div class="column">
                    <div class="field">
                      <label class="label">分期數</label>
                      <div class="control">
                        <input class="input" type="number" name="installments"
                          value="{{ loan_record.installments if edit_mode and loan_record else 0 }}" id="installments">
                      </div>
                    </div>
                  </div>
                  <div class="column">
                    <div class="field">
                      <label class="label">固定還款月同(數)</label>
                      <div class="control">
                        <input class="input" type="number" name="fixed_repayment_day"
                          value="{{ loan_record.fixed_repayment_day if edit_mode and loan_record else 1 }}">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 第四行：借款期間和借款入帳日期 -->
                <div class="columns">
                  <div class="column">
                    <div class="field">
                      <label class="label">借款期間</label>
                      <div class="field has-addons">
                        <div class="control">
                          <input class="input" type="date" name="loan_start_date"
                            value="{{ loan_record.loan_start_date.strftime('%Y-%m-%d') if edit_mode and loan_record and loan_record.loan_start_date else '2025-07-12' }}">
                        </div>
                        <div class="control">
                          <span class="button is-static">至</span>
                        </div>
                        <div class="control">
                          <input class="input" type="date" name="loan_end_date"
                            value="{{ loan_record.loan_end_date.strftime('%Y-%m-%d') if edit_mode and loan_record and loan_record.loan_end_date else '2025-12-12' }}">
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="column">
                    <div class="field">
                      <label class="label">借款入帳日期 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="date" name="loan_date"
                          value="{{ loan_record.loan_date.strftime('%Y-%m-%d') if edit_mode and loan_record and loan_record.loan_date else '2025-07-12' }}"
                          required>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="field">
                  <label class="label">備註</label>
                  <div class="control">
                    <textarea class="textarea" name="note"
                      placeholder="請輸入備註">{{ loan_record.note if edit_mode and loan_record else '' }}</textarea>
                  </div>
                </div>
                <div class="field">
                  <label class="label">上傳憑證</label>
                  <div class="control">
                    <div class="upload-box">
                      <span class="icon"><i class="fas fa-upload"></i></span>
                      <span>上傳憑證</span>
                      <input type="file" style="display:none;">
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右側計算和紀錄區域 (1/3) -->
              <div class="column is-one-third">
                <div class="field">
                  <label class="label">借款支付計算</label>
                  <div class="box has-background-light">
                    <div class="content">
                      <div class="is-flex is-justify-content-space-between">
                        <span>本金：</span>
                        <span id="calc-principal">$0</span>
                      </div>
                      <div class="is-flex is-justify-content-space-between">
                        <span>手續費：</span>
                        <span id="calc-fee">$0</span>
                      </div>
                      <div class="is-flex is-justify-content-space-between">
                        <span>累計利息：</span>
                        <span id="calc-interest">$0</span>
                      </div>
                      <hr>
                      <div class="is-flex is-justify-content-space-between has-text-weight-bold">
                        <span>總借款支付金額：</span>
                        <span id="calc-total">$0</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="field">
                  <label class="label">還款紀錄</label>
                  <div class="box has-background-light">
                    <div class="content">
                      <div class="table-container" style="max-height: 300px; overflow-y: auto;">
                        <table class="table is-fullwidth is-striped">
                          <thead>
                            <tr>
                              <th>還款日期</th>
                              <th>利息</th>
                              <th>本金</th>
                            </tr>
                          </thead>
                          <tbody id="repayment-records">
                            <tr>
                              <td colspan="3" class="has-text-centered has-text-grey">
                                暫無還款紀錄
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      <hr>
                      <div class="is-flex is-justify-content-space-between has-text-weight-bold">
                        <span>已還款總額：</span>
                        <span id="total-repaid">$0</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="field is-grouped is-grouped-right mt-4">
              <p class="control">
                <a href="/bankloan/list" class="button is-light">取消</a>
              </p>
              <p class="control">
                <button class="button is-link" type="submit">儲存</button>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 獲取輸入元素
      const principalInput = document.getElementById('principal');
      const feeInput = document.getElementById('fee');
      const depositAmountInput = document.getElementById('deposit-amount');
      const installmentsInput = document.getElementById('installments');

      // 獲取顯示元素
      const calcPrincipal = document.getElementById('calc-principal');
      const calcFee = document.getElementById('calc-fee');
      const calcInterest = document.getElementById('calc-interest');
      const calcTotal = document.getElementById('calc-total');

      // 計算函數
      function updateCalculations() {
        const principal = parseFloat(principalInput.value) || 0;
        const fee = parseFloat(feeInput.value) || 0;
        const installments = parseInt(installmentsInput.value) || 0;

        // 計算入帳金額 (本金 - 手續費)
        const depositAmount = principal - fee;
        depositAmountInput.value = depositAmount;

        // 簡單利息計算 (假設年利率 3%)
        const annualRate = 0.03;
        const monthlyRate = annualRate / 12;
        const totalInterest = principal * monthlyRate * installments;

        // 總借款支付金額
        const totalPayment = principal + fee + totalInterest;

        // 更新顯示
        calcPrincipal.textContent = `$${principal.toLocaleString()}`;
        calcFee.textContent = `$${fee.toLocaleString()}`;
        calcInterest.textContent = `$${totalInterest.toLocaleString()}`;
        calcTotal.textContent = `$${totalPayment.toLocaleString()}`;

        // 生成還款紀錄
        generateRepaymentSchedule(principal, totalInterest, installments);
      }

      // 生成還款紀錄
      function generateRepaymentSchedule(principal, totalInterest, installments) {
        const tbody = document.getElementById('repayment-records');
        tbody.innerHTML = '';

        if (installments <= 0) {
          tbody.innerHTML = '<tr><td colspan="3" class="has-text-centered has-text-grey">暫無還款紀錄</td></tr>';
          document.getElementById('total-repaid').textContent = '$0';
          return;
        }

        const monthlyPrincipal = principal / installments;
        const monthlyInterest = totalInterest / installments;
        const currentDate = new Date();
        let totalRepaid = 0;

        for (let i = 1; i <= installments; i++) {
          const repaymentDate = new Date(currentDate);
          repaymentDate.setMonth(repaymentDate.getMonth() + i);

          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${repaymentDate.toLocaleDateString('zh-TW')}</td>
            <td>$${monthlyInterest.toLocaleString()}</td>
            <td>$${monthlyPrincipal.toLocaleString()}</td>
          `;
          tbody.appendChild(row);

          totalRepaid += monthlyPrincipal + monthlyInterest;
        }

        document.getElementById('total-repaid').textContent = `$${totalRepaid.toLocaleString()}`;
      }

      // 綁定事件監聽器
      principalInput.addEventListener('input', updateCalculations);
      feeInput.addEventListener('input', updateCalculations);
      installmentsInput.addEventListener('input', updateCalculations);

      // 初始計算
      updateCalculations();
    });
  </script>
</body>

</html>