<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新支出記錄</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .sidebar {
            width: 200px;
            font-size: 20px;
        }

        .main-content {
            margin-left: 220px;
            padding: 20px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .tax-info {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
    </style>
</head>

<body>
    <!-- 側邊欄 -->
    {% include 'sidebar.html' %}

    <!-- 主要內容 -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="columns">
                <div class="column">
                    <div class="card">
                        <div class="card-header">
                            <h1 class="card-title">
                                <i class="fas fa-minus-circle"></i> 新支出記錄（含稅處理）
                            </h1>
                        </div>
                        <div class="card-content">
                            <!-- 顯示錯誤訊息 -->
                            {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                            {% for category, message in messages %}
                            <div class="notification is-{{ 'danger' if category == 'error' else 'success' }}">
                                <button class="delete"></button>
                                {{ message }}
                            </div>
                            {% endfor %}
                            {% endif %}
                            {% endwith %}

                            <form method="POST">
                                <!-- 基本資訊 -->
                                <div class="form-section">
                                    <h3 class="title is-5">基本資訊</h3>
                                    <div class="columns">
                                        <div class="column is-6">
                                            <div class="field">
                                                <label class="label">記帳日期 *</label>
                                                <div class="control">
                                                    <input type="date" name="a_time" class="input" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="column is-6">
                                            <div class="field">
                                                <label class="label">支出名稱 *</label>
                                                <div class="control">
                                                    <input type="text" name="name" class="input" placeholder="例：辦公用品採購"
                                                        required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 金額資訊 -->
                                <div class="form-section">
                                    <h3 class="title is-5">金額資訊</h3>
                                    <div class="columns">
                                        <div class="column is-4">
                                            <div class="field">
                                                <label class="label">總金額（含稅）*</label>
                                                <div class="control">
                                                    <input type="number" name="total" class="input" placeholder="105"
                                                        required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="column is-4">
                                            <div class="field">
                                                <label class="label">營業稅額</label>
                                                <div class="control">
                                                    <input type="number" name="tax" class="input" placeholder="5">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="column is-4">
                                            <div class="field">
                                                <label class="label">手續費</label>
                                                <div class="control">
                                                    <input type="number" name="extra_fee" class="input" placeholder="0">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="tax-info">
                                        <p><strong>稅額處理說明：</strong></p>
                                        <ul>
                                            <li>總金額：實際支付的含稅金額</li>
                                            <li>營業稅額：可扣抵的進項稅額（將記錄到 1290 進項稅額科目）</li>
                                            <li>費用金額：總金額 - 營業稅額</li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- 會計科目 -->
                                <div class="form-section">
                                    <h3 class="title is-5">會計科目</h3>
                                    <div class="columns">
                                        <div class="column is-6">
                                            <div class="field">
                                                <label class="label">費用科目 *</label>
                                                <div class="control">
                                                    <div class="select is-fullwidth">
                                                        <select name="subject_code" required>
                                                            <option value="">請選擇費用科目</option>
                                                            {% for subject in subjects %}
                                                            <option value="{{ subject.code }}">{{ subject.code }} {{
                                                                subject.name }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="column is-6">
                                            <div class="field">
                                                <label class="label">付款帳戶 *</label>
                                                <div class="control">
                                                    <div class="select is-fullwidth">
                                                        <select name="account_id" required>
                                                            <option value="">請選擇付款帳戶</option>
                                                            {% for account in accounts %}
                                                            <option value="{{ account.id }}">{{ account.name }} ({{
                                                                account.category }})</option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 其他資訊 -->
                                <div class="form-section">
                                    <h3 class="title is-5">其他資訊</h3>
                                    <div class="columns">
                                        <div class="column is-6">
                                            <div class="field">
                                                <label class="label">收支對象</label>
                                                <div class="control">
                                                    <div class="select is-fullwidth">
                                                        <select name="payment_identity_id">
                                                            <option value="">請選擇收支對象</option>
                                                            {% for identity in payment_identities %}
                                                            <option value="{{ identity.id }}">{{ identity.name }}
                                                            </option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="column is-6">
                                            <div class="field">
                                                <label class="label">發票號碼</label>
                                                <div class="control">
                                                    <input type="text" name="number" class="input"
                                                        placeholder="AB12345678">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="field">
                                        <label class="label">備註</label>
                                        <div class="control">
                                            <textarea name="note" class="textarea" placeholder="其他說明..."></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- 提交按鈕 -->
                                <div class="field is-grouped">
                                    <div class="control">
                                        <button type="submit" class="button is-primary is-large">
                                            <i class="fas fa-save"></i> 儲存支出記錄
                                        </button>
                                    </div>
                                    <div class="control">
                                        <a href="{{ url_for('new_income_expense.new_expense_list') }}"
                                            class="button is-light is-large">
                                            <i class="fas fa-list"></i> 查看支出列表
                                        </a>
                                    </div>
                                </div>
                            </form>

                            <!-- 分錄預覽 -->
                            <div class="notification is-info is-light">
                                <p><strong>會計分錄預覽：</strong></p>
                                <p>當您填入金額後，系統將自動產生以下分錄：</p>
                                <ul>
                                    <li><strong>借：</strong>選擇的費用科目（總金額 - 營業稅額）</li>
                                    <li><strong>借：</strong>進項稅額 1290（營業稅額）</li>
                                    <li><strong>貸：</strong>選擇的付款帳戶（總金額）</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>

</html>