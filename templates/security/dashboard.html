<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全監控儀表板</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .security-card {
            border-left: 4px solid #00d1b2;
            transition: transform 0.2s;
        }
        .security-card:hover {
            transform: translateY(-2px);
        }
        .risk-high { border-color: #ff3860 !important; }
        .risk-medium { border-color: #ffdd57 !important; }
        .risk-low { border-color: #00d1b2 !important; }
        
        .alert-item {
            border-left: 3px solid;
            padding: 1rem;
            margin-bottom: 0.5rem;
        }
        .alert-critical { border-color: #ff3860; background: #fff5f5; }
        .alert-high { border-color: #ff7875; background: #fff8f8; }
        .alert-medium { border-color: #ffdd57; background: #fffbeb; }
        .alert-low { border-color: #00d1b2; background: #f0fdfa; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .chart-container {
            height: 300px;
            position: relative;
        }
        
        .ip-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .activity-log {
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        
        .timestamp {
            color: #666;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師 - 安全監控中心</h1>
        
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            
            <div class="column">
                <!-- 頁面標題和風險評分 -->
                <div class="columns is-vcentered mb-5">
                    <div class="column">
                        <h1 class="title is-4">
                            <i class="fas fa-shield-alt mr-2"></i>
                            安全監控儀表板
                        </h1>
                        <p class="subtitle is-6">即時安全狀態監控和威脅分析</p>
                    </div>
                    <div class="column is-narrow">
                        <div class="box has-text-centered">
                            <p class="heading">風險分數</p>
                            <p class="title is-3" id="risk-score">{{ data.risk_score or 0 }}</p>
                            <p class="subtitle is-6" id="risk-level">計算中...</p>
                        </div>
                    </div>
                </div>
                
                <!-- 統計卡片 -->
                <div class="stats-grid mb-5">
                    <div class="card security-card">
                        <div class="card-content has-text-centered">
                            <div class="content">
                                <p class="title is-4">{{ data.stats.total_login_attempts }}</p>
                                <p class="subtitle is-6">總登入嘗試</p>
                                <span class="icon">
                                    <i class="fas fa-sign-in-alt fa-2x"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card security-card risk-medium">
                        <div class="card-content has-text-centered">
                            <div class="content">
                                <p class="title is-4">{{ data.stats.failed_login_attempts }}</p>
                                <p class="subtitle is-6">失敗登入</p>
                                <span class="icon">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card security-card risk-high">
                        <div class="card-content has-text-centered">
                            <div class="content">
                                <p class="title is-4">{{ data.stats.blocked_ips_count }}</p>
                                <p class="subtitle is-6">封鎖 IP</p>
                                <span class="icon">
                                    <i class="fas fa-ban fa-2x"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card security-card risk-medium">
                        <div class="card-content has-text-centered">
                            <div class="content">
                                <p class="title is-4">{{ data.stats.suspicious_activities_count }}</p>
                                <p class="subtitle is-6">可疑活動</p>
                                <span class="icon">
                                    <i class="fas fa-user-secret fa-2x"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card security-card risk-high">
                        <div class="card-content has-text-centered">
                            <div class="content">
                                <p class="title is-4">{{ data.stats.alerts_triggered }}</p>
                                <p class="subtitle is-6">觸發告警</p>
                                <span class="icon">
                                    <i class="fas fa-bell fa-2x"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="columns">
                    <!-- 最近告警 -->
                    <div class="column is-half">
                        <div class="card">
                            <header class="card-header">
                                <p class="card-header-title">
                                    <i class="fas fa-exclamation-circle mr-2"></i>
                                    最近告警
                                </p>
                                <a href="#" class="card-header-icon" onclick="refreshAlerts()">
                                    <span class="icon">
                                        <i class="fas fa-sync-alt"></i>
                                    </span>
                                </a>
                            </header>
                            <div class="card-content">
                                <div id="recent-alerts">
                                    {% for alert in data.recent_alerts %}
                                    <div class="alert-item alert-{{ alert.details.severity|lower or 'low' }}">
                                        <div class="level">
                                            <div class="level-left">
                                                <div class="level-item">
                                                    <div>
                                                        <strong>{{ alert.type }}</strong><br>
                                                        <span>{{ alert.description }}</span><br>
                                                        <span class="timestamp">{{ alert.timestamp }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 封鎖的 IP -->
                    <div class="column is-half">
                        <div class="card">
                            <header class="card-header">
                                <p class="card-header-title">
                                    <i class="fas fa-ban mr-2"></i>
                                    封鎖的 IP 地址
                                </p>
                                <a href="#" class="card-header-icon" onclick="refreshBlockedIPs()">
                                    <span class="icon">
                                        <i class="fas fa-sync-alt"></i>
                                    </span>
                                </a>
                            </header>
                            <div class="card-content">
                                <div class="ip-list" id="blocked-ips">
                                    {% for ip, info in data.blocked_ips.items() %}
                                    <div class="level is-mobile">
                                        <div class="level-left">
                                            <div class="level-item">
                                                <div>
                                                    <strong>{{ ip }}</strong><br>
                                                    <span class="is-size-7">{{ info.reason }}</span><br>
                                                    <span class="timestamp">封鎖於: {{ info.blocked_at }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="level-right">
                                            <div class="level-item">
                                                <button class="button is-small is-warning" 
                                                        onclick="unblockIP('{{ ip }}')">
                                                    解封
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="columns">
                    <!-- 登入嘗試日誌 -->
                    <div class="column is-half">
                        <div class="card">
                            <header class="card-header">
                                <p class="card-header-title">
                                    <i class="fas fa-history mr-2"></i>
                                    最近登入嘗試
                                </p>
                                <a href="#" class="card-header-icon" onclick="refreshLoginAttempts()">
                                    <span class="icon">
                                        <i class="fas fa-sync-alt"></i>
                                    </span>
                                </a>
                            </header>
                            <div class="card-content">
                                <div class="activity-log" id="login-attempts">
                                    載入中...
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 可疑活動 -->
                    <div class="column is-half">
                        <div class="card">
                            <header class="card-header">
                                <p class="card-header-title">
                                    <i class="fas fa-user-secret mr-2"></i>
                                    可疑活動
                                </p>
                                <a href="#" class="card-header-icon" onclick="refreshSuspiciousActivities()">
                                    <span class="icon">
                                        <i class="fas fa-sync-alt"></i>
                                    </span>
                                </a>
                            </header>
                            <div class="card-content">
                                <div class="activity-log" id="suspicious-activities">
                                    載入中...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 操作按鈕 -->
                <div class="buttons is-centered mt-5">
                    <button class="button is-info" onclick="exportLogs()">
                        <span class="icon">
                            <i class="fas fa-download"></i>
                        </span>
                        <span>匯出安全日誌</span>
                    </button>
                    
                    <button class="button is-warning" onclick="generateReport()">
                        <span class="icon">
                            <i class="fas fa-file-alt"></i>
                        </span>
                        <span>生成安全報告</span>
                    </button>
                    
                    <button class="button is-success" onclick="refreshAll()">
                        <span class="icon">
                            <i class="fas fa-sync-alt"></i>
                        </span>
                        <span>重新整理全部</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 自動重新整理間隔（30秒）
        const REFRESH_INTERVAL = 30000;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshAll();
            
            // 設置自動重新整理
            setInterval(refreshAll, REFRESH_INTERVAL);
        });
        
        function refreshAll() {
            refreshRiskScore();
            refreshLoginAttempts();
            refreshSuspiciousActivities();
            refreshBlockedIPs();
            refreshAlerts();
        }
        
        function refreshRiskScore() {
            fetch('/api/security/risk-assessment')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('risk-score').textContent = data.risk_score;
                    document.getElementById('risk-level').textContent = data.risk_level;
                    
                    // 根據風險等級設置顏色
                    const scoreElement = document.getElementById('risk-score');
                    scoreElement.className = 'title is-3 ';
                    if (data.risk_level === 'HIGH') {
                        scoreElement.className += 'has-text-danger';
                    } else if (data.risk_level === 'MEDIUM') {
                        scoreElement.className += 'has-text-warning';
                    } else {
                        scoreElement.className += 'has-text-success';
                    }
                })
                .catch(console.error);
        }
        
        function refreshLoginAttempts() {
            fetch('/api/security/login-attempts')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('login-attempts');
                    if (data.length === 0) {
                        container.innerHTML = '<p class="has-text-grey">暫無登入記錄</p>';
                        return;
                    }
                    
                    container.innerHTML = data.slice(0, 20).map(attempt => `
                        <div class="mb-2">
                            <span class="tag ${attempt.success ? 'is-success' : 'is-danger'} is-small">
                                ${attempt.success ? '成功' : '失敗'}
                            </span>
                            <strong>${attempt.username}</strong> 
                            從 <code>${attempt.ip}</code>
                            <br>
                            <span class="timestamp">${new Date(attempt.timestamp).toLocaleString()}</span>
                        </div>
                        <hr class="my-2">
                    `).join('');
                })
                .catch(console.error);
        }
        
        function refreshSuspiciousActivities() {
            fetch('/api/security/suspicious-activities?limit=20')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('suspicious-activities');
                    if (data.length === 0) {
                        container.innerHTML = '<p class="has-text-grey">暫無可疑活動</p>';
                        return;
                    }
                    
                    container.innerHTML = data.map(activity => `
                        <div class="mb-2">
                            <span class="tag is-warning is-small">${activity.type}</span>
                            <br>
                            <strong>${activity.description}</strong>
                            <br>
                            <span class="timestamp">${new Date(activity.timestamp).toLocaleString()}</span>
                        </div>
                        <hr class="my-2">
                    `).join('');
                })
                .catch(console.error);
        }
        
        function refreshBlockedIPs() {
            fetch('/api/security/blocked-ips')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('blocked-ips');
                    const ips = Object.keys(data);
                    
                    if (ips.length === 0) {
                        container.innerHTML = '<p class="has-text-grey">暫無封鎖的 IP</p>';
                        return;
                    }
                    
                    container.innerHTML = ips.map(ip => `
                        <div class="level is-mobile">
                            <div class="level-left">
                                <div class="level-item">
                                    <div>
                                        <strong>${ip}</strong><br>
                                        <span class="is-size-7">${data[ip].reason}</span><br>
                                        <span class="timestamp">剩餘: ${Math.ceil(data[ip].remaining_hours || 0)} 小時</span>
                                    </div>
                                </div>
                            </div>
                            <div class="level-right">
                                <div class="level-item">
                                    <button class="button is-small is-warning" onclick="unblockIP('${ip}')">
                                        解封
                                    </button>
                                </div>
                            </div>
                        </div>
                        <hr>
                    `).join('');
                })
                .catch(console.error);
        }
        
        function refreshAlerts() {
            // 這個功能可以擴展為顯示最新的告警
            // 暫時使用現有數據
        }
        
        function unblockIP(ip) {
            if (!confirm(`確定要解除封鎖 IP ${ip} 嗎？`)) return;
            
            fetch('/api/security/unblock-ip', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ ip: ip })
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    refreshBlockedIPs();
                } else {
                    alert('操作失敗: ' + (data.error || '未知錯誤'));
                }
            })
            .catch(console.error);
        }
        
        function exportLogs() {
            fetch('/api/security/export-logs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ type: 'all' })
            })
            .then(response => response.json())
            .then(data => {
                // 創建下載連結
                const blob = new Blob([JSON.stringify(data, null, 2)], 
                                     { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `security_logs_${new Date().toISOString().slice(0,10)}.json`;
                a.click();
                window.URL.revokeObjectURL(url);
            })
            .catch(console.error);
        }
        
        function generateReport() {
            alert('安全報告功能開發中...');
        }
    </script>
</body>
</html>