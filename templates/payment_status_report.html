<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>付款狀態報表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar {
            width: 200px;
            font-size: 20px;
        }

        .status-overdue {
            color: #e74c3c;
            font-weight: bold;
        }

        .status-upcoming {
            color: #f39c12;
            font-weight: bold;
        }

        .status-paid {
            color: #27ae60;
            font-weight: bold;
        }

        .status-normal {
            color: #3498db;
        }

        .amount-large {
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>
</head>

<body style="background:#f5f6fa;">
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 導航 -->
                <div class="box mb-5">
                    <h2 class="subtitle">
                        <a href="/?main=我的報表"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        <i class="fas fa-credit-card"></i> 付款狀態報表
                    </h2>
                </div>

                {% if report %}
                <!-- 付款狀態摘要 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-chart-pie"></i> 付款狀態摘要
                    </h3>
                    <div class="columns">
                        <div class="column">
                            <div class="notification is-danger">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ report.overdue_count or 0 }}</p>
                                                <p class="subtitle">逾期未付</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <p class="has-text-weight-semibold">金額: {{ "{:,}".format(report.overdue_amount or 0) }}</p>
                            </div>
                        </div>

                        <div class="column">
                            <div class="notification is-warning">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ report.upcoming_count or 0 }}</p>
                                                <p class="subtitle">即將到期</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <p class="has-text-weight-semibold">金額: {{ "{:,}".format(report.upcoming_amount or 0) }}</p>
                            </div>
                        </div>

                        <div class="column">
                            <div class="notification is-success">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ report.paid_count or 0 }}</p>
                                                <p class="subtitle">已付款</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <p class="has-text-weight-semibold">金額: {{ "{:,}".format(report.paid_amount or 0) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 逾期未付款項 -->
                {% if report.overdue_payments %}
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-exclamation-triangle has-text-danger"></i> 逾期未付款項
                    </h3>
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>應付日期</th>
                                    <th>項目</th>
                                    <th>收支對象</th>
                                    <th>金額</th>
                                    <th>逾期天數</th>
                                    <th>狀態</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in report.overdue_payments %}
                                <tr>
                                    <td>{{ payment.should_paid_date.strftime('%Y/%m/%d') if payment.should_paid_date else '-' }}</td>
                                    <td>{{ payment.name }}</td>
                                    <td>{{ payment.payment_identity.name if payment.payment_identity else '-' }}</td>
                                    <td class="has-text-right amount-large status-overdue">{{ "{:,}".format(payment.total) }}</td>
                                    <td class="status-overdue">{{ payment.overdue_days }}天</td>
                                    <td>
                                        <span class="tag is-danger">逾期</span>
                                    </td>
                                    <td>
                                        <button class="button is-small is-success" onclick="markAsPaid({{ payment.id }})">
                                            <span class="icon"><i class="fas fa-check"></i></span>
                                            <span>標記已付</span>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                <!-- 即將到期款項 -->
                {% if report.upcoming_payments %}
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-clock has-text-warning"></i> 即將到期款項（7天內）
                    </h3>
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>應付日期</th>
                                    <th>項目</th>
                                    <th>收支對象</th>
                                    <th>金額</th>
                                    <th>剩餘天數</th>
                                    <th>狀態</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in report.upcoming_payments %}
                                <tr>
                                    <td>{{ payment.should_paid_date.strftime('%Y/%m/%d') if payment.should_paid_date else '-' }}</td>
                                    <td>{{ payment.name }}</td>
                                    <td>{{ payment.payment_identity.name if payment.payment_identity else '-' }}</td>
                                    <td class="has-text-right amount-large status-upcoming">{{ "{:,}".format(payment.total) }}</td>
                                    <td class="status-upcoming">{{ payment.days_until_due }}天</td>
                                    <td>
                                        <span class="tag is-warning">即將到期</span>
                                    </td>
                                    <td>
                                        <button class="button is-small is-success" onclick="markAsPaid({{ payment.id }})">
                                            <span class="icon"><i class="fas fa-check"></i></span>
                                            <span>標記已付</span>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                <!-- 付款狀態圖表 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-chart-bar"></i> 付款狀態分析
                    </h3>
                    <canvas id="paymentStatusChart" width="400" height="200"></canvas>
                </div>

                {% else %}
                <!-- 無資料提示 -->
                <div class="box">
                    <div class="notification is-info">
                        <h4 class="title is-4">
                            <i class="fas fa-info-circle"></i> 無付款資料
                        </h4>
                        <p>目前沒有需要追蹤的付款項目。</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        {% if report %}
        // 付款狀態圖表
        const ctx = document.getElementById('paymentStatusChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['逾期未付', '即將到期', '已付款'],
                datasets: [{
                    data: [
                        {{ report.overdue_count or 0 }},
                        {{ report.upcoming_count or 0 }},
                        {{ report.paid_count or 0 }}
                    ],
                    backgroundColor: [
                        'rgba(231, 76, 60, 0.8)',   // 紅色 - 逾期
                        'rgba(243, 156, 18, 0.8)',  // 橙色 - 即將到期
                        'rgba(39, 174, 96, 0.8)'    // 綠色 - 已付款
                    ],
                    borderColor: [
                        'rgba(231, 76, 60, 1)',
                        'rgba(243, 156, 18, 1)',
                        'rgba(39, 174, 96, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '付款狀態分布'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // 標記為已付款
        function markAsPaid(paymentId) {
            if (confirm('確定要標記此項目為已付款嗎？')) {
                fetch(`/api/payments/${paymentId}/mark_paid`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('已標記為已付款');
                        location.reload();
                    } else {
                        alert('操作失敗：' + data.error);
                    }
                })
                .catch(error => {
                    alert('操作失敗：' + error);
                });
            }
        }
        {% endif %}
    </script>
</body>

</html>
