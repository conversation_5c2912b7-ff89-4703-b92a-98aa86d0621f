<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>試算表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .sidebar {
            width: 200px;
            font-size: 20px;
        }

        .main-content {
            margin-left: 220px;
            padding: 20px;
        }

        .trial-balance-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .trial-balance-table th,
        .trial-balance-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: left;
        }

        .trial-balance-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .amount-cell {
            text-align: right;
            font-family: monospace;
        }

        .category-header {
            background-color: #e8f4f8;
            font-weight: bold;
        }

        .total-row {
            background-color: #e9ecef;
            font-weight: bold;
            border-top: 2px solid #333;
        }

        .balance-indicator {
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .balanced {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .unbalanced {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .date-filter {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .zero-balance {
            color: #999;
        }
    </style>
</head>

<body>
    <h1 class="title has-text-centered">印錢大師</h1>
    <!-- 側邊欄 -->
    {% include 'sidebar.html' %}

    <!-- 主要內容 -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="columns">
                <div class="column">
                    <div class="card">
                        <div class="card-header">
                            <h1 class="card-title">
                                <i class="fas fa-calculator"></i> 試算表
                            </h1>
                        </div>
                        <div class="card-content">
                            <!-- 日期篩選 -->
                            <div class="date-filter">
                                <form method="GET" class="field is-grouped">
                                    <div class="control">
                                        <label class="label">截止日期：</label>
                                        <input type="date" name="as_of_date" value="{{ as_of_date }}" class="input">
                                    </div>
                                    <div class="control">
                                        <button type="submit" class="button is-primary">
                                            <i class="fas fa-search"></i> 查詢
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- 平衡狀態指示器 -->
                            <div class="balance-indicator {{ 'balanced' if totals.is_balanced else 'unbalanced' }}">
                                {% if totals.is_balanced %}
                                <i class="fas fa-check-circle"></i> 試算表平衡
                                {% else %}
                                <i class="fas fa-exclamation-triangle"></i> 試算表不平衡
                                {% endif %}
                                <div class="mt-2">
                                    <strong>借方總計：</strong> {{ "{:,}".format(totals.total_debit) }} 元<br>
                                    <strong>貸方總計：</strong> {{ "{:,}".format(totals.total_credit) }} 元<br>
                                    <strong>差額：</strong> {{ "{:,}".format(totals.total_debit - totals.total_credit) }} 元
                                </div>
                            </div>

                            <!-- 試算表 -->
                            <table class="trial-balance-table">
                                <thead>
                                    <tr>
                                        <th>科目代碼</th>
                                        <th>科目名稱</th>
                                        <th>借方金額</th>
                                        <th>貸方金額</th>
                                        <th>餘額</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for category_code, category_data in categories.items() %}
                                    {% if category_data['items'] %}
                                    <tr class="category-header">
                                        <td colspan="6">{{ category_code }} - {{ category_data.name }}</td>
                                    </tr>
                                    {% for item in category_data['items'] %}
                                    <tr {% if item.balance==0 %}class="zero-balance" {% endif %}>
                                        <td>{{ item.subject_code }}</td>
                                        <td>{{ item.subject_name }}</td>
                                        <td class="amount-cell">
                                            {% if item.total_debit > 0 %}
                                            {{ "{:,}".format(item.total_debit) }}
                                            {% else %}
                                            -
                                            {% endif %}
                                        </td>
                                        <td class="amount-cell">
                                            {% if item.total_credit > 0 %}
                                            {{ "{:,}".format(item.total_credit) }}
                                            {% else %}
                                            -
                                            {% endif %}
                                        </td>
                                        <td class="amount-cell">
                                            {% if item.balance != 0 %}
                                            {{ "{:,}".format(item.balance) }}
                                            {% else %}
                                            <span class="zero-balance">0</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('new_reports.subject_detail', subject_code=item.subject_code) }}"
                                                class="button is-small is-info">
                                                <i class="fas fa-eye"></i> 明細
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}

                                    <!-- 類別小計 -->
                                    <tr class="sub-category">
                                        <td colspan="2"><strong>{{ category_data.name }}小計</strong></td>
                                        <td class="amount-cell">
                                            <strong>{{
                                                "{:,}".format(category_data['items']|sum(attribute='total_debit'))
                                                }}</strong>
                                        </td>
                                        <td class="amount-cell">
                                            <strong>{{
                                                "{:,}".format(category_data['items']|sum(attribute='total_credit'))
                                                }}</strong>
                                        </td>
                                        <td class="amount-cell">
                                            <strong>{{ "{:,}".format(category_data['items']|sum(attribute='balance'))
                                                }}</strong>
                                        </td>
                                        <td></td>
                                    </tr>
                                    {% endif %}
                                    {% endfor %}

                                    <!-- 總計 -->
                                    <tr class="total-row">
                                        <td colspan="2"><strong>總計</strong></td>
                                        <td class="amount-cell"><strong>{{ "{:,}".format(totals.total_debit) }}</strong>
                                        </td>
                                        <td class="amount-cell"><strong>{{ "{:,}".format(totals.total_credit)
                                                }}</strong></td>
                                        <td class="amount-cell"><strong>{{ "{:,}".format(totals.total_debit -
                                                totals.total_credit) }}</strong></td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>

                            <!-- 報表說明 -->
                            <div class="notification is-info is-light">
                                <p><strong>試算表說明：</strong></p>
                                <ul>
                                    <li>本試算表基於新的複式記帳系統生成</li>
                                    <li>數據來源：Transaction 和 JournalEntry 表</li>
                                    <li>截止日期：{{ as_of_date }}</li>
                                    <li>借方總計應等於貸方總計</li>
                                    <li>點擊「明細」可查看科目的詳細分錄</li>
                                    <li>灰色項目表示餘額為零的科目</li>
                                </ul>
                            </div>

                            <!-- 科目分類說明 -->
                            <div class="notification is-light">
                                <p><strong>科目分類：</strong></p>
                                <div class="columns">
                                    <div class="column">
                                        <ul>
                                            <li><strong>1 - 資產：</strong>現金、銀行存款、應收帳款、存貨、固定資產等</li>
                                            <li><strong>2 - 負債：</strong>應付帳款、短期借款、長期借款等</li>
                                            <li><strong>3 - 權益：</strong>實收資本、保留盈餘等</li>
                                        </ul>
                                    </div>
                                    <div class="column">
                                        <ul>
                                            <li><strong>4 - 收入：</strong>銷貨收入、服務收入、其他收入等</li>
                                            <li><strong>5 - 成本：</strong>銷貨成本、製造成本等</li>
                                            <li><strong>6 - 費用：</strong>營業費用、管理費用、財務費用等</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>

</html>