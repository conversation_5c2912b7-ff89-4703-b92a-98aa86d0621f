<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>新增資金紀錄</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }

    .sidebar {
      max-width: 200px;
      min-width: 180px;
      width: 200px;
    }

    .sidebar .menu-label {
      font-size: 14px;
      font-weight: bold;
    }

    .sidebar .menu-list a {
      font-size: 20px;
      padding: 0.5em 0.75em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-content {
      padding: 2rem 1rem;
    }

    .form-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .form-header {
      background: #3273dc;
      color: white;
      padding: 1rem 1.5rem;
      font-size: 1.2rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .back-link {
      color: white;
      text-decoration: none;
      font-size: 1.1rem;
    }

    .back-link:hover {
      color: #f0f0f0;
    }

    .form-content {
      padding: 2rem;
    }

    .field-group {
      margin-bottom: 1.5rem;
    }

    .field-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .field-row .field {
      flex: 1;
    }

    .upload-area {
      border: 2px dashed #dbdbdb;
      border-radius: 6px;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: border-color 0.3s;
    }

    .upload-area:hover {
      border-color: #3273dc;
    }

    .upload-area .icon {
      font-size: 2rem;
      color: #b5b5b5;
      margin-bottom: 0.5rem;
    }

    .submit-section {
      text-align: center;
      padding-top: 1.5rem;
      border-top: 1px solid #dbdbdb;
      margin-top: 2rem;
    }

    .radio-group {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .select-full {
      width: 100%;
    }

    /* 修復下拉選單箭頭對齊問題 */
    .field-row .select {
      width: 100%;
      display: block;
    }

    .field-row .select select {
      width: 100%;
      border: 1px solid #dbdbdb;
      border-radius: 4px;
      padding: 0.5em 2.5em 0.5em 0.75em;
      background-color: white;
    }

    /* 確保下拉選單箭頭正確顯示並移除多餘的箭頭 */
    .field-row .select:not(.is-multiple):not(.is-loading)::after {
      border: 3px solid transparent;
      border-radius: 2px;
      border-right: 0;
      border-top: 0;
      content: " ";
      display: block;
      height: 0.625em;
      margin-top: -0.4375em;
      pointer-events: none;
      position: absolute;
      top: 50%;
      transform: rotate(-45deg);
      transform-origin: center;
      width: 0.625em;
      border-color: #3273dc;
      right: 1.125em;
      z-index: 4;
    }

    /* 移除可能出現的額外箭頭 */
    .field-row .control::after {
      display: none !important;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <!-- 頁面標題 -->
        <div class="mb-4">
          <h1 class="title is-4">
            <a href="/?main=資金管理"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            新增資金紀錄
          </h1>
        </div>
        <div class="main-content">
          <div class="form-container">
            <div class="form-header">
              <button class="button is-info is-light">頁面指南</button>
            </div>

            <div class="form-content">
              <!-- Flash 訊息 -->
              {% with messages = get_flashed_messages(with_categories=true) %}
              {% if messages %}
              {% for category, message in messages %}
              <div class="notification is-{{ 'success' if category == 'success' else 'danger' }}">
                <button class="delete"></button>
                {{ message }}
              </div>
              {% endfor %}
              {% endif %}
              {% endwith %}

              <form method="POST" enctype="multipart/form-data">
                <!-- 第一行：收支類型、金額、記帳日期、標籤、備註 -->
                <div class="field-group">
                  <div class="field-row">
                    <div class="field">
                      <label class="label">收支類型</label>
                      <div class="control">
                        <div class="radio-group">
                          <label class="radio">
                            <input type="radio" name="money_type" value="收入" {{ 'checked' if edit_mode and record_data
                              and record_data.money_type=='收入' else '' }} required>
                            收入
                          </label>
                          <label class="radio">
                            <input type="radio" name="money_type" value="支出" {{ 'checked' if edit_mode and record_data
                              and record_data.money_type=='支出' else '' }} required>
                            支出
                          </label>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">金額 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="number" name="amount" placeholder="請輸入金額"
                          value="{{ record_data.amount if edit_mode and record_data else '' }}" required>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">記帳日期 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <input class="input" type="date" name="record_date"
                          value="{{ record_data.record_date.strftime('%Y-%m-%d') if edit_mode and record_data and record_data.record_date else '' }}"
                          required>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">標籤</label>
                      <div class="control">
                        <input class="input" type="text" name="tags" placeholder="請輸入標籤"
                          value="{{ record_data.tags if edit_mode and record_data else '' }}">
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">備註</label>
                      <div class="control">
                        <input class="input" type="text" name="note" placeholder="請輸入備註"
                          value="{{ record_data.note if edit_mode and record_data else '' }}">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 第二行：所有下拉選單 -->
                <div class="field-group">
                  <div class="field-row">
                    <div class="field">
                      <label class="label">收付款帳戶 <span class="has-text-danger">*</span></label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="account_id" required>
                            <option value="">請選擇收付款帳戶</option>
                            {% for account in accounts %}
                            <option value="{{ account.id }}" {{ 'selected' if edit_mode and record_data and
                              record_data.account_id==account.id else '' }}>{{ account.display_name }}</option>
                            {% endfor %}
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">收付款性質</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="subject_code">
                            <option value="">請選擇收付款性質</option>
                            <option value="2205" {{ 'selected' if edit_mode and record_data and
                              record_data.subject_code=='2205' else '' }}>2205 暫付款</option>
                            <option value="2210" {{ 'selected' if edit_mode and record_data and
                              record_data.subject_code=='2210' else '' }}>2210 業主股東往來</option>
                            <option value="2910" {{ 'selected' if edit_mode and record_data and
                              record_data.subject_code=='2910' else '' }}>2910 存入保證金</option>
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">收支對象</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="payment_identity_id">
                            <option value="">請選擇收支對象</option>
                            {% for identity in payment_identities %}
                            <option value="{{ identity.id }}" {{ 'selected' if edit_mode and record_data and
                              record_data.payment_identity_id==identity.id else '' }}>{{ identity.name }}{% if
                              identity.tax_id %} ({{ identity.tax_id }}){% endif %}</option>
                            {% endfor %}
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">部門</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="department_id">
                            <option value="">請選擇部門</option>
                            {% for department in departments %}
                            <option value="{{ department.id }}" {{ 'selected' if edit_mode and record_data and
                              record_data.department_id==department.id else '' }}>{{ department.name }}</option>
                            {% endfor %}
                          </select>
                        </div>
                      </div>
                    </div>
                    <div class="field">
                      <label class="label">專案</label>
                      <div class="control">
                        <div class="select select-full">
                          <select name="project_id">
                            <option value="">請選擇專案</option>
                            {% for project in projects %}
                            <option value="{{ project.id }}" {{ 'selected' if edit_mode and record_data and
                              record_data.project_id==project.id else '' }}>{{ project.name }} ({{ project.code }})
                            </option>
                            {% endfor %}
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 憑證上傳 -->
                <div class="field-group">
                  <div class="field">
                    <label class="label">憑證</label>
                    <div class="control">
                      <div class="upload-area" onclick="document.getElementById('voucher-file').click()">
                        <div class="icon">
                          <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <p>點擊上傳憑證</p>
                        <input type="file" id="voucher-file" name="voucher" style="display: none;"
                          accept="image/*,.pdf">
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 提交按鈕 -->
                <div class="submit-section">
                  <button type="submit" class="button is-primary is-large">儲存</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <script>
    // 設定今天的日期為預設值
    document.addEventListener('DOMContentLoaded', function () {
      const today = new Date().toISOString().split('T')[0];
      document.querySelector('input[name="record_date"]').value = today;
    });

    // 檔案上傳處理
    document.getElementById('voucher-file').addEventListener('change', function (e) {
      const file = e.target.files[0];
      if (file) {
        const uploadArea = document.querySelector('.upload-area p');
        uploadArea.textContent = `已選擇: ${file.name}`;
      }
    });
  </script>
</body>

</html>