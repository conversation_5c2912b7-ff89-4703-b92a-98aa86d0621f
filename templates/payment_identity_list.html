<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>收支對象管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .table thead th {
            background-color: #3273dc !important;
            color: white !important;
            font-weight: 600 !important;
            border-color: #3273dc !important;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=設定"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        收支對象管理
                    </h1>
                </div>
                <div class="box">
                    <div class="level">
                        <div class="level-left">
                            <div class="level-item">
                                <a href="/payment_identity_type/manage" class="button is-info is-rounded">
                                    <span class="icon"><i class="fas fa-tags"></i></span>
                                    <span>對象類別管理</span>
                                </a>
                            </div>
                        </div>
                        <div class="level-right">
                            <a href="/payment_identity/add" class="button is-warning is-rounded">
                                <span class="icon"><i class="fas fa-plus"></i></span>
                                <span>新增收支對象</span>
                            </a>
                        </div>
                    </div>

                    <table class="table is-fullwidth is-striped is-hoverable">
                        <thead>
                            <tr>
                                <th>客戶類別</th>
                                <th>公司名稱</th>
                                <th>公司統編</th>
                                <th>銀行代碼</th>
                                <th>銀行帳號</th>
                                <th>聯絡人姓名</th>
                                <th>手機</th>
                                <th>Line ID</th>
                                <th>備註</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for i in identities %}
                            <tr>
                                <td>{{ i.type }}</td>
                                <td>{{ i.name }}</td>
                                <td>{{ i.tax_id }}</td>
                                <td>{{ i.bank_code }}</td>
                                <td>{{ i.bank_account }}</td>
                                <td>{{ i.contact }}</td>
                                <td>{{ i.mobile }}</td>
                                <td>{{ i.line }}</td>
                                <td>{{ i.note }}</td>
                                <td>
                                    <div class="buttons are-small">
                                        <a href="/payment_identity/edit/{{ i.id }}" class="button is-link is-light" title="編輯">
                                            <span class="icon is-small">
                                                <i class="fas fa-pen"></i>
                                            </span>
                                        </a>
                                        <button class="button is-danger is-light delete-btn" data-id="{{ i.id }}" data-name="{{ i.name }}" title="刪除">
                                            <span class="icon is-small">
                                                <i class="fas fa-trash"></i>
                                            </span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 刪除確認對話框 -->
    <div id="deleteModal" class="modal">
        <div class="modal-background"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">確認刪除</p>
                <button class="delete" aria-label="close" onclick="closeDeleteModal()"></button>
            </header>
            <section class="modal-card-body">
                <p>確定要刪除收支對象「<span id="deleteName"></span>」嗎？</p>
                <p class="has-text-danger is-size-7 mt-2">此操作無法復原，請謹慎操作。</p>
            </section>
            <footer class="modal-card-foot">
                <button class="button is-danger" id="confirmDeleteBtn">確認刪除</button>
                <button class="button" onclick="closeDeleteModal()">取消</button>
            </footer>
        </div>
    </div>

    <script>
        // 刪除功能
        function deleteIdentity(id, name) {
            document.getElementById('deleteName').textContent = name;
            document.getElementById('deleteModal').classList.add('is-active');
            
            // 設置確認刪除按鈕的事件
            document.getElementById('confirmDeleteBtn').onclick = function() {
                performDelete(id);
            };
        }
        
        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.remove('is-active');
        }
        
        async function performDelete(id) {
            try {
                const response = await fetch(`/payment_identity/delete/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    // 刪除成功，重新載入頁面
                    window.location.reload();
                } else {
                    alert('刪除失敗：' + (result.error || '未知錯誤'));
                }
            } catch (error) {
                console.error('刪除失敗:', error);
                alert('刪除失敗，請稍後重試');
            }
            
            closeDeleteModal();
        }
        
        // 為所有刪除按鈕添加事件監聽器
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.delete-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    deleteIdentity(id, name);
                });
            });
        });
    </script>
</body>

</html>