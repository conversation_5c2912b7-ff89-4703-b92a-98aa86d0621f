<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>審計儀表板</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 審計系統導航 -->
                <div class="box mb-5">
                    <h2 class="subtitle">
                        <a href="/" style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        <i class="fas fa-shield-alt"></i> 審計系統
                    </h2>
                    <div class="buttons">
                        <a class="button is-primary" href="/audit_dashboard">
                            <span class="icon"><i class="fas fa-tachometer-alt"></i></span>
                            <span>儀表板</span>
                        </a>
                        <a class="button is-info is-light" href="/audit_search">
                            <span class="icon"><i class="fas fa-search"></i></span>
                            <span>審計搜尋</span>
                        </a>
                        <a class="button is-warning is-light" href="/deleted_records">
                            <span class="icon"><i class="fas fa-trash-restore"></i></span>
                            <span>已刪除記錄</span>
                        </a>
                    </div>
                </div>

                <!-- 統計資訊 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-chart-bar"></i> 統計資訊
                    </h3>
                
                    <div class="columns">
                        <div class="column">
                            <div class="notification is-primary">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ stats.today_records }}</p>
                                                <p class="subtitle">今日新增記錄</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-plus-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="column">
                            <div class="notification is-success">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ stats.week_updates }}</p>
                                                <p class="subtitle">本週修改記錄</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-edit fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="column">
                            <div class="notification is-warning">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ stats.deleted_records }}</p>
                                                <p class="subtitle">已刪除記錄</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-trash fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="column">
                            <div class="notification is-info">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ recent_activities|length }}</p>
                                                <p class="subtitle">最近活動</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-history fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 最近活動記錄 -->
                <div class="box">
                    <h3 class="subtitle">
                        <i class="fas fa-clock"></i> 最近活動記錄
                    </h3>
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>時間</th>
                                    <th>動作</th>
                                    <th>表格</th>
                                    <th>記錄</th>
                                    <th>用戶</th>
                                    <th>詳細</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in recent_activities %}
                                <tr>
                                    <td>{{ activity.time.strftime('%Y-%m-%d %H:%M:%S') if activity.time else '' }}</td>
                                    <td>
                                        {% if activity.type == '建立' %}
                                            <span class="tag is-success">{{ activity.type }}</span>
                                        {% elif activity.type == '修改' %}
                                            <span class="tag is-warning">{{ activity.type }}</span>
                                        {% else %}
                                            <span class="tag is-danger">{{ activity.type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ activity.table }}</td>
                                    <td>
                                        <strong>{{ activity.record_name }}</strong>
                                        <br><small class="has-text-grey">(ID: {{ activity.record_id }})</small>
                                    </td>
                                    <td>{{ activity.user }}</td>
                                    <td>
                                        {% if activity.amount %}
                                            金額: {{ "{:,}".format(activity.amount) }}
                                        {% endif %}
                                        {% if activity.version %}
                                            版本: {{ activity.version }}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>