<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>註冊印錢大師帳號</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .register-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .plan-card {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 1.5rem;
            height: 100%;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .plan-card:hover {
            border-color: #667eea;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
        }
        
        .plan-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }
        
        .plan-price {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin: 1rem 0;
        }
        
        .plan-features {
            list-style: none;
            padding: 0;
        }
        
        .plan-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .plan-features li:before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .form-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
        }
        
        .slug-preview {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 0.75rem;
            margin-top: 0.5rem;
            font-family: monospace;
        }
        
        .slug-status {
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
        
        .slug-available {
            color: #10b981;
        }
        
        .slug-unavailable {
            color: #ef4444;
        }
        
        .register-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .register-btn:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <section class="hero is-primary">
        <div class="hero-body">
            <div class="container">
                <h1 class="title is-2">
                    <i class="fas fa-coins"></i>
                    印錢大師
                </h1>
                <p class="subtitle is-4">
                    專業雲端會計系統 - 立即開始您的30天免費試用
                </p>
            </div>
        </div>
    </section>

    <div class="register-container">
        <!-- 方案選擇 -->
        <div class="section">
            <h2 class="title is-3 has-text-centered">選擇您的方案</h2>
            <div class="columns is-multiline" id="plan-selection">
                {% for plan_key, plan_info in plan_levels.items() %}
                <div class="column is-3">
                    <div class="plan-card" data-plan="{{ plan_key }}" onclick="selectPlan('{{ plan_key }}')">
                        <h3 class="title is-4">{{ plan_info.name }}</h3>
                        <div class="plan-price">{{ plan_info.price }}</div>
                        <p class="subtitle is-6">{{ plan_info.description }}</p>
                        <ul class="plan-features">
                            {% for feature in plan_info.features %}
                            <li>{{ feature }}</li>
                            {% endfor %}
                        </ul>
                        {% if plan_key == 'BASIC' %}
                        <div class="tag is-success">推薦新手</div>
                        {% elif plan_key == 'STANDARD' %}
                        <div class="tag is-warning">最受歡迎</div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 註冊表單 -->
        <form method="POST" class="form-section">
            <h2 class="title is-3">建立您的帳號</h2>
            
            <input type="hidden" name="plan_level" id="selected-plan" value="{{ form_data.plan_level or 'BASIC' }}">
            
            <div class="columns">
                <div class="column is-6">
                    <div class="field">
                        <label class="label">公司/組織名稱 <span class="has-text-danger">*</span></label>
                        <div class="control">
                            <input class="input" type="text" name="company_name" 
                                   value="{{ form_data.company_name or '' }}" 
                                   placeholder="請輸入您的公司或組織名稱" required>
                        </div>
                    </div>
                </div>
                
                <div class="column is-6">
                    <div class="field">
                        <label class="label">系統識別名稱 <span class="has-text-danger">*</span></label>
                        <div class="control">
                            <input class="input" type="text" name="slug" id="slug-input"
                                   value="{{ form_data.slug or '' }}"
                                   placeholder="yourcompany" 
                                   pattern="[a-z0-9-]+" 
                                   title="只能包含小寫字母、數字和連字符"
                                   required>
                        </div>
                        <div class="slug-preview">
                            您的系統網址將是: <strong><span id="slug-preview">yourcompany</span>.會計系統.com</strong>
                        </div>
                        <div class="slug-status" id="slug-status"></div>
                    </div>
                </div>
            </div>

            <div class="columns">
                <div class="column is-4">
                    <div class="field">
                        <label class="label">聯絡人姓名 <span class="has-text-danger">*</span></label>
                        <div class="control">
                            <input class="input" type="text" name="contact_person" 
                                   value="{{ form_data.contact_person or '' }}"
                                   placeholder="請輸入聯絡人姓名" required>
                        </div>
                    </div>
                </div>
                
                <div class="column is-4">
                    <div class="field">
                        <label class="label">電子郵箱 <span class="has-text-danger">*</span></label>
                        <div class="control">
                            <input class="input" type="email" name="contact_email" 
                                   value="{{ form_data.contact_email or '' }}"
                                   placeholder="<EMAIL>" required>
                        </div>
                        <p class="help">此郵箱將作為主要聯絡方式和帳單郵箱</p>
                    </div>
                </div>
                
                <div class="column is-4">
                    <div class="field">
                        <label class="label">聯絡電話</label>
                        <div class="control">
                            <input class="input" type="tel" name="contact_phone" 
                                   value="{{ form_data.contact_phone or '' }}"
                                   placeholder="02-1234-5678">
                        </div>
                    </div>
                </div>
            </div>

            <hr>

            <h3 class="title is-4">設定管理員帳號</h3>
            
            <div class="columns">
                <div class="column is-4">
                    <div class="field">
                        <label class="label">管理員帳號 <span class="has-text-danger">*</span></label>
                        <div class="control">
                            <input class="input" type="text" name="admin_username" 
                                   value="{{ form_data.admin_username or '' }}"
                                   placeholder="admin" 
                                   minlength="3" required>
                        </div>
                        <p class="help">至少3個字符，將用於登入系統</p>
                    </div>
                </div>
                
                <div class="column is-4">
                    <div class="field">
                        <label class="label">密碼 <span class="has-text-danger">*</span></label>
                        <div class="control">
                            <input class="input" type="password" name="admin_password" 
                                   placeholder="請輸入密碼" 
                                   minlength="6" required>
                        </div>
                        <p class="help">至少6個字符</p>
                    </div>
                </div>
                
                <div class="column is-4">
                    <div class="field">
                        <label class="label">確認密碼 <span class="has-text-danger">*</span></label>
                        <div class="control">
                            <input class="input" type="password" name="confirm_password" 
                                   placeholder="請再次輸入密碼" 
                                   minlength="6" required>
                        </div>
                    </div>
                </div>
            </div>

            <div class="field">
                <div class="control">
                    <label class="checkbox">
                        <input type="checkbox" name="agree_terms" required>
                        我同意 <a href="#" target="_blank">服務條款</a> 和 <a href="#" target="_blank">隱私政策</a>
                    </label>
                </div>
            </div>

            <div class="field is-grouped is-grouped-centered">
                <div class="control">
                    <button type="submit" class="button register-btn is-large">
                        <span class="icon">
                            <i class="fas fa-rocket"></i>
                        </span>
                        <span>開始免費試用</span>
                    </button>
                </div>
            </div>
            
            <p class="has-text-centered has-text-grey">
                已經有帳號了？ <a href="{{ url_for('auth.login') }}">立即登入</a>
            </p>
        </form>
    </div>

    <!-- 顯示錯誤訊息 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <div class="modal is-active" id="message-modal">
        <div class="modal-background" onclick="closeModal()"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">提示訊息</p>
                <button class="delete" onclick="closeModal()"></button>
            </header>
            <section class="modal-card-body">
                {% for category, message in messages %}
                <div class="notification {% if category == 'error' %}is-danger{% else %}is-info{% endif %}">
                    {{ message }}
                </div>
                {% endfor %}
            </section>
            <footer class="modal-card-foot">
                <button class="button" onclick="closeModal()">確定</button>
            </footer>
        </div>
    </div>
    {% endif %}
    {% endwith %}

    <script>
        // 方案選擇
        function selectPlan(planKey) {
            // 移除所有選中狀態
            document.querySelectorAll('.plan-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 添加選中狀態
            document.querySelector(`[data-plan="${planKey}"]`).classList.add('selected');
            
            // 設置隱藏字段
            document.getElementById('selected-plan').value = planKey;
        }
        
        // 初始化預設選中方案
        document.addEventListener('DOMContentLoaded', function() {
            const defaultPlan = document.getElementById('selected-plan').value || 'BASIC';
            selectPlan(defaultPlan);
        });
        
        // Slug輸入處理
        document.getElementById('slug-input').addEventListener('input', function() {
            const slug = this.value.toLowerCase();
            const preview = document.getElementById('slug-preview');
            const status = document.getElementById('slug-status');
            
            preview.textContent = slug || 'yourcompany';
            
            if (slug.length >= 3) {
                // 檢查slug可用性
                fetch(`/register/check-slug?slug=${encodeURIComponent(slug)}`)
                    .then(response => response.json())
                    .then(data => {
                        status.textContent = data.message;
                        status.className = `slug-status ${data.available ? 'slug-available' : 'slug-unavailable'}`;
                    });
            } else {
                status.textContent = '';
            }
        });
        
        // 關閉模態框
        function closeModal() {
            document.getElementById('message-modal').classList.remove('is-active');
        }
        
        // 密碼確認驗證
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.querySelector('input[name="admin_password"]').value;
            const confirmPassword = document.querySelector('input[name="confirm_password"]').value;
            
            if (password !== confirmPassword) {
                alert('密碼確認不匹配');
                e.preventDefault();
            }
        });
    </script>
</body>
</html>