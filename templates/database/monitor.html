<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>資料庫監控儀表板</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-theme.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .dashboard-card:hover {
            transform: translateY(-2px);
        }
        .health-excellent { color: #48c774; }
        .health-good { color: #3298dc; }
        .health-fair { color: #ffdd57; }
        .health-poor { color: #f14668; }
        .metric-large {
            font-size: 2rem;
            font-weight: bold;
        }
        .optimization-immediate { border-left: 4px solid #f14668; }
        .optimization-short-term { border-left: 4px solid #ffdd57; }
        .optimization-long-term { border-left: 4px solid #3298dc; }
        .optimization-maintenance { border-left: 4px solid #6c757d; }
        .table-row-clickable:hover {
            background-color: #f5f5f5;
            cursor: pointer;
        }
        .notification.is-loading {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .loader {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container is-fluid p-4">
        <h1 class="title has-text-centered">印錢大師</h1>
        <!-- 標題列 -->
        <div class="level mb-4">
            <div class="level-left">
                <div class="level-item">
                    <div>
                        <h2 class="title is-3"><i class="fas fa-database"></i> 資料庫監控儀表板</h2>
                        <p class="subtitle is-6 has-text-grey">即時監控資料庫性能和健康狀況</p>
                    </div>
                </div>
            </div>
            <div class="level-right">
                <div class="level-item">
                    <div class="buttons">
                        <button class="button is-primary" onclick="refreshAll()">
                            <span class="icon"><i class="fas fa-sync-alt"></i></span>
                            <span>重新整理</span>
                        </button>
                        <button class="button is-success" onclick="executeOptimization(true)">
                            <span class="icon"><i class="fas fa-tools"></i></span>
                            <span>執行優化（預覽）</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 健康狀況卡片 -->
        <div class="columns mb-4">
            <div class="column is-3">
                <div class="box dashboard-card has-background-light">
                    <div class="has-text-centered">
                        <i class="fas fa-heartbeat fa-2x mb-2" id="health-icon"></i>
                        <h5 class="title is-5">健康分數</h5>
                        <div class="metric-large" id="health-score">--</div>
                        <small class="has-text-grey" id="health-status">載入中...</small>
                    </div>
                </div>
            </div>
            <div class="column is-3">
                <div class="box dashboard-card">
                    <div class="has-text-centered">
                        <i class="fas fa-hdd fa-2x mb-2 has-text-info"></i>
                        <h5 class="title is-5">資料庫大小</h5>
                        <div class="metric-large" id="db-size">--</div>
                        <small class="has-text-grey">MB</small>
                    </div>
                </div>
            </div>
            <div class="column is-3">
                <div class="box dashboard-card">
                    <div class="has-text-centered">
                        <i class="fas fa-table fa-2x mb-2 has-text-warning"></i>
                        <h5 class="title is-5">總記錄數</h5>
                        <div class="metric-large" id="total-records">--</div>
                        <small class="has-text-grey">筆</small>
                    </div>
                </div>
            </div>
            <div class="column is-3">
                <div class="box dashboard-card">
                    <div class="has-text-centered">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2 has-text-danger"></i>
                        <h5 class="title is-5">性能問題</h5>
                        <div class="metric-large" id="performance-issues">--</div>
                        <small class="has-text-grey">個</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要內容區 -->
        <div class="columns">
            <!-- 左側 -->
            <div class="column is-8">
                <!-- 表統計圖表 -->
                <div class="box dashboard-card mb-4">
                    <div class="box-header">
                        <h5 class="title is-5"><i class="fas fa-chart-bar"></i> 表記錄數分布</h5>
                    </div>
                    <div class="content">
                        <canvas id="tableStatsChart" height="300"></canvas>
                    </div>
                </div>

                <!-- 查詢性能 -->
                <div class="box dashboard-card mb-4">
                    <div class="box-header">
                        <h5 class="title is-5"><i class="fas fa-tachometer-alt"></i> 查詢性能</h5>
                    </div>
                    <div class="content">
                        <div class="columns" id="query-performance-metrics">
                            <div class="column">
                                <div class="has-text-centered">
                                    <h6 class="subtitle is-6">平均執行時間</h6>
                                    <div class="title is-4" id="avg-query-time">--</div>
                                    <small class="has-text-grey">秒</small>
                                </div>
                            </div>
                            <div class="column">
                                <div class="has-text-centered">
                                    <h6 class="subtitle is-6">慢查詢數量</h6>
                                    <div class="title is-4" id="slow-queries-count">--</div>
                                    <small class="has-text-grey">個</small>
                                </div>
                            </div>
                            <div class="column">
                                <div class="has-text-centered">
                                    <h6 class="subtitle is-6">慢查詢比例</h6>
                                    <div class="title is-4" id="slow-queries-rate">--</div>
                                    <small class="has-text-grey">%</small>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <h6 class="subtitle is-6">最近慢查詢</h6>
                        <div class="table-container">
                            <table class="table is-fullwidth is-striped">
                                <thead>
                                    <tr>
                                        <th>查詢</th>
                                        <th>執行時間</th>
                                        <th>類型</th>
                                        <th>時間</th>
                                    </tr>
                                </thead>
                                <tbody id="slow-queries-table">
                                    <tr><td colspan="4" class="has-text-centered">載入中...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右側 -->
            <div class="column is-4">
                <!-- 優化建議 -->
                <div class="box dashboard-card mb-4">
                    <div class="box-header">
                        <h5 class="title is-5"><i class="fas fa-lightbulb"></i> 優化建議</h5>
                    </div>
                    <div class="content">
                        <div id="optimization-recommendations">
                            <div class="has-text-centered notification is-loading">
                                <div class="loader"></div>
                                <span>載入優化建議中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 維護任務 -->
                <div class="box dashboard-card mb-4">
                    <div class="box-header">
                        <h5 class="title is-5"><i class="fas fa-tasks"></i> 維護任務</h5>
                    </div>
                    <div class="content">
                        <div id="maintenance-tasks">
                            <div class="has-text-centered notification is-loading">
                                <div class="loader"></div>
                                <span>載入維護任務中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 表統計 -->
                <div class="box dashboard-card">
                    <div class="box-header">
                        <h5 class="title is-5"><i class="fas fa-list"></i> 表統計</h5>
                    </div>
                    <div class="content">
                        <div class="table-container">
                            <table class="table is-fullwidth is-striped">
                                <thead>
                                    <tr>
                                        <th>表名</th>
                                        <th class="has-text-right">記錄數</th>
                                        <th class="has-text-right">比例</th>
                                    </tr>
                                </thead>
                                <tbody id="table-stats-table">
                                    <tr><td colspan="3" class="has-text-centered">載入中...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let tableStatsChart = null;

        // 載入所有數據
        function loadAllData() {
            loadHealthSummary();
            loadTableStats();
            loadQueryPerformance();
            loadOptimizationPlan();
            loadMaintenanceTasks();
        }

        // 載入健康摘要
        async function loadHealthSummary() {
            try {
                const response = await fetch('/api/database/health');
                const data = await response.json();
                
                if (data.success) {
                    const health = data.data;
                    document.getElementById('health-score').textContent = health.health_score || '--';
                    document.getElementById('health-status').textContent = getHealthStatusText(health.status);
                    document.getElementById('db-size').textContent = (health.database_size_mb || 0).toFixed(2);
                    document.getElementById('performance-issues').textContent = health.performance_issues || 0;
                    
                    const healthIcon = document.getElementById('health-icon');
                    const healthClass = `health-${health.status}`;
                    healthIcon.className = `fas fa-heartbeat fa-2x mb-2 ${healthClass}`;
                }
            } catch (error) {
                console.error('載入健康摘要失敗:', error);
            }
        }

        // 載入表統計
        async function loadTableStats() {
            try {
                const response = await fetch('/api/database/table-stats');
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.data;
                    document.getElementById('total-records').textContent = stats.total_records.toLocaleString();
                    
                    // 更新表統計表格
                    const tableBody = document.getElementById('table-stats-table');
                    tableBody.innerHTML = '';
                    
                    stats.table_stats.slice(0, 10).forEach(table => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td><small>${table.table_name}</small></td>
                            <td class="has-text-right"><small>${table.record_count.toLocaleString()}</small></td>
                            <td class="has-text-right"><small>${table.percentage}%</small></td>
                        `;
                        tableBody.appendChild(row);
                    });
                    
                    // 更新圖表
                    updateTableStatsChart(stats.table_stats);
                }
            } catch (error) {
                console.error('載入表統計失敗:', error);
            }
        }

        // 載入查詢性能
        async function loadQueryPerformance() {
            try {
                const response = await fetch('/api/database/query-performance');
                const data = await response.json();
                
                if (data.success && data.data.performance_summary !== 'No query history available') {
                    const perf = data.data.performance_summary;
                    document.getElementById('avg-query-time').textContent = (perf.avg_execution_time || 0).toFixed(3);
                    document.getElementById('slow-queries-count').textContent = perf.slow_queries_count || 0;
                    document.getElementById('slow-queries-rate').textContent = (perf.slow_queries_rate || 0).toFixed(1);
                    
                    // 更新慢查詢表格
                    const slowQueriesTable = document.getElementById('slow-queries-table');
                    slowQueriesTable.innerHTML = '';
                    
                    if (data.data.recent_slow_queries && data.data.recent_slow_queries.length > 0) {
                        data.data.recent_slow_queries.forEach(query => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td><small>${query.query}</small></td>
                                <td class="has-text-right"><small>${query.execution_time.toFixed(3)}s</small></td>
                                <td><small><span class="tag is-light">${query.query_type}</span></small></td>
                                <td><small>${new Date(query.timestamp).toLocaleTimeString()}</small></td>
                            `;
                            slowQueriesTable.appendChild(row);
                        });
                    } else {
                        slowQueriesTable.innerHTML = '<tr><td colspan="4" class="has-text-centered has-text-grey">暫無慢查詢記錄</td></tr>';
                    }
                } else {
                    // 沒有查詢歷史
                    document.getElementById('avg-query-time').textContent = '0.000';
                    document.getElementById('slow-queries-count').textContent = '0';
                    document.getElementById('slow-queries-rate').textContent = '0.0';
                    document.getElementById('slow-queries-table').innerHTML = '<tr><td colspan="4" class="has-text-centered has-text-grey">暫無查詢記錄</td></tr>';
                }
            } catch (error) {
                console.error('載入查詢性能失敗:', error);
            }
        }

        // 載入優化計畫
        async function loadOptimizationPlan() {
            try {
                const response = await fetch('/api/database/optimization-plan');
                const data = await response.json();
                
                if (data.success) {
                    const plan = data.data;
                    const container = document.getElementById('optimization-recommendations');
                    container.innerHTML = '';
                    
                    const categories = [
                        { key: 'immediate', title: '立即執行', class: 'optimization-immediate' },
                        { key: 'short_term', title: '短期執行', class: 'optimization-short-term' },
                        { key: 'long_term', title: '長期執行', class: 'optimization-long-term' },
                        { key: 'maintenance', title: '定期維護', class: 'optimization-maintenance' }
                    ];
                    
                    categories.forEach(category => {
                        if (plan[category.key] && plan[category.key].length > 0) {
                            const categoryDiv = document.createElement('div');
                            categoryDiv.className = `notification mb-3 p-2 ${category.class}`;
                            categoryDiv.innerHTML = `<strong>${category.title}</strong>`;
                            
                            plan[category.key].forEach(item => {
                                const itemDiv = document.createElement('div');
                                itemDiv.className = 'mt-2';
                                itemDiv.innerHTML = `
                                    <div class="content is-small">
                                        <strong>${item.action}</strong><br>
                                        ${item.description}
                                        ${item.frequency ? `<br><em>頻率: ${item.frequency}</em>` : ''}
                                    </div>
                                `;
                                categoryDiv.appendChild(itemDiv);
                            });
                            
                            container.appendChild(categoryDiv);
                        }
                    });
                    
                    if (container.innerHTML === '') {
                        container.innerHTML = '<div class="notification has-text-centered has-text-grey">目前沒有優化建議</div>';
                    }
                }
            } catch (error) {
                console.error('載入優化計畫失敗:', error);
            }
        }

        // 載入維護任務
        async function loadMaintenanceTasks() {
            try {
                const response = await fetch('/api/database/maintenance-tasks');
                const data = await response.json();
                
                if (data.success) {
                    const tasks = data.data;
                    const container = document.getElementById('maintenance-tasks');
                    container.innerHTML = '';
                    
                    tasks.tasks.forEach(task => {
                        const statusClass = task.status === 'completed' ? 'is-success' : 
                                          task.status === 'pending' ? 'is-warning' : 'is-light';
                        const statusIcon = task.status === 'completed' ? 'check' : 
                                         task.status === 'pending' ? 'clock' : 'question';
                        
                        const taskDiv = document.createElement('div');
                        taskDiv.className = 'notification mb-2 p-2';
                        taskDiv.innerHTML = `
                            <div class="level">
                                <div class="level-left">
                                    <div class="level-item">
                                        <div class="content is-small">
                                            <strong>${task.name}</strong><br>
                                            <span class="has-text-grey">
                                                頻率: ${task.frequency}
                                                ${task.last_run ? `<br>上次執行: ${new Date(task.last_run).toLocaleString()}` : ''}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="level-right">
                                    <div class="level-item">
                                        <span class="tag ${statusClass}">
                                            <i class="fas fa-${statusIcon}"></i>&nbsp;${getTaskStatusText(task.status)}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        `;
                        container.appendChild(taskDiv);
                    });
                }
            } catch (error) {
                console.error('載入維護任務失敗:', error);
            }
        }

        // 更新表統計圖表
        function updateTableStatsChart(tableStats) {
            const ctx = document.getElementById('tableStatsChart').getContext('2d');
            
            if (tableStatsChart) {
                tableStatsChart.destroy();
            }
            
            const topTables = tableStats.slice(0, 10);
            
            tableStatsChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: topTables.map(table => table.table_name),
                    datasets: [{
                        label: '記錄數',
                        data: topTables.map(table => table.record_count),
                        backgroundColor: 'rgba(54, 162, 235, 0.6)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '記錄數'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        // 執行優化
        async function executeOptimization(dryRun = true) {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="icon"><i class="fas fa-spinner fa-spin"></i></span><span>執行中...</span>';
            button.disabled = true;
            
            try {
                const response = await fetch('/api/database/execute-optimization', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ dry_run: dryRun })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const result = data.data;
                    let message = `優化${dryRun ? '預覽' : '執行'}完成！\n`;
                    message += `總時間: ${result.total_time?.toFixed(2) || 0}秒\n`;
                    message += `錯誤數: ${result.total_errors || 0}`;
                    
                    alert(message);
                    
                    if (!dryRun) {
                        // 重新載入數據
                        loadAllData();
                    }
                } else {
                    alert(`優化失敗: ${data.error}`);
                }
            } catch (error) {
                alert(`執行失敗: ${error.message}`);
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        // 重新整理所有數據
        function refreshAll() {
            loadAllData();
        }

        // 輔助函數
        function getHealthStatusText(status) {
            const statusTexts = {
                'excellent': '優秀',
                'good': '良好',
                'fair': '一般',
                'poor': '差',
                'unknown': '未知'
            };
            return statusTexts[status] || '未知';
        }

        function getTaskStatusText(status) {
            const statusTexts = {
                'completed': '已完成',
                'pending': '待執行',
                'running': '執行中'
            };
            return statusTexts[status] || status;
        }

        // 頁面載入完成後執行
        document.addEventListener('DOMContentLoaded', function() {
            loadAllData();
            
            // 每30秒自動刷新一次
            setInterval(loadHealthSummary, 30000);
        });
    </script>
</body>
</html>