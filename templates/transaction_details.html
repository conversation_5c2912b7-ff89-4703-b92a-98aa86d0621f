<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易明細</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- daterangepicker 日期區間選擇器 CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <style>
        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .filter-row {
            margin-bottom: 15px;
        }

        .filter-label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
            font-size: 14px;
        }

        .help-icon {
            color: #999;
            margin-left: 5px;
            cursor: help;
        }

        .date-range-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .date-range-container .input {
            width: 120px;
        }

        .quick-filter-buttons {
            margin-top: 15px;
        }

        .quick-filter-buttons .button {
            margin-right: 10px;
            margin-bottom: 5px;
        }

        /* 自定義日期選擇器樣式 */
        .flatpickr-calendar {
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        .flatpickr-day.selected {
            background: #3498db;
            border-color: #3498db;
        }

        .flatpickr-day.today {
            border-color: #3498db;
        }

        .flatpickr-months .flatpickr-month {
            background: #3498db;
        }

        .flatpickr-current-month .flatpickr-monthDropdown-months {
            font-weight: 600;
        }

        .flatpickr-weekday {
            font-weight: 600;
        }

        /* 左側快速選擇區域樣式 */
        .date-shortcuts {
            background-color: #f5f5f5;
            border-right: 1px solid #e0e0e0;
            padding: 10px 0;
        }

        .date-shortcuts button {
            display: block;
            width: 100%;
            text-align: left;
            padding: 8px 15px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #333;
        }

        .date-shortcuts button:hover {
            background-color: #e0e0e0;
        }
        /* daterangepicker 快捷選單 Bulma 風格 */
        .daterangepicker .ranges li {
            background: #f5f5f5;
            color: #363636;
            border-radius: 4px;
            font-family: 'Noto Sans TC', 'Microsoft JhengHei', Arial, sans-serif;
            transition: background 0.2s, color 0.2s;
        }
        .daterangepicker .ranges li.active,
        .daterangepicker .ranges li:hover {
            background: #3273dc;
            color: #fff;
        }
        .daterangepicker .ranges {
            border-radius: 6px 0 0 6px;
        }
        
        /* 表格字體調小 */
        .table td, .table th {
            font-size: 13px;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="javascript:history.back()"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        交易明細
                        <span class="icon is-small help-icon" title="查詢交易明細資料">
                            <i class="fas fa-question-circle"></i>
                        </span>
                    </h1>
                </div>

                <form method="get" action="">
                    <!-- 篩選條件區域 -->
                    <div class="filter-section">
                        <div class="columns filter-row">
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        自訂篩選
                                        <span class="help-icon" title="可多選一個日期範圍，篩選條件為空">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="columns filter-row">
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        收付日期
                                        <span class="help-icon" title="篩選條件">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control ml-2">
                                        <input type="text" name="payment_daterange" class="input" id="payment-daterange" placeholder="請選擇日期區間" autocomplete="off" value="{{ request.args.get('payment_daterange', '') }}" />
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        收付原因
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="subject_code">
                                                <option value="">不限</option>
                                                {% for subject in subjects %}
                                                <option value="{{ subject.code }}">{{ subject.code }} — {{ subject.name
                                                    }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        交易對象
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="payment_identity_type">
                                                <option value="">不限</option>
                                                {% for type in payment_identity_types %}
                                                <option value="{{ type.id }}">{{ type.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        交易帳戶
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="account">
                                                <option value="">不限</option>
                                                {% for account in accounts %}
                                                <option value="{{ account.id }}">{{ account.name }}（{{ account.category
                                                    }}）</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        營業稅
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="tax_type">
                                                <option value="">不限</option>
                                                <option value="應稅">應稅</option>
                                                <option value="免稅">免稅</option>
                                                <option value="零稅率">零稅率</option>
                                                <option value="無營業稅">無營業稅</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="columns filter-row">
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        憑證日期
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control ml-2">
                                        <input type="text" name="voucher_daterange" class="input" id="voucher-daterange" placeholder="請選擇日期區間" autocomplete="off" value="{{ request.args.get('voucher_daterange', '') }}" />
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        預計日期
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control ml-2">
                                        <input type="text" name="expected_daterange" class="input" id="expected-daterange" placeholder="請選擇日期區間" autocomplete="off" value="{{ request.args.get('expected_daterange', '') }}" />
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        附件
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="attachment">
                                                <option value="">不限</option>
                                                <option value="has">有</option>
                                                <option value="none">無</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        交易狀況
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="transaction_status">
                                                <option value="">不限</option>
                                                <option value="completed">已完成</option>
                                                <option value="pending">未完成</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="columns filter-row">
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        金額範圍
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="field has-addons">
                                        <div class="control">
                                            <input class="input" type="number" name="amount_min" placeholder="最低金額"
                                                min="0" value="{{ request.args.get('amount_min', '') }}">
                                        </div>
                                        <div class="control">
                                            <a class="button is-static">至</a>
                                        </div>
                                        <div class="control">
                                            <input class="input" type="number" name="amount_max" placeholder="最高金額"
                                                min="0" value="{{ request.args.get('amount_max', '') }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        標籤
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="tag">
                                                <option>不限</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-4">
                                <div class="field">
                                    <label class="filter-label">
                                        關鍵字
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="field has-addons">
                                        <div class="control is-expanded">
                                            <input class="input" type="text" name="keyword" placeholder="輸入關鍵字搜尋，可多筆搜尋">
                                        </div>
                                        <div class="control">
                                            <button class="button is-info" type="submit">
                                                搜尋
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="columns filter-row">
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        主要帳戶
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="date-range-container">
                                        <input class="input" type="text" placeholder="以上條件">
                                        <span>至</span>
                                        <input class="input" type="text" placeholder="以下條件">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">
                                        交易排序
                                        <span class="help-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </span>
                                    </label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="order_by">
                                                <option value="a_time_desc">收付日期 - 由新到舊</option>
                                                <option value="a_time_asc">收付日期 - 由舊到新</option>
                                                <option value="voucher_date_desc">憑證日期 - 由新到舊</option>
                                                <option value="voucher_date_asc">憑證日期 - 由舊到新</option>
                                                <option value="invoice_number_desc">發票號碼 - 由大到小</option>
                                                <option value="invoice_number_asc">發票號碼 - 由小到大</option>
                                                <option value="created_at_desc">新增異動 - 由新到舊</option>
                                                <option value="created_at_asc">新增異動 - 由舊到新</option>
                                                <option value="amount_desc">金額 - 由高到低</option>
                                                <option value="amount_asc">金額 - 由低到高</option>
                                                <option value="prepay_date_asc">預計收付日 - 由舊到新</option>
                                                <option value="prepay_date_desc">預計收付日 - 由新到舊</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 快速篩選按鈕 -->
                        <div class="quick-filter-buttons">
                            <label class="filter-label">快速篩選</label>
                            <div>
                                <button class="button is-small is-light" type="button" onclick="setQuickFilter(7)">近 7天</button>
                                <button class="button is-small is-light" type="button" onclick="setQuickFilter(30)">近 30天</button>
                                <button class="button is-small is-light" type="button" onclick="setQuickFilter(90)">近 90天</button>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- 結果區域 -->
                <div class="box">
                    {% if transactions %}
                    <!-- 頂部統計區域 -->
                    <div class="level mb-4" style="background-color: #f8f9fa; padding: 15px; border-radius: 6px;">
                        <div class="level-item">
                            <div>
                                <p class="heading">交易明細</p>
                                <p class="title is-6">{{ total_count }}</p>
                            </div>
                        </div>
                        <div class="level-item">
                            <div>
                                <p class="heading">收款金額</p>
                                <p class="title is-6 has-text-success">{{ "{:,}".format(income_amount) if income_amount else '0' }}</p>
                            </div>
                        </div>
                        <div class="level-item">
                            <div>
                                <p class="heading">付款金額</p>
                                <p class="title is-6 has-text-danger">{{ "{:,}".format(expense_amount) if expense_amount else '0' }}</p>
                            </div>
                        </div>
                        <div class="level-item">
                            <div class="field is-grouped">
                                <p class="control">
                                    <span class="select is-small">
                                        <select>
                                            <option>顯示筆數：25</option>
                                            <option>顯示筆數：50</option>
                                            <option>顯示筆數：100</option>
                                        </select>
                                    </span>
                                </p>
                                <p class="control">
                                    <button class="button is-small is-info">
                                        <span class="icon is-small">
                                            <i class="fas fa-download"></i>
                                        </span>
                                        <span>下載</span>
                                    </button>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="table is-fullwidth is-striped is-hoverable">
                            <thead>
                                <tr>
                                    <th style="width: 80px;"></th>
                                    <th>收付日期</th>
                                    <th>憑證日期</th>
                                    <th>收付原因</th>
                                    <th>交易金額</th>
                                    <th>資金帳戶</th>
                                    <th>預計收付日</th>
                                    <th>附註說明</th>
                                    <th>對象</th>
                                    <th>標籤</th>
                                    <th>營業稅</th>
                                    <th>發票號碼</th>
                                    <th>附件</th>
                                    <th>功能</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td>
                                        {% if transaction.transaction_type == 'income' %}
                                        <span class="tag is-info is-small">收款</span>
                                        {% else %}
                                        <span class="tag is-success is-small">支出</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ transaction.transaction_date.strftime('%Y-%m-%d') if transaction.transaction_date else '' }}</td>
                                    <td>{{ transaction.invoice_date if transaction.invoice_date else '←' }}</td>
                                    <td>
                                        <a href="#" class="has-text-info">{{ transaction.description or '' }}</a>
                                    </td>
                                    <td class="has-text-right">
                                        {% if transaction.transaction_type == 'income' %}
                                        <span class="has-text-success">{{ "{:,}".format(transaction.total_amount) if transaction.total_amount else '0' }}</span>
                                        {% else %}
                                        <span class="has-text-danger">{{ "{:,}".format(transaction.total_amount) if transaction.total_amount else '0' }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% for account in accounts %}
                                        {% if account.id == transaction.account_id %}
                                        {{ account.name }}
                                        {% endif %}
                                        {% endfor %}
                                    </td>
                                    <td>{{ transaction.should_paid_date.strftime('%Y-%m-%d') if transaction.should_paid_date else '' }}</td>
                                    <td>{{ transaction.note or '' }}</td>
                                    <td>
                                        {% for identity in payment_identities %}
                                        {% if identity.id == transaction.payment_identity_id %}
                                        {{ identity.name }}
                                        {% endif %}
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% if transaction.tags %}
                                        <span class="icon has-text-grey">
                                            <i class="fas fa-tag"></i>
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>{{ transaction.tax_type or 'TX' }}</td>
                                    <td>{{ transaction.invoice_number or '' }}</td>
                                    <td>
                                        {% if transaction.image_path %}
                                        <span class="icon has-text-info">
                                            <i class="fas fa-paperclip"></i>
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="buttons are-small">
                                            <a class="button is-small is-warning" title="編輯"
                                               href="{% if transaction.transaction_type == 'income' %}/income_record?edit_id={{ transaction.id }}{% else %}/expense_record?edit_id={{ transaction.id }}{% endif %}">
                                                <span class="icon is-small">
                                                    <i class="fas fa-edit"></i>
                                                </span>
                                            </a>
                                            <button class="button is-small is-link" title="複製">
                                                <span class="icon is-small">
                                                    <i class="fas fa-copy"></i>
                                                </span>
                                            </button>
                                            <button class="button is-small is-danger" title="刪除">
                                                <span class="icon is-small">
                                                    <i class="fas fa-trash"></i>
                                                </span>
                                            </button>
                                            {% if transaction.is_paid %}
                                            <button class="button is-small is-success" title="已完成">
                                                <span class="icon is-small">
                                                    <i class="fas fa-check"></i>
                                                </span>
                                            </button>
                                            {% else %}
                                            <button class="button is-small is-light" title="未完成">
                                                <span class="icon is-small">
                                                    <i class="fas fa-clock"></i>
                                                </span>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="has-text-centered mt-4">
                        <p class="has-text-grey">共找到 {{ transactions|length }} 筆交易記錄</p>
                    </div>
                    {% else %}
                    <div class="content has-text-centered" style="padding: 60px 20px;">
                        <p class="has-text-grey">請設定篩選條件後進行搜尋</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- daterangepicker 相關 JS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script>
        $(function() {
            // 收付日期
            $('#payment-daterange').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    format: 'YYYY-MM-DD',
                    separator: ' ~ ',
                    applyLabel: '確定',
                    cancelLabel: '取消',
                    fromLabel: '從',
                    toLabel: '到',
                    customRangeLabel: '自訂',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    firstDay: 1
                },
                ranges: {
                    '今天': [moment(), moment()],
                    '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    '最近7天': [moment().subtract(6, 'days'), moment()],
                    '最近30天': [moment().subtract(29, 'days'), moment()],
                    '本月': [moment().startOf('month'), moment().endOf('month')],
                    '上個月': [
                        moment().subtract(1, 'month').startOf('month'),
                        moment().subtract(1, 'month').endOf('month')
                    ]
                }
            });
            
            // 恢復收付日期的值
            var paymentDateValue = $('#payment-daterange').val();
            if (paymentDateValue && paymentDateValue.includes(' ~ ')) {
                var parts = paymentDateValue.split(' ~ ');
                if (parts.length === 2) {
                    var startDate = moment(parts[0], 'YYYY-MM-DD');
                    var endDate = moment(parts[1], 'YYYY-MM-DD');
                    if (startDate.isValid() && endDate.isValid()) {
                        $('#payment-daterange').data('daterangepicker').setStartDate(startDate);
                        $('#payment-daterange').data('daterangepicker').setEndDate(endDate);
                    }
                }
            }
            
            $('#payment-daterange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' ~ ' + picker.endDate.format('YYYY-MM-DD'));
            });
            $('#payment-daterange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });

            // 憑證日期
            $('#voucher-daterange').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    format: 'YYYY-MM-DD',
                    separator: ' ~ ',
                    applyLabel: '確定',
                    cancelLabel: '取消',
                    fromLabel: '從',
                    toLabel: '到',
                    customRangeLabel: '自訂',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    firstDay: 1
                },
                ranges: {
                    '今天': [moment(), moment()],
                    '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    '最近7天': [moment().subtract(6, 'days'), moment()],
                    '最近30天': [moment().subtract(29, 'days'), moment()],
                    '本月': [moment().startOf('month'), moment().endOf('month')],
                    '上個月': [
                        moment().subtract(1, 'month').startOf('month'),
                        moment().subtract(1, 'month').endOf('month')
                    ]
                }
            });
            
            // 恢復憑證日期的值
            var voucherDateValue = $('#voucher-daterange').val();
            if (voucherDateValue && voucherDateValue.includes(' ~ ')) {
                var parts = voucherDateValue.split(' ~ ');
                if (parts.length === 2) {
                    var startDate = moment(parts[0], 'YYYY-MM-DD');
                    var endDate = moment(parts[1], 'YYYY-MM-DD');
                    if (startDate.isValid() && endDate.isValid()) {
                        $('#voucher-daterange').data('daterangepicker').setStartDate(startDate);
                        $('#voucher-daterange').data('daterangepicker').setEndDate(endDate);
                    }
                }
            }
            
            $('#voucher-daterange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' ~ ' + picker.endDate.format('YYYY-MM-DD'));
            });
            $('#voucher-daterange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });

            // 預計日期
            $('#expected-daterange').daterangepicker({
                autoUpdateInput: false,
                locale: {
                    format: 'YYYY-MM-DD',
                    separator: ' ~ ',
                    applyLabel: '確定',
                    cancelLabel: '取消',
                    fromLabel: '從',
                    toLabel: '到',
                    customRangeLabel: '自訂',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    firstDay: 1
                },
                ranges: {
                    '今天': [moment(), moment()],
                    '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    '最近7天': [moment().subtract(6, 'days'), moment()],
                    '最近30天': [moment().subtract(29, 'days'), moment()],
                    '本月': [moment().startOf('month'), moment().endOf('month')],
                    '上個月': [
                        moment().subtract(1, 'month').startOf('month'),
                        moment().subtract(1, 'month').endOf('month')
                    ]
                }
            });
            
            // 恢復預計日期的值
            var expectedDateValue = $('#expected-daterange').val();
            if (expectedDateValue && expectedDateValue.includes(' ~ ')) {
                var parts = expectedDateValue.split(' ~ ');
                if (parts.length === 2) {
                    var startDate = moment(parts[0], 'YYYY-MM-DD');
                    var endDate = moment(parts[1], 'YYYY-MM-DD');
                    if (startDate.isValid() && endDate.isValid()) {
                        $('#expected-daterange').data('daterangepicker').setStartDate(startDate);
                        $('#expected-daterange').data('daterangepicker').setEndDate(endDate);
                    }
                }
            }
            
            $('#expected-daterange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('YYYY-MM-DD') + ' ~ ' + picker.endDate.format('YYYY-MM-DD'));
            });
            $('#expected-daterange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
        });

        // 快速篩選按鈕功能
        function setQuickFilter(days) {
            const today = moment();
            const startDate = moment().subtract(days - 1, 'days');
            
            // 設定收付日期
            const paymentPicker = $('#payment-daterange').data('daterangepicker');
            if (paymentPicker) {
                paymentPicker.setStartDate(startDate);
                paymentPicker.setEndDate(today);
                $('#payment-daterange').val(startDate.format('YYYY-MM-DD') + ' ~ ' + today.format('YYYY-MM-DD'));
            }
            
            // 自動提交表單
            document.querySelector('form').submit();
        }
    </script>
</body>

</html>