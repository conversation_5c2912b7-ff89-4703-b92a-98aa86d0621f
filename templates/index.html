<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>印錢大師</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="/static/css/index.css">
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                {% if transfer_form %}
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=資金管理"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        新增移轉紀錄
                    </h1>
                </div>
                {% include 'transfer_form.html' %}
                {% elif selected_buttons %}
                <!-- 子選單頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main={{ selected }}"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        {{ button_label }}
                    </h1>
                </div>
                <div class="box mb-5">
                    <div class="buttons">
                        {% for child in selected_buttons %}
                        {% if child == '收入紀錄' %}
                        <a class="button is-link is-light" href="/income_record">{{ child }}</a>
                        {% elif child == '支出紀錄' %}
                        <a class="button is-link is-light" href="/expense_record">{{ child }}</a>
                        {% else %}
                        <button class="button is-link is-light">{{ child }}</button>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% else %}
                <!-- 主選單頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        {{ selected }}
                    </h1>
                </div>
                {% for submenu in submenus %}
                <div class="box mb-5">
                    <h2 class="subtitle">{{ submenu.title }}</h2>
                    <div class="buttons">
                        {% for btn in submenu.buttons %}
                        {% if btn.label == '新增移轉紀錄' %}
                        <a class="button is-link is-light" href="/transfer">{{ btn.label }}</a>
                        {% elif btn.label == '資金移轉列表' %}
                        <a class="button is-link is-light" href="/transfer/list">{{ btn.label }}</a>

                        {# 不顯示這個按鈕 #}
                        {% elif btn.label == '分享帳簿' %}
                        <a class="button is-link is-light" href="/share_account/list">{{ btn.label }}</a>
                        {% elif btn.label == '新增預付費用' %}
                        <a class="button is-link is-light" href="/add_prepaid_expense">{{ btn.label }}</a>
                        {% elif btn.label == '新增各項攤提' %}
                        <a class="button is-link is-light" href="/add_amortization">{{ btn.label }}</a>
                        {% elif btn.label == '新增固定資產' %}
                        <a class="button is-link is-light" href="/add_fixed_asset">{{ btn.label }}</a>
                        {% elif btn.label == '新增 無形資產/商譽' %}
                        <a class="button is-link is-light" href="/add_intangible_asset">{{ btn.label }}</a>
                        {% elif btn.label == '財產列表' %}
                        <a class="button is-link is-light" href="/asset_list">{{ btn.label }}</a>
                        {% elif btn.label == '發薪作業' %}
                        <a class="button is-link is-light" href="/payroll_process">{{ btn.label }}</a>
                        {% elif btn.label == '勞保/健保/退休金管理' %}
                        <a class="button is-link is-light" href="/insurance_manage">{{ btn.label }}</a>
                        {% elif btn.label == '新增員工' %}
                        <a class="button is-link is-light" href="/add_employee">{{ btn.label }}</a>
                        {% elif btn.label == '員工管理列表' %}
                        <a class="button is-link is-light" href="/employee_list">{{ btn.label }}</a>
                        {% elif btn.label == '公司設定' %}
                        <a class="button is-link is-light" href="/company_setting">{{ btn.label }}</a>
                        {% elif btn.label == '薪資設定' %}
                        <a class="button is-link is-light" href="/salary_setting">{{ btn.label }}</a>
                        {% elif btn.label == '建立勞報單' or btn.label == '新增勞務報酬單' %}
                        <a class="button is-link is-light" href="/add_service_reward">{{ btn.label }}</a>
                        {% elif btn.label == '勞務報酬列表' %}
                        <a class="button is-link is-light" href="/service_reward_list">{{ btn.label }}</a>
                        {% elif btn.label == '扣繳申報作業' %}
                        <a class="button is-link is-light" href="/withholding_declare">{{ btn.label }}</a>
                        {% elif btn.label == '部門管理' %}
                        <a class="button is-link is-light" href="/department_manage">{{ btn.label }}</a>
                        {% elif btn.label == '專案管理' %}
                        <a class="button is-link is-light" href="/project_manage">{{ btn.label }}</a>
                        {% elif btn.label == '收支對象管理' %}
                        <a class="button is-link is-light" href="/payment_identity_list">{{ btn.label }}</a>
                        {% elif btn.label == '帳戶設定' %}
                        <a class="button is-link is-light" href="/account_setting">{{ btn.label }}</a>
                        {% elif btn.label == '開帳設定' %}
                        <a class="button is-link is-light" href="/opening_setting">{{ btn.label }}</a>
                        {% elif btn.label == '新增銀行借款' %}
                        <a class="button is-link is-light" href="/bankloan/create">{{ btn.label }}</a>
                        {% elif btn.label == '銀行借款列表' %}
                        <a class="button is-link is-light" href="/bankloan/list">{{ btn.label }}</a>
                        {% elif btn.children and btn.children|length > 0 %}
                        <a class="button is-link is-light"
                            href="/?main={{ selected }}&submenu={{ submenu.title }}&button={{ btn.label }}">{{ btn.label
                            }}</a>
                        {% elif btn.label == '收入紀錄列表' %}
                        <a class="button is-link is-light" href="/income_list">{{ btn.label }}</a>
                        {% elif btn.label == '支出紀錄列表' %}
                        <a class="button is-link is-light" href="/expense_list">{{ btn.label }}</a>
                        {% elif btn.label == '應收應付逾期' %}
                        <a class="button is-link is-light" href="/ac_delay_list">{{ btn.label }}</a>
                        {% elif btn.label == '傳票管理' %}
                        <a class="button is-link is-light" href="/voucher_manage">{{ btn.label }}</a>
                        {% elif btn.label == '科目管理' %}
                        <a class="button is-link is-light" href="/accounting/subject_manage">{{ btn.label }}</a>
                        {% elif btn.label == '成本結轉' %}
                        <a class="button is-link is-light" href="/cost_transfer">{{ btn.label }}</a>
                        {% elif btn.label == '進/銷項稅額管理' %}
                        <a class="button is-link is-light" href="/tax_manage">{{ btn.label }}</a>
                        {% elif btn.label == '基本資料' %}
                        <a class="button is-link is-light" href="/basic_info">{{ btn.label }}</a>
                        {% elif btn.label == '新增資金紀錄' %}
                        <a class="button is-link is-light" href="/fund_record/create">{{ btn.label }}</a>
                        {% elif btn.label == '資金紀錄列表' %}
                        <a class="button is-link is-light" href="/fund_record/list">{{ btn.label }}</a>
                        {% elif btn.label == '帳戶明細' %}
                        <a class="button is-link is-light" href="/account_detail">{{ btn.label }}</a>
                        {% elif btn.label == '資產負債表' %}
                        <a class="button is-link is-light" href="/reports/balance_sheet">{{ btn.label }}</a>
                        {% elif btn.label == '損益表' %}
                        <a class="button is-link is-light" href="/reports/income_statement">{{ btn.label }}</a>
                        {% elif btn.label == '會計科目明細' %}
                        <a class="button is-link is-light" href="/reports/account_subject_detail">{{ btn.label }}</a>

                        {% elif btn.label == '專案/部門別分析' %}
                        <a class="button is-link is-light" href="/reports/project_department_analysis">{{ btn.label
                            }}</a>
                        {% elif btn.label == '收支對象分析' %}
                        <a class="button is-link is-light" href="/reports/payment_object_analysis">{{ btn.label }}</a>

                        {% elif btn.label == '月度報表' %}
                        <a class="button is-link is-light" href="/reports/monthly">{{ btn.label }}</a>
                        {% elif btn.label == '年度報表' %}
                        <a class="button is-link is-light" href="/reports/yearly">{{ btn.label }}</a>
                        {% elif btn.label == '帳戶對帳單' %}
                        <a class="button is-link is-light" href="/reports/account_statement">{{ btn.label }}</a>
                        {% elif btn.label == '現金流量' %}
                        <a class="button is-link is-light" href="/reports/cash_flow">{{ btn.label }}</a>
                        {% elif btn.label == '付款狀態' %}
                        <a class="button is-link is-light" href="/reports/payment_status">{{ btn.label }}</a>
                        {% elif btn.label == '快取管理' %}
                        <a class="button is-link is-light" href="/reports">{{ btn.label }}</a>
                        {% elif btn.get('url') %}
                        <a class="button is-link is-light" href="{{ btn.url }}">{{ btn.label }}</a>
                        {% else %}
                        <button class="button is-link is-light">{{ btn.label }}</button>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 右下角浮動管理面板 -->
    <div id="adminPanel" class="admin-panel">
        <button id="adminToggle" class="admin-toggle">
            <i class="fas fa-cog"></i>
        </button>
        <div id="adminMenu" class="admin-menu is-hidden">
            <!-- 密碼驗證畫面 -->
            <div id="passwordScreen" class="password-screen">
                <div class="admin-menu-header">
                    <h6 class="title is-6 has-text-white">
                        <i class="fas fa-lock"></i> 管理員驗證
                    </h6>
                    <button id="adminClose" class="delete is-small"></button>
                </div>
                <div class="password-content">
                    <div class="field">
                        <label class="label">請輸入管理員密碼</label>
                        <div class="control has-icons-left">
                            <input id="adminPassword" class="input" type="password" placeholder="輸入密碼">
                            <span class="icon is-small is-left">
                                <i class="fas fa-key"></i>
                            </span>
                        </div>
                    </div>
                    <div class="field">
                        <div class="control">
                            <button id="loginBtn" class="button is-primary is-fullwidth">
                                <span class="icon"><i class="fas fa-sign-in-alt"></i></span>
                                <span>登入</span>
                            </button>
                        </div>
                    </div>
                    <div id="passwordError" class="notification is-danger is-light is-hidden">
                        <i class="fas fa-exclamation-triangle"></i> 密碼錯誤，請重新輸入
                    </div>
                </div>
            </div>

            <!-- 管理功能畫面 -->
            <div id="adminContent" class="admin-content is-hidden">
                <div class="admin-menu-header">
                    <h6 class="title is-6 has-text-white">系統管理</h6>
                    <div class="header-buttons">
                        <button id="logoutBtn" class="button is-small is-light" title="登出">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                        <button id="adminClose2" class="delete is-small"></button>
                    </div>
                </div>
                <div class="admin-menu-content">
                    <div class="admin-section">
                        <p class="admin-section-title">監控與效能</p>
                        <a href="/admin/monitoring" class="admin-link">
                            <i class="fas fa-chart-line"></i> 系統監控
                        </a>
                        <a href="/admin/errors/dashboard" class="admin-link">
                            <i class="fas fa-exclamation-triangle"></i> 錯誤監控
                        </a>
                        <a href="/admin/error-test/dashboard" class="admin-link">
                            <i class="fas fa-bug"></i> 錯誤測試
                        </a>
                        <a href="/admin/performance_test" class="admin-link">
                            <i class="fas fa-tachometer-alt"></i> 效能測試
                        </a>
                    </div>
                    <div class="admin-section">
                        <p class="admin-section-title">審計系統</p>
                        <a href="/audit_dashboard" class="admin-link">
                            <i class="fas fa-shield-alt"></i> 審計儀表板
                        </a>
                        <a href="/audit_search" class="admin-link">
                            <i class="fas fa-search"></i> 審計搜尋
                        </a>
                        <a href="/deleted_records" class="admin-link">
                            <i class="fas fa-trash-restore"></i> 已刪除記錄
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/index.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</body>

</html>