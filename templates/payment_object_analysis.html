<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收支對象分析</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar {
            width: 200px;
            font-size: 20px;
        }
        .main-content {
            margin-top: 20px;
        }
        .analysis-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #3273dc;
        }
        .metric-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }
        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .data-table {
            margin-top: 20px;
        }
        .positive {
            color: #28a745;
        }
        .negative {
            color: #dc3545;
        }
        .object-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: box-shadow 0.3s;
        }
        .object-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .ranking-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .ranking-item:last-child {
            border-bottom: none;
        }
        .ranking-number {
            background: #3273dc;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="main-content">
                    <!-- 導航 -->
                    <div class="box mb-5">
                        <h2 class="subtitle">
                            <a href="javascript:history.back()" style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                            <i class="fas fa-users"></i> 收支對象分析
                        </h2>
                    </div>

                    <!-- 篩選條件 -->
                    <div class="filter-section">
                        <div class="columns">
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">分析類型</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="analysisType" onchange="switchAnalysisType()">
                                                <option value="all">全部對象</option>
                                                <option value="income">收入對象</option>
                                                <option value="expense">支出對象</option>
                                                <option value="top">主要往來對象</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">起始日期</label>
                                    <div class="control">
                                        <input class="input" type="date" id="startDate" value="2024-01-01">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">結束日期</label>
                                    <div class="control">
                                        <input class="input" type="date" id="endDate" value="2024-12-31">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">&nbsp;</label>
                                    <div class="control">
                                        <button class="button is-primary is-fullwidth" onclick="generateAnalysis()">
                                            <span class="icon"><i class="fas fa-chart-bar"></i></span>
                                            <span>生成分析</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 總覽指標 -->
                    <div class="analysis-header">
                        <h3 class="title is-4 has-text-white">收支對象總覽</h3>
                        <div class="columns">
                            <div class="column">
                                <div class="metric-card">
                                    <div class="metric-label">收入對象數</div>
                                    <div class="metric-value" id="incomeObjects">0</div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="metric-card">
                                    <div class="metric-label">支出對象數</div>
                                    <div class="metric-value" id="expenseObjects">0</div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="metric-card">
                                    <div class="metric-label">總收入金額</div>
                                    <div class="metric-value positive" id="totalIncome">NT$ 0</div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="metric-card">
                                    <div class="metric-label">總支出金額</div>
                                    <div class="metric-value negative" id="totalExpense">NT$ 0</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 圖表區域 -->
                    <div class="columns">
                        <div class="column is-half">
                            <div class="box">
                                <h4 class="subtitle">收支對象分布</h4>
                                <div class="chart-container">
                                    <canvas id="objectDistributionChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="column is-half">
                            <div class="box">
                                <h4 class="subtitle">主要往來對象排名</h4>
                                <div id="objectRanking">
                                    <!-- 動態載入排名 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收支對象明細表格 -->
                    <div class="box">
                        <h4 class="subtitle">
                            <i class="fas fa-table"></i> 收支對象明細
                        </h4>
                        <div class="field has-addons mb-4">
                            <div class="control">
                                <input class="input" type="text" placeholder="搜尋對象名稱..." id="searchInput" onkeyup="filterTable()">
                            </div>
                            <div class="control">
                                <button class="button is-info" onclick="filterTable()">
                                    <span class="icon"><i class="fas fa-search"></i></span>
                                </button>
                            </div>
                        </div>
                        <div class="table-container data-table">
                            <table class="table is-fullwidth is-striped is-hoverable">
                                <thead>
                                    <tr class="has-background-primary has-text-white">
                                        <th>對象名稱</th>
                                        <th>對象類型</th>
                                        <th>收入金額</th>
                                        <th>支出金額</th>
                                        <th>淨額</th>
                                        <th>交易次數</th>
                                        <th>平均金額</th>
                                        <th>最後交易日</th>
                                    </tr>
                                </thead>
                                <tbody id="objectTable">
                                    <!-- 動態載入內容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 分析洞察 -->
                    <div class="box">
                        <h4 class="subtitle">
                            <i class="fas fa-lightbulb"></i> 分析洞察
                        </h4>
                        <div id="analysisInsights">
                            <div class="notification is-info is-light">
                                <p><strong>提示：</strong>請選擇分析期間並點擊"生成分析"按鈕來查看詳細的收支對象分析報告。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let distributionChart = null;

        // 從後端傳來的真實資料
        let realData = null;
        {% if analysis_data %}
        realData = {{ analysis_data | tojson }};
        {% endif %}

        // 切換分析類型
        function switchAnalysisType() {
            generateAnalysis();
        }

        // 生成分析
        function generateAnalysis() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const analysisType = document.getElementById('analysisType').value;
            
            if (!startDate || !endDate) {
                alert('請選擇分析期間');
                return;
            }

            loadAnalysisData(startDate, endDate, analysisType);
        }

        // 載入分析資料
        function loadAnalysisData(startDate, endDate, analysisType) {
            // 使用後端真實資料或預設資料
            const data = realData || {
                overview: {
                    income_objects: 0,
                    expense_objects: 0,
                    total_income: 0,
                    total_expense: 0
                },
                objects: []
            };

            // 根據分析類型篩選資料
            let filteredObjects = data.objects;
            if (analysisType === 'income') {
                filteredObjects = data.objects.filter(obj => obj.income > 0);
            } else if (analysisType === 'expense') {
                filteredObjects = data.objects.filter(obj => obj.expense > 0);
            } else if (analysisType === 'top') {
                filteredObjects = data.objects.slice(0, 10); // 前10大往來對象
            }

            updateOverview(data.overview);
            updateChart(filteredObjects);
            updateRanking(filteredObjects);
            updateTable(filteredObjects);
            updateInsights(data);
        }

        // 更新總覽指標
        function updateOverview(overview) {
            document.getElementById('incomeObjects').textContent = overview.income_objects;
            document.getElementById('expenseObjects').textContent = overview.expense_objects;
            document.getElementById('totalIncome').textContent = `NT$ ${overview.total_income.toLocaleString()}`;
            document.getElementById('totalExpense').textContent = `NT$ ${overview.total_expense.toLocaleString()}`;
        }

        // 更新圖表
        function updateChart(objects) {
            if (!objects || objects.length === 0) {
                const ctx = document.getElementById('objectDistributionChart').getContext('2d');
                if (distributionChart) distributionChart.destroy();
                
                distributionChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['無資料'],
                        datasets: [{
                            label: '收入',
                            data: [0],
                            backgroundColor: '#28a745'
                        }, {
                            label: '支出',
                            data: [0],
                            backgroundColor: '#dc3545'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                return;
            }

            const ctx = document.getElementById('objectDistributionChart').getContext('2d');
            if (distributionChart) distributionChart.destroy();
            
            // 按類型分組
            const typeGroups = {};
            objects.forEach(obj => {
                if (!typeGroups[obj.type]) {
                    typeGroups[obj.type] = { income: 0, expense: 0 };
                }
                typeGroups[obj.type].income += obj.income;
                typeGroups[obj.type].expense += obj.expense;
            });

            distributionChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: Object.keys(typeGroups),
                    datasets: [{
                        label: '收入',
                        data: Object.values(typeGroups).map(g => g.income),
                        backgroundColor: '#28a745'
                    }, {
                        label: '支出',
                        data: Object.values(typeGroups).map(g => g.expense),
                        backgroundColor: '#dc3545'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'NT$ ' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }

        // 更新排名
        function updateRanking(objects) {
            const ranking = document.getElementById('objectRanking');
            
            if (!objects || objects.length === 0) {
                ranking.innerHTML = '<div class="notification is-info">目前沒有收支對象資料</div>';
                return;
            }

            const sortedObjects = [...objects].sort((a, b) => Math.abs(b.net) - Math.abs(a.net)).slice(0, 5);
            
            ranking.innerHTML = sortedObjects.map((obj, index) => `
                <div class="ranking-item">
                    <div style="display: flex; align-items: center;">
                        <div class="ranking-number">${index + 1}</div>
                        <div>
                            <strong>${obj.name}</strong>
                            <br>
                            <small class="has-text-grey">${obj.type}</small>
                        </div>
                    </div>
                    <div class="has-text-right">
                        <div class="${obj.net >= 0 ? 'positive' : 'negative'}" style="font-weight: bold;">
                            NT$ ${Math.abs(obj.net).toLocaleString()}
                        </div>
                        <small class="has-text-grey">${obj.transactions} 筆交易</small>
                    </div>
                </div>
            `).join('');
        }

        // 更新表格
        function updateTable(objects) {
            const tbody = document.getElementById('objectTable');
            
            if (!objects || objects.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="has-text-centered">無資料</td></tr>';
                return;
            }

            tbody.innerHTML = objects.map(obj => `
                <tr>
                    <td><strong>${obj.name}</strong></td>
                    <td><span class="tag is-light">${obj.type}</span></td>
                    <td class="positive">${obj.income > 0 ? 'NT$ ' + obj.income.toLocaleString() : '-'}</td>
                    <td class="negative">${obj.expense > 0 ? 'NT$ ' + obj.expense.toLocaleString() : '-'}</td>
                    <td class="${obj.net >= 0 ? 'positive' : 'negative'}">NT$ ${obj.net.toLocaleString()}</td>
                    <td>${obj.transactions}</td>
                    <td>NT$ ${obj.avg_amount.toLocaleString()}</td>
                    <td>${obj.last_date || '-'}</td>
                </tr>
            `).join('');
        }

        // 篩選表格
        function filterTable() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const rows = document.querySelectorAll('#objectTable tr');
            
            rows.forEach(row => {
                if (row.cells.length > 1) { // 忽略無資料的行
                    const name = row.cells[0].textContent.toLowerCase();
                    const type = row.cells[1].textContent.toLowerCase();
                    if (name.includes(searchTerm) || type.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            });
        }

        // 更新洞察
        function updateInsights(data) {
            const insights = document.getElementById('analysisInsights');
            
            if (!data.objects || data.objects.length === 0) {
                insights.innerHTML = '<div class="notification is-info">暫無足夠資料進行分析</div>';
                return;
            }

            const incomeObjects = data.objects.filter(obj => obj.income > 0);
            const expenseObjects = data.objects.filter(obj => obj.expense > 0);
            
            const topIncomeObject = incomeObjects.length > 0 ? 
                incomeObjects.reduce((max, obj) => obj.income > max.income ? obj : max, incomeObjects[0]) : null;
            const topExpenseObject = expenseObjects.length > 0 ? 
                expenseObjects.reduce((max, obj) => obj.expense > max.expense ? obj : max, expenseObjects[0]) : null;
            
            let insightsHtml = '';
            
            if (topIncomeObject) {
                insightsHtml += `
                    <div class="notification is-success is-light">
                        <h5 class="subtitle is-6"><i class="fas fa-trophy"></i> 主要收入來源</h5>
                        <p><strong>${topIncomeObject.name}</strong> 是最大收入來源，貢獻 NT$ ${topIncomeObject.income.toLocaleString()}</p>
                    </div>
                `;
            }
            
            if (topExpenseObject) {
                insightsHtml += `
                    <div class="notification is-warning is-light">
                        <h5 class="subtitle is-6"><i class="fas fa-exclamation-triangle"></i> 主要支出對象</h5>
                        <p><strong>${topExpenseObject.name}</strong> 是最大支出對象，支出 NT$ ${topExpenseObject.expense.toLocaleString()}</p>
                    </div>
                `;
            }
            
            const totalTransactions = data.objects.reduce((sum, o) => sum + o.transactions, 0);
            const avgTransactionAmount = totalTransactions > 0 ? 
                (data.overview.total_income + data.overview.total_expense) / totalTransactions : 0;
            
            insightsHtml += `
                <div class="notification is-info is-light">
                    <h5 class="subtitle is-6"><i class="fas fa-chart-line"></i> 往來分析</h5>
                    <ul>
                        <li>活躍收支對象：${data.objects.length} 個</li>
                        <li>總交易次數：${totalTransactions} 筆</li>
                        <li>平均交易金額：NT$ ${avgTransactionAmount.toLocaleString()}</li>
                    </ul>
                </div>
            `;
            
            insights.innerHTML = insightsHtml;
        }

        // 頁面載入時自動載入資料
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const currentYear = today.getFullYear();
            document.getElementById('startDate').value = `${currentYear}-01-01`;
            document.getElementById('endDate').value = `${currentYear}-12-31`;
            
            // 自動載入真實資料
            loadAnalysisData();
        });
    </script>
</body>
</html>
