<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新資產負債表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .sidebar {
            width: 200px;
            font-size: 20px;
        }

        .main-content {
            margin-left: 220px;
            padding: 20px;
        }

        .balance-sheet-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .balance-sheet-table th,
        .balance-sheet-table td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: left;
        }

        .balance-sheet-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .amount-cell {
            text-align: right;
            font-family: monospace;
        }

        .category-header {
            background-color: #e8f4f8;
            font-weight: bold;
        }

        .sub-category {
            background-color: #f8f9fa;
            font-style: italic;
        }

        .total-row {
            background-color: #e9ecef;
            font-weight: bold;
            border-top: 2px solid #333;
        }

        .percentage {
            color: #666;
            font-size: 0.9em;
        }

        .balance-indicator {
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .balanced {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .unbalanced {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .date-filter {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>

<body>
    <h1 class="title has-text-centered">印錢大師</h1>
    <!-- 側邊欄 -->
    {% include 'sidebar.html' %}

    <!-- 主要內容 -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="columns">
                <div class="column">
                    <div class="card">
                        <div class="card-header">
                            <h1 class="card-title">
                                <i class="fas fa-balance-scale"></i> 資產負債表（新版）
                            </h1>
                        </div>
                        <div class="card-content">
                            <!-- 日期篩選 -->
                            <div class="date-filter">
                                <form method="GET" class="field is-grouped">
                                    <div class="control">
                                        <label class="label">截止日期：</label>
                                        <input type="date" name="as_of_date" value="{{ as_of_date }}" class="input">
                                    </div>
                                    <div class="control">
                                        <button type="submit" class="button is-primary">
                                            <i class="fas fa-search"></i> 查詢
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- 平衡狀態指示器 -->
                            <div class="balance-indicator {{ 'balanced' if data.totals.is_balanced else 'unbalanced' }}">
                                {% if data.totals.is_balanced %}
                                <i class="fas fa-check-circle"></i> 資產負債表平衡
                                {% else %}
                                <i class="fas fa-exclamation-triangle"></i> 資產負債表不平衡
                                {% endif %}
                                <div class="mt-2">
                                    <strong>總資產：</strong> {{ "{:,}".format(data.totals.total_assets) }} 元<br>
                                    <strong>負債 + 權益：</strong> {{ "{:,}".format(data.totals.total_liabilities_equity) }} 元
                                </div>
                            </div>

                            <!-- 資產負債表 -->
                            <div class="columns">
                                <!-- 左側：資產 -->
                                <div class="column is-6">
                                    <table class="balance-sheet-table">
                                        <thead>
                                            <tr>
                                                <th colspan="3" class="has-text-centered">資產</th>
                                            </tr>
                                            <tr>
                                                <th>科目</th>
                                                <th>金額</th>
                                                <th>%</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for category_name, items in data.assets.items() %}
                                            {% if items %}
                                            <tr class="category-header">
                                                <td colspan="3">{{ category_name }}</td>
                                            </tr>
                                            {% for item in items %}
                                            <tr>
                                                <td>
                                                    <a href="{{ url_for('new_reports.subject_detail', subject_code=item.subject_code) }}">
                                                        {{ item.subject_code }} {{ item.subject_name }}
                                                    </a>
                                                </td>
                                                <td class="amount-cell">{{ "{:,}".format(item.balance) }}</td>
                                                <td class="amount-cell percentage">{{ "%.1f"|format(item.percentage) }}%</td>
                                            </tr>
                                            {% endfor %}
                                            <tr class="sub-category">
                                                <td><strong>{{ category_name }}小計</strong></td>
                                                <td class="amount-cell"><strong>{{ "{:,}".format(items|sum(attribute='balance')) }}</strong></td>
                                                <td class="amount-cell percentage"><strong>{{ "%.1f"|format(items|sum(attribute='percentage')) }}%</strong></td>
                                            </tr>
                                            {% endif %}
                                            {% endfor %}
                                            <tr class="total-row">
                                                <td><strong>資產總計</strong></td>
                                                <td class="amount-cell"><strong>{{ "{:,}".format(data.totals.total_assets) }}</strong></td>
                                                <td class="amount-cell"><strong>100.0%</strong></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 右側：負債與權益 -->
                                <div class="column is-6">
                                    <table class="balance-sheet-table">
                                        <thead>
                                            <tr>
                                                <th colspan="3" class="has-text-centered">負債與權益</th>
                                            </tr>
                                            <tr>
                                                <th>科目</th>
                                                <th>金額</th>
                                                <th>%</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- 負債 -->
                                            {% for category_name, items in data.liabilities.items() %}
                                            {% if items %}
                                            <tr class="category-header">
                                                <td colspan="3">{{ category_name }}</td>
                                            </tr>
                                            {% for item in items %}
                                            <tr>
                                                <td>
                                                    <a href="{{ url_for('new_reports.subject_detail', subject_code=item.subject_code) }}">
                                                        {{ item.subject_code }} {{ item.subject_name }}
                                                    </a>
                                                </td>
                                                <td class="amount-cell">{{ "{:,}".format(item.balance) }}</td>
                                                <td class="amount-cell percentage">{{ "%.1f"|format(item.percentage) }}%</td>
                                            </tr>
                                            {% endfor %}
                                            <tr class="sub-category">
                                                <td><strong>{{ category_name }}小計</strong></td>
                                                <td class="amount-cell"><strong>{{ "{:,}".format(items|sum(attribute='balance')) }}</strong></td>
                                                <td class="amount-cell percentage"><strong>{{ "%.1f"|format(items|sum(attribute='percentage')) }}%</strong></td>
                                            </tr>
                                            {% endif %}
                                            {% endfor %}
                                            
                                            <!-- 權益 -->
                                            {% for category_name, items in data.equity.items() %}
                                            {% if items %}
                                            <tr class="category-header">
                                                <td colspan="3">{{ category_name }}</td>
                                            </tr>
                                            {% for item in items %}
                                            <tr>
                                                <td>
                                                    <a href="{{ url_for('new_reports.subject_detail', subject_code=item.subject_code) }}">
                                                        {{ item.subject_code }} {{ item.subject_name }}
                                                    </a>
                                                </td>
                                                <td class="amount-cell">{{ "{:,}".format(item.balance) }}</td>
                                                <td class="amount-cell percentage">{{ "%.1f"|format(item.percentage) }}%</td>
                                            </tr>
                                            {% endfor %}
                                            <tr class="sub-category">
                                                <td><strong>{{ category_name }}小計</strong></td>
                                                <td class="amount-cell"><strong>{{ "{:,}".format(items|sum(attribute='balance')) }}</strong></td>
                                                <td class="amount-cell percentage"><strong>{{ "%.1f"|format(items|sum(attribute='percentage')) }}%</strong></td>
                                            </tr>
                                            {% endif %}
                                            {% endfor %}
                                            
                                            <tr class="total-row">
                                                <td><strong>負債與權益總計</strong></td>
                                                <td class="amount-cell"><strong>{{ "{:,}".format(data.totals.total_liabilities_equity) }}</strong></td>
                                                <td class="amount-cell"><strong>{{ "%.1f"|format((data.totals.total_liabilities_equity / data.totals.total_assets * 100) if data.totals.total_assets > 0 else 0) }}%</strong></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- 報表說明 -->
                            <div class="notification is-info is-light">
                                <p><strong>報表說明：</strong></p>
                                <ul>
                                    <li>本報表基於新的複式記帳系統生成</li>
                                    <li>數據來源：Transaction 和 JournalEntry 表</li>
                                    <li>截止日期：{{ data.as_of_date }}</li>
                                    <li>點擊科目名稱可查看明細</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
