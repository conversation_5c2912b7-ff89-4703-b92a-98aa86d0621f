{# 資金移轉紀錄列表頁 #}
<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <title>資金移轉紀錄</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      background: #f5f6fa;
    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }

    .sidebar {
      max-width: 200px;
      min-width: 180px;
      width: 200px;
    }

    .sidebar .menu-label {
      font-size: 14px;
      font-weight: bold;
    }

    .sidebar .menu-list a {
      font-size: 20px;
      padding: 0.5em 0.75em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .main-content {
      padding: 2rem 1rem;
    }

    .header-section {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;
    }

    .back-link {
      font-size: 1.5rem;
      color: #363636;
      text-decoration: none;
      margin-right: 1rem;
      font-weight: 600;
    }

    .back-link:hover {
      color: #3273dc;
    }

    .header-buttons {
      margin-left: auto;
      display: flex;
      gap: 0.5rem;
    }

    .filter-section {
      background: white;
      padding: 1rem;
      border-radius: 6px;
      margin-bottom: 1rem;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .table-container {
      background: white;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
    }

    .table {
      margin-bottom: 0;
    }

    .table th {
      background-color: #3273dc;
      color: white;
      font-weight: 600;
      border: none;
      padding: 1rem 0.75rem;
      text-align: center;
      vertical-align: middle;
      font-size: 0.9rem;
    }

    .table td {
      padding: 0.75rem;
      text-align: center;
      vertical-align: middle;
      border-bottom: 1px solid #dbdbdb;
      font-size: 0.9rem;
    }

    .table tbody tr:hover {
      background-color: #fafafa;
    }

    .amount-cell {
      font-family: 'Courier New', monospace;
      font-weight: 600;
    }

    .edit-btn {
      background: #48c774;
      color: white;
      border: none;
      border-radius: 50%;
      padding: 0.5rem;
      font-size: 1rem;
      cursor: pointer;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 2.2rem;
      height: 2.2rem;
      transition: all 0.2s;
      line-height: 1;
    }

    .edit-btn:hover {
      background: #3ec46d;
      color: white;
      transform: scale(1.1);
    }

    .edit-btn i {
      font-size: 1rem;
    }



    .pagination-section {
      background: white;
      padding: 1rem;
      border-radius: 6px;
      margin-top: 1rem;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .pagination-info {
      color: #666;
      font-size: 0.9rem;
    }

    .date-range-input {
      width: 140px;
      font-size: 0.9rem;
    }

    .filter-section .control {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .transfer-table thead th {
      background-color: #3273dc !important;
      color: white !important;
      font-weight: 600;
      border-color: #3273dc !important;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">

        <div class="main-content">
          <div class="header-section">
            <a href="/?main=資金管理" class="back-link">← 資金移轉紀錄</a>
            <div class="header-buttons">
              <button class="button is-info is-light">頁面指南</button>
              <a class="button is-link" href="/transfer">＋建立一筆新紀錄</a>
            </div>
          </div>

          <div class="filter-section">
            <div class="control">
              <span class="has-text-weight-semibold">查詢區間</span>
              <input class="input date-range-input is-small" type="date" id="start-date" value="2025-06-12">
              <span>-</span>
              <input class="input date-range-input is-small" type="date" id="end-date" value="2025-07-12">
              <button class="button is-primary is-small" onclick="filterByDate()">查詢</button>
            </div>
          </div>
          <div class="table-container">
            <table class="table is-fullwidth">
              <thead>
                <tr>
                  <th>轉出帳戶</th>
                  <th>轉入帳戶</th>
                  <th>總金額</th>
                  <th>手續費</th>
                  <th>轉移日期</th>
                  <th>備註</th>
                  <th>憑證圖檔</th>
                  <th>編輯</th>
                </tr>
              </thead>
              <tbody>
                {% for row in transfer_records %}
                <tr>
                  <td>{{ row.out_account.name if row.out_account else '-' }}</td>
                  <td>{{ row.in_account.name if row.in_account else '-' }}</td>
                  <td class="amount-cell">${{ '{:,}'.format(row.amount|int if row.amount else 0) }}</td>
                  <td class="amount-cell">${{ '{:,}'.format(row.fee|int if row.fee else 0) }}</td>
                  <td>{{ row.transfer_date.strftime('%Y-%m-%d') if row.transfer_date else '-' }}</td>
                  <td>{{ row.note or '-' }}</td>
                  <td>
                    {% if row.voucher %}
                    <a href="{{ row.voucher }}" target="_blank" class="has-text-link">查看</a>
                    {% else %}
                    -
                    {% endif %}
                  </td>
                  <td>
                    <a class="edit-btn" href="/transfer?edit={{ row.id }}" title="編輯">
                      <i class="fas fa-pen"></i>
                    </a>
                  </td>
                </tr>
                {% else %}
                <tr>
                  <td colspan="8" class="has-text-centered has-text-grey">
                    暫無資金移轉紀錄
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>

          <div class="pagination-section">
            <div class="pagination-info">
              共 1 頁　{{ transfer_records|length }} 筆紀錄
            </div>
            <nav class="pagination is-small" role="navigation" aria-label="pagination">
              <ul class="pagination-list">
                <li><a class="pagination-link is-current" aria-label="Page 1" aria-current="page">1</a></li>
              </ul>
            </nav>
          </div>

        </div>
      </div>
    </div>
  </div>
  <!-- 添加 Font Awesome 圖標 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

  <script>
    function filterByDate() {
      const startDate = document.getElementById('start-date').value;
      const endDate = document.getElementById('end-date').value;

      if (startDate && endDate) {
        // 重新載入頁面並帶上日期參數
        const url = new URL(window.location);
        url.searchParams.set('start_date', startDate);
        url.searchParams.set('end_date', endDate);
        window.location.href = url.toString();
      } else {
        alert('請選擇完整的日期區間');
      }
    }

    // 頁面載入時設定日期參數
    document.addEventListener('DOMContentLoaded', function () {
      const urlParams = new URLSearchParams(window.location.search);
      const startDate = urlParams.get('start_date');
      const endDate = urlParams.get('end_date');

      if (startDate) {
        document.getElementById('start-date').value = startDate;
      }
      if (endDate) {
        document.getElementById('end-date').value = endDate;
      }
    });
  </script>
</body>

</html>