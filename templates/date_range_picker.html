<!-- 日期區間選擇器組件 -->
<div class="date-range-picker" id="date-range-picker">
    <!-- 快速篩選按鈕 -->
    <div class="quick-filters mb-3">
        <div class="buttons has-addons">
            <button class="button is-small" data-range="7">近七日</button>
            <button class="button is-small" data-range="30">近三十日</button>
            <button class="button is-small" data-range="this_month">本月</button>
            <button class="button is-small" data-range="last_month">上月</button>
            <button class="button is-small" data-range="this_quarter">本季</button>
            <button class="button is-small" data-range="last_quarter">上季</button>
            <button class="button is-small" data-range="this_year">今年</button>
            <button class="button is-small" data-range="last_year">去年</button>
            <button class="button is-small" data-range="custom" id="custom-range-btn">自選</button>
        </div>
    </div>

    <!-- 日期輸入框 -->
    <div class="date-inputs mb-3">
        <div class="field has-addons">
            <div class="control is-expanded">
                <input class="input is-rounded" type="text" id="date-display" placeholder="選擇日期範圍" readonly
                    style="min-width: 250px; cursor: pointer;">
            </div>
            <div class="control">
                <button class="button is-rounded" id="calendar-toggle">
                    <span class="icon">
                        <i class="fas fa-calendar"></i>
                    </span>
                </button>
            </div>
        </div>
        <input type="hidden" id="date-start-hidden" name="start_date">
        <input type="hidden" id="date-end-hidden" name="end_date">
    </div>

    <!-- 雙月曆視圖 -->
    <div class="calendar-container" id="calendar-container" style="display: none;">
        <div class="calendar-wrapper">
            <div class="calendar-header">
                <h3 class="title is-6">選擇日期範圍</h3>
                <button class="delete" id="close-calendar"></button>
            </div>

            <div class="calendars">
                <!-- 開始日期月曆 -->
                <div class="calendar">
                    <div class="calendar-nav">
                        <button class="button is-small" id="prev-month-start">
                            <span class="icon is-small">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </button>
                        <span class="month-year" id="start-month-year">2025年1月</span>
                        <button class="button is-small" id="next-month-start">
                            <span class="icon is-small">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </button>
                    </div>
                    <div class="calendar-grid" id="start-calendar">
                        <div class="calendar-header-row">
                            <div>日</div>
                            <div>一</div>
                            <div>二</div>
                            <div>三</div>
                            <div>四</div>
                            <div>五</div>
                            <div>六</div>
                        </div>
                        <div class="calendar-days" id="start-days"></div>
                    </div>
                </div>

                <!-- 結束日期月曆 -->
                <div class="calendar">
                    <div class="calendar-nav">
                        <button class="button is-small" id="prev-month-end">
                            <span class="icon is-small">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </button>
                        <span class="month-year" id="end-month-year">2025年7月</span>
                        <button class="button is-small" id="next-month-end">
                            <span class="icon is-small">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </button>
                    </div>
                    <div class="calendar-grid" id="end-calendar">
                        <div class="calendar-header-row">
                            <div>日</div>
                            <div>一</div>
                            <div>二</div>
                            <div>三</div>
                            <div>四</div>
                            <div>五</div>
                            <div>六</div>
                        </div>
                        <div class="calendar-days" id="end-days"></div>
                    </div>
                </div>
            </div>

            <div class="calendar-footer">
                <button class="button is-primary" id="apply-date-range">確定</button>
                <button class="button" id="cancel-date-range">取消</button>
            </div>
        </div>
    </div>
</div>

<style>
    .date-range-picker {
        position: relative;
    }

    .quick-filters .button {
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
    }

    .quick-filters .button.is-active {
        background-color: #2563eb;
        color: white;
    }

    .calendar-container {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 9999;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        min-width: 600px;
        margin-top: 5px;
    }

    .calendar-wrapper {
        padding: 1rem;
    }

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #eee;
    }

    .calendars {
        display: flex;
        gap: 1rem;
    }

    .calendar {
        flex: 1;
    }

    .calendar-nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .month-year {
        font-weight: bold;
        font-size: 1rem;
    }

    .calendar-grid {
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .calendar-header-row {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        background: #f5f5f5;
        font-weight: bold;
        text-align: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #ddd;
    }

    .calendar-header-row>div {
        padding: 0.25rem;
    }

    .calendar-days {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
    }

    .calendar-day {
        padding: 0.5rem;
        text-align: center;
        cursor: pointer;
        border: none;
        background: none;
        transition: background-color 0.2s;
    }

    .calendar-day:hover {
        background-color: #f0f0f0;
    }

    .calendar-day.other-month {
        color: #ccc;
    }

    .calendar-day.selected {
        background-color: #2563eb;
        color: white;
        border-radius: 4px;
    }

    .calendar-day.in-range {
        background-color: #e3f2fd;
    }

    .calendar-day.selecting {
        background-color: #ffeb3b;
        color: #333;
    }

    .calendar-day.today {
        font-weight: bold;
        border: 2px solid #2563eb;
        border-radius: 4px;
    }

    .calendar-footer {
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #eee;
    }
</style>

<script>
    class DateRangePicker {
        constructor(containerId) {
            this.container = document.getElementById(containerId);
            this.startDate = null;
            this.endDate = null;
            this.currentStartMonth = new Date();
            this.currentEndMonth = new Date();
            this.currentEndMonth.setMonth(this.currentEndMonth.getMonth() + 1);

            // 簡單的選擇狀態：false=選擇起始日期，true=選擇結束日期
            this.selectingEndDate = false;

            this.init();
        }

        init() {
            this.bindEvents();
            this.renderCalendars();
            this.setDefaultRange();
        }

        bindEvents() {
            // 快速篩選按鈕
            this.container.querySelectorAll('.quick-filters .button').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.handleQuickFilter(e.target.dataset.range);
                });
            });

            // 日曆切換
            const calendarToggle = this.container.querySelector('#calendar-toggle');
            const dateDisplay = this.container.querySelector('#date-display');

            if (calendarToggle) {
                calendarToggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.toggleCalendar();
                });
            }

            // 點擊日期輸入框也能打開日曆
            if (dateDisplay) {
                dateDisplay.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showCalendar();
                });
            }

            // 關閉日曆
            const closeCalendar = this.container.querySelector('#close-calendar');
            if (closeCalendar) {
                closeCalendar.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.hideCalendar();
                });
            }

            // 月份導航
            const prevMonthStart = this.container.querySelector('#prev-month-start');
            const nextMonthStart = this.container.querySelector('#next-month-start');
            const prevMonthEnd = this.container.querySelector('#prev-month-end');
            const nextMonthEnd = this.container.querySelector('#next-month-end');

            if (prevMonthStart) {
                prevMonthStart.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.changeMonth('start', -1);
                });
            }
            if (nextMonthStart) {
                nextMonthStart.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.changeMonth('start', 1);
                });
            }
            if (prevMonthEnd) {
                prevMonthEnd.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.changeMonth('end', -1);
                });
            }
            if (nextMonthEnd) {
                nextMonthEnd.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.changeMonth('end', 1);
                });
            }

            // 確定和取消
            const applyBtn = this.container.querySelector('#apply-date-range');
            const cancelBtn = this.container.querySelector('#cancel-date-range');

            if (applyBtn) {
                applyBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.applyDateRange();
                });
            }
            if (cancelBtn) {
                cancelBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.hideCalendar();
                });
            }

            // 點擊外部關閉
            document.addEventListener('click', (e) => {
                const calendarContainer = this.container.querySelector('#calendar-container');
                const calendarToggle = this.container.querySelector('#calendar-toggle');
                const dateDisplay = this.container.querySelector('#date-display');

                if (calendarContainer && calendarContainer.style.display !== 'none' &&
                    !calendarContainer.contains(e.target) &&
                    !calendarToggle.contains(e.target) &&
                    !dateDisplay.contains(e.target)) {
                    this.hideCalendar();
                }
            });
        }

        handleQuickFilter(range) {
            const today = new Date();
            let startDate, endDate;

            if (range === 'custom') {
                this.startDate = null;
                this.endDate = null;
                this.selectingEndDate = false;
                this.showCalendar();
                return;
            }

            switch (range) {
                case '7':
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() - 6);
                    endDate = new Date(today);
                    break;
                case '30':
                    startDate = new Date(today);
                    startDate.setDate(today.getDate() - 29);
                    endDate = new Date(today);
                    break;
                case 'this_month':
                    startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                    break;
                case 'last_month':
                    startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    endDate = new Date(today.getFullYear(), today.getMonth(), 0);
                    break;
                case 'this_quarter':
                    const quarter = Math.floor(today.getMonth() / 3);
                    startDate = new Date(today.getFullYear(), quarter * 3, 1);
                    endDate = new Date(today.getFullYear(), (quarter + 1) * 3, 0);
                    break;
                case 'last_quarter':
                    const lastQuarter = Math.floor(today.getMonth() / 3) - 1;
                    const lastQuarterYear = lastQuarter < 0 ? today.getFullYear() - 1 : today.getFullYear();
                    const lastQuarterMonth = lastQuarter < 0 ? 9 : lastQuarter * 3;
                    startDate = new Date(lastQuarterYear, lastQuarterMonth, 1);
                    endDate = new Date(lastQuarterYear, lastQuarterMonth + 3, 0);
                    break;
                case 'this_year':
                    startDate = new Date(today.getFullYear(), 0, 1);
                    endDate = new Date(today.getFullYear(), 11, 31);
                    break;
                case 'last_year':
                    startDate = new Date(today.getFullYear() - 1, 0, 1);
                    endDate = new Date(today.getFullYear() - 1, 11, 31);
                    break;
            }

            this.startDate = startDate;
            this.endDate = endDate;
            this.selectingEndDate = false;
            this.updateDisplay();
            this.triggerChange();
            this.hideCalendar();
        }

        setDefaultRange() {
            this.handleQuickFilter('this_month');
        }

        updateDisplay() {
            const display = this.container.querySelector('#date-display');
            if (this.startDate && this.endDate) {
                const startStr = this.formatDate(this.startDate);
                const endStr = this.formatDate(this.endDate);
                display.value = `${startStr} ~ ${endStr}`;

                this.container.querySelector('#date-start-hidden').value = startStr;
                this.container.querySelector('#date-end-hidden').value = endStr;
            } else if (this.startDate) {
                const startStr = this.formatDate(this.startDate);
                display.value = `${startStr} ~ 請選擇結束日期`;

                this.container.querySelector('#date-start-hidden').value = startStr;
                this.container.querySelector('#date-end-hidden').value = '';
            } else {
                display.value = '';
                this.container.querySelector('#date-start-hidden').value = '';
                this.container.querySelector('#date-end-hidden').value = '';
            }
        }

        formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        toggleCalendar() {
            const container = this.container.querySelector('#calendar-container');
            if (container.style.display === 'none' || !container.style.display) {
                this.showCalendar();
            } else {
                this.hideCalendar();
            }
        }

        showCalendar() {
            const container = this.container.querySelector('#calendar-container');
            container.style.display = 'block';
            this.renderCalendars();
        }

        hideCalendar() {
            const container = this.container.querySelector('#calendar-container');
            container.style.display = 'none';
        }

        changeMonth(type, delta) {
            if (type === 'start') {
                this.currentStartMonth.setMonth(this.currentStartMonth.getMonth() + delta);
            } else {
                this.currentEndMonth.setMonth(this.currentEndMonth.getMonth() + delta);
            }
            this.renderCalendars();
        }

        renderCalendars() {
            this.renderCalendar('start', this.currentStartMonth);
            this.renderCalendar('end', this.currentEndMonth);
        }

        renderCalendar(type, date) {
            const daysContainer = this.container.querySelector(`#${type}-days`);
            const monthYearElement = this.container.querySelector(`#${type}-month-year`);

            monthYearElement.textContent = `${date.getFullYear()}年${date.getMonth() + 1}月`;
            daysContainer.innerHTML = '';

            const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
            const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
            const firstDayOfWeek = firstDay.getDay();

            // 添加上個月的日期
            for (let i = firstDayOfWeek - 1; i >= 0; i--) {
                const prevDate = new Date(firstDay);
                prevDate.setDate(firstDay.getDate() - (firstDayOfWeek - i));
                this.addDayElement(daysContainer, prevDate, 'other-month');
            }

            // 添加當月的日期
            for (let day = 1; day <= lastDay.getDate(); day++) {
                const currentDate = new Date(date.getFullYear(), date.getMonth(), day);
                this.addDayElement(daysContainer, currentDate, 'current-month');
            }

            // 添加下個月的日期
            const remainingDays = 42 - (firstDayOfWeek + lastDay.getDate());
            for (let day = 1; day <= remainingDays; day++) {
                const nextDate = new Date(lastDay);
                nextDate.setDate(lastDay.getDate() + day);
                this.addDayElement(daysContainer, nextDate, 'other-month');
            }
        }

        addDayElement(container, date, className) {
            const dayElement = document.createElement('button');
            dayElement.className = `calendar-day ${className}`;
            dayElement.textContent = date.getDate();
            dayElement.dataset.date = this.formatDate(date);

            const today = new Date();
            if (date.toDateString() === today.toDateString()) {
                dayElement.classList.add('today');
            }

            if (this.startDate && this.formatDate(date) === this.formatDate(this.startDate)) {
                dayElement.classList.add('selected');
            }
            if (this.endDate && this.formatDate(date) === this.formatDate(this.endDate)) {
                dayElement.classList.add('selected');
            }

            if (this.startDate && this.endDate) {
                const dateStr = this.formatDate(date);
                const startStr = this.formatDate(this.startDate);
                const endStr = this.formatDate(this.endDate);

                if (dateStr >= startStr && dateStr <= endStr) {
                    dayElement.classList.add('in-range');
                }
            }

            dayElement.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.selectDate(date);
            });

            container.appendChild(dayElement);
        }

        // 修正的日期選擇邏輯
        selectDate(date) {
            const selectedDate = new Date(date);
            console.log('選擇日期:', this.formatDate(selectedDate), '當前狀態:', this.selectingEndDate ? '選擇結束日期' : '選擇起始日期');

            if (!this.selectingEndDate) {
                // 選擇起始日期
                console.log('設置起始日期');
                this.startDate = selectedDate;
                this.endDate = null;
                this.selectingEndDate = true;
            } else {
                // 選擇結束日期
                console.log('設置結束日期');

                if (selectedDate < this.startDate) {
                    // 如果選擇的日期早於起始日期，交換它們
                    this.endDate = new Date(this.startDate);
                    this.startDate = selectedDate;
                } else {
                    this.endDate = selectedDate;
                }

                this.selectingEndDate = false;
            }

            console.log('選擇後：起始=', this.formatDate(this.startDate), '結束=', this.endDate ? this.formatDate(this.endDate) : 'null');

            this.renderCalendars();
            this.updateDisplay();
            this.triggerChange();
        }

        applyDateRange() {
            this.hideCalendar();
            this.triggerChange();
        }

        triggerChange() {
            const event = new CustomEvent('dateRangeChanged', {
                detail: {
                    startDate: this.startDate,
                    endDate: this.endDate,
                    startDateStr: this.startDate ? this.formatDate(this.startDate) : '',
                    endDateStr: this.endDate ? this.formatDate(this.endDate) : ''
                }
            });
            this.container.dispatchEvent(event);
        }
    }

    // 初始化日期選擇器
    document.addEventListener('DOMContentLoaded', function () {
        const datePicker = new DateRangePicker('date-range-picker');

        // 監聽日期變化事件
        document.getElementById('date-range-picker').addEventListener('dateRangeChanged', function (e) {
            document.getElementById('date-start-hidden').value = e.detail.startDateStr;
            document.getElementById('date-end-hidden').value = e.detail.endDateStr;
        });
    });
</script>