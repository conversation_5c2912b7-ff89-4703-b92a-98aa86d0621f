<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>資料庫效能測試</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 導航 -->
                <div class="box mb-5">
                    <h2 class="subtitle">
                        <a href="/" style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        <i class="fas fa-tachometer-alt"></i> 資料庫效能測試
                    </h2>
                    <div class="buttons">
                        <a class="button is-info is-light" href="/admin/monitoring">
                            <span class="icon"><i class="fas fa-chart-line"></i></span>
                            <span>系統監控</span>
                        </a>
                        <a class="button is-primary" href="/admin/performance_test">
                            <span class="icon"><i class="fas fa-database"></i></span>
                            <span>效能測試</span>
                        </a>
                    </div>
                </div>

                <!-- 控制面板 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-play"></i> 測試控制
                    </h3>
                    <div class="buttons">
                        <button class="button is-success" id="runTestsBtn">
                            <span class="icon"><i class="fas fa-play"></i></span>
                            <span>執行效能測試</span>
                        </button>
                        <button class="button is-info" id="loadIndexBtn">
                            <span class="icon"><i class="fas fa-list"></i></span>
                            <span>查看索引</span>
                        </button>
                        <button class="button is-warning" id="loadStatsBtn">
                            <span class="icon"><i class="fas fa-chart-bar"></i></span>
                            <span>表格統計</span>
                        </button>
                    </div>
                </div>

                <!-- 載入狀態 -->
                <div id="loadingIndicator" class="notification is-info is-hidden">
                    <button class="delete"></button>
                    <i class="fas fa-spinner fa-spin"></i> 正在執行測試，請稍候...
                </div>

                <!-- 表格統計 -->
                <div id="tableStatsSection" class="box mb-5 is-hidden">
                    <h3 class="subtitle">
                        <i class="fas fa-table"></i> 表格統計資訊
                    </h3>
                    <div id="tableStatsContent"></div>
                </div>

                <!-- 索引資訊 -->
                <div id="indexSection" class="box mb-5 is-hidden">
                    <h3 class="subtitle">
                        <i class="fas fa-database"></i> 資料庫索引
                    </h3>
                    <div id="indexContent"></div>
                </div>

                <!-- 測試結果 -->
                <div id="testResultsSection" class="is-hidden">
                    <div class="box mb-5">
                        <h3 class="subtitle">
                            <i class="fas fa-chart-line"></i> 效能測試結果
                        </h3>
                        <div class="content">
                            <p class="has-text-grey">測試說明：每個查詢執行 10 次，顯示平均、最小、最大執行時間（毫秒）</p>
                        </div>
                        <div id="testResultsContent"></div>
                    </div>

                    <!-- 效能圖表 -->
                    <div class="box">
                        <h3 class="subtitle">
                            <i class="fas fa-chart-bar"></i> 效能對比圖表
                        </h3>
                        <canvas id="performanceChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let performanceChart = null;

        // 執行效能測試
        document.getElementById('runTestsBtn').addEventListener('click', function() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            const testResultsSection = document.getElementById('testResultsSection');
            
            loadingIndicator.classList.remove('is-hidden');
            testResultsSection.classList.add('is-hidden');
            
            fetch('/admin/performance_test/api/run_tests')
                .then(response => response.json())
                .then(data => {
                    loadingIndicator.classList.add('is-hidden');
                    
                    if (data.success) {
                        displayTestResults(data.test_results);
                        createPerformanceChart(data.test_results);
                        testResultsSection.classList.remove('is-hidden');
                    } else {
                        alert('測試失敗: ' + (data.error || '未知錯誤'));
                    }
                })
                .catch(error => {
                    loadingIndicator.classList.add('is-hidden');
                    console.error('Error:', error);
                    alert('執行測試時發生錯誤');
                });
        });

        // 載入索引資訊
        document.getElementById('loadIndexBtn').addEventListener('click', function() {
            fetch('/admin/performance_test/api/index_usage')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayIndexInfo(data.indexes);
                        document.getElementById('indexSection').classList.remove('is-hidden');
                    } else {
                        alert('載入索引資訊失敗: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('載入索引資訊時發生錯誤');
                });
        });

        // 載入表格統計
        document.getElementById('loadStatsBtn').addEventListener('click', function() {
            fetch('/admin/performance_test/api/table_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayTableStats(data.table_stats);
                        document.getElementById('tableStatsSection').classList.remove('is-hidden');
                    } else {
                        alert('載入表格統計失敗: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('載入表格統計時發生錯誤');
                });
        });

        // 顯示測試結果
        function displayTestResults(testResults) {
            const container = document.getElementById('testResultsContent');
            container.innerHTML = '';

            testResults.forEach(test => {
                const testBox = document.createElement('div');
                testBox.className = 'box';
                
                let html = `
                    <h4 class="title is-5">${test.test_name}</h4>
                    <p class="subtitle is-6">${test.description}</p>
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>查詢類型</th>
                                    <th>平均時間 (ms)</th>
                                    <th>最小時間 (ms)</th>
                                    <th>最大時間 (ms)</th>
                                    <th>狀態</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                Object.entries(test.queries).forEach(([queryName, result]) => {
                    const statusClass = result.error ? 'tag is-danger' : 
                                      result.avg_time < 10 ? 'tag is-success' :
                                      result.avg_time < 50 ? 'tag is-warning' : 'tag is-danger';
                    
                    const statusText = result.error ? '錯誤' :
                                     result.avg_time < 10 ? '優秀' :
                                     result.avg_time < 50 ? '良好' : '需優化';

                    html += `
                        <tr>
                            <td>${queryName}</td>
                            <td>${result.error ? '-' : result.avg_time}</td>
                            <td>${result.error ? '-' : result.min_time}</td>
                            <td>${result.error ? '-' : result.max_time}</td>
                            <td><span class="${statusClass}">${statusText}</span></td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;

                testBox.innerHTML = html;
                container.appendChild(testBox);
            });
        }

        // 顯示索引資訊
        function displayIndexInfo(indexes) {
            const container = document.getElementById('indexContent');
            
            let html = `
                <div class="notification is-info is-light">
                    <strong>總計：${indexes.length} 個索引</strong>
                </div>
                <div class="table-container">
                    <table class="table is-fullwidth is-striped">
                        <thead>
                            <tr>
                                <th>索引名稱</th>
                                <th>表格</th>
                                <th>SQL 定義</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            indexes.forEach(index => {
                html += `
                    <tr>
                        <td><code>${index.name}</code></td>
                        <td>${index.table}</td>
                        <td><small>${index.sql || '系統索引'}</small></td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = html;
        }

        // 顯示表格統計
        function displayTableStats(stats) {
            const container = document.getElementById('tableStatsContent');
            
            let html = `
                <div class="columns is-multiline">
            `;

            Object.entries(stats).forEach(([table, count]) => {
                html += `
                    <div class="column is-one-third">
                        <div class="notification is-primary is-light">
                            <p class="title is-4">${count.toLocaleString()}</p>
                            <p class="subtitle is-6">${table}</p>
                        </div>
                    </div>
                `;
            });

            html += `</div>`;
            container.innerHTML = html;
        }

        // 創建效能圖表
        function createPerformanceChart(testResults) {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            
            if (performanceChart) {
                performanceChart.destroy();
            }

            const labels = [];
            const avgTimes = [];
            const colors = [];

            testResults.forEach(test => {
                Object.entries(test.queries).forEach(([queryName, result]) => {
                    if (!result.error) {
                        labels.push(queryName.substring(0, 30) + '...');
                        avgTimes.push(result.avg_time);
                        
                        // 根據效能設定顏色
                        if (result.avg_time < 10) {
                            colors.push('rgba(72, 187, 120, 0.8)'); // 綠色
                        } else if (result.avg_time < 50) {
                            colors.push('rgba(246, 173, 85, 0.8)'); // 橙色
                        } else {
                            colors.push('rgba(245, 101, 101, 0.8)'); // 紅色
                        }
                    }
                });
            });

            performanceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '平均執行時間 (ms)',
                        data: avgTimes,
                        backgroundColor: colors,
                        borderColor: colors.map(color => color.replace('0.8', '1')),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '執行時間 (毫秒)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '查詢類型'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '資料庫查詢效能對比'
                        },
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        // 關閉通知
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('delete')) {
                e.target.parentElement.classList.add('is-hidden');
            }
        });
    </script>
</body>
</html>