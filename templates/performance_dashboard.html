<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能監控儀表板</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <section class="hero is-primary">
            <div class="hero-body">
                <div class="container">
                    <h1 class="title">
                        📊 性能監控儀表板
                    </h1>
                    <h2 class="subtitle">
                        會計系統實時性能監控
                    </h2>
                </div>
            </div>
        </section>

        <div class="section">
            <!-- 關鍵指標 -->
            <div class="columns">
                <div class="column">
                    <div class="metric-card">
                        <div class="metric-value" id="uptime">--</div>
                        <div class="metric-label">系統運行時間</div>
                    </div>
                </div>
                <div class="column">
                    <div class="metric-card">
                        <div class="metric-value" id="total-requests">--</div>
                        <div class="metric-label">總請求數</div>
                    </div>
                </div>
                <div class="column">
                    <div class="metric-card">
                        <div class="metric-value" id="avg-response-time">--</div>
                        <div class="metric-label">平均響應時間 (ms)</div>
                    </div>
                </div>
                <div class="column">
                    <div class="metric-card">
                        <div class="metric-value" id="memory-usage">--</div>
                        <div class="metric-label">記憶體使用率</div>
                    </div>
                </div>
            </div>

            <!-- 圖表區域 -->
            <div class="columns">
                <div class="column is-half">
                    <div class="box">
                        <h3 class="title is-5">📈 響應時間趨勢</h3>
                        <div class="chart-container">
                            <canvas id="responseTimeChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="column is-half">
                    <div class="box">
                        <h3 class="title is-5">💻 系統資源使用</h3>
                        <div class="chart-container">
                            <canvas id="systemResourceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 詳細統計 -->
            <div class="columns">
                <div class="column is-half">
                    <div class="box">
                        <h3 class="title is-5">🔥 熱門端點</h3>
                        <div id="top-endpoints">
                            <div class="has-text-centered">
                                <span class="icon">
                                    <i class="fas fa-spinner fa-pulse"></i>
                                </span>
                                載入中...
                            </div>
                        </div>
                    </div>
                </div>
                <div class="column is-half">
                    <div class="box">
                        <h3 class="title is-5">🐌 慢請求記錄</h3>
                        <div id="slow-requests">
                            <div class="has-text-centered">
                                <span class="icon">
                                    <i class="fas fa-spinner fa-pulse"></i>
                                </span>
                                載入中...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 錯誤統計 -->
            <div class="box">
                <h3 class="title is-5">❌ 錯誤統計</h3>
                <div id="error-stats">
                    <div class="has-text-centered">
                        <span class="icon">
                            <i class="fas fa-spinner fa-pulse"></i>
                        </span>
                        載入中...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 刷新按鈕 -->
    <button class="button is-primary is-large refresh-btn" onclick="refreshData()">
        <span class="icon">
            <i class="fas fa-sync-alt"></i>
        </span>
        <span>刷新</span>
    </button>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script>
        let responseTimeChart, systemResourceChart;

        // 初始化圖表
        function initCharts() {
            // 響應時間圖表
            const ctx1 = document.getElementById('responseTimeChart').getContext('2d');
            responseTimeChart = new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '響應時間 (ms)',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 系統資源圖表
            const ctx2 = document.getElementById('systemResourceChart').getContext('2d');
            systemResourceChart = new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU使用率 (%)',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.1
                    }, {
                        label: '記憶體使用率 (%)',
                        data: [],
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        // 獲取性能數據
        async function fetchPerformanceData() {
            try {
                const response = await fetch('/admin/performance/api');
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('獲取性能數據失敗:', error);
                return null;
            }
        }

        // 更新儀表板
        async function updateDashboard() {
            const data = await fetchPerformanceData();
            if (!data) return;

            // 更新關鍵指標
            updateMetrics(data.performance_summary);
            
            // 更新圖表
            updateCharts(data);
            
            // 更新詳細統計
            updateTopEndpoints(data.performance_summary.top_endpoints);
            updateSlowRequests(data.slow_requests);
            updateErrorStats(data.performance_summary.error_counts);
        }

        // 更新關鍵指標
        function updateMetrics(summary) {
            document.getElementById('uptime').textContent = formatUptime(summary.uptime_seconds);
            document.getElementById('total-requests').textContent = summary.total_requests.toLocaleString();
            document.getElementById('avg-response-time').textContent = Math.round(summary.avg_response_time);
            document.getElementById('memory-usage').textContent = summary.current_memory_percent.toFixed(1) + '%';
        }

        // 格式化運行時間
        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            if (days > 0) {
                return `${days}天 ${hours}小時`;
            } else if (hours > 0) {
                return `${hours}小時 ${minutes}分鐘`;
            } else {
                return `${minutes}分鐘`;
            }
        }

        // 更新圖表
        function updateCharts(data) {
            // 更新響應時間圖表
            if (data.slow_requests && data.slow_requests.length > 0) {
                const labels = data.slow_requests.map(req => new Date(req.time).toLocaleTimeString());
                const responseData = data.slow_requests.map(req => req.response_time);
                
                responseTimeChart.data.labels = labels;
                responseTimeChart.data.datasets[0].data = responseData;
                responseTimeChart.update();
            }

            // 更新系統資源圖表
            if (data.system_metrics) {
                const cpuData = data.system_metrics.cpu_usage || [];
                const memoryData = data.system_metrics.memory_usage || [];
                
                if (cpuData.length > 0) {
                    const labels = cpuData.map(item => new Date(item.time).toLocaleTimeString());
                    
                    systemResourceChart.data.labels = labels;
                    systemResourceChart.data.datasets[0].data = cpuData.map(item => item.percent);
                    systemResourceChart.data.datasets[1].data = memoryData.map(item => item.percent);
                    systemResourceChart.update();
                }
            }
        }

        // 更新熱門端點
        function updateTopEndpoints(endpoints) {
            const container = document.getElementById('top-endpoints');
            if (!endpoints || endpoints.length === 0) {
                container.innerHTML = '<p class="has-text-grey">暫無數據</p>';
                return;
            }

            const html = endpoints.map(endpoint => `
                <div class="level">
                    <div class="level-left">
                        <div class="level-item">
                            <div>
                                <p class="heading">端點</p>
                                <p class="title is-6">${endpoint.endpoint}</p>
                            </div>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <div class="has-text-right">
                                <p class="heading">請求數</p>
                                <p class="title is-6">${endpoint.count}</p>
                            </div>
                        </div>
                        <div class="level-item">
                            <div class="has-text-right">
                                <p class="heading">平均時間</p>
                                <p class="title is-6">${endpoint.avg_time}ms</p>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 更新慢請求
        function updateSlowRequests(slowRequests) {
            const container = document.getElementById('slow-requests');
            if (!slowRequests || slowRequests.length === 0) {
                container.innerHTML = '<p class="has-text-grey">暫無慢請求</p>';
                return;
            }

            const html = slowRequests.slice(0, 5).map(req => `
                <div class="notification is-warning is-light">
                    <p><strong>${req.method} ${req.endpoint}</strong></p>
                    <p>響應時間: ${Math.round(req.response_time)}ms</p>
                    <p>時間: ${new Date(req.time).toLocaleString()}</p>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 更新錯誤統計
        function updateErrorStats(errorCounts) {
            const container = document.getElementById('error-stats');
            if (!errorCounts || Object.keys(errorCounts).length === 0) {
                container.innerHTML = '<p class="has-text-grey">暫無錯誤記錄</p>';
                return;
            }

            const html = Object.entries(errorCounts).map(([code, count]) => `
                <div class="level">
                    <div class="level-left">
                        <div class="level-item">
                            <span class="tag is-danger">HTTP ${code}</span>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <span class="tag is-light">${count} 次</span>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // 刷新數據
        function refreshData() {
            updateDashboard();
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            updateDashboard();
            
            // 每30秒自動刷新
            setInterval(updateDashboard, 30000);
        });
    </script>
</body>
</html>