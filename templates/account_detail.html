<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>帳戶明細</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    body {
      margin: 0;
      padding: 0;

      background: #f5f6fa;
      font-family: 'Microsoft JhengHei', sans-serif;

    }

    .column.is-narrow {
      flex: none;
      width: 200px !important;
      max-width: 200px !important;
      min-width: 200px !important;
    }

    .sidebar {
      max-width: 200px;
      min-width: 180px;
      width: 200px;
    }

    .sidebar .menu-label {
      font-size: 14px;
      font-weight: bold;
    }

    .sidebar .menu-list a {
      font-size: 20px;
      padding: 0.5em 0.75em;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    /* 帳戶資訊卡片 */
    .account-info-card {
      background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
      color: white;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .account-info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .account-name {
      font-size: 18px;
      font-weight: bold;
      margin: 0;
    }

    .account-details {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .account-meta {
      flex: 1;
    }

    .account-number {
      font-size: 14px;
      opacity: 0.9;
      margin-bottom: 5px;
    }

    .account-bank {
      font-size: 14px;
      opacity: 0.9;
    }

    .account-balance {
      text-align: right;
    }

    .balance-label {
      font-size: 12px;
      opacity: 0.8;
      margin-bottom: 5px;
    }

    .balance-amount {
      font-size: 28px;
      font-weight: bold;
      color: #fff;
    }

    /* 主要內容區域 */
    .main-content {
      padding: 2rem 1rem;
    }

    .header-section {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;
    }

    .back-link {
      font-size: 1.5rem;
      color: #363636;
      text-decoration: none;
      margin-right: 1rem;
      font-weight: 600;
    }

    .back-link:hover {
      color: #3273dc;
    }

    .header-buttons {
      margin-left: auto;
      display: flex;
      gap: 0.5rem;
    }

    .account-selector {
      background: white;
      padding: 1rem;
      border-radius: 6px;
      margin-bottom: 1rem;
      box-shadow: 0 2px 3px rgba(10, 10, 10, 0.1);
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .select-full {
      width: 100%;
    }

    /* 交易記錄表格 */
    .transactions-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .table-header {
      background: linear-gradient(90deg, #4a90e2 0%, #357abd 100%);
      color: white;
      padding: 15px 20px;
      font-weight: bold;
    }

    .transactions-table {
      width: 100%;
      border-collapse: collapse;
    }


    .transactions-table th {
      background: #4a90e2;
      color: white;
      padding: 12px 8px;

      text-align: center;
      font-weight: bold;
      font-size: 14px;
      border: none;
    }

    .transactions-table td {
      padding: 10px 8px;
      text-align: center;
      border-bottom: 1px solid #e8e8e8;
      font-size: 13px;
    }

    .transactions-table tbody tr:hover {
      background-color: #f8f9fa;
    }

    .transactions-table tbody tr:nth-child(even) {
      background-color: #fafbfc;
    }

    .amount-positive {
      color: #28a745;
      font-weight: bold;
    }

    .amount-negative {
      color: #dc3545;
      font-weight: bold;
    }

    .amount-neutral {
      color: #333;
    }

    /* 分頁 */
    .pagination-container {
      padding: 15px 20px;
      background: white;
      border-top: 1px solid #e8e8e8;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .pagination {
      display: flex;
      gap: 5px;
      align-items: center;
    }

    .pagination-btn {
      padding: 8px 12px;
      border: 1px solid #ddd;
      background: white;
      color: #333;
      text-decoration: none;
      border-radius: 4px;
      font-size: 12px;
    }

    .pagination-btn:hover {
      background: #f8f9fa;
    }

    .pagination-btn.active {
      background: #4a90e2;
      color: white;
      border-color: #4a90e2;
    }

    .pagination-info {
      margin-left: 20px;
      font-size: 12px;
      color: #666;
    }
  </style>
</head>

<body>
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <!-- 頁面標題 -->
        <div class="mb-4">
          <h1 class="title is-4">
            <a href="/?main=資金管理"
              style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
            帳戶明細
          </h1>
        </div>
        <div class="main-content">
          <div class="header-section">
            <div class="header-buttons">
              <button class="button is-info is-light">頁面指南</button>
            </div>
          </div>

          <!-- 帳戶選擇器 -->
          <div class="account-selector">
            <div class="field">
              <label class="label">選擇帳戶：</label>
              <div class="control">
                <div class="select select-full">
                  <select id="account-selector" onchange="selectAccount()">
                    <option value="">請選擇帳戶</option>
                    {% for account in all_accounts %}
                    <option value="{{ account.id }}" {% if account.id==selected_account_id %}selected{% endif %}>
                      {{ account.name }}{% if account.account_number %} ({{ account.account_number }}){% endif %}
                    </option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- 交易記錄表格 -->
          <div class="transactions-container">
            <div class="table-header">
              交易明細
            </div>

            <table class="transactions-table">
              <thead>
                <tr>
                  <th>交易日期</th>
                  <th>收入金額</th>
                  <th>支出金額</th>
                  <th>餘額</th>
                  <th>摘要</th>
                  <th>備註</th>
                  <th>憑證</th>
                </tr>
              </thead>
              <tbody>
                {% if transactions %}
                {% for transaction in transactions %}
                <tr>
                  <td>{{ transaction.date }}</td>
                  <td>
                    {% if transaction.amount_in > 0 %}
                    <span class="amount-positive">${{ "{:,}".format(transaction.amount_in) }}</span>
                    {% else %}
                    <span class="amount-positive">$0</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if transaction.amount_out > 0 %}
                    <span class="amount-negative">${{ "{:,}".format(transaction.amount_out) }}</span>
                    {% else %}
                    <span class="amount-negative">$0</span>
                    {% endif %}
                  </td>
                  <td><span class="amount-neutral">${{ "{:,}".format(transaction.balance) }}</span></td>
                  <td>{{ transaction.description }}</td>
                  <td>{{ transaction.note }}</td>
                  <td>{{ transaction.voucher }}</td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td colspan="7" style="text-align: center; color: #666; padding: 2rem;">
                    {% if account_info %}
                    此帳戶目前沒有交易記錄
                    {% else %}
                    請選擇帳戶以查看交易記錄
                    {% endif %}
                  </td>
                </tr>
                {% endif %}
              </tbody>
            </table>

            <!-- 分頁 -->
            <div class="pagination-container">
              <div class="pagination">
                <a href="#" class="pagination-btn">上一頁</a>
                <a href="#" class="pagination-btn active">1</a>
                <a href="#" class="pagination-btn">下一頁</a>
              </div>
              <div class="pagination-info">共 1 頁，{{ transactions|length if transactions else 0 }} 筆記錄</div>
            </div>
          </div>

          <!-- 帳戶資訊卡片 -->
          {% if account_info %}
          <div class="account-info-card">
            <div class="account-info-header">
              <h3 class="account-name">{{ account_info.name }}</h3>
              <div class="account-balance">
                <div class="balance-label">目前餘額</div>
                <div class="balance-amount">¥{{ "{:,}".format(account_info.balance) }}元</div>
              </div>
            </div>
            <div class="account-details">
              <div class="account-meta">
                <div class="account-number">帳號：{{ account_info.account_number or '無' }}</div>
                {% if account_info.bank_name %}
                <div class="account-bank">銀行：{{ account_info.bank_name }}</div>
                {% endif %}
                {% if account_info.init_amount %}
                <div class="account-bank">期初金額：¥{{ "{:,}".format(account_info.init_amount) }}元</div>
                {% endif %}
              </div>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <script>
    function selectAccount() {
      const selector = document.getElementById('account-selector');
      const accountId = selector.value;

      if (accountId) {
        // 重新載入頁面並帶上選擇的帳戶ID
        window.location.href = `/account_detail?account_id=${accountId}`;
      } else {
        // 清除帳戶選擇
        window.location.href = '/account_detail';
      }
    }
  </script>
</body>

</html>