<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支出紀錄</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .column.is-narrow {
            flex: none;
            width: 200px !important;
            max-width: 200px !important;
            min-width: 200px !important;
        }

        .sidebar {
            max-width: 200px;
            min-width: 180px;
            width: 200px;
        }

        .sidebar .menu-label {
            font-size: 14px;
            font-weight: bold;
        }

        .sidebar .menu-list a {
            font-size: 20px;
            padding: 0.5em 0.75em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .tab-custom {
            background: #2563eb;
            color: #fff;
            border-radius: 8px 8px 0 0;
            padding: 0.75rem 2rem;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .tab-custom.inactive {
            background: #e5e7eb;
            color: #222;
        }

        .box-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .is-toggle .button.is-selected {
            background: #2563eb;
            color: #fff;
        }

        .button.is-selected,
        .button.is-selected.is-primary {
            background: #2563eb !important;
            color: #fff !important;
            border-color: #2563eb !important;
            box-shadow: 0 2px 8px rgba(37, 99, 235, 0.08);
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題和分頁標籤 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=收支帳簿"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        新增帳務
                    </h1>
                    
                    <!-- 分頁標籤 -->
                    <div class="tabs is-toggle is-fullwidth mb-4">
                        <ul>
                            <li>
                                <a href="/income_record" class="tab-custom inactive" onclick="switchTab('income')">
                                    <span class="icon is-small">
                                        <i class="fas fa-plus-circle"></i>
                                    </span>
                                    <span>收入紀錄</span>
                                </a>
                            </li>
                            <li class="is-active">
                                <a href="/expense_record" class="tab-custom" onclick="switchTab('expense')">
                                    <span class="icon is-small">
                                        <i class="fas fa-minus-circle"></i>
                                    </span>
                                    <span>支出紀錄</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="box box-shadow" style="padding:0;">
                    <form class="p-5" method="post" enctype="multipart/form-data">
                        <div class="columns">
                            <div class="column is-6">
                                <div class="columns is-mobile">
                                    <div class="column is-3">
                                        <div class="field">
                                            <label class="label">記帳時間</label>
                                            <div class="control has-icons-right">
                                                <input class="input" type="date" name="a_time" value="2025-07-01">
                                                <span class="icon is-small is-right">
                                                    <i class="fas fa-calendar"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-4">
                                        <div class="field">
                                            <label class="label">名稱</label>
                                            <div class="control">
                                                <input class="input" type="text" name="name" placeholder="請輸入名稱">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-3">
                                        <div class="field">
                                            <label class="label">總計(含稅)</label>
                                            <div class="control">
                                                <input class="input" type="number" name="total" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-2">
                                        <div class="field">
                                            <label class="label">手續費</label>
                                            <div class="control">
                                                <input class="input" type="number" name="extra_fee" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                                                </div>
                                <div class="columns is-mobile">
                                    <div class="column">
                                        <div class="field">
                                            <label class="label">收支對象</label>
                                            <div class="control" style="position: relative;">
                                                <div class="field has-addons">
                                                    <div class="control is-expanded">
                                                        <input class="input" type="text" id="identity-input" 
                                                               placeholder="輸入對象名稱，支持搜索和自動新增" 
                                                               autocomplete="off">
                                                        <input type="hidden" id="payment_identity_id" name="payment_identity_id" value="">
                                                    </div>
                                                    <div class="control">
                                                        <button type="button" class="button is-link" id="manage-identity-btn"
                                                            title="管理收支對象">
                                                            <span class="icon">
                                                                <i class="fas fa-users"></i>
                                                            </span>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div id="identity-suggestions" class="dropdown-menu" style="display: none; position: absolute; top: 100%; left: 0; right: 0; z-index: 1000; max-height: 200px; overflow-y: auto; background: white; border: 1px solid #ddd; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-top: 2px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="field">
                                            <label class="label">科目</label>
                                            <div class="control is-flex is-align-items-center">
                                                <div class="select">
                                                    <select id="subject-group" name="subject_group">
                                                        <option value="">請選擇科目大類</option>
                                                    </select>
                                                </div>
                                                <div class="select ml-2">
                                                    <select id="subject-code" name="subject_code" disabled>
                                                        <option value="">請選擇科目代碼</option>
                                                    </select>
                                                </div>
                                                <button type="button" class="button is-link ml-3" id="edit-subject-btn"
                                                    title="管理科目" style="padding: 0.4rem 0.7rem;">
                                                    <span class="icon is-medium">
                                                        <i class="fas fa-pen"
                                                            style="color: #fff; font-size: 1.3rem;"></i>
                                                    </span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column">
                                        <div class="field">
                                            <label class="label">資金帳戶</label>
                                            <div class="control">
                                                <div class="select is-fullwidth">
                                                    <select name="account_id">
                                                        <option value="">請選擇帳戶</option>
                                                        {% for acc in accounts %}
                                                        <option value="{{ acc.id }}">{{ acc.name }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 使用更多功能模組 -->
                                {% include 'more_section.html' %}
                            </div>
                            <div class="column is-6">
                                <!-- 使用憑證模組 -->
                                {% include 'voucher_section.html' %}
                                <!-- 使用收付款狀態模組 -->
                                {% include 'payment_status_section.html' %}
                            </div>
                        </div>
                        <div class="field is-grouped is-justify-content-flex-end mt-5">
                            <div class="control">
                                <button class="button is-primary is-large" type="submit">儲存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 更多功能的 JavaScript 已移至 more_section.html 模組中

            const subjects = [
                {
                    group: "進貨成本",
                    items: [
                        { name: "銷貨成本", code: "5000" },
                        { name: "進貨", code: "5100" },
                        { name: "進貨-進貨轉商品存貨", code: "5100999" },
                        { name: "進貨退出", code: "5110" },
                        { name: "進貨折讓", code: "5120" },
                        { name: "其他成本", code: "5990" }
                    ]
                },
                {
                    group: "人事支出",
                    items: [
                        { name: "勞務成本", code: "5910" },
                        { name: "薪資支出", code: "6010" },
                        { name: "薪資支出-公司提撥", code: "6011" },
                        { name: "薪資支出-自願提撥", code: "6012" },
                        { name: "薪資支出-員工獎勵", code: "6013" },
                        { name: "保險費", code: "6100" },
                        { name: "保險費-健保費", code: "6100998" },
                        { name: "保險費-勞保費", code: "6100999" }
                    ]
                },
                {
                    group: "營業費用",
                    items: [
                        { name: "租金支出", code: "6020" },
                        { name: "文具用品", code: "6030" },
                        { name: "運費", code: "6050" },
                        { name: "郵電費", code: "6060" },
                        { name: "修繕費", code: "6070" },
                        { name: "廣告費", code: "6080" },
                        { name: "水電瓦斯費", code: "6090" },
                        { name: "交際費", code: "6110" }
                    ]
                },
                {
                    group: "差旅報銷",
                    items: [
                        { name: "旅費", code: "6040" },
                        { name: "交通費", code: "6310" },
                        { name: "燃料費", code: "6330" }
                    ]
                },
                {
                    group: "其他費損",
                    items: [
                        { name: "利息支出", code: "8010" },
                        { name: "處分資產損失", code: "8030" },
                        { name: "處分投資損失", code: "8033" },
                        { name: "其他損失", code: "8070" },
                        { name: "其他損失-庫存調整數", code: "8070999" }
                    ]
                }
            ];

            const groupSelect = document.getElementById('subject-group');
            const codeSelect = document.getElementById('subject-code');

            subjects.forEach((g, idx) => {
                const opt = document.createElement('option');
                opt.value = idx;
                opt.textContent = g.group;
                groupSelect.appendChild(opt);
            });

            groupSelect.addEventListener('change', function () {
                codeSelect.innerHTML = '<option value="">請選擇科目代碼</option>';
                if (this.value === '') {
                    codeSelect.disabled = true;
                    return;
                }
                const items = subjects[this.value].items;
                items.forEach((item) => {
                    const opt = document.createElement('option');
                    opt.value = item.code;
                    opt.textContent = `${item.code} ${item.name}`;
                    codeSelect.appendChild(opt);
                });
                codeSelect.disabled = false;
            });

            const editSubjectBtn = document.getElementById('edit-subject-btn');
            if (editSubjectBtn) {
                editSubjectBtn.addEventListener('click', function () {
                    window.open('/accounting/subject_manage', '_blank', 'width=900,height=700');
                });
            }

            // 收支對象智能輸入邏輯
            const identityInput = document.getElementById('identity-input');
            const paymentIdentityIdInput = document.getElementById('payment_identity_id');
            const suggestionsContainer = document.getElementById('identity-suggestions');
            let searchTimeout = null;
            let selectedIndex = -1;

            // 搜索建議
            async function searchIdentities(query) {
                if (!query.trim()) {
                    suggestionsContainer.style.display = 'none';
                    return;
                }

                try {
                    const response = await fetch(`/api/payment_identities/search?q=${encodeURIComponent(query)}`);
                    const results = await response.json();
                    showSuggestions(results, query);
                } catch (error) {
                    console.error('搜索失敗:', error);
                }
            }

            // 顯示建議
            function showSuggestions(results, query) {
                suggestionsContainer.innerHTML = '';
                
                if (results.length === 0) {
                    // 沒有找到匹配的，顯示新增選項
                    const addItem = document.createElement('div');
                    addItem.className = 'dropdown-item';
                    addItem.style.padding = '8px 12px';
                    addItem.style.cursor = 'pointer';
                    addItem.style.borderBottom = '1px solid #eee';
                    addItem.innerHTML = `<i class="fas fa-plus" style="color: #3273dc; margin-right: 8px;"></i>新增「${query}」`;
                    addItem.onclick = () => createNewIdentity(query);
                    suggestionsContainer.appendChild(addItem);
                } else {
                    // 顯示現有結果
                    results.forEach((result, index) => {
                        const item = document.createElement('div');
                        item.className = 'dropdown-item';
                        item.style.padding = '8px 12px';
                        item.style.cursor = 'pointer';
                        item.style.borderBottom = '1px solid #eee';
                        item.innerHTML = `
                            <div style="font-weight: 500;">${result.name}</div>
                            <div style="font-size: 0.8em; color: #666;">${result.type_name}${result.tax_id ? ' | ' + result.tax_id : ''}</div>
                        `;
                        item.onclick = () => selectIdentity(result);
                        item.onmouseenter = () => {
                            selectedIndex = index;
                            updateSelection();
                        };
                        suggestionsContainer.appendChild(item);
                    });
                }
                
                suggestionsContainer.style.display = 'block';
                selectedIndex = -1;
            }

            // 更新選中狀態
            function updateSelection() {
                const items = suggestionsContainer.querySelectorAll('.dropdown-item');
                items.forEach((item, index) => {
                    if (index === selectedIndex) {
                        item.style.backgroundColor = '#f0f0f0';
                    } else {
                        item.style.backgroundColor = 'white';
                    }
                });
            }

            // 選擇對象
            function selectIdentity(identity) {
                identityInput.value = identity.name;
                paymentIdentityIdInput.value = identity.id;
                suggestionsContainer.style.display = 'none';
                
                // 顯示選中提示
                showNotification(`已選擇: ${identity.name} (${identity.type_name})`, 'success');
            }

            // 創建新對象
            async function createNewIdentity(name) {
                try {
                    const response = await fetch('/api/payment_identities/create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            name: name,
                            type_name: '一般客戶'  // 預設類別
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        selectIdentity(result);
                        showNotification(`已創建新對象: ${result.name}`, 'success');
                    } else {
                        showNotification(result.error || '創建失敗', 'error');
                    }
                } catch (error) {
                    console.error('創建失敗:', error);
                    showNotification('創建失敗，請稍後重試', 'error');
                }
            }

            // 顯示通知
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification is-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'}`;
                notification.style.position = 'fixed';
                notification.style.top = '20px';
                notification.style.right = '20px';
                notification.style.zIndex = '9999';
                notification.style.maxWidth = '300px';
                notification.innerHTML = `
                    <button class="delete" onclick="this.parentElement.remove()"></button>
                    ${message}
                `;
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 3000);
            }

            // 輸入事件處理
            identityInput.addEventListener('input', function() {
                const query = this.value;
                paymentIdentityIdInput.value = ''; // 清空隱藏的ID
                
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }
                
                searchTimeout = setTimeout(() => {
                    searchIdentities(query);
                }, 300);
            });

            // 鍵盤導航
            identityInput.addEventListener('keydown', function(e) {
                const items = suggestionsContainer.querySelectorAll('.dropdown-item');
                
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                    updateSelection();
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, -1);
                    updateSelection();
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (selectedIndex >= 0 && items[selectedIndex]) {
                        items[selectedIndex].click();
                    } else if (this.value.trim()) {
                        // 如果沒有選中任何項目，但有輸入內容，則創建新的
                        createNewIdentity(this.value.trim());
                    }
                } else if (e.key === 'Escape') {
                    suggestionsContainer.style.display = 'none';
                    selectedIndex = -1;
                }
            });

            // 點擊外部關閉建議
            document.addEventListener('click', function(e) {
                if (!identityInput.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                    suggestionsContainer.style.display = 'none';
                    selectedIndex = -1;
                }
            });

            document.getElementById('manage-identity-btn').addEventListener('click', function () {
                window.open('/payment_identity_list', '_blank', 'width=1000,height=700');
            });

            // 憑證功能的 JavaScript 已移至 voucher_section.html 模組中

            // 分頁切換函數
            window.switchTab = function(tabType) {
                if (tabType === 'income') {
                    window.location.href = '/income_record';
                } else if (tabType === 'expense') {
                    window.location.href = '/expense_record';
                }
            };
        });
    </script>
</body>

</html>