<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色權限設定 - 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="columns">
            <div class="column">
                <nav class="breadcrumb" aria-label="breadcrumbs">
                    <ul>
                        <li><a href="/">首頁</a></li>
                        <li><a href="#">設定</a></li>
                        <li><a href="/admin/permissions/">權限管理</a></li>
                        <li><a href="/admin/permissions/roles">角色管理</a></li>
                        <li class="is-active"><a href="#" aria-current="page">角色權限設定</a></li>
                    </ul>
                </nav>

                <div class="level">
                    <div class="level-left">
                        <div class="level-item">
                            <h1 class="title">
                                <i class="fas fa-key mr-2"></i>
                                角色權限設定
                            </h1>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <a href="/admin/permissions/roles" class="button is-light">
                                <span class="icon">
                                    <i class="fas fa-arrow-left"></i>
                                </span>
                                <span>返回</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 顯示訊息 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="notification is-{{ 'danger' if category == 'error' else 'info' if category == 'info' else 'warning' if category == 'warning' else 'success' }}">
                                <button class="delete"></button>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="box">
                    <div class="media">
                        <div class="media-left">
                            <figure class="image is-64x64">
                                <span class="icon is-large has-text-primary">
                                    <i class="fas fa-user-tag fa-3x"></i>
                                </span>
                            </figure>
                        </div>
                        <div class="media-content">
                            <p class="title is-4">{{ role.display_name }}</p>
                            <p class="subtitle is-6">{{ role.name }}</p>
                            {% if role.description %}
                                <p class="is-size-7 has-text-grey">{{ role.description }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <form method="POST">
                    <div class="box">
                        <h2 class="subtitle">
                            <i class="fas fa-key mr-2"></i>
                            權限分配
                        </h2>
                        
                        <div class="field">
                            <div class="control">
                                <label class="checkbox">
                                    <input type="checkbox" id="select-all">
                                    <strong class="ml-2">全選/全不選</strong>
                                </label>
                            </div>
                        </div>

                        {% for module, permissions in permissions_by_module.items() %}
                        <div class="box">
                            <h3 class="subtitle is-5">
                                <i class="fas fa-folder mr-2"></i>
                                {{ module }} 模組
                            </h3>
                            
                            <div class="columns is-multiline">
                                {% for permission in permissions %}
                                <div class="column is-half">
                                    <label class="checkbox is-block">
                                        <input type="checkbox" name="permissions" value="{{ permission.id }}" 
                                               {% if permission.id in role_permission_ids %}checked{% endif %}>
                                        <strong class="ml-2">{{ permission.display_name }}</strong>
                                        <span class="tag is-light ml-2">{{ permission.action }}</span>
                                        {% if permission.description %}
                                            <br>
                                            <span class="is-size-7 has-text-grey ml-6">{{ permission.description }}</span>
                                        {% endif %}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}

                        <div class="field is-grouped">
                            <div class="control">
                                <button type="submit" class="button is-primary">
                                    <span class="icon">
                                        <i class="fas fa-save"></i>
                                    </span>
                                    <span>儲存權限設定</span>
                                </button>
                            </div>
                            <div class="control">
                                <a href="/admin/permissions/roles" class="button is-light">
                                    <span class="icon">
                                        <i class="fas fa-times"></i>
                                    </span>
                                    <span>取消</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 自動關閉通知
        document.addEventListener('DOMContentLoaded', () => {
            (document.querySelectorAll('.notification .delete') || []).forEach(($delete) => {
                const $notification = $delete.parentNode;
                $delete.addEventListener('click', () => {
                    $notification.parentNode.removeChild($notification);
                });
            });

            // 全選/全不選功能
            const selectAllCheckbox = document.getElementById('select-all');
            const permissionCheckboxes = document.querySelectorAll('input[name="permissions"]');

            selectAllCheckbox.addEventListener('change', function() {
                permissionCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // 檢查是否所有權限都被選中
            permissionCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const allChecked = Array.from(permissionCheckboxes).every(cb => cb.checked);
                    const noneChecked = Array.from(permissionCheckboxes).every(cb => !cb.checked);
                    
                    if (allChecked) {
                        selectAllCheckbox.checked = true;
                        selectAllCheckbox.indeterminate = false;
                    } else if (noneChecked) {
                        selectAllCheckbox.checked = false;
                        selectAllCheckbox.indeterminate = false;
                    } else {
                        selectAllCheckbox.indeterminate = true;
                    }
                });
            });

            // 初始化全選狀態
            const allChecked = Array.from(permissionCheckboxes).every(cb => cb.checked);
            const noneChecked = Array.from(permissionCheckboxes).every(cb => !cb.checked);
            
            if (allChecked) {
                selectAllCheckbox.checked = true;
            } else if (!noneChecked) {
                selectAllCheckbox.indeterminate = true;
            }
        });
    </script>
</body>
</html>