{% extends "admin/base.html" %}

{% block title %}角色管理{% endblock %}

{% block page_icon %}fa-user-tag{% endblock %}
{% block page_title %}角色管理{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/modern-theme.css') }}">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
{% endblock %}

{% block breadcrumb %}
<li><a href="#">設定</a></li>
<li><a href="/admin/permissions/">權限管理</a></li>
<li class="is-active"><a href="#" aria-current="page">角色管理</a></li>
{% endblock %}

{% block content %}

                <div class="level mb-4">
                    <div class="level-left">
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <a href="/admin/permissions/roles/create" class="btn-gradient-primary">
                                <span class="icon">
                                    <i class="fas fa-plus"></i>
                                </span>
                                <span>新增角色</span>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="tabs is-boxed">
                    <ul>
                        <li>
                            <a href="/admin/permissions/">
                                <span class="icon is-small"><i class="fas fa-users" aria-hidden="true"></i></span>
                                <span>用戶管理</span>
                            </a>
                        </li>
                        <li class="is-active">
                            <a>
                                <span class="icon is-small"><i class="fas fa-user-tag" aria-hidden="true"></i></span>
                                <span>角色管理</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 顯示訊息 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="notification is-{{ 'danger' if category == 'error' else 'info' if category == 'info' else 'warning' if category == 'warning' else 'success' }}">
                                <button class="delete"></button>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 角色列表 -->
                <div class="modern-card fade-in-up">
                    <div class="table-container">
                        <table class="modern-table table is-fullwidth">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>角色名稱</th>
                                    <th>顯示名稱</th>
                                    <th>描述</th>
                                    <th>狀態</th>
                                    <th>建立時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for role in roles %}
                                <tr>
                                    <td>{{ role.id }}</td>
                                    <td>
                                        <span class="status-tag-modern is-info">{{ role.name }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ role.display_name }}</strong>
                                    </td>
                                    <td>{{ role.description or '-' }}</td>
                                    <td>
                                        {% if role.is_active %}
                                            <span class="status-tag-modern is-success">啟用</span>
                                        {% else %}
                                            <span class="status-tag-modern is-danger">停用</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if role.created_at %}
                                            {{ role.created_at.strftime('%Y-%m-%d') }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="buttons are-small">
                                            <a href="/admin/permissions/roles/{{ role.id }}/edit" class="btn-gradient-primary" style="font-size: 0.875rem; padding: 6px 12px;">
                                                <span class="icon is-small">
                                                    <i class="fas fa-edit"></i>
                                                </span>
                                                <span>編輯</span>
                                            </a>
                                            <a href="/admin/permissions/roles/{{ role.id }}/permissions" class="btn-gradient-success" style="font-size: 0.875rem; padding: 6px 12px; margin-left: 8px;">
                                                <span class="icon is-small">
                                                    <i class="fas fa-key"></i>
                                                </span>
                                                <span>設定權限</span>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
{% endblock %}

{% block extra_js %}
<script>
    // 自動關閉通知
    document.addEventListener('DOMContentLoaded', () => {
        (document.querySelectorAll('.notification .delete') || []).forEach(($delete) => {
            const $notification = $delete.parentNode;
            $delete.addEventListener('click', () => {
                $notification.parentNode.removeChild($notification);
            });
        });
    });
</script>
{% endblock %}