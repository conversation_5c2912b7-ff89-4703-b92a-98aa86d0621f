{% extends "admin/base.html" %}

{% block title %}權限管理{% endblock %}

{% block page_icon %}fa-shield-alt{% endblock %}
{% block page_title %}權限管理{% endblock %}

{% block breadcrumb %}
<li><a href="#">系統管理</a></li>
<li class="is-active"><a href="#" aria-current="page">權限管理</a></li>
{% endblock %}

{% block content %}
<div class="tabs is-boxed">
    <ul>
        <li class="is-active">
            <a>
                <span class="icon is-small"><i class="fas fa-users" aria-hidden="true"></i></span>
                <span>用戶管理</span>
            </a>
        </li>
        <li>
            <a href="/admin/permissions/roles">
                <span class="icon is-small"><i class="fas fa-user-tag" aria-hidden="true"></i></span>
                <span>角色管理</span>
            </a>
        </li>
    </ul>
</div>

<!-- 用戶管理 -->
<div class="box">
    <h2 class="subtitle">
        <i class="fas fa-users mr-2"></i>
        用戶列表
    </h2>

    <div class="table-container">
        <table class="table is-fullwidth is-striped is-hoverable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用戶名</th>
                    <th>真實姓名</th>
                    <th>電子郵件</th>
                    <th>狀態</th>
                    <th>最後登入</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>{{ user.id }}</td>
                    <td>
                        <strong>{{ user.username }}</strong>
                    </td>
                    <td>{{ user.full_name or '-' }}</td>
                    <td>{{ user.email }}</td>
                    <td>
                        {% if user.is_active %}
                        <span class="tag is-success">啟用</span>
                        {% else %}
                        <span class="tag is-danger">停用</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if user.last_login %}
                        {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                        <span class="has-text-grey">從未登入</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="buttons are-small">
                            <a href="/admin/permissions/users/{{ user.id }}/roles" class="button is-info is-small">
                                <span class="icon">
                                    <i class="fas fa-user-cog"></i>
                                </span>
                                <span>設定角色</span>
                            </a>
                            <a href="/admin/permissions/preview-menu/{{ user.id }}" class="button is-link is-small">
                                <span class="icon">
                                    <i class="fas fa-eye"></i>
                                </span>
                                <span>預覽選單</span>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- 角色概覽 -->
<div class="box">
    <h2 class="subtitle">
        <i class="fas fa-user-tag mr-2"></i>
        角色概覽
    </h2>

    <div class="columns is-multiline">
        {% for role in roles %}
        <div class="column is-one-third">
            <div class="card">
                <div class="card-content">
                    <div class="media">
                        <div class="media-left">
                            <figure class="image is-48x48">
                                <span class="icon is-large has-text-primary">
                                    <i class="fas fa-user-tag fa-2x"></i>
                                </span>
                            </figure>
                        </div>
                        <div class="media-content">
                            <p class="title is-6">{{ role.display_name }}</p>
                            <p class="subtitle is-7 has-text-grey">{{ role.name }}</p>
                        </div>
                    </div>

                    <div class="content">
                        <p class="is-size-7">{{ role.description or '無描述' }}</p>

                        <div class="buttons are-small mt-3">
                            <a href="/admin/permissions/roles/{{ role.id }}/permissions"
                                class="button is-info is-small is-fullwidth">
                                <span class="icon">
                                    <i class="fas fa-key"></i>
                                </span>
                                <span>設定權限</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- 快速統計 -->
<div class="box">
    <h2 class="subtitle">
        <i class="fas fa-chart-bar mr-2"></i>
        系統統計
    </h2>

    <div class="columns">
        <div class="column">
            <div class="has-text-centered">
                <p class="heading">總用戶數</p>
                <p class="title is-3 has-text-info">{{ users|length }}</p>
            </div>
        </div>
        <div class="column">
            <div class="has-text-centered">
                <p class="heading">角色數</p>
                <p class="title is-3 has-text-success">{{ roles|length }}</p>
            </div>
        </div>
        <div class="column">
            <div class="has-text-centered">
                <p class="heading">權限數</p>
                <p class="title is-3 has-text-warning">{{ permissions|length }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}