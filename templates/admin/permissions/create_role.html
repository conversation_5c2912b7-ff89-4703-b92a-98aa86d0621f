<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增角色 - 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="columns">
            <div class="column">
                <nav class="breadcrumb" aria-label="breadcrumbs">
                    <ul>
                        <li><a href="/">首頁</a></li>
                        <li><a href="#">設定</a></li>
                        <li><a href="/admin/permissions/">權限管理</a></li>
                        <li><a href="/admin/permissions/roles">角色管理</a></li>
                        <li class="is-active"><a href="#" aria-current="page">新增角色</a></li>
                    </ul>
                </nav>

                <div class="level">
                    <div class="level-left">
                        <div class="level-item">
                            <h1 class="title">
                                <i class="fas fa-plus mr-2"></i>
                                新增角色
                            </h1>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <a href="/admin/permissions/roles" class="button is-light">
                                <span class="icon">
                                    <i class="fas fa-arrow-left"></i>
                                </span>
                                <span>返回</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 顯示訊息 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="notification is-{{ 'danger' if category == 'error' else 'info' if category == 'info' else 'warning' if category == 'warning' else 'success' }}">
                                <button class="delete"></button>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="box">
                    <div class="media">
                        <div class="media-left">
                            <figure class="image is-64x64">
                                <span class="icon is-large has-text-primary">
                                    <i class="fas fa-user-plus fa-3x"></i>
                                </span>
                            </figure>
                        </div>
                        <div class="media-content">
                            <p class="title is-4">建立新角色</p>
                            <p class="subtitle is-6">為系統建立新的用戶角色</p>
                        </div>
                    </div>
                </div>

                <form method="POST">
                    <div class="box">
                        <h2 class="subtitle">
                            <i class="fas fa-info-circle mr-2"></i>
                            基本資訊
                        </h2>
                        
                        <div class="field">
                            <label class="label">角色名稱 <span class="has-text-danger">*</span></label>
                            <div class="control">
                                <input class="input" type="text" name="name" placeholder="例如: manager" required>
                            </div>
                            <p class="help">用於系統內部識別的角色名稱，建議使用英文，不可重複</p>
                        </div>

                        <div class="field">
                            <label class="label">顯示名稱 <span class="has-text-danger">*</span></label>
                            <div class="control">
                                <input class="input" type="text" name="display_name" placeholder="例如: 部門經理" required>
                            </div>
                            <p class="help">用戶界面中顯示的角色名稱</p>
                        </div>

                        <div class="field">
                            <label class="label">角色描述</label>
                            <div class="control">
                                <textarea class="textarea" name="description" placeholder="描述這個角色的職責和權限範圍"></textarea>
                            </div>
                            <p class="help">可選，用於說明角色的用途</p>
                        </div>
                    </div>

                    <div class="box">
                        <h2 class="subtitle">
                            <i class="fas fa-lightbulb mr-2"></i>
                            角色建議
                        </h2>
                        
                        <div class="content">
                            <p>以下是一些常見的角色設定建議：</p>
                            <div class="columns is-multiline">
                                <div class="column is-half">
                                    <div class="card">
                                        <div class="card-content">
                                            <p class="title is-6">財務主管</p>
                                            <p class="subtitle is-7">finance_manager</p>
                                            <p class="is-size-7">負責財務報表、資金管理等高級財務功能</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="column is-half">
                                    <div class="card">
                                        <div class="card-content">
                                            <p class="title is-6">會計助理</p>
                                            <p class="subtitle is-7">accounting_assistant</p>
                                            <p class="is-size-7">負責日常記帳、收支管理等基本會計工作</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="column is-half">
                                    <div class="card">
                                        <div class="card-content">
                                            <p class="title is-6">人事專員</p>
                                            <p class="subtitle is-7">hr_specialist</p>
                                            <p class="is-size-7">負責薪資管理、員工資料等人事相關功能</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="column is-half">
                                    <div class="card">
                                        <div class="card-content">
                                            <p class="title is-6">查看者</p>
                                            <p class="subtitle is-7">viewer</p>
                                            <p class="is-size-7">只能查看報表和基本資訊，無法修改資料</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="field is-grouped">
                        <div class="control">
                            <button type="submit" class="button is-primary">
                                <span class="icon">
                                    <i class="fas fa-plus"></i>
                                </span>
                                <span>建立角色</span>
                            </button>
                        </div>
                        <div class="control">
                            <a href="/admin/permissions/roles" class="button is-light">
                                <span class="icon">
                                    <i class="fas fa-times"></i>
                                </span>
                                <span>取消</span>
                            </a>
                        </div>
                    </div>
                </form>

                <!-- 提示信息 -->
                <div class="box has-background-info-light">
                    <h2 class="subtitle has-text-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        溫馨提示
                    </h2>
                    
                    <div class="content">
                        <ul>
                            <li>建立角色後，您需要為角色設定具體的權限</li>
                            <li>角色名稱建議使用英文，方便系統識別</li>
                            <li>顯示名稱可以使用中文，用戶更容易理解</li>
                            <li>建立後可以隨時編輯角色資訊和權限設定</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 自動關閉通知
        document.addEventListener('DOMContentLoaded', () => {
            (document.querySelectorAll('.notification .delete') || []).forEach(($delete) => {
                const $notification = $delete.parentNode;
                $delete.addEventListener('click', () => {
                    $notification.parentNode.removeChild($notification);
                });
            });
        });
    </script>
</body>
</html>