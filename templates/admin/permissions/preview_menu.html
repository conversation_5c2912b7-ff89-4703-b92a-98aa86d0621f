<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>選單預覽 - 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="columns">
            <div class="column">
                <nav class="breadcrumb" aria-label="breadcrumbs">
                    <ul>
                        <li><a href="/">首頁</a></li>
                        <li><a href="#">設定</a></li>
                        <li><a href="/admin/permissions/">權限管理</a></li>
                        <li class="is-active"><a href="#" aria-current="page">選單預覽</a></li>
                    </ul>
                </nav>

                <div class="level">
                    <div class="level-left">
                        <div class="level-item">
                            <h1 class="title">
                                <i class="fas fa-sitemap mr-2"></i>
                                選單預覽
                            </h1>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <button onclick="window.close()" class="button is-light">
                                <span class="icon">
                                    <i class="fas fa-times"></i>
                                </span>
                                <span>關閉</span>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="box">
                    <div class="media">
                        <div class="media-left">
                            <figure class="image is-64x64">
                                <span class="icon is-large has-text-primary">
                                    <i class="fas fa-user-circle fa-3x"></i>
                                </span>
                            </figure>
                        </div>
                        <div class="media-content">
                            <p class="title is-4">{{ user.full_name or user.username }}</p>
                            <p class="subtitle is-6">{{ user.email }}</p>
                            <p class="is-size-7 has-text-grey">此用戶可以看到以下選單項目</p>
                        </div>
                    </div>
                </div>

                {% if user_menu %}
                    {% for main_menu, submenus in user_menu.items() %}
                    <div class="box">
                        <h2 class="title is-4">
                            <i class="fas fa-folder mr-2"></i>
                            {{ main_menu }}
                        </h2>
                        
                        {% for submenu in submenus %}
                        <div class="box ml-4">
                            <h3 class="subtitle is-5">
                                <i class="fas fa-folder-open mr-2"></i>
                                {{ submenu.title }}
                            </h3>
                            
                            <div class="content ml-4">
                                {% for button in submenu.buttons %}
                                <div class="mb-3">
                                    <p class="has-text-weight-semibold">
                                        <i class="fas fa-circle mr-2" style="font-size: 0.5rem;"></i>
                                        {{ button.label }}
                                        {% if button.get('url') %}
                                            <span class="tag is-info is-small ml-2">{{ button.url }}</span>
                                        {% endif %}
                                    </p>
                                    
                                    {% if button.get('children') %}
                                        <div class="ml-4">
                                            {% for child in button.children %}
                                            <p class="is-size-7 has-text-grey">
                                                <i class="fas fa-minus mr-2"></i>
                                                {{ child }}
                                            </p>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="box">
                        <div class="notification is-warning">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            此用戶沒有任何可訪問的選單項目。
                        </div>
                    </div>
                {% endif %}

                <!-- 統計信息 -->
                <div class="box">
                    <h2 class="subtitle">
                        <i class="fas fa-chart-bar mr-2"></i>
                        統計信息
                    </h2>
                    
                    <div class="columns">
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">可訪問主選單</p>
                                <p class="title is-3 has-text-info">{{ user_menu|length }}</p>
                            </div>
                        </div>
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">總子選單數</p>
                                <p class="title is-3 has-text-success">
                                    {% set submenu_count = 0 %}
                                    {% for main_menu, submenus in user_menu.items() %}
                                        {% set submenu_count = submenu_count + submenus|length %}
                                    {% endfor %}
                                    {{ submenu_count }}
                                </p>
                            </div>
                        </div>
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">總功能按鈕</p>
                                <p class="title is-3 has-text-warning">
                                    {% set button_count = 0 %}
                                    {% for main_menu, submenus in user_menu.items() %}
                                        {% for submenu in submenus %}
                                            {% set button_count = button_count + submenu.buttons|length %}
                                        {% endfor %}
                                    {% endfor %}
                                    {{ button_count }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>