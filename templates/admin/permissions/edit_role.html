<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>編輯角色 - 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="columns">
            <div class="column">
                <nav class="breadcrumb" aria-label="breadcrumbs">
                    <ul>
                        <li><a href="/">首頁</a></li>
                        <li><a href="#">設定</a></li>
                        <li><a href="/admin/permissions/">權限管理</a></li>
                        <li><a href="/admin/permissions/roles">角色管理</a></li>
                        <li class="is-active"><a href="#" aria-current="page">編輯角色</a></li>
                    </ul>
                </nav>

                <div class="level">
                    <div class="level-left">
                        <div class="level-item">
                            <h1 class="title">
                                <i class="fas fa-edit mr-2"></i>
                                編輯角色
                            </h1>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <a href="/admin/permissions/roles" class="button is-light">
                                <span class="icon">
                                    <i class="fas fa-arrow-left"></i>
                                </span>
                                <span>返回</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 顯示訊息 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="notification is-{{ 'danger' if category == 'error' else 'info' if category == 'info' else 'warning' if category == 'warning' else 'success' }}">
                                <button class="delete"></button>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="box">
                    <div class="media">
                        <div class="media-left">
                            <figure class="image is-64x64">
                                <span class="icon is-large has-text-primary">
                                    <i class="fas fa-user-tag fa-3x"></i>
                                </span>
                            </figure>
                        </div>
                        <div class="media-content">
                            <p class="title is-4">編輯角色資訊</p>
                            <p class="subtitle is-6">修改角色的基本資訊和狀態</p>
                        </div>
                    </div>
                </div>

                <form method="POST">
                    <div class="box">
                        <h2 class="subtitle">
                            <i class="fas fa-info-circle mr-2"></i>
                            基本資訊
                        </h2>
                        
                        <div class="field">
                            <label class="label">角色名稱 <span class="has-text-danger">*</span></label>
                            <div class="control">
                                <input class="input" type="text" name="name" value="{{ role.name }}" placeholder="例如: manager" required>
                            </div>
                            <p class="help">用於系統內部識別的角色名稱，建議使用英文</p>
                        </div>

                        <div class="field">
                            <label class="label">顯示名稱 <span class="has-text-danger">*</span></label>
                            <div class="control">
                                <input class="input" type="text" name="display_name" value="{{ role.display_name }}" placeholder="例如: 部門經理" required>
                            </div>
                            <p class="help">用戶界面中顯示的角色名稱</p>
                        </div>

                        <div class="field">
                            <label class="label">角色描述</label>
                            <div class="control">
                                <textarea class="textarea" name="description" placeholder="描述這個角色的職責和權限範圍">{{ role.description or '' }}</textarea>
                            </div>
                            <p class="help">可選，用於說明角色的用途</p>
                        </div>

                        <div class="field">
                            <div class="control">
                                <label class="checkbox">
                                    <input type="checkbox" name="is_active" {% if role.is_active %}checked{% endif %}>
                                    <strong class="ml-2">啟用此角色</strong>
                                </label>
                            </div>
                            <p class="help">停用的角色將無法分配給用戶</p>
                        </div>
                    </div>

                    <div class="box">
                        <h2 class="subtitle">
                            <i class="fas fa-info mr-2"></i>
                            角色資訊
                        </h2>
                        
                        <div class="columns">
                            <div class="column">
                                <div class="field">
                                    <label class="label">角色ID</label>
                                    <div class="control">
                                        <input class="input" type="text" value="{{ role.id }}" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="field">
                                    <label class="label">建立時間</label>
                                    <div class="control">
                                        <input class="input" type="text" value="{% if role.created_at %}{{ role.created_at.strftime('%Y-%m-%d %H:%M:%S') }}{% else %}未知{% endif %}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if role.updated_at %}
                        <div class="field">
                            <label class="label">最後更新</label>
                            <div class="control">
                                <input class="input" type="text" value="{{ role.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}" readonly>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <div class="field is-grouped">
                        <div class="control">
                            <button type="submit" class="button is-primary">
                                <span class="icon">
                                    <i class="fas fa-save"></i>
                                </span>
                                <span>儲存變更</span>
                            </button>
                        </div>
                        <div class="control">
                            <a href="/admin/permissions/roles" class="button is-light">
                                <span class="icon">
                                    <i class="fas fa-times"></i>
                                </span>
                                <span>取消</span>
                            </a>
                        </div>
                        <div class="control">
                            <a href="/admin/permissions/roles/{{ role.id }}/permissions" class="button is-info">
                                <span class="icon">
                                    <i class="fas fa-key"></i>
                                </span>
                                <span>設定權限</span>
                            </a>
                        </div>
                    </div>
                </form>

                <!-- 危險操作區域 -->
                <div class="box has-background-danger-light">
                    <h2 class="subtitle has-text-danger">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        危險操作
                    </h2>
                    
                    <div class="content">
                        <p>以下操作可能會影響系統安全，請謹慎使用：</p>
                        <ul>
                            <li>停用角色會影響所有擁有此角色的用戶</li>
                            <li>修改角色名稱可能會影響系統權限檢查</li>
                            <li>建議在修改前先備份相關資料</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 自動關閉通知
        document.addEventListener('DOMContentLoaded', () => {
            (document.querySelectorAll('.notification .delete') || []).forEach(($delete) => {
                const $notification = $delete.parentNode;
                $delete.addEventListener('click', () => {
                    $notification.parentNode.removeChild($notification);
                });
            });
        });
    </script>
</body>
</html>