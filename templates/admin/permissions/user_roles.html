<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用戶角色設定 - 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="columns">
            <div class="column">
                <nav class="breadcrumb" aria-label="breadcrumbs">
                    <ul>
                        <li><a href="/">首頁</a></li>
                        <li><a href="#">設定</a></li>
                        <li><a href="/admin/permissions/">權限管理</a></li>
                        <li class="is-active"><a href="#" aria-current="page">用戶角色設定</a></li>
                    </ul>
                </nav>

                <div class="level">
                    <div class="level-left">
                        <div class="level-item">
                            <h1 class="title">
                                <i class="fas fa-user-cog mr-2"></i>
                                用戶角色設定
                            </h1>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <a href="/admin/permissions/" class="button is-light">
                                <span class="icon">
                                    <i class="fas fa-arrow-left"></i>
                                </span>
                                <span>返回</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 顯示訊息 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="notification is-{{ 'danger' if category == 'error' else 'info' if category == 'info' else 'warning' if category == 'warning' else 'success' }}">
                                <button class="delete"></button>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="box">
                    <div class="media">
                        <div class="media-left">
                            <figure class="image is-64x64">
                                <span class="icon is-large has-text-primary">
                                    <i class="fas fa-user-circle fa-3x"></i>
                                </span>
                            </figure>
                        </div>
                        <div class="media-content">
                            <p class="title is-4">{{ user.full_name or user.username }}</p>
                            <p class="subtitle is-6">{{ user.email }}</p>
                            <p class="is-size-7 has-text-grey">用戶ID: {{ user.id }}</p>
                        </div>
                    </div>
                </div>

                <form method="POST">
                    <div class="box">
                        <h2 class="subtitle">
                            <i class="fas fa-user-tag mr-2"></i>
                            角色分配
                        </h2>
                        
                        <div class="field">
                            <label class="label">選擇角色</label>
                            <div class="control">
                                {% for role in all_roles %}
                                <label class="checkbox is-block mb-2">
                                    <input type="checkbox" name="roles" value="{{ role.id }}" 
                                           {% if role.id in user_role_ids %}checked{% endif %}>
                                    <strong class="ml-2">{{ role.display_name }}</strong>
                                    <span class="tag is-light ml-2">{{ role.name }}</span>
                                    {% if role.description %}
                                        <br>
                                        <span class="is-size-7 has-text-grey ml-6">{{ role.description }}</span>
                                    {% endif %}
                                </label>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="field is-grouped">
                            <div class="control">
                                <button type="submit" class="button is-primary">
                                    <span class="icon">
                                        <i class="fas fa-save"></i>
                                    </span>
                                    <span>儲存設定</span>
                                </button>
                            </div>
                            <div class="control">
                                <a href="/admin/permissions/" class="button is-light">
                                    <span class="icon">
                                        <i class="fas fa-times"></i>
                                    </span>
                                    <span>取消</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- 預覽權限 -->
                <div class="box">
                    <h2 class="subtitle">
                        <i class="fas fa-eye mr-2"></i>
                        權限預覽
                    </h2>
                    
                    <div class="buttons">
                        <a href="/admin/permissions/api/user-permissions/{{ user.id }}" class="button is-info is-small" target="_blank">
                            <span class="icon">
                                <i class="fas fa-list"></i>
                            </span>
                            <span>查看詳細權限</span>
                        </a>
                        <a href="/admin/permissions/preview-menu/{{ user.id }}" class="button is-link is-small" target="_blank">
                            <span class="icon">
                                <i class="fas fa-sitemap"></i>
                            </span>
                            <span>預覽選單</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 自動關閉通知
        document.addEventListener('DOMContentLoaded', () => {
            (document.querySelectorAll('.notification .delete') || []).forEach(($delete) => {
                const $notification = $delete.parentNode;
                $delete.addEventListener('click', () => {
                    $notification.parentNode.removeChild($notification);
                });
            });
        });
    </script>
</body>
</html>