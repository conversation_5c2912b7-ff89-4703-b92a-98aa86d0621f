{% extends "admin/base.html" %}

{% block title %}備份管理{% endblock %}

{% block page_icon %}fa-download{% endblock %}
{% block page_title %}備份管理{% endblock %}

{% block breadcrumb %}
<li><a href="#">系統管理</a></li>
<li class="is-active"><a href="#" aria-current="page">備份管理</a></li>
{% endblock %}

{% block content %}
                <div class="level">
                    <div class="level-left">
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <form method="POST" action="/admin/permissions/backup/create" style="display: inline;">
                                <button type="submit" class="btn-gradient-primary" onclick="return confirm('確定要建立新的資料庫備份嗎？')">
                                    <span class="icon">
                                        <i class="fas fa-plus"></i>
                                    </span>
                                    <span>建立備份</span>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 備份概覽 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-chart-bar mr-2"></i>
                        備份概覽
                    </h2>
                    
                    <div class="columns">
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">備份檔案數</p>
                                <p class="title is-3 has-text-info">{{ backups_data|length }}</p>
                            </div>
                        </div>
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">總大小</p>
                                <p class="title is-3 has-text-success">
                                    {% set total_size = backups_data|sum(attribute='size') %}
                                    {{ "%.2f"|format(total_size / 1024**2) }} MB
                                </p>
                            </div>
                        </div>
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">最新備份</p>
                                <p class="title is-6 has-text-grey">
                                    {% if backups_data %}
                                        {% set latest = backups_data|max(attribute='created') %}
                                        {{ latest.created.strftime('%m-%d %H:%M') }}
                                    {% else %}
                                        無備份
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 備份檔案列表 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-list mr-2"></i>
                        備份檔案
                    </h2>
                    
                    {% if backups_data %}
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped is-hoverable">
                            <thead>
                                <tr>
                                    <th>檔案名稱</th>
                                    <th>大小</th>
                                    <th>建立時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for backup in backups_data|sort(attribute='created', reverse=true) %}
                                <tr>
                                    <td>
                                        <span class="icon-text">
                                            <span class="icon">
                                                <i class="fas fa-database has-text-primary"></i>
                                            </span>
                                            <span><strong>{{ backup.name }}</strong></span>
                                        </span>
                                    </td>
                                    <td>
                                        {% if backup.size < 1024 %}
                                            {{ backup.size }} B
                                        {% elif backup.size < 1024**2 %}
                                            {{ "%.1f"|format(backup.size / 1024) }} KB
                                        {% else %}
                                            {{ "%.2f"|format(backup.size / 1024**2) }} MB
                                        {% endif %}
                                    </td>
                                    <td>{{ backup.created.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                    <td>
                                        <div class="buttons are-small">
                                            <a href="/admin/permissions/backup/download/{{ backup.name }}" class="btn-gradient-success" style="font-size: 0.875rem; padding: 6px 12px; margin-right: 8px;">
                                                <span class="icon">
                                                    <i class="fas fa-download"></i>
                                                </span>
                                                <span>下載</span>
                                            </a>
                                            <form method="POST" action="/admin/permissions/backup/restore/{{ backup.name }}" style="display: inline; margin-right: 8px;">
                                                <button type="submit" class="btn-gradient-warning" style="font-size: 0.875rem; padding: 6px 12px;" onclick="return confirm('警告：還原備份會覆蓋現有的所有資料！\n\n確定要還原備份 &quot;{{ backup.name }}&quot; 嗎？')">
                                                    <span class="icon">
                                                        <i class="fas fa-undo"></i>
                                                    </span>
                                                    <span>還原</span>
                                                </button>
                                            </form>
                                            <form method="POST" action="/admin/permissions/backup/delete/{{ backup.name }}" style="display: inline;">
                                                <button type="submit" class="btn-gradient-danger" style="font-size: 0.875rem; padding: 6px 12px;" onclick="return confirm('確定要刪除備份檔案 &quot;{{ backup.name }}&quot; 嗎？\n\n此操作無法復原！')">
                                                    <span class="icon">
                                                        <i class="fas fa-trash"></i>
                                                    </span>
                                                    <span>刪除</span>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="notification is-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        目前沒有備份檔案。建議定期建立資料庫備份以確保資料安全。
                    </div>
                    {% endif %}
                </div>

                <!-- 備份說明 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-info-circle mr-2"></i>
                        備份說明
                    </h2>
                    
                    <div class="content">
                        <div class="columns">
                            <div class="column">
                                <div class="card">
                                    <div class="card-content">
                                        <div class="media">
                                            <div class="media-left">
                                                <span class="icon is-large has-text-success">
                                                    <i class="fas fa-shield-alt fa-2x"></i>
                                                </span>
                                            </div>
                                            <div class="media-content">
                                                <p class="title is-6">自動備份</p>
                                                <p class="is-size-7">系統會在重要操作前自動建立備份，確保資料安全</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="card">
                                    <div class="card-content">
                                        <div class="media">
                                            <div class="media-left">
                                                <span class="icon is-large has-text-info">
                                                    <i class="fas fa-clock fa-2x"></i>
                                                </span>
                                            </div>
                                            <div class="media-content">
                                                <p class="title is-6">定期備份</p>
                                                <p class="is-size-7">建議每週手動建立一次完整備份</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="card">
                                    <div class="card-content">
                                        <div class="media">
                                            <div class="media-left">
                                                <span class="icon is-large has-text-warning">
                                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                                </span>
                                            </div>
                                            <div class="media-content">
                                                <p class="title is-6">注意事項</p>
                                                <p class="is-size-7">還原備份會覆蓋現有資料，請謹慎操作</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 備份建議 -->
                <div class="modern-card fade-in-up has-background-info-light">
                    <h2 class="subtitle has-text-info">
                        <i class="fas fa-lightbulb mr-2"></i>
                        備份建議
                    </h2>
                    
                    <div class="content">
                        <ul>
                            <li><strong>定期備份</strong>：建議每週建立一次完整備份</li>
                            <li><strong>重要操作前</strong>：在進行重要資料修改前先建立備份</li>
                            <li><strong>異地存儲</strong>：將備份檔案存儲到不同的位置或雲端</li>
                            <li><strong>測試還原</strong>：定期測試備份檔案的完整性</li>
                            <li><strong>保留多個版本</strong>：保留不同時間點的備份檔案</li>
                        </ul>
                    </div>
                </div>
{% endblock %}

{% block extra_js %}
<script>
    // 備份功能已經通過表單實現，不需要額外的 JavaScript
    console.log('備份管理頁面已載入');
</script>
{% endblock %}