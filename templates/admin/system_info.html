{% extends "admin/base.html" %}

{% block title %}系統資訊{% endblock %}

{% block page_icon %}fa-info-circle{% endblock %}
{% block page_title %}系統資訊{% endblock %}

{% block breadcrumb %}
<li><a href="#">系統管理</a></li>
<li class="is-active"><a href="#" aria-current="page">系統資訊</a></li>
{% endblock %}

{% block content %}

                <!-- 系統概覽 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-desktop mr-2"></i>
                        系統概覽
                    </h2>
                    
                    <div class="columns">
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">總用戶數</p>
                                <p class="title is-3 has-text-info">{{ db_stats.users_count }}</p>
                            </div>
                        </div>
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">啟用用戶</p>
                                <p class="title is-3 has-text-success">{{ db_stats.active_users }}</p>
                            </div>
                        </div>
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">角色數</p>
                                <p class="title is-3 has-text-warning">{{ db_stats.roles_count }}</p>
                            </div>
                        </div>
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">權限數</p>
                                <p class="title is-3 has-text-danger">{{ db_stats.permissions_count }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系統資訊 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-server mr-2"></i>
                        系統環境
                    </h2>
                    
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <tbody>
                                <tr>
                                    <td><strong>作業系統</strong></td>
                                    <td>{{ system_data.platform }}</td>
                                </tr>
                                <tr>
                                    <td><strong>系統架構</strong></td>
                                    <td>{{ system_data.architecture }}</td>
                                </tr>
                                <tr>
                                    <td><strong>處理器</strong></td>
                                    <td>{{ system_data.processor or '未知' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>主機名稱</strong></td>
                                    <td>{{ system_data.hostname }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Python 版本</strong></td>
                                    <td>{{ system_data.python_version.split()[0] }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 記憶體使用情況 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-memory mr-2"></i>
                        記憶體使用情況
                    </h2>
                    
                    <div class="columns">
                        <div class="column">
                            <div class="field">
                                <label class="label">記憶體使用率</label>
                                <progress class="progress is-{% if system_data.memory_percent < 50 %}success{% elif system_data.memory_percent < 80 %}warning{% else %}danger{% endif %}" value="{{ system_data.memory_percent }}" max="100">{{ system_data.memory_percent }}%</progress>
                                <p class="help">{{ "%.1f"|format(system_data.memory_percent) }}% ({{ "%.2f"|format(system_data.memory_available / 1024**3) }} GB 可用 / {{ "%.2f"|format(system_data.memory_total / 1024**3) }} GB 總計)</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 磁碟使用情況 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-hdd mr-2"></i>
                        磁碟使用情況
                    </h2>
                    
                    <div class="columns">
                        <div class="column">
                            <div class="field">
                                <label class="label">磁碟使用率</label>
                                <progress class="progress is-{% if system_data.disk_percent < 50 %}success{% elif system_data.disk_percent < 80 %}warning{% else %}danger{% endif %}" value="{{ system_data.disk_percent }}" max="100">{{ system_data.disk_percent }}%</progress>
                                <p class="help">{{ "%.1f"|format(system_data.disk_percent) }}% ({{ "%.2f"|format(system_data.disk_free / 1024**3) }} GB 可用 / {{ "%.2f"|format(system_data.disk_total / 1024**3) }} GB 總計)</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 資料庫資訊 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-database mr-2"></i>
                        資料庫資訊
                    </h2>
                    
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <tbody>
                                <tr>
                                    <td><strong>資料庫路徑</strong></td>
                                    <td><code>{{ system_data.database_path }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>資料庫大小</strong></td>
                                    <td>{{ "%.2f"|format(system_data.database_size / 1024**2) }} MB</td>
                                </tr>
                                <tr>
                                    <td><strong>收支記錄數</strong></td>
                                    <td>{{ db_stats.money_records }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 應用程式資訊 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-cogs mr-2"></i>
                        應用程式資訊
                    </h2>
                    
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <tbody>
                                <tr>
                                    <td><strong>應用程式名稱</strong></td>
                                    <td>會計系統</td>
                                </tr>
                                <tr>
                                    <td><strong>版本</strong></td>
                                    <td>1.0.0</td>
                                </tr>
                                <tr>
                                    <td><strong>框架</strong></td>
                                    <td>Flask + SQLAlchemy</td>
                                </tr>
                                <tr>
                                    <td><strong>資料庫</strong></td>
                                    <td>SQLite</td>
                                </tr>
                                <tr>
                                    <td><strong>當前時間</strong></td>
                                    <td>{{ system_data.app_start_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-tools mr-2"></i>
                        快速操作
                    </h2>
                    
                    <div class="buttons">
                        <a href="/admin/permissions/logs" class="btn-gradient-info" style="margin-right: 8px;">
                            <span class="icon">
                                <i class="fas fa-file-alt"></i>
                            </span>
                            <span>查看日誌</span>
                        </a>
                        <a href="/admin/permissions/backup" class="btn-gradient-success" style="margin-right: 8px;">
                            <span class="icon">
                                <i class="fas fa-download"></i>
                            </span>
                            <span>備份管理</span>
                        </a>
                        <a href="/admin/permissions/" class="btn-gradient-primary">
                            <span class="icon">
                                <i class="fas fa-users-cog"></i>
                            </span>
                            <span>權限管理</span>
                        </a>
                    </div>
                </div>
{% endblock %}