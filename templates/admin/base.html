<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}管理系統{% endblock %} - 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-theme.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <nav class="breadcrumb" aria-label="breadcrumbs">
                    <ul>
                        <li><a href="/">首頁</a></li>
                        {% block breadcrumb %}{% endblock %}
                    </ul>
                </nav>

                <div class="level">
                    <div class="level-left">
                        <div class="level-item">
                            <h1 class="title">
                                <i class="fas {% block page_icon %}fa-cog{% endblock %} mr-2"></i>
                                {% block page_title %}管理系統{% endblock %}
                            </h1>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <a href="/auth/logout" class="btn-gradient-primary">
                                <span class="icon">
                                    <i class="fas fa-sign-out-alt"></i>
                                </span>
                                <span>登出</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 顯示訊息 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                {% for category, message in messages %}
                <div
                    class="notification is-{{ 'danger' if category == 'error' else 'info' if category == 'info' else 'warning' if category == 'warning' else 'success' }}">
                    <button class="delete"></button>
                    {{ message }}
                </div>
                {% endfor %}
                {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/sidebar-menu.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modern-interactions.js') }}"></script>
    <script>
        // 自動關閉通知
        document.addEventListener('DOMContentLoaded', () => {
            (document.querySelectorAll('.notification .delete') || []).forEach(($delete) => {
                const $notification = $delete.parentNode;
                $delete.addEventListener('click', () => {
                    $notification.parentNode.removeChild($notification);
                });
            });
            
            // 初始化現代化交互效果
            if (typeof initModernInteractions !== 'undefined') {
                initModernInteractions();
            }
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>

</html>