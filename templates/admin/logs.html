{% extends "admin/base.html" %}

{% block title %}日誌管理{% endblock %}

{% block page_icon %}fa-file-alt{% endblock %}
{% block page_title %}日誌管理{% endblock %}

{% block breadcrumb %}
<li><a href="#">系統管理</a></li>
<li class="is-active"><a href="#" aria-current="page">日誌管理</a></li>
{% endblock %}

{% block content %}

                <!-- 日誌概覽 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-chart-bar mr-2"></i>
                        日誌概覽
                    </h2>
                    
                    <div class="columns">
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">日誌檔案數</p>
                                <p class="title is-3 has-text-info">{{ logs_data|length }}</p>
                            </div>
                        </div>
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">總大小</p>
                                <p class="title is-3 has-text-success">
                                    {% set total_size = logs_data|sum(attribute='size') %}
                                    {{ "%.2f"|format(total_size / 1024**2) }} MB
                                </p>
                            </div>
                        </div>
                        <div class="column">
                            <div class="has-text-centered">
                                <p class="heading">最新更新</p>
                                <p class="title is-6 has-text-grey">
                                    {% if logs_data %}
                                        {% set latest = logs_data|max(attribute='modified') %}
                                        {{ latest.modified.strftime('%m-%d %H:%M') }}
                                    {% else %}
                                        無日誌
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日誌檔案列表 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-list mr-2"></i>
                        日誌檔案
                    </h2>
                    
                    {% if logs_data %}
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped is-hoverable">
                            <thead>
                                <tr>
                                    <th>檔案名稱</th>
                                    <th>大小</th>
                                    <th>最後修改</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs_data %}
                                <tr>
                                    <td>
                                        <span class="icon-text">
                                            <span class="icon">
                                                {% if 'error' in log.name %}
                                                    <i class="fas fa-exclamation-triangle has-text-danger"></i>
                                                {% elif 'access' in log.name %}
                                                    <i class="fas fa-globe has-text-info"></i>
                                                {% else %}
                                                    <i class="fas fa-file-alt has-text-grey"></i>
                                                {% endif %}
                                            </span>
                                            <span><strong>{{ log.name }}</strong></span>
                                        </span>
                                    </td>
                                    <td>
                                        {% if log.size < 1024 %}
                                            {{ log.size }} B
                                        {% elif log.size < 1024**2 %}
                                            {{ "%.1f"|format(log.size / 1024) }} KB
                                        {% else %}
                                            {{ "%.2f"|format(log.size / 1024**2) }} MB
                                        {% endif %}
                                    </td>
                                    <td>{{ log.modified.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                    <td>
                                        <div class="buttons are-small">
                                            <button class="btn-gradient-info" style="font-size: 0.875rem; padding: 6px 12px; margin-right: 8px;" onclick="viewLog('{{ log.path }}')">
                                                <span class="icon">
                                                    <i class="fas fa-eye"></i>
                                                </span>
                                                <span>查看</span>
                                            </button>
                                            <a href="/admin/permissions/logs/download/{{ log.name }}" class="btn-gradient-success" style="font-size: 0.875rem; padding: 6px 12px;">
                                                <span class="icon">
                                                    <i class="fas fa-download"></i>
                                                </span>
                                                <span>下載</span>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="notification is-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        目前沒有日誌檔案。系統運行後會自動產生日誌檔案。
                    </div>
                    {% endif %}
                </div>

                <!-- 日誌類型說明 -->
                <div class="modern-card fade-in-up">
                    <h2 class="subtitle">
                        <i class="fas fa-question-circle mr-2"></i>
                        日誌類型說明
                    </h2>
                    
                    <div class="content">
                        <div class="columns">
                            <div class="column">
                                <div class="card">
                                    <div class="card-content">
                                        <div class="media">
                                            <div class="media-left">
                                                <span class="icon is-large has-text-grey">
                                                    <i class="fas fa-file-alt fa-2x"></i>
                                                </span>
                                            </div>
                                            <div class="media-content">
                                                <p class="title is-6">accounting.log</p>
                                                <p class="subtitle is-7">應用程式主日誌</p>
                                                <p class="is-size-7">記錄應用程式的一般運行資訊、警告和錯誤</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="card">
                                    <div class="card-content">
                                        <div class="media">
                                            <div class="media-left">
                                                <span class="icon is-large has-text-danger">
                                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                                </span>
                                            </div>
                                            <div class="media-content">
                                                <p class="title is-6">error.log</p>
                                                <p class="subtitle is-7">錯誤日誌</p>
                                                <p class="is-size-7">記錄系統錯誤和異常情況</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="card">
                                    <div class="card-content">
                                        <div class="media">
                                            <div class="media-left">
                                                <span class="icon is-large has-text-info">
                                                    <i class="fas fa-globe fa-2x"></i>
                                                </span>
                                            </div>
                                            <div class="media-content">
                                                <p class="title is-6">access.log</p>
                                                <p class="subtitle is-7">訪問日誌</p>
                                                <p class="is-size-7">記錄用戶訪問和請求資訊</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日誌查看模態框 -->
                <div class="modal" id="logModal">
                    <div class="modal-background"></div>
                    <div class="modal-card">
                        <header class="modal-card-head">
                            <p class="modal-card-title">日誌內容</p>
                            <button class="delete" aria-label="close" onclick="closeLogModal()"></button>
                        </header>
                        <section class="modal-card-body">
                            <pre id="logContent" style="max-height: 400px; overflow-y: auto; background: #f5f5f5; padding: 1rem; border-radius: 4px;"></pre>
                        </section>
                        <footer class="modal-card-foot">
                            <button class="button" onclick="closeLogModal()">關閉</button>
                        </footer>
                    </div>
                </div>
{% endblock %}

{% block extra_js %}
<script>
    function viewLog(logPath) {
        // 這裡可以實作 AJAX 請求來獲取日誌內容
        document.getElementById('logContent').textContent = '正在載入日誌內容...';
        document.getElementById('logModal').classList.add('is-active');
        
        // 模擬載入日誌內容
        setTimeout(() => {
            document.getElementById('logContent').textContent = '日誌內容載入功能需要後端支援\n\n檔案路徑: ' + logPath + '\n\n請直接下載檔案查看完整內容。';
        }, 500);
    }

    function closeLogModal() {
        document.getElementById('logModal').classList.remove('is-active');
    }
</script>
{% endblock %}