<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>新增帳戶</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .main-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2.5rem 2rem;
            max-width: 600px;
            margin: 2rem auto;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 1.2rem;
        }

        .is-readonly {
            background: #f5f6fa;
            color: #888;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="main-card">
                    <div class="section-title mb-5">
                        <a href="/account_setting" style="color:#2563eb;text-decoration:none;font-size:1.1rem;">←</a>
                        新增帳戶
                    </div>
                    <form method="post">
                        <div class="field mb-3">
                            <label class="label">帳戶名稱</label>
                            <div class="control">
                                <input class="input" name="name" type="text" placeholder="請輸入帳戶名稱" required>
                            </div>
                        </div>
                        <div class="field mb-3">
                            <label class="label">帳戶類別</label>
                            <div class="control">
                                <div class="select">
                                    <select name="category" id="account-category" required
                                        onchange="toggleBankFields()">
                                        <option value="現金">現金</option>
                                        <option value="銀行帳戶">銀行帳戶</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div id="bank-fields" style="display:none;">
                            <div class="field mb-3">
                                <label class="label">銀行名稱</label>
                                <div class="control">
                                    <div class="select is-fullwidth">
                                        <select id="bank-head-select" name="bank_name" required>
                                            <option value="">請選擇銀行</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="field mb-3">
                                <label class="label">分行</label>
                                <div class="control">
                                    <div class="select is-fullwidth">
                                        <select id="bank-branch-select" name="branch">
                                            <option value="">請先選擇銀行</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="field mb-3">
                                <label class="label">帳號</label>
                                <div class="control">
                                    <input class="input" name="account_number" type="text" placeholder="請輸入帳號">
                                </div>
                            </div>
                            <div class="field mb-3">
                                <label class="label">戶名</label>
                                <div class="control">
                                    <input class="input" name="account_holder" type="text" placeholder="請輸入戶名">
                                </div>
                            </div>
                        </div>
                        <div class="field mb-3">
                            <label class="label">備註</label>
                            <div class="control">
                                <textarea class="textarea" name="note" placeholder="備註"></textarea>
                            </div>
                        </div>
                        <div class="field is-grouped is-grouped-right mt-4">
                            <div class="control">
                                <a class="button is-light" href="/account_setting">取消</a>
                            </div>
                            <div class="control">
                                <button class="button is-link" type="submit">儲存</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        function toggleBankFields() {
            var category = document.getElementById('account-category').value;
            var bankFields = document.getElementById('bank-fields');
            if (category === '銀行帳戶') {
                bankFields.style.display = '';
            } else {
                bankFields.style.display = 'none';
            }
        }
        // 頁面載入時初始化
        document.addEventListener('DOMContentLoaded', function () {
            toggleBankFields();

            // 取得總行
            fetch('/api/bank_heads')
                .then(res => res.json())
                .then(heads => {
                    const select = document.getElementById('bank-head-select');
                    for (const [code, name] of Object.entries(heads)) {
                        select.innerHTML += `<option value="${code}">${code} - ${name}</option>`;
                    }
                });

            // 當總行選擇改變時，載入分行
            document.getElementById('bank-head-select').addEventListener('change', function () {
                const headCode = this.value;
                const branchSelect = document.getElementById('bank-branch-select');
                branchSelect.innerHTML = '<option value="">請選擇分行</option>';
                if (!headCode) return;
                fetch('/api/bank_branches/' + headCode)
                    .then(res => res.json())
                    .then(branches => {
                        for (const branch of branches) {
                            branchSelect.innerHTML += `<option value="${branch.code}">${branch.code} - ${branch.name}</option>`;
                        }
                    });
            });
        });
    </script>
</body>

</html>