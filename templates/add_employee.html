<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>新增員工</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .main-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.09375rem;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-top: 0.125rem;
            margin-bottom: 0.0625rem;
        }

        .form-section {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px #eee;
            padding: 0.125rem;
            margin-bottom: 0.125rem;
        }

        .field {
            margin-bottom: 0.075rem;
        }

        .is-required:after {
            content: '*';
            color: #e53e3e;
            margin-left: 0.2em;
        }

        .form-note {
            color: #888;
            font-size: 0.95em;
            margin-bottom: 0.0625em;
        }

        .blue-bar {
            background: #f5f6fa;
            border-left: 4px solid #2563eb;
            padding: 0.04375em 0.075em;
            margin: 0.125em 0 0.0625em 0;
            color: #2563eb;
            font-weight: bold;
        }

        .select select,
        .input[type='date'] {
            height: 2.5em;
        }

        .label {
            min-width: 7em;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="javascript:history.back()"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        {% if is_edit %}編輯員工{% else %}新增員工{% endif %}
                    </h1>
                </div>
                <form method="post" class="form-section">
                    <div class="section-title">基本資料</div>
                    <div class="columns is-multiline">
                        <div class="column is-2">
                            <div class="field"><label class="label">職稱</label><input class="input" type="text"
                                    name="title" value="{{ employee.title if employee else '' }}" placeholder="請輸入職稱">
                            </div>
                        </div>
                        <div class="column is-2">
                            <div class="field"><label class="label">員工編號</label><input class="input" type="text"
                                    name="emp_id" placeholder="請輸入員工編號"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">到職日</label><input class="input" type="date"
                                    name="onboard_date"></div>
                        </div>
                        <div class="column is-2">
                            <div class="field"><label class="label">部門</label><input class="input" type="text"
                                    name="department" placeholder="請輸入部門"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">身份證號</label><input class="input" type="text"
                                    name="identity" placeholder="請輸入身份證號"></div>
                        </div>
                    </div>
                    <div class="columns is-multiline">
                        <div class="column is-3">
                            <div class="field"><label class="label">姓名</label><input class="input" type="text"
                                    name="name" placeholder="請輸入姓名"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">通訊地址</label><input class="input" type="text"
                                    name="address" placeholder="請輸入通訊地址"></div>
                        </div>
                        <div class="column is-2">
                            <div class="field"><label class="label">聯絡電話</label><input class="input" type="text"
                                    name="phone" placeholder="請輸入聯絡電話"></div>
                        </div>
                        <div class="column is-2">
                            <div class="field"><label class="label">電子信箱</label><input class="input" type="email"
                                    name="email" placeholder="請輸入電子信箱"></div>
                        </div>
                        <div class="column is-2">
                            <div class="field"><label class="label">離職日</label><input class="input" type="date"
                                    name="leave_date"></div>
                        </div>
                    </div>
                    <div class="section-title">薪資資訊</div>
                    <div class="columns is-multiline">
                        <div class="column is-3">
                            <div class="field"><label class="label">本薪</label><input class="input" type="number"
                                    name="salary" value="0"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">伙食費</label><input class="input" type="number"
                                    name="meal" value="0"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">薪資匯款銀行</label><input class="input" type="text"
                                    name="bank"></div>
                        </div>
                        <div class="column is-3">
                            <div class="field"><label class="label">薪資匯款帳號</label><input class="input" type="text"
                                    name="bank_account"></div>
                        </div>
                    </div>
                    <!-- 新增保險區塊 -->
                    <div class="section-title">保險</div>
                    <div class="columns is-vcentered">
                        <div class="column is-4">
                            <label class="label">保險身份 <span class="is-required"></span></label>
                            <div class="select is-fullwidth">
                                <select name="insurance_identity" required>
                                    <option value="">請選擇保險身份</option>
                                    <option value="負責人">負責人</option>
                                    <option value="勞工">勞工</option>
                                </select>
                            </div>
                        </div>
                        <div class="column is-4">
                            <label class="label">是否投保勞保、勞退 <span class="is-required"></span></label>
                            <div class="field is-grouped">
                                <div class="control">
                                    <label class="radio">
                                        <input type="radio" name="labor_insurance" value="yes" checked>
                                        是
                                    </label>
                                </div>
                                <div class="control">
                                    <label class="radio">
                                        <input type="radio" name="labor_insurance" value="no">
                                        否
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="column is-4">
                            <label class="label">是否投保健保 <span class="is-required"></span></label>
                            <div class="field is-grouped">
                                <div class="control">
                                    <label class="radio">
                                        <input type="radio" name="health_insurance" value="yes" checked>
                                        是
                                    </label>
                                </div>
                                <div class="control">
                                    <label class="radio">
                                        <input type="radio" name="health_insurance" value="no">
                                        否
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 動態顯示區塊 start -->

                    <!-- 健康保險區塊 -->
                    <div id="health-section">
                        <div class="section-title">健康保險</div>
                        <div class="columns">
                            <div class="column is-12">
                                <!-- 健保所有欄位合併成一行 -->
                                <div class="columns is-multiline">
                                    <div class="column is-1">
                                        <div class="field">
                                            <label class="label" style="font-size:0.8em;">加保日</label>
                                            <div class="control">
                                                <input class="input" type="date" name="health_insurance_date"
                                                    style="font-size:0.8em;">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-2">
                                        <div class="field">
                                            <label class="label" style="font-size:0.8em;">本人健保補助資格 <span
                                                    style="color:#e53e3e;">*</span></label>
                                            <div class="control">
                                                <div class="select is-fullwidth">
                                                    <select name="health_subsidy_qualification" required
                                                        style="font-size:0.8em;">
                                                        <option value="">請選擇補助資格</option>
                                                        <option value="無">無</option>
                                                        <option value="補助1/4">補助1/4</option>
                                                        <option value="補助1/2">補助1/2</option>
                                                        <option value="補助全額">補助全額</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-1">
                                        <div class="field">
                                            <label class="label" style="font-size:0.8em;">健保級距法規生效日 <span
                                                    style="color:#e53e3e;">*</span></label>
                                            <div class="control">
                                                <input class="input" type="date" name="health_law_effective_date"
                                                    required style="font-size:0.8em;">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-2">
                                        <div class="field">
                                            <label class="label" style="font-size:0.8em;">健保投保級距 <span
                                                    style="color:#e53e3e;">*</span></label>
                                            <div class="control">
                                                <div class="select is-fullwidth">
                                                    <select name="health_level" id="health_level" required
                                                        onchange="calculateHealthInsurancePremium()"
                                                        style="font-size:0.8em;">
                                                        <option value="">請選擇投保級距</option>
                                                        {% for level in health_insurance_levels %}
                                                        <option value="{{ level.value }}"
                                                            data-insured-amount="{{ level.insured_amount }}" {% if
                                                            employee and employee.health_level==level.value|string
                                                            %}selected{% endif %}>
                                                            {{ level.label }}
                                                        </option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div id="health_insurance_result"></div>
                                        </div>
                                    </div>
                                    <div class="column is-1">
                                        <div class="field">
                                            <label class="label" style="font-size:0.8em;">無補助</label>
                                            <div class="field has-addons">
                                                <div class="control">
                                                    <input class="input" type="number" name="dependents_none" value="0"
                                                        min="0" style="width:50px; font-size:0.8em;">
                                                </div>
                                                <div class="control">
                                                    <span class="button is-static" style="font-size:0.8em;">人</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-1">
                                        <div class="field">
                                            <label class="label" style="font-size:0.8em;">補助1/4</label>
                                            <div class="field has-addons">
                                                <div class="control">
                                                    <input class="input" type="number" name="dependents_1_4" value="0"
                                                        min="0" style="width:50px; font-size:0.8em;">
                                                </div>
                                                <div class="control">
                                                    <span class="button is-static" style="font-size:0.8em;">人</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-1">
                                        <div class="field">
                                            <label class="label" style="font-size:0.8em;">補助1/2</label>
                                            <div class="field has-addons">
                                                <div class="control">
                                                    <input class="input" type="number" name="dependents_1_2" value="0"
                                                        min="0" style="width:50px; font-size:0.8em;">
                                                </div>
                                                <div class="control">
                                                    <span class="button is-static" style="font-size:0.8em;">人</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-1">
                                        <div class="field">
                                            <label class="label" style="font-size:0.8em;">補助地區人口保費</label>
                                            <div class="field has-addons">
                                                <div class="control">
                                                    <input class="input" type="number" name="dependents_local" value="0"
                                                        min="0" style="width:50px; font-size:0.8em;">
                                                </div>
                                                <div class="control">
                                                    <span class="button is-static" style="font-size:0.8em;">人</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="column is-1">
                                        <div class="field">
                                            <label class="label" style="font-size:0.8em;">補助全額</label>
                                            <div class="field has-addons">
                                                <div class="control">
                                                    <input class="input" type="number" name="dependents_full" value="0"
                                                        min="0" style="width:50px; font-size:0.8em;">
                                                </div>
                                                <div class="control">
                                                    <span class="button is-static" style="font-size:0.8em;">人</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> <!-- 健康保險 end -->

                    <div id="labor-section">
                        <div class="section-title">勞工保險</div>
                        <!-- 勞保投保項目移到上面 -->
                        <div class="columns">
                            <div class="column is-12">
                                <div class="field">
                                    <label class="label">勞保投保項目</label>
                                    <div class="control">
                                        <label class="checkbox mr-3">
                                            <input type="checkbox" name="labor_insurance_item" value="普通事故保險"> 普通事故保險
                                        </label>
                                        <label class="checkbox mr-3" style="position:relative;">
                                            <input type="checkbox" id="occupational_ins_checkbox"
                                                name="labor_insurance_item" value="職業災害保險"> 職業災害保險
                                        </label>
                                        <label class="checkbox">
                                            <input type="checkbox" name="labor_insurance_item" value="就業保險"> 就業保險
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="columns">
                            <div class="column is-12">
                                <strong>由公司為勞工進行加保，是一種強制雇主應為勞工加保的制度。其收費的比例為：勞工自費20%、雇主70%、政府10%。</strong>
                            </div>
                        </div>
                        <div class="columns is-vcentered">
                            <div class="column is-2">
                                <div class="field">
                                    <label class="label"
                                        style="height: 2.5em; display: flex; align-items: flex-end;">加保日 <span
                                            class="icon has-text-info" title="加保日說明"><i
                                                class="fas fa-question-circle"></i></span></label>
                                    <div class="control">
                                        <input class="input" type="date" name="labor_insurance_date">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label"
                                        style="height: 2.5em; display: flex; align-items: flex-end;">勞保級距法規生效日 <span
                                            style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <input class="input" type="date" name="labor_law_effective_date" required>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="label"
                                        style="height: 2.5em; display: flex; align-items: flex-end;">勞保投保級距 <span
                                            style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="labor_level" required id="labor_level">
                                                <option value="">請選擇投保級距</option>
                                                {% for level in labor_insurance_levels %}
                                                <option value="{{ level.value }}"
                                                    data-insured-salary="{{ level.insured_salary }}"
                                                    data-min-salary="{{ level.min_salary }}"
                                                    data-max-salary="{{ level.max_salary }}" {% if employee and
                                                    employee.labor_level==level.value|string %}selected{% endif %}>
                                                    {{ level.label }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 職業災害保險動態欄位 -->
                            <div class="column is-3" id="occupational-date-field" style="display:none;">
                                <div class="field">
                                    <label class="label"
                                        style="height: 2.5em; display: flex; align-items: flex-end;">職保級距法規生效日 <span
                                            style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <input class="input" type="date" name="occupational_law_effective_date">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2" id="occupational-level-field" style="display:none;">
                                <div class="field">
                                    <label class="label"
                                        style="height: 2.5em; display: flex; align-items: flex-end;">職保投保級距 <span
                                            style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="occupational_level">
                                                <option value="">請選擇投保級距</option>
                                                <option value="1">1級</option>
                                                <option value="2">2級</option>
                                                <option value="3">3級</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div> <!-- 勞工保險 end -->

                    <!-- 勞工退休金區塊 -->
                    <div id="retirement-section">
                        <div class="section-title">勞工退休金</div>

                        <div class="columns">
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">勞退級距法規生效日 <span style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <input class="input" type="date" name="pension_law_effective_date"
                                            value="2025-01-01" required>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">勞退投保級距 <span style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="pension_level" id="pension_level" required
                                                onchange="calculatePensionContribution()">
                                                <option value="">請選擇投保級距</option>
                                                {% for level in pension_levels %}
                                                <option value="{{ level.value }}"
                                                    data-pension-salary="{{ level.pension_salary }}" {% if employee and
                                                    employee.pension_level==level.value|string %}selected{% endif %}>
                                                    {{ level.label }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div id="pension_contribution_result"></div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">加保日 <span style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <input class="input" type="date" name="pension_start_date" required>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">勞退雇主提繳率 <span style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <input class="input" type="number" name="pension_employer_rate" value="0.06"
                                            min="0.06" max="0.15" step="0.01" placeholder="0.06" required
                                            onchange="calculatePensionContribution()">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="columns">
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">勞退自願提繳率 <span style="color:#e53e3e;">*</span></label>
                                    <div class="control">
                                        <input class="input" type="number" name="pension_employee_rate" value="0.00"
                                            min="0.00" max="0.06" step="0.01" placeholder="0.00" required
                                            onchange="calculatePensionContribution()">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div> <!-- 勞工退休金 end -->

                    <div class="field is-grouped is-grouped-right mt-5">
                        <div class="control">
                            <button class="button is-link" type="submit">儲存</button>
                        </div>
                        <div class="control">
                            <a class="button is-light" href="/">取消</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 勞保勞退區塊顯示/隱藏控制
        const laborInsuranceRadios = document.querySelectorAll('input[name="labor_insurance"]');
        const laborSection = document.getElementById('labor-section');
        const retirementSection = document.getElementById('retirement-section');

        function toggleLaborSection() {
            const selectedValue = document.querySelector('input[name="labor_insurance"]:checked')?.value;
            if (laborSection) {
                laborSection.style.display = selectedValue === 'yes' ? 'block' : 'none';
            }
            if (retirementSection) {
                retirementSection.style.display = selectedValue === 'yes' ? 'block' : 'none';
            }
        }

        // 為每個勞保選項添加事件監聽器
        laborInsuranceRadios.forEach(radio => {
            radio.addEventListener('change', toggleLaborSection);
        });

        // 頁面載入時初始化顯示狀態
        toggleLaborSection();

        // 健保區塊顯示/隱藏控制
        const healthInsuranceRadios = document.querySelectorAll('input[name="health_insurance"]');
        const healthSection = document.getElementById('health-section');

        function toggleHealthSection() {
            const selectedValue = document.querySelector('input[name="health_insurance"]:checked')?.value;
            if (healthSection) {
                healthSection.style.display = selectedValue === 'yes' ? 'block' : 'none';
            }
        }

        // 為每個健保選項添加事件監聽器
        healthInsuranceRadios.forEach(radio => {
            radio.addEventListener('change', toggleHealthSection);
        });

        // 頁面載入時初始化健保顯示狀態
        toggleHealthSection();

        // 職業災害保險的顯示/隱藏控制
        const occCheckbox = document.getElementById('occupational_ins_checkbox');
        const occDateField = document.getElementById('occupational-date-field');
        const occLevelField = document.getElementById('occupational-level-field');

        if (occCheckbox && occDateField && occLevelField) {
            function toggleOccFields() {
                const display = occCheckbox.checked ? '' : 'none';
                occDateField.style.display = display;
                occLevelField.style.display = display;
            }
            occCheckbox.addEventListener('change', toggleOccFields);
            toggleOccFields(); // 頁面載入時自動判斷
        }

        // 初始化健保費計算
        calculateHealthInsurancePremium();

        // 初始化勞退提繳計算
        calculatePensionContribution();
    });

    // 健保費計算函數
    function calculateHealthInsurancePremium() {
        const healthLevelSelect = document.getElementById('health_level');
        if (!healthLevelSelect) return;

        const selectedOption = healthLevelSelect.options[healthLevelSelect.selectedIndex];
        if (!selectedOption || !selectedOption.value) return;

        const insuredAmount = parseInt(selectedOption.getAttribute('data-insured-amount') || 0);
        if (!insuredAmount) return;

        // 健保費計算 (一般保險對象)
        // 健保費率 5.17%
        // 員工負擔 30%，雇主負擔 60%，政府負擔 10%
        const totalRate = 5.17;
        const totalPremium = Math.round(insuredAmount * totalRate / 100);

        const employeePremium = Math.round(totalPremium * 0.3);  // 員工負擔 30%
        const employerPremium = Math.round(totalPremium * 0.6);  // 雇主負擔 60%
        const governmentPremium = Math.round(totalPremium * 0.1);  // 政府負擔 10%

        // 更新顯示
        const resultElement = document.getElementById('health_insurance_result');
        if (resultElement) {
            resultElement.innerHTML = `
                <div class="notification is-light is-info is-size-7 mt-2 mb-0 py-2 px-3">
                    <p><strong>健保費計算結果：</strong></p>
                    <p>投保金額：${insuredAmount.toLocaleString()} 元</p>
                    <p>員工負擔：${employeePremium.toLocaleString()} 元 (30%)</p>
                    <p>雇主負擔：${employerPremium.toLocaleString()} 元 (60%)</p>
                    <p>政府負擔：${governmentPremium.toLocaleString()} 元 (10%)</p>
                </div>
            `;
        }
    }

    // 勞退提繳計算函數
    function calculatePensionContribution() {
        const pensionLevelSelect = document.getElementById('pension_level');
        const employerRateInput = document.querySelector('input[name="pension_employer_rate"]');
        const employeeRateInput = document.querySelector('input[name="pension_employee_rate"]');

        if (!pensionLevelSelect || !employerRateInput || !employeeRateInput) return;

        const selectedOption = pensionLevelSelect.options[pensionLevelSelect.selectedIndex];
        if (!selectedOption || !selectedOption.value) return;

        const pensionSalary = parseInt(selectedOption.getAttribute('data-pension-salary') || 0);
        const employerRate = parseFloat(employerRateInput.value || 0.06);
        const employeeRate = parseFloat(employeeRateInput.value || 0.00);

        if (!pensionSalary) return;

        // 勞退提繳計算
        const employerContribution = Math.round(pensionSalary * employerRate);
        const employeeContribution = Math.round(pensionSalary * employeeRate);
        const totalContribution = employerContribution + employeeContribution;

        // 更新顯示
        const resultElement = document.getElementById('pension_contribution_result');
        if (resultElement) {
            resultElement.innerHTML = `
                <div class="notification is-light is-success is-size-7 mt-2 mb-0 py-2 px-3">
                    <p><strong>勞退提繳計算結果：</strong></p>
                    <p>投保薪資：${pensionSalary.toLocaleString()} 元</p>
                    <p>雇主提繳：${employerContribution.toLocaleString()} 元 (${(employerRate * 100).toFixed(1)}%)</p>
                    <p>員工自提：${employeeContribution.toLocaleString()} 元 (${(employeeRate * 100).toFixed(1)}%)</p>
                    <p>總提繳額：${totalContribution.toLocaleString()} 元</p>
                </div>
            `;
        }
    }
</script>

</html>