<!-- 憑證功能模組 -->
<div class="field">
    <label class="label">憑證取得</label>
    <div class="buttons has-addons" id="voucher-btns">
        <button class="button is-selected" type="button">憑證尚未取得</button>
        <button class="button" type="button">沒有憑證</button>
        <button class="button" type="button">有憑證</button>
    </div>
</div>

<div class="field" id="invoice-fields" style="display:none;">
    <div class="field mb-3">
        <label class="checkbox">
            <input type="checkbox" name="is_paper"> 收據類憑證（無發票號碼）
        </label>
    </div>
    <div class="columns is-multiline">
        <div class="column is-4">
            <div class="field">
                <label class="label">發票號碼</label>
                <div class="control is-flex is-align-items-center">
                    <input class="input" type="text" name="invoice_number" id="invoice-number-input"
                        placeholder="請輸入發票號碼">
                    <span id="invoice-number-status" class="ml-2"></span>
                </div>
            </div>
        </div>
        <div class="column is-4">
            <div class="field">
                <label class="label">稅別</label>
                <div class="control">
                    <div class="select is-fullwidth">
                        <select name="tax_type">
                            <option value="應稅" selected>應稅</option>
                            <option value="免稅">免稅</option>
                            <option value="零稅率">零稅率</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="column is-4">
            <div class="field">
                <label class="label">發票日期</label>
                <div class="control">
                    <input class="input" type="date" name="invoice_date">
                </div>
            </div>
        </div>
    </div>
    <div class="columns is-multiline">
        <div class="column is-4">
            <div class="field">
                <label class="label">買方統編</label>
                <div class="control">
                    <input class="input" type="text" name="buyer_tax_id" placeholder="請輸入買方統編">
                </div>
            </div>
        </div>
        <div class="column is-4">
            <div class="field">
                <label class="label">賣方統編</label>
                <div class="control">
                    <input class="input" type="text" name="seller_tax_id" placeholder="請輸入賣方統編"
                        value="{{ company_id }}">
                </div>
            </div>
        </div>
    </div>
    <!-- 隱藏的稅額欄位，用於傳送計算出的稅額到後端 -->
    <input type="hidden" name="tax" id="tax-amount-input" value="0">
</div>

<script>
    // 憑證功能 JavaScript
    (function () {
        // 憑證取得按鈕切換發票欄位顯示
        const voucherBtns = document.querySelectorAll('#voucher-btns button');
        const invoiceFields = document.getElementById('invoice-fields');

        voucherBtns.forEach(btn => {
            btn.addEventListener('click', function () {
                voucherBtns.forEach(b => {
                    b.classList.remove('is-selected', 'is-primary');
                });
                this.classList.add('is-selected', 'is-primary');

                if (this.textContent.trim() === '有憑證') {
                    invoiceFields.style.display = '';
                } else {
                    invoiceFields.style.display = 'none';
                }
            });
        });

        // 發票號碼即時檢查功能
        const invoiceNumberInput = document.getElementById('invoice-number-input');
        const invoiceNumberStatus = document.getElementById('invoice-number-status');

        if (invoiceNumberInput && invoiceNumberStatus) {
            let lastValue = '';
            let timer = null;
            invoiceNumberInput.addEventListener('input', function () {
                const value = this.value.trim();
                if (value === lastValue) return;
                lastValue = value;
                invoiceNumberStatus.textContent = '';
                if (timer) clearTimeout(timer);
                if (!value) return;
                timer = setTimeout(() => {
                    fetch(`/api/check_invoice_number?number=${encodeURIComponent(value)}`)
                        .then(r => r.json())
                        .then(data => {
                            if (data.exists) {
                                invoiceNumberStatus.textContent = '號碼已存在';
                                invoiceNumberStatus.style.color = 'red';
                            } else {
                                invoiceNumberStatus.textContent = '號碼正確';
                                invoiceNumberStatus.style.color = 'green';
                            }
                        })
                        .catch(() => {
                            invoiceNumberStatus.textContent = '查詢失敗';
                            invoiceNumberStatus.style.color = 'orange';
                        });
                }, 400);
            });
        }

        // 稅額自動計算功能（僅適用於收入記錄）
        function setupTaxCalculation() {
            const totalInput = document.querySelector('input[name="total"]');
            const taxTypeSelect = document.querySelector('select[name="tax_type"]');
            const taxInput = document.getElementById('tax-amount-input');

            if (!totalInput || !taxTypeSelect || !taxInput) return;

            function calculateTax() {
                // 檢查是否選擇了「有憑證」且稅別為「應稅」
                const hasVoucher = document.querySelector('#voucher-btns .is-selected')?.textContent.trim() === '有憑證';
                const isTaxable = taxTypeSelect.value === '應稅';

                if (hasVoucher && isTaxable && totalInput.value) {
                    const totalAmount = parseFloat(totalInput.value);
                    if (totalAmount > 0) {
                        // 計算未稅金額：總計 ÷ 1.05，四捨五入
                        const untaxedAmount = Math.round(totalAmount / 1.05);
                        // 計算稅額：總計 - 未稅金額
                        const taxAmount = totalAmount - untaxedAmount;

                        // 更新總計欄位為未稅金額
                        totalInput.value = untaxedAmount;

                        // 更新隱藏的稅額欄位
                        taxInput.value = taxAmount;

                        // 顯示計算結果
                        console.log(`原總計: ${totalAmount}, 未稅金額: ${untaxedAmount}, 稅額: ${taxAmount}`);

                        // 顯示提示訊息
                        const existingMsg = document.getElementById('tax-calculation-msg');
                        if (existingMsg) existingMsg.remove();

                        const msgDiv = document.createElement('div');
                        msgDiv.id = 'tax-calculation-msg';
                        msgDiv.className = 'notification is-info is-light mt-2';
                        msgDiv.innerHTML = `<strong>稅額計算：</strong>含稅總計 ${totalAmount} → 未稅金額 ${untaxedAmount} + 稅額 ${taxAmount}`;
                        totalInput.parentElement.parentElement.appendChild(msgDiv);

                        // 3秒後自動隱藏提示
                        setTimeout(() => {
                            if (msgDiv.parentElement) {
                                msgDiv.remove();
                            }
                        }, 3000);
                    }
                } else {
                    // 如果不符合條件，清空稅額
                    taxInput.value = 0;
                }
            }

            // 監聽總計欄位失去焦點
            totalInput.addEventListener('blur', calculateTax);

            // 監聽稅別變化
            taxTypeSelect.addEventListener('change', calculateTax);

            // 監聽憑證狀態變化
            voucherBtns.forEach(btn => {
                btn.addEventListener('click', function () {
                    setTimeout(calculateTax, 100); // 延遲執行，確保狀態已更新
                });
            });
        }

        // 初始化稅額計算功能
        setupTaxCalculation();
    })();
</script>