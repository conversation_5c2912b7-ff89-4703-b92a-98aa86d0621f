<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>已刪除記錄管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 審計系統導航 -->
                <div class="box mb-5">
                    <h2 class="subtitle">
                        <a href="/" style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        <i class="fas fa-shield-alt"></i> 審計系統
                    </h2>
                    <div class="buttons">
                        <a class="button is-info is-light" href="/audit_dashboard">
                            <span class="icon"><i class="fas fa-tachometer-alt"></i></span>
                            <span>儀表板</span>
                        </a>
                        <a class="button is-info is-light" href="/audit_search">
                            <span class="icon"><i class="fas fa-search"></i></span>
                            <span>審計搜尋</span>
                        </a>
                        <a class="button is-warning" href="/deleted_records">
                            <span class="icon"><i class="fas fa-trash-restore"></i></span>
                            <span>已刪除記錄</span>
                        </a>
                    </div>
                </div>
                <!-- 收支記錄 -->
                <div class="box mb-5">
                    <div class="level">
                        <div class="level-left">
                            <div class="level-item">
                                <h3 class="subtitle">
                                    <i class="fas fa-money-bill"></i> 已刪除的收支記錄
                                </h3>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <span class="tag is-danger">{{ deleted_money|length }} 筆</span>
                            </div>
                        </div>
                    </div>
                    
                    {% if deleted_money %}
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>名稱</th>
                                    <th>類型</th>
                                    <th>金額</th>
                                    <th>記帳日期</th>
                                    <th>刪除者</th>
                                    <th>刪除時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in deleted_money %}
                                <tr>
                                    <td>{{ record.id }}</td>
                                    <td>{{ record.name or '' }}</td>
                                    <td>
                                        <span class="tag is-{{ 'success' if record.money_type == '收入' else 'danger' }}">
                                            {{ record.money_type }}
                                        </span>
                                    </td>
                                    <td>{{ "{:,}".format(record.total) if record.total else '0' }}</td>
                                    <td>{{ record.a_time.strftime('%Y-%m-%d') if record.a_time else '' }}</td>
                                    <td>{{ record.deleted_by or '' }}</td>
                                    <td>{{ record.deleted_at.strftime('%Y-%m-%d %H:%M:%S') if record.deleted_at else '' }}</td>
                                    <td>
                                        <button class="button is-small is-success" onclick="restoreRecord('money', {{ record.id }})">
                                            <span class="icon"><i class="fas fa-undo"></i></span>
                                            <span>復原</span>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="notification is-light">
                        <p class="has-text-grey">沒有已刪除的收支記錄</p>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 帳戶記錄 -->
                <div class="box mb-5">
                    <div class="level">
                        <div class="level-left">
                            <div class="level-item">
                                <h3 class="subtitle">
                                    <i class="fas fa-university"></i> 已刪除的帳戶
                                </h3>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <span class="tag is-danger">{{ deleted_accounts|length }} 筆</span>
                            </div>
                        </div>
                    </div>
                    
                    {% if deleted_accounts %}
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>帳戶名稱</th>
                                    <th>類別</th>
                                    <th>銀行</th>
                                    <th>帳號</th>
                                    <th>刪除者</th>
                                    <th>刪除時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in deleted_accounts %}
                                <tr>
                                    <td>{{ record.id }}</td>
                                    <td>{{ record.name }}</td>
                                    <td>
                                        <span class="tag is-info">{{ record.category }}</span>
                                    </td>
                                    <td>{{ record.bank_name or '' }}</td>
                                    <td>{{ record.account_number or '' }}</td>
                                    <td>{{ record.deleted_by or '' }}</td>
                                    <td>{{ record.deleted_at.strftime('%Y-%m-%d %H:%M:%S') if record.deleted_at else '' }}</td>
                                    <td>
                                        <button class="button is-small is-success" onclick="restoreRecord('account', {{ record.id }})">
                                            <span class="icon"><i class="fas fa-undo"></i></span>
                                            <span>復原</span>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="notification is-light">
                        <p class="has-text-grey">沒有已刪除的帳戶</p>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 轉帳記錄 -->
                <div class="box mb-5">
                    <div class="level">
                        <div class="level-left">
                            <div class="level-item">
                                <h3 class="subtitle">
                                    <i class="fas fa-exchange-alt"></i> 已刪除的轉帳記錄
                                </h3>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <span class="tag is-danger">{{ deleted_transfers|length }} 筆</span>
                            </div>
                        </div>
                    </div>
                    
                    {% if deleted_transfers %}
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>轉出帳戶</th>
                                    <th>轉入帳戶</th>
                                    <th>金額</th>
                                    <th>轉帳日期</th>
                                    <th>刪除者</th>
                                    <th>刪除時間</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in deleted_transfers %}
                                <tr>
                                    <td>{{ record.id }}</td>
                                    <td>{{ record.out_account.name if record.out_account else '' }}</td>
                                    <td>{{ record.in_account.name if record.in_account else '' }}</td>
                                    <td>{{ "{:,}".format(record.amount) if record.amount else '0' }}</td>
                                    <td>{{ record.transfer_date.strftime('%Y-%m-%d') if record.transfer_date else '' }}</td>
                                    <td>{{ record.deleted_by or '' }}</td>
                                    <td>{{ record.deleted_at.strftime('%Y-%m-%d %H:%M:%S') if record.deleted_at else '' }}</td>
                                    <td>
                                        <button class="button is-small is-success" onclick="restoreRecord('transfer', {{ record.id }})">
                                            <span class="icon"><i class="fas fa-undo"></i></span>
                                            <span>復原</span>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="notification is-light">
                        <p class="has-text-grey">沒有已刪除的轉帳記錄</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 確認復原對話框 -->
    <div class="modal" id="confirmModal">
        <div class="modal-background"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">確認復原</p>
                <button class="delete" aria-label="close" onclick="closeModal()"></button>
            </header>
            <section class="modal-card-body">
                <p>您確定要復原這筆記錄嗎？</p>
            </section>
            <footer class="modal-card-foot">
                <button class="button is-success" id="confirmRestore">確認復原</button>
                <button class="button" onclick="closeModal()">取消</button>
            </footer>
        </div>
    </div>
    <script>
        let currentTable = '';
        let currentId = '';
        
        function restoreRecord(table, id) {
            currentTable = table;
            currentId = id;
            
            document.getElementById('confirmModal').classList.add('is-active');
        }
        
        function closeModal() {
            document.getElementById('confirmModal').classList.remove('is-active');
        }
        
        document.getElementById('confirmRestore').addEventListener('click', function() {
            const formData = new FormData();
            formData.append('table', currentTable);
            formData.append('id', currentId);
            
            fetch('/restore_record', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('記錄已成功復原！');
                    location.reload();
                } else {
                    alert('復原失敗: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('復原時發生錯誤');
            });
            
            closeModal();
        });
        
        // 點擊背景關閉對話框
        document.querySelector('.modal-background').addEventListener('click', closeModal);
    </script>
</body>
</html>