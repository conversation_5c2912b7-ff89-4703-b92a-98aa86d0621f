<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登入 - 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="has-background-light">
    <section class="hero is-fullheight">
        <div class="hero-body">
            <div class="container">
                <div class="columns is-centered">
                    <div class="column is-4">
                        <div class="box">
                            <div class="has-text-centered mb-5">
                                <h1 class="title is-3">
                                    <i class="fas fa-calculator mr-2"></i>
                                    會計系統
                                </h1>
                                <p class="subtitle is-6">請登入您的帳號</p>
                            </div>

                            <!-- 顯示訊息 -->
                            {% with messages = get_flashed_messages(with_categories=true) %}
                                {% if messages %}
                                    {% for category, message in messages %}
                                        <div class="notification is-{{ 'danger' if category == 'error' else 'info' if category == 'info' else 'warning' if category == 'warning' else 'success' }}">
                                            <button class="delete"></button>
                                            {{ message }}
                                        </div>
                                    {% endfor %}
                                {% endif %}
                            {% endwith %}

                            <form method="POST" action="{{ url_for('auth.login') }}">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                <div class="field">
                                    <label class="label">用戶名</label>
                                    <div class="control has-icons-left">
                                        <input class="input" type="text" name="username" placeholder="請輸入用戶名" required>
                                        <span class="icon is-small is-left">
                                            <i class="fas fa-user"></i>
                                        </span>
                                    </div>
                                </div>

                                <div class="field">
                                    <label class="label">密碼</label>
                                    <div class="control has-icons-left">
                                        <input class="input" type="password" name="password" placeholder="請輸入密碼" required>
                                        <span class="icon is-small is-left">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                    </div>
                                </div>

                                <div class="field">
                                    <div class="control">
                                        <button class="button is-primary is-fullwidth" type="submit">
                                            <span class="icon">
                                                <i class="fas fa-sign-in-alt"></i>
                                            </span>
                                            <span>登入</span>
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <hr>
                            
                            <div class="has-text-centered">
                                <p class="mb-3">
                                    <span class="has-text-grey">還沒有帳號嗎？</span>
                                    <a href="{{ url_for('registration.register') }}" class="button is-link is-light is-small ml-2">
                                        <span class="icon is-small">
                                            <i class="fas fa-user-plus"></i>
                                        </span>
                                        <span>立即註冊</span>
                                    </a>
                                </p>
                                <p class="is-size-7 has-text-grey">
                                    <i class="fas fa-gift mr-1"></i>
                                    新用戶可享30天免費試用
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // 自動關閉通知
        document.addEventListener('DOMContentLoaded', () => {
            (document.querySelectorAll('.notification .delete') || []).forEach(($delete) => {
                const $notification = $delete.parentNode;
                $delete.addEventListener('click', () => {
                    $notification.parentNode.removeChild($notification);
                });
            });
        });
    </script>
</body>
</html>