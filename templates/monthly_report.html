<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>月度報表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 導航 -->
                <div class="box mb-5">
                    <h2 class="subtitle">
                        <a href="/?main=我的報表" style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        <i class="fas fa-calendar-alt"></i> 月度報表 - {{ year }}年{{ month }}月
                    </h2>
                    
                    <!-- 日期選擇 -->
                    <form method="GET" class="field is-grouped">
                        <div class="control">
                            <div class="select">
                                <select name="year">
                                    {% for y in range(2020, 2030) %}
                                    <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}年</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="control">
                            <div class="select">
                                <select name="month">
                                    {% for m in range(1, 13) %}
                                    <option value="{{ m }}" {% if m == month %}selected{% endif %}>{{ m }}月</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="control">
                            <button type="submit" class="button is-primary">
                                <span class="icon"><i class="fas fa-search"></i></span>
                                <span>查詢</span>
                            </button>
                        </div>
                    </form>
                </div>

                {% if report %}
                <!-- 收支統計摘要 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-chart-pie"></i> 收支統計摘要
                    </h3>
                    <div class="columns">
                        <div class="column">
                            <div class="notification is-success">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ "{:,}".format(report.summary.get('收入', {}).get('total', 0)) }}</p>
                                                <p class="subtitle">收入總額</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-arrow-up fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <p class="has-text-weight-semibold">筆數: {{ report.summary.get('收入', {}).get('count', 0) }}</p>
                            </div>
                        </div>
                        
                        <div class="column">
                            <div class="notification is-danger">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ "{:,}".format(report.summary.get('支出', {}).get('total', 0)) }}</p>
                                                <p class="subtitle">支出總額</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-arrow-down fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <p class="has-text-weight-semibold">筆數: {{ report.summary.get('支出', {}).get('count', 0) }}</p>
                            </div>
                        </div>
                        
                        <div class="column">
                            <div class="notification is-info">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                {% set net_amount = report.summary.get('收入', {}).get('total', 0) - report.summary.get('支出', {}).get('total', 0) %}
                                                <p class="title">{{ "{:,}".format(net_amount) }}</p>
                                                <p class="subtitle">淨收支</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-balance-scale fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <p class="has-text-weight-semibold">
                                    {% if net_amount > 0 %}
                                        <span class="has-text-success">盈餘</span>
                                    {% elif net_amount < 0 %}
                                        <span class="has-text-danger">虧損</span>
                                    {% else %}
                                        <span class="has-text-grey">平衡</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收支圖表 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-chart-bar"></i> 收支圖表
                    </h3>
                    <canvas id="summaryChart" width="400" height="200"></canvas>
                </div>

                <!-- 會計科目分析 -->
                {% if report.by_subject %}
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-list-alt"></i> 會計科目分析
                    </h3>
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>科目名稱</th>
                                    <th>科目代碼</th>
                                    <th>收支類型</th>
                                    <th>金額</th>
                                    <th>筆數</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in report.by_subject %}
                                <tr>
                                    <td>{{ item.subject_name }}</td>
                                    <td><code>{{ item.subject_code }}</code></td>
                                    <td>
                                        <span class="tag is-{{ 'success' if item.money_type == '收入' else 'danger' }}">
                                            {{ item.money_type }}
                                        </span>
                                    </td>
                                    <td class="has-text-right">{{ "{:,}".format(item.total) }}</td>
                                    <td class="has-text-right">{{ item.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                <!-- 帳戶分析 -->
                {% if report.by_account %}
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-university"></i> 帳戶分析
                    </h3>
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>帳戶名稱</th>
                                    <th>帳戶類別</th>
                                    <th>收支類型</th>
                                    <th>金額</th>
                                    <th>筆數</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in report.by_account %}
                                <tr>
                                    <td>{{ item.account_name }}</td>
                                    <td>
                                        <span class="tag is-info">{{ item.account_category }}</span>
                                    </td>
                                    <td>
                                        <span class="tag is-{{ 'success' if item.money_type == '收入' else 'danger' }}">
                                            {{ item.money_type }}
                                        </span>
                                    </td>
                                    <td class="has-text-right">{{ "{:,}".format(item.total) }}</td>
                                    <td class="has-text-right">{{ item.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                <!-- 收支對象分析 -->
                {% if report.by_identity %}
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-users"></i> 收支對象分析 (前20名)
                    </h3>
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>對象名稱</th>
                                    <th>對象類型</th>
                                    <th>收支類型</th>
                                    <th>金額</th>
                                    <th>筆數</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in report.by_identity %}
                                <tr>
                                    <td>{{ item.identity_name }}</td>
                                    <td>
                                        <span class="tag is-warning">{{ item.identity_type or '未分類' }}</span>
                                    </td>
                                    <td>
                                        <span class="tag is-{{ 'success' if item.money_type == '收入' else 'danger' }}">
                                            {{ item.money_type }}
                                        </span>
                                    </td>
                                    <td class="has-text-right">{{ "{:,}".format(item.total) }}</td>
                                    <td class="has-text-right">{{ item.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                {% else %}
                <!-- 無資料提示 -->
                <div class="box">
                    <div class="notification is-warning">
                        <h4 class="title is-4">
                            <i class="fas fa-exclamation-triangle"></i> 無資料
                        </h4>
                        <p>{{ year }}年{{ month }}月沒有收支記錄，請選擇其他月份或先新增收支記錄。</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        {% if report and report.summary %}
        // 建立收支圖表
        const ctx = document.getElementById('summaryChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['收入', '支出'],
                datasets: [{
                    label: '金額',
                    data: [
                        {{ report.summary.get('收入', {}).get('total', 0) }},
                        {{ report.summary.get('支出', {}).get('total', 0) }}
                    ],
                    backgroundColor: [
                        'rgba(72, 187, 120, 0.8)',
                        'rgba(245, 101, 101, 0.8)'
                    ],
                    borderColor: [
                        'rgba(72, 187, 120, 1)',
                        'rgba(245, 101, 101, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '{{ year }}年{{ month }}月收支統計'
                    },
                    legend: {
                        display: false
                    }
                }
            }
        });
        {% endif %}
    </script>
</body>
</html>