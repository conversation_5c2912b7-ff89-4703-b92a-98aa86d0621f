<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <title>會計分錄明細</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        body { background: #f7f9fb; }
        .main-box { background: #fff; border-radius: 10px; box-shadow: 0 2px 12px #0001; padding: 2.5rem 2rem; }
        .info-row { font-size: 1.1rem; margin-bottom: 0.5rem; }
        .info-label { color: #888; margin-right: 0.5em; }
        .table th, .table td { text-align: center; font-size: 1.05rem; }
        .debit { color: #2563eb; font-weight: 600; }
        .credit { color: #e67e22; font-weight: 600; }
        .subject-code { color: #888; font-size: 0.95em; margin-left: 0.2em; }
        .amount-cell { font-family: '<PERSON><PERSON>', 'Consolas', monospace; letter-spacing: 1px; }
        .back-btn { margin-top: 2rem; }
    </style>
</head>
<body>
<section class="section">
    <div class="container" style="max-width: 700px;">
        <div class="main-box">
            <h1 class="title is-4 mb-4" style="letter-spacing:1px;">會計分錄明細</h1>
            <div class="columns is-mobile is-multiline mb-4">
                <div class="column is-half info-row"><span class="info-label">交易摘要：</span>{{ main_entry.name or main_entry.note or '' }}</div>
                <div class="column is-half info-row"><span class="info-label">分錄編號：</span>{{ main_entry.journal_reference }}</div>
                <div class="column is-half info-row"><span class="info-label">交易日期：</span>{{ main_entry.a_time }}</div>
            </div>
            <div class="table-container">
                <table class="table is-fullwidth is-striped is-hoverable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>借方/貸方</th>
                            <th>科目名稱 <span class="info-label">(代號)</span></th>
                            <th>金額</th>
                            <th>摘要</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% for entry in entries %}
                        <tr>
                            <td>{{ entry.a_time.strftime('%Y-%m-%d') if entry.a_time else '' }}</td>
                            <td>
                                {% if entry.entry_side == 'DEBIT' %}
                                    <span class="debit">借方</span>
                                {% else %}
                                    <span class="credit">貸方</span>
                                {% endif %}
                            </td>
                            <td>{{ subjects.get(entry.subject_code, '') }}<span class="subject-code">{{ entry.subject_code }}</span></td>
                            <td class="amount-cell">{{ '{:,}'.format(entry.total) if entry.total else '0' }}</td>
                            <td>{{ entry.name or entry.note or '' }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
            <a class="button is-link is-light back-btn" href="javascript:window.close();"><span class="icon"><i class="fas fa-arrow-left"></i></span> <span>關閉分頁</span></a>
        </div>
    </div>
</section>
</body>
</html> 