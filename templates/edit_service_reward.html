<!DOCTYPE html>
<html lang="zh-Hant">

<head>
  <meta charset="UTF-8">
  <title>編輯勞務報酬單</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  <style>
    .main-title {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 1.5rem;
    }

    .form-section {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px #eee;
      padding: 2rem;
      margin-bottom: 2rem;
    }

    .field {
      margin-bottom: 1.2rem;
    }

    .is-required:after {
      content: '*';
      color: #e53e3e;
      margin-left: 0.2em;
    }

    .form-btns {
      display: flex;
      justify-content: flex-end;
      gap: 1.5em;
      margin-top: 2.5em;
    }

    .sidebar {
      width: 200px;
      font-size: 20px;
    }

    .edit-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 1rem;
      border-radius: 12px 12px 0 0;
      margin-bottom: 0;
    }

    .edit-form {
      border-radius: 0 0 12px 12px;
    }

    .status-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 600;
    }

    .status-draft {
      background-color: #fff3cd;
      color: #856404;
    }

    .status-submitted {
      background-color: #d1ecf1;
      color: #0c5460;
    }
  </style>
</head>

<body style="background:#f5f6fa;">
  <div class="container is-fluid">
    <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
      <div class="column is-narrow">
        {% include 'sidebar.html' %}
      </div>
      <div class="column">
        <div class="main-title mt-5 mb-4">
          <a href="javascript:history.back()"
            style="color:#222;text-decoration:none;font-size:1.5rem;vertical-align:middle;">←</a>
          編輯勞務報酬單
          <span class="ml-4 status-badge status-{{ reward.status }}">
            {% if reward.status == 'draft' %}草稿
            {% elif reward.status == 'submitted' %}已提交
            {% elif reward.status == 'approved' %}已核准
            {% elif reward.status == 'paid' %}已付款
            {% else %}{{ reward.status }}
            {% endif %}
          </span>
        </div>

        <form method="POST" enctype="multipart/form-data">
          <div class="edit-header">
            <h3 class="title is-4 has-text-white mb-2">
              <span class="icon mr-2"><i class="fas fa-edit"></i></span>
              編輯勞務報酬單 #SR{{ '%04d'|format(reward.id) }}
            </h3>
            <p class="subtitle is-6 has-text-white-ter mb-0">
              建立日期：{{ reward.create_date }}
            </p>
          </div>

          <div class="form-section edit-form">
            <!-- 基本資料 -->
            <h4 class="title is-5 mb-4">
              <span class="icon mr-2"><i class="fas fa-user"></i></span>
              基本資料
            </h4>
            
            <div class="columns">
              <div class="column">
                <div class="field">
                  <label class="label is-required">姓名</label>
                  <div class="control">
                    <input class="input" type="text" name="name" value="{{ reward.name }}" required>
                  </div>
                </div>
              </div>
              <div class="column">
                <div class="field">
                  <label class="label">電子郵件</label>
                  <div class="control">
                    <input class="input" type="email" name="email" value="{{ reward.email }}">
                  </div>
                </div>
              </div>
              <div class="column">
                <div class="field">
                  <label class="label">電話</label>
                  <div class="control">
                    <input class="input" type="tel" name="phone" value="{{ reward.phone }}">
                  </div>
                </div>
              </div>
            </div>

            <!-- 勞務資訊 -->
            <h4 class="title is-5 mb-4 mt-5">
              <span class="icon mr-2"><i class="fas fa-briefcase"></i></span>
              勞務資訊
            </h4>
            
            <div class="columns">
              <div class="column is-6">
                <div class="field">
                  <label class="label is-required">勞務內容</label>
                  <div class="control">
                    <textarea class="textarea" name="service_content" rows="3" required>{{ reward.service_content }}</textarea>
                  </div>
                </div>
              </div>
              <div class="column is-3">
                <div class="field">
                  <label class="label is-required">申報類別</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select name="declaration_type" required>
                        <option value="">請選擇</option>
                        <option value="9A" {% if reward.declaration_type == '9A' %}selected{% endif %}>9A 執行業務所得</option>
                        <option value="9B" {% if reward.declaration_type == '9B' %}selected{% endif %}>9B 稿費</option>
                        <option value="50" {% if reward.declaration_type == '50' %}selected{% endif %}>50 薪資所得</option>
                        <option value="92" {% if reward.declaration_type == '92' %}selected{% endif %}>92 其他所得</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              <div class="column is-3">
                <div class="field">
                  <label class="label">執行業務類別</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select name="business_category">
                        <option value="">請選擇</option>
                        <option value="01" {% if reward.business_category == '01' %}selected{% endif %}>01.律師</option>
                        <option value="02" {% if reward.business_category == '02' %}selected{% endif %}>02.會計師</option>
                        <!-- 更多選項... -->
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="columns">
              <div class="column is-3">
                <div class="field">
                  <label class="label">勞務開始日期</label>
                  <div class="control">
                    <input class="input" type="date" name="service_start_date" value="{{ reward.service_start_date }}">
                  </div>
                </div>
              </div>
              <div class="column is-3">
                <div class="field">
                  <label class="label">勞務結束日期</label>
                  <div class="control">
                    <input class="input" type="date" name="service_end_date" value="{{ reward.service_end_date }}">
                  </div>
                </div>
              </div>
              <div class="column is-3">
                <div class="field">
                  <label class="label is-required">金額</label>
                  <div class="control">
                    <input class="input" type="number" name="amount" value="{{ reward.amount }}" required>
                  </div>
                </div>
              </div>
              <div class="column is-3">
                <div class="field">
                  <label class="label">付款帳戶</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select name="payment_account">
                        <option value="">請選擇</option>
                        {% for account in accounts %}
                        <option value="{{ account.id }}" {% if account.id == reward.payment_account_id %}selected{% endif %}>
                          {{ account.name }}{% if account.bank_name %} ({{ account.bank_name }}){% endif %}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 其他資訊 -->
            <h4 class="title is-5 mb-4 mt-5">
              <span class="icon mr-2"><i class="fas fa-cog"></i></span>
              其他資訊
            </h4>
            
            <div class="columns">
              <div class="column is-4">
                <div class="field">
                  <label class="label">部門</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select name="department_id">
                        <option value="">請選擇</option>
                        {% for department in departments %}
                        <option value="{{ department.id }}" {% if department.id == reward.department_id %}selected{% endif %}>
                          {{ department.name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              <div class="column is-4">
                <div class="field">
                  <label class="label">專案</label>
                  <div class="control">
                    <div class="select is-fullwidth">
                      <select name="project_id">
                        <option value="">請選擇</option>
                        {% for project in projects %}
                        <option value="{{ project.id }}" {% if project.id == reward.project_id %}selected{% endif %}>
                          {{ project.name }}
                        </option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="columns">
              <div class="column is-6">
                <div class="field">
                  <label class="label">通知事項</label>
                  <div class="control">
                    <textarea class="textarea" name="notice" rows="3">{{ reward.notice }}</textarea>
                  </div>
                </div>
              </div>
              <div class="column is-6">
                <div class="field">
                  <label class="label">備註</label>
                  <div class="control">
                    <textarea class="textarea" name="note" rows="3">{{ reward.note }}</textarea>
                  </div>
                </div>
              </div>
            </div>

            <!-- 表單按鈕 -->
            <div class="form-btns">
              <a href="/service_reward_list" class="button is-light is-large">
                <span class="icon"><i class="fas fa-times"></i></span>
                <span>取消</span>
              </a>
              <button type="submit" class="button is-primary is-large">
                <span class="icon"><i class="fas fa-save"></i></span>
                <span>儲存變更</span>
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</body>

</html>
