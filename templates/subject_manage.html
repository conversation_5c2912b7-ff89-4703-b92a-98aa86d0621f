<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科目管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .subject-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2.5rem 2rem;
            max-width: 700px;
            margin: 2rem auto;
        }

        .subject-search {
            border: 1.5px solid #2563eb;
            border-radius: 8px;
            padding: 0.5em 1em;
            margin-bottom: 1.5em;
            display: flex;
            align-items: center;
        }

        .subject-search input {
            border: none;
            outline: none;
            flex: 1;
        }

        .subject-search .icon {
            color: #2563eb;
        }

        .subject-list {
            background: #fff;
            border-radius: 0 0 12px 12px;
            padding: 1.5em 1.5em 1em 1.5em;
        }

        .category-item {
            display: flex;
            align-items: center;
            font-size: 1.18rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 0.7em;
            cursor: pointer;
        }

        .category-badge {
            background: #c7d7fa;
            color: #2563eb;
            border-radius: 999px;
            font-size: 1rem;
            font-weight: bold;
            padding: 0.1em 0.9em;
            margin-left: 0.5em;
            margin-right: 0.5em;
        }

        .category-arrow {
            margin-left: auto;
            color: #2563eb;
            font-size: 1.2em;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .category-arrow.open {
            transform: rotate(180deg);
        }

        .category-children {
            margin-left: 2.5em;
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            display: none;
        }

        .category-children.open {
            display: block;
        }

        .subject-item {
            display: flex;
            align-items: center;
            font-size: 1.08rem;
            font-weight: bold;
            color: #1d3557;
            margin-bottom: 0.5em;
            cursor: pointer;
        }

        .subject-arrow {
            margin-left: auto;
            color: #1d3557;
            font-size: 1.1em;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .subject-arrow.open {
            transform: rotate(180deg);
        }

        .subject-children {
            margin-left: 2.5em;
            margin-top: 0.3em;
            margin-bottom: 0.3em;
            display: none;
        }

        .subject-children.open {
            display: block;
        }

        .add-btn {
            float: right;
            margin-top: 0.5em;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=會計科目"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        科目管理
                    </h1>
                </div>
                <div class="subject-card">
                    <div
                        style="font-size:1.3rem;font-weight:bold;color:#fff;background:#2563eb;padding:0.7em 1.5em 0.7em 1.5em;border-radius:12px 12px 0 0;">
                        科目資訊
                    </div>
                    <div class="subject-search mt-4 mb-3">
                        <input class="input" id="subject-search-input" type="text" placeholder="請輸入科目名稱或科目代碼"
                            oninput="filterSubjects()">
                        <span class="icon"><i class="fas fa-filter"></i></span>
                    </div>
                    <div class="subject-list">
                        {% for category, subjects in category_dict.items() %}
                        <div class="category-item" onclick="toggleCategory('{{ category|e }}')">
                            {{ category }}
                            <span class="category-badge">{{ subjects|length }}</span>
                            <span class="category-arrow" id="cat-arrow-{{ category|e }}">&#x25BC;</span>
                        </div>
                        <div class="category-children" id="cat-{{ category|e }}" data-cat-id="{{ category|e }}">
                            {% for subject in subjects %}
                            <div class="subject-item" data-sub-id="{{ subject.code }}">
                                <span>{{ subject.name }}-{{ subject.code }}</span>
                                <span style="margin-left:auto; display:flex; align-items:center; gap:8px;">
                                    <span class="subject-arrow" id="sub-arrow-{{ subject.code }}"
                                        onclick="event.stopPropagation(); toggleSubject('{{ subject.code }}')">&#x25BC;</span>
                                    <a class="button is-small is-link is-light ml-2" style="padding:2px 8px;"
                                        title="新增子科目" href="/accounting/add_subject?parent_code={{ subject.code }}">
                                        <span class="icon is-small">＋</span>
                                    </a>
                                </span>
                            </div>
                            <div class="subject-children" id="sub-{{ subject.code }}">
                                {% for child in subject.children %}
                                <div data-child-id="{{ child.code }}"
                                    style="color:#222;font-weight:normal;font-size:1.05rem;margin-bottom:0.4em;display:flex;align-items:center;justify-content:space-between;">
                                    <span>{{ child.name }} ({{ child.code }})</span>
                                    <a class="button is-small is-light ml-2" style="padding:2px 8px;" title="編輯子科目"
                                        href="/accounting/edit_subject?code={{ child.code }}">
                                        <span class="icon is-small"><i class="fa fa-pencil-alt"></i></span>
                                    </a>
                                </div>
                                {% endfor %}
                            </div>
                            {% endfor %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        // 幫助函式：收合所有分類與科目
        function collapseAll() {
            document.querySelectorAll('.category-children').forEach(function (el) {
                el.classList.remove('open');
            });
            document.querySelectorAll('.category-arrow').forEach(function (el) {
                el.classList.remove('open');
            });
            document.querySelectorAll('.subject-children').forEach(function (el) {
                el.classList.remove('open');
            });
            document.querySelectorAll('.subject-arrow').forEach(function (el) {
                el.classList.remove('open');
            });
        }

        // 展開/收合分類
        function toggleCategory(catId) {
            var children = document.getElementById('cat-' + catId);
            var arrow = document.getElementById('cat-arrow-' + catId);
            if (!children || !arrow) return;
            children.classList.toggle('open');
            arrow.classList.toggle('open');
        }
        // 展開/收合母科目下的子科目
        function toggleSubject(subId) {
            var children = document.getElementById('sub-' + subId);
            var arrow = document.getElementById('sub-arrow-' + subId);
            if (!children || !arrow) return;
            children.classList.toggle('open');
            arrow.classList.toggle('open');
        }
        // 搜尋功能
        function filterSubjects() {
            var input = document.getElementById('subject-search-input').value.trim().toLowerCase();
            // 先全部收合
            collapseAll();
            // 取得所有分類
            var categoryBlocks = document.querySelectorAll('.category-children');
            var categoryItems = document.querySelectorAll('.category-item');
            categoryBlocks.forEach(function (catBlock, i) {
                var catId = catBlock.getAttribute('data-cat-id');
                var hasVisible = false;
                var subjectItems = catBlock.querySelectorAll('.subject-item');
                subjectItems.forEach(function (subjItem) {
                    var subjId = subjItem.getAttribute('data-sub-id');
                    var subjText = subjItem.textContent.replace(/\s+/g, '').toLowerCase();
                    var childrenBlock = document.getElementById('sub-' + subjId);
                    var childVisible = false;
                    if (childrenBlock) {
                        var childDivs = childrenBlock.querySelectorAll('div[data-child-id]');
                        childDivs.forEach(function (childDiv) {
                            var childText = childDiv.textContent.replace(/\s+/g, '').toLowerCase();
                            if (input && childText.indexOf(input) !== -1) {
                                childDiv.style.display = '';
                                childVisible = true;
                            } else {
                                childDiv.style.display = 'none';
                            }
                        });
                        // 如果有子科目符合，展開母科目
                        if (childVisible) {
                            childrenBlock.classList.add('open');
                            var arrow = document.getElementById('sub-arrow-' + subjId);
                            if (arrow) arrow.classList.add('open');
                        } else {
                            childrenBlock.classList.remove('open');
                            var arrow = document.getElementById('sub-arrow-' + subjId);
                            if (arrow) arrow.classList.remove('open');
                        }
                    }
                    // 母科目本身符合 or 子科目有符合
                    if ((input && subjText.indexOf(input) !== -1) || childVisible) {
                        subjItem.style.display = '';
                        if (childrenBlock) childrenBlock.style.display = '';
                        hasVisible = true;
                    } else {
                        subjItem.style.display = 'none';
                        if (childrenBlock) childrenBlock.style.display = 'none';
                    }
                });
                // 若此分類下有任何科目顯示，分類也顯示
                if (hasVisible) {
                    catBlock.style.display = '';
                    if (categoryItems[i]) categoryItems[i].style.display = '';
                } else {
                    catBlock.style.display = 'none';
                    if (categoryItems[i]) categoryItems[i].style.display = 'none';
                }
            });
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</body>

</html>