<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 測試工具 - 印錢大師</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="columns">
            <div class="column is-3">
                <!-- 側邊欄導航 -->
                <aside class="menu">
                    <p class="menu-label">API 安全管理</p>
                    <ul class="menu-list">
                        <li><a href="{{ url_for('api_security_admin.dashboard') }}">
                            <span class="icon"><i class="fas fa-tachometer-alt"></i></span>
                            儀表板
                        </a></li>
                        <li><a href="{{ url_for('api_security_admin.security_logs') }}">
                            <span class="icon"><i class="fas fa-shield-alt"></i></span>
                            安全日誌
                        </a></li>
                        <li><a href="{{ url_for('api_security_admin.test_api') }}" class="is-active">
                            <span class="icon"><i class="fas fa-vial"></i></span>
                            API 測試
                        </a></li>
                    </ul>
                    <p class="menu-label">系統</p>
                    <ul class="menu-list">
                        <li><a href="{{ url_for('main.index') }}">
                            <span class="icon"><i class="fas fa-home"></i></span>
                            返回主頁
                        </a></li>
                    </ul>
                </aside>
            </div>
            
            <div class="column">
                <h1 class="title">API 測試工具</h1>
                
                <div class="box">
                    <h2 class="subtitle">測試 API 端點</h2>
                    
                    <div class="field">
                        <label class="label">選擇端點</label>
                        <div class="control">
                            <div class="select is-fullwidth">
                                <select id="endpointSelect" onchange="updateEndpointInfo()">
                                    <option value="">-- 選擇 API 端點 --</option>
                                    <option value="GET:/api/bank_heads">GET /api/bank_heads - 獲取銀行總行列表</option>
                                    <option value="GET:/api/bank_branches/{code}">GET /api/bank_branches/{code} - 獲取銀行分行</option>
                                    <option value="GET:/api/check_invoice_number">GET /api/check_invoice_number - 檢查發票號碼</option>
                                    <option value="GET:/api/payment_identities/search">GET /api/payment_identities/search - 搜索收支對象</option>
                                    <option value="POST:/api/payment_identities/create">POST /api/payment_identities/create - 創建收支對象</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">API 密鑰</label>
                        <div class="control">
                            <input class="input" type="text" id="apiKey" placeholder="輸入您的 API 密鑰">
                        </div>
                        <p class="help">從儀表板複製您的 API 密鑰，或留空使用會話認證</p>
                    </div>
                    
                    <div id="parameterSection" style="display: none;">
                        <div class="field">
                            <label class="label">參數</label>
                            <div class="control">
                                <textarea class="textarea" id="parameters" rows="4" placeholder='JSON 格式參數，例如: {"name": "測試公司"}'></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="field">
                        <div class="control">
                            <button class="button is-primary" onclick="testApi()">
                                <span class="icon"><i class="fas fa-play"></i></span>
                                <span>執行測試</span>
                            </button>
                            <button class="button is-light" onclick="clearResult()">
                                清除結果
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="box" id="resultSection" style="display: none;">
                    <h2 class="subtitle">測試結果</h2>
                    
                    <div class="field">
                        <label class="label">HTTP 狀態碼</label>
                        <div class="control">
                            <span class="tag" id="statusCode"></span>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">響應內容</label>
                        <div class="control">
                            <pre id="responseContent" class="has-background-light p-4" style="white-space: pre-wrap; max-height: 400px; overflow-y: auto;"></pre>
                        </div>
                    </div>
                    
                    <div class="field">
                        <label class="label">響應時間</label>
                        <div class="control">
                            <span id="responseTime"></span>
                        </div>
                    </div>
                </div>
                
                <!-- API 文檔 -->
                <div class="box">
                    <h2 class="subtitle">API 文檔</h2>
                    
                    <div class="content">
                        <h4>可用端點</h4>
                        <table class="table is-fullwidth">
                            <thead>
                                <tr>
                                    <th>方法</th>
                                    <th>端點</th>
                                    <th>描述</th>
                                    <th>認證</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="tag is-info">GET</span></td>
                                    <td><code>/api/bank_heads</code></td>
                                    <td>獲取所有銀行總行列表</td>
                                    <td>必需</td>
                                </tr>
                                <tr>
                                    <td><span class="tag is-info">GET</span></td>
                                    <td><code>/api/bank_branches/{code}</code></td>
                                    <td>獲取指定總行的分行列表</td>
                                    <td>必需</td>
                                </tr>
                                <tr>
                                    <td><span class="tag is-info">GET</span></td>
                                    <td><code>/api/check_invoice_number</code></td>
                                    <td>檢查發票號碼是否存在</td>
                                    <td>必需</td>
                                </tr>
                                <tr>
                                    <td><span class="tag is-info">GET</span></td>
                                    <td><code>/api/payment_identities/search</code></td>
                                    <td>搜索收支對象</td>
                                    <td>必需</td>
                                </tr>
                                <tr>
                                    <td><span class="tag is-success">POST</span></td>
                                    <td><code>/api/payment_identities/create</code></td>
                                    <td>創建新的收支對象</td>
                                    <td>必需</td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <h4>認證方式</h4>
                        <p>支援兩種認證方式：</p>
                        <ul>
                            <li><strong>API 密鑰：</strong>在請求標頭中添加 <code>X-API-Key: your_api_key</code></li>
                            <li><strong>會話認證：</strong>使用已登入的瀏覽器會話</li>
                        </ul>
                        
                        <h4>速率限制</h4>
                        <p>不同端點有不同的速率限制：</p>
                        <ul>
                            <li>查詢端點：每分鐘 30-50 次請求</li>
                            <li>創建端點：每 5 分鐘 10 次請求</li>
                            <li>全局限制：每小時 1000 次請求</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function updateEndpointInfo() {
            const select = document.getElementById('endpointSelect');
            const paramSection = document.getElementById('parameterSection');
            const paramTextarea = document.getElementById('parameters');
            
            const isPostMethod = select.value.startsWith('POST:');
            paramSection.style.display = isPostMethod ? 'block' : 'none';
            
            // 預填充一些示例參數
            if (select.value === 'POST:/api/payment_identities/create') {
                paramTextarea.value = JSON.stringify({
                    "name": "測試公司",
                    "type_name": "客戶"
                }, null, 2);
            } else {
                paramTextarea.value = '';
            }
        }
        
        async function testApi() {
            const endpoint = document.getElementById('endpointSelect').value;
            const apiKey = document.getElementById('apiKey').value.trim();
            const parameters = document.getElementById('parameters').value.trim();
            
            if (!endpoint) {
                alert('請選擇要測試的端點');
                return;
            }
            
            const [method, path] = endpoint.split(':');
            let url = path;
            let requestOptions = {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };
            
            // 添加 API 密鑰到標頭
            if (apiKey) {
                requestOptions.headers['X-API-Key'] = apiKey;
            }
            
            // 處理參數
            if (method === 'POST' && parameters) {
                try {
                    requestOptions.body = parameters;
                } catch (e) {
                    alert('參數格式錯誤，請使用有效的 JSON 格式');
                    return;
                }
            }
            
            // 處理路徑參數（簡化處理）
            if (path.includes('{code}')) {
                const code = prompt('請輸入銀行總行代碼（例如：004）：');
                if (!code) return;
                url = path.replace('{code}', code);
            }
            
            // 記錄開始時間
            const startTime = performance.now();
            
            try {
                const response = await fetch(url, requestOptions);
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                const responseText = await response.text();
                let responseJson;
                try {
                    responseJson = JSON.parse(responseText);
                } catch (e) {
                    responseJson = responseText;
                }
                
                // 顯示結果
                displayResult(response.status, responseJson, responseTime);
                
            } catch (error) {
                const endTime = performance.now();
                const responseTime = Math.round(endTime - startTime);
                
                displayResult(0, { error: error.message }, responseTime);
            }
        }
        
        function displayResult(statusCode, content, responseTime) {
            const resultSection = document.getElementById('resultSection');
            const statusElement = document.getElementById('statusCode');
            const contentElement = document.getElementById('responseContent');
            const timeElement = document.getElementById('responseTime');
            
            // 顯示結果區域
            resultSection.style.display = 'block';
            
            // 設置狀態碼樣式
            statusElement.textContent = statusCode;
            statusElement.className = 'tag ';
            if (statusCode >= 200 && statusCode < 300) {
                statusElement.classList.add('is-success');
            } else if (statusCode >= 400 && statusCode < 500) {
                statusElement.classList.add('is-warning');
            } else if (statusCode >= 500) {
                statusElement.classList.add('is-danger');
            } else {
                statusElement.classList.add('is-light');
            }
            
            // 格式化響應內容
            contentElement.textContent = JSON.stringify(content, null, 2);
            
            // 顯示響應時間
            timeElement.textContent = responseTime + ' ms';
        }
        
        function clearResult() {
            document.getElementById('resultSection').style.display = 'none';
            document.getElementById('endpointSelect').value = '';
            document.getElementById('apiKey').value = '';
            document.getElementById('parameters').value = '';
            document.getElementById('parameterSection').style.display = 'none';
        }
    </script>
</body>
</html>