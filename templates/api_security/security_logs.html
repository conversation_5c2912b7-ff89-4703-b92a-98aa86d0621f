<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 安全日誌 - 印錢大師</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="columns">
            <div class="column is-3">
                <!-- 側邊欄導航 -->
                <aside class="menu">
                    <p class="menu-label">API 安全管理</p>
                    <ul class="menu-list">
                        <li><a href="{{ url_for('api_security_admin.dashboard') }}">
                            <span class="icon"><i class="fas fa-tachometer-alt"></i></span>
                            儀表板
                        </a></li>
                        <li><a href="{{ url_for('api_security_admin.security_logs') }}" class="is-active">
                            <span class="icon"><i class="fas fa-shield-alt"></i></span>
                            安全日誌
                        </a></li>
                        <li><a href="{{ url_for('api_security_admin.test_api') }}">
                            <span class="icon"><i class="fas fa-vial"></i></span>
                            API 測試
                        </a></li>
                    </ul>
                    <p class="menu-label">系統</p>
                    <ul class="menu-list">
                        <li><a href="{{ url_for('main.index') }}">
                            <span class="icon"><i class="fas fa-home"></i></span>
                            返回主頁
                        </a></li>
                    </ul>
                </aside>
            </div>
            
            <div class="column">
                <h1 class="title">API 安全日誌</h1>
                
                <div class="box">
                    {% if logs %}
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>時間</th>
                                    <th>事件類型</th>
                                    <th>詳細信息</th>
                                    <th>IP 位址</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs %}
                                <tr>
                                    <td>{{ log.timestamp[:19] }}</td>
                                    <td>
                                        {% if log.event_type == 'API_KEY_USED' %}
                                        <span class="tag is-info">API 使用</span>
                                        {% elif log.event_type == 'RATE_LIMIT_EXCEEDED' %}
                                        <span class="tag is-warning">頻率超限</span>
                                        {% elif log.event_type == 'API_ERROR' %}
                                        <span class="tag is-danger">API 錯誤</span>
                                        {% else %}
                                        <span class="tag">{{ log.event_type }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if log.details.endpoint %}
                                        端點: <code>{{ log.details.endpoint }}</code>
                                        {% endif %}
                                        {% if log.details.status %}
                                        狀態: {{ log.details.status }}
                                        {% endif %}
                                    </td>
                                    <td>{{ log.ip_address }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="has-text-centered py-6">
                        <span class="icon is-large has-text-grey">
                            <i class="fas fa-shield-alt fa-3x"></i>
                        </span>
                        <p class="has-text-grey mt-3">暫無安全日誌記錄</p>
                        <p class="has-text-grey-light">API 使用記錄將顯示在此處</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</body>
</html>