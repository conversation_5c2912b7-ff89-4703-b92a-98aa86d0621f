<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 安全管理 - 印錢大師</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="columns">
            <div class="column is-3">
                <!-- 側邊欄導航 -->
                <aside class="menu">
                    <p class="menu-label">API 安全管理</p>
                    <ul class="menu-list">
                        <li><a href="{{ url_for('api_security_admin.dashboard') }}" class="is-active">
                            <span class="icon"><i class="fas fa-tachometer-alt"></i></span>
                            儀表板
                        </a></li>
                        <li><a href="{{ url_for('api_security_admin.security_logs') }}">
                            <span class="icon"><i class="fas fa-shield-alt"></i></span>
                            安全日誌
                        </a></li>
                        <li><a href="{{ url_for('api_security_admin.test_api') }}">
                            <span class="icon"><i class="fas fa-vial"></i></span>
                            API 測試
                        </a></li>
                    </ul>
                    <p class="menu-label">系統</p>
                    <ul class="menu-list">
                        <li><a href="{{ url_for('main.index') }}">
                            <span class="icon"><i class="fas fa-home"></i></span>
                            返回主頁
                        </a></li>
                    </ul>
                </aside>
            </div>
            
            <div class="column">
                <!-- 標題區域 -->
                <div class="level">
                    <div class="level-left">
                        <div class="level-item">
                            <h1 class="title">API 安全管理</h1>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="level-item">
                            <button class="button is-primary" onclick="showGenerateKeyModal()">
                                <span class="icon"><i class="fas fa-plus"></i></span>
                                <span>生成新密鑰</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 統計概覽 -->
                <div class="columns">
                    <div class="column">
                        <div class="box">
                            <div class="level">
                                <div class="level-item has-text-centered">
                                    <div>
                                        <p class="heading">總密鑰數</p>
                                        <p class="title">{{ api_keys|length }}</p>
                                    </div>
                                </div>
                                <div class="level-item has-text-centered">
                                    <div>
                                        <p class="heading">活躍密鑰</p>
                                        <p class="title">{{ api_keys|selectattr('is_active')|list|length }}</p>
                                    </div>
                                </div>
                                <div class="level-item has-text-centered">
                                    <div>
                                        <p class="heading">過去1小時請求</p>
                                        <p class="title">{{ usage_stats|sum(attribute='global_requests_last_hour') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- API 密鑰列表 -->
                <div class="box">
                    <h2 class="subtitle">API 密鑰管理</h2>
                    
                    {% if api_keys %}
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped">
                            <thead>
                                <tr>
                                    <th>密鑰 ID</th>
                                    <th>描述</th>
                                    <th>狀態</th>
                                    <th>創建時間</th>
                                    <th>過期時間</th>
                                    <th>最後使用</th>
                                    <th>使用次數</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for key in api_keys %}
                                <tr>
                                    <td>
                                        <code>{{ key.key_id }}</code>
                                    </td>
                                    <td>{{ key.description or '無描述' }}</td>
                                    <td>
                                        {% if key.is_active %}
                                        <span class="tag is-success">活躍</span>
                                        {% else %}
                                        <span class="tag is-danger">已停用</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ key.created_at[:19] }}</td>
                                    <td>{{ key.expires_at[:19] }}</td>
                                    <td>{{ key.last_used[:19] if key.last_used else '從未使用' }}</td>
                                    <td>{{ key.request_count or 0 }}</td>
                                    <td>
                                        {% if key.is_active %}
                                        <button class="button is-small is-danger" 
                                                onclick="revokeKey('{{ key.key_id }}')">
                                            撤銷
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="has-text-centered py-6">
                        <p class="has-text-grey">尚未生成任何 API 密鑰</p>
                        <button class="button is-primary mt-3" onclick="showGenerateKeyModal()">
                            生成第一個密鑰
                        </button>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 使用統計 -->
                {% if usage_stats %}
                <div class="box">
                    <h2 class="subtitle">使用統計</h2>
                    <div class="table-container">
                        <table class="table is-fullwidth">
                            <thead>
                                <tr>
                                    <th>密鑰 ID</th>
                                    <th>描述</th>
                                    <th>過去1小時請求</th>
                                    <th>使用的端點數</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stats in usage_stats %}
                                <tr>
                                    <td><code>{{ stats.key_id }}</code></td>
                                    <td>{{ stats.description or '無描述' }}</td>
                                    <td>{{ stats.global_requests_last_hour }}</td>
                                    <td>{{ stats.total_endpoints_used }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 生成密鑰模態框 -->
    <div class="modal" id="generateKeyModal">
        <div class="modal-background"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">生成新的 API 密鑰</p>
                <button class="delete" onclick="hideGenerateKeyModal()"></button>
            </header>
            <section class="modal-card-body">
                <form id="generateKeyForm">
                    <div class="field">
                        <label class="label">密鑰描述 *</label>
                        <div class="control">
                            <input class="input" type="text" name="description" 
                                   placeholder="例如：移動應用 API、第三方系統集成..." required>
                        </div>
                        <p class="help">請提供有意義的描述以便識別此密鑰的用途</p>
                    </div>
                    
                    <div class="field">
                        <label class="label">有效期（天）</label>
                        <div class="control">
                            <div class="select">
                                <select name="expires_days">
                                    <option value="30">30 天</option>
                                    <option value="60">60 天</option>
                                    <option value="90" selected>90 天</option>
                                    <option value="180">180 天</option>
                                    <option value="365">365 天</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
                
                <div class="notification is-warning is-light">
                    <strong>重要提醒：</strong>
                    <ul>
                        <li>生成後的完整密鑰將只顯示一次，請立即複製保存</li>
                        <li>密鑰具有與您帳戶相同的權限，請妥善保管</li>
                        <li>如懷疑密鑰洩露，請立即撤銷</li>
                    </ul>
                </div>
            </section>
            <footer class="modal-card-foot">
                <button class="button is-primary" onclick="generateApiKey()">生成密鑰</button>
                <button class="button" onclick="hideGenerateKeyModal()">取消</button>
            </footer>
        </div>
    </div>
    
    <!-- 顯示新密鑰模態框 -->
    <div class="modal" id="showKeyModal">
        <div class="modal-background"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">新 API 密鑰已生成</p>
            </header>
            <section class="modal-card-body">
                <div class="notification is-success">
                    <strong>密鑰生成成功！</strong>
                </div>
                
                <div class="field">
                    <label class="label">API 密鑰（請立即複製保存）</label>
                    <div class="field has-addons">
                        <div class="control is-expanded">
                            <input class="input" type="text" id="newApiKey" readonly>
                        </div>
                        <div class="control">
                            <button class="button is-primary" onclick="copyApiKey()">
                                <span class="icon"><i class="fas fa-copy"></i></span>
                                <span>複製</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="notification is-danger is-light">
                    <strong>安全警告：</strong>此密鑰只會顯示這一次，關閉後將無法再次查看。請立即複製並安全保存。
                </div>
            </section>
            <footer class="modal-card-foot">
                <button class="button is-primary" onclick="hideShowKeyModal()">我已保存密鑰</button>
            </footer>
        </div>
    </div>
    
    <script>
        // 模態框控制
        function showGenerateKeyModal() {
            document.getElementById('generateKeyModal').classList.add('is-active');
        }
        
        function hideGenerateKeyModal() {
            document.getElementById('generateKeyModal').classList.remove('is-active');
            document.getElementById('generateKeyForm').reset();
        }
        
        function hideShowKeyModal() {
            document.getElementById('showKeyModal').classList.remove('is-active');
            location.reload(); // 刷新頁面顯示新密鑰
        }
        
        // 生成 API 密鑰
        async function generateApiKey() {
            const form = document.getElementById('generateKeyForm');
            const formData = new FormData(form);
            
            try {
                const response = await fetch('{{ url_for("api_security_admin.generate_api_key") }}', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('newApiKey').value = result.api_key;
                    hideGenerateKeyModal();
                    document.getElementById('showKeyModal').classList.add('is-active');
                } else {
                    alert('生成密鑰失敗: ' + result.error);
                }
            } catch (error) {
                alert('生成密鑰時發生錯誤');
                console.error(error);
            }
        }
        
        // 複製 API 密鑰
        function copyApiKey() {
            const keyInput = document.getElementById('newApiKey');
            keyInput.select();
            document.execCommand('copy');
            
            // 顯示複製成功提示
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="icon"><i class="fas fa-check"></i></span><span>已複製</span>';
            button.classList.remove('is-primary');
            button.classList.add('is-success');
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('is-success');
                button.classList.add('is-primary');
            }, 2000);
        }
        
        // 撤銷密鑰
        async function revokeKey(keyId) {
            if (!confirm('確定要撤銷這個 API 密鑰嗎？此操作無法復原。')) {
                return;
            }
            
            try {
                const formData = new FormData();
                formData.append('key_id', keyId);
                
                const response = await fetch('{{ url_for("api_security_admin.revoke_api_key") }}', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('密鑰已成功撤銷');
                    location.reload();
                } else {
                    alert('撤銷密鑰失敗: ' + result.error);
                }
            } catch (error) {
                alert('撤銷密鑰時發生錯誤');
                console.error(error);
            }
        }
    </script>
</body>
</html>