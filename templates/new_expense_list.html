<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新支出列表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .sidebar {
            width: 200px;
            font-size: 20px;
        }

        .main-content {
            margin-left: 220px;
            padding: 20px;
        }

        .transaction-table {
            width: 100%;
            border-collapse: collapse;
        }

        .transaction-table th,
        .transaction-table td {
            padding: 12px;
            border: 1px solid #ddd;
            text-align: left;
        }

        .transaction-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .amount-cell {
            text-align: right;
            font-family: monospace;
        }

        .status-paid {
            color: #28a745;
        }

        .status-unpaid {
            color: #dc3545;
        }

        .journal-entries {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .entry-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }

        .entry-debit {
            color: #007bff;
        }

        .entry-credit {
            color: #28a745;
        }
    </style>
</head>

<body>
    <h1 class="title has-text-centered">印錢大師</h1>
    <!-- 側邊欄 -->
    {% include 'sidebar.html' %}

    <!-- 主要內容 -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="columns">
                <div class="column">
                    <div class="card">
                        <div class="card-header">
                            <h1 class="card-title">
                                <i class="fas fa-list"></i> 新支出列表（含稅處理）
                            </h1>
                        </div>
                        <div class="card-content">
                            <!-- 操作按鈕 -->
                            <div class="field is-grouped mb-4">
                                <div class="control">
                                    <a href="{{ url_for('new_income_expense.new_expense_record') }}" class="button is-primary">
                                        <i class="fas fa-plus"></i> 新增支出記錄
                                    </a>
                                </div>
                                <div class="control">
                                    <a href="{{ url_for('new_reports.trial_balance') }}" class="button is-info">
                                        <i class="fas fa-calculator"></i> 查看試算表
                                    </a>
                                </div>
                                <div class="control">
                                    <a href="{{ url_for('new_reports.new_balance_sheet') }}" class="button is-success">
                                        <i class="fas fa-balance-scale"></i> 資產負債表
                                    </a>
                                </div>
                            </div>

                            <!-- 支出交易列表 -->
                            {% if transactions %}
                            <table class="transaction-table">
                                <thead>
                                    <tr>
                                        <th>交易日期</th>
                                        <th>描述</th>
                                        <th>總金額</th>
                                        <th>稅額</th>
                                        <th>付款狀態</th>
                                        <th>會計分錄</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for transaction in transactions %}
                                    <tr>
                                        <td>{{ transaction.transaction_date }}</td>
                                        <td>
                                            <strong>{{ transaction.description }}</strong>
                                            {% if transaction.note %}
                                            <br><small class="text-muted">{{ transaction.note }}</small>
                                            {% endif %}
                                        </td>
                                        <td class="amount-cell">
                                            <strong>{{ "{:,}".format(transaction.total_amount) }}</strong>
                                        </td>
                                        <td class="amount-cell">
                                            {% if transaction.tax_amount > 0 %}
                                            {{ "{:,}".format(transaction.tax_amount) }}
                                            {% else %}
                                            -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if transaction.is_paid %}
                                            <span class="tag is-success">
                                                <i class="fas fa-check"></i> 已付款
                                            </span>
                                            {% else %}
                                            <span class="tag is-warning">
                                                <i class="fas fa-clock"></i> 未付款
                                            </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="journal-entries">
                                                {% for entry in transaction.journal_entries %}
                                                <div class="entry-item">
                                                    <span>
                                                        {% if entry.debit_amount > 0 %}
                                                        <strong class="entry-debit">借：</strong>
                                                        {% else %}
                                                        <strong class="entry-credit">貸：</strong>
                                                        {% endif %}
                                                        {{ entry.subject_code }}
                                                        {% if entry.account_subject %}
                                                        {{ entry.account_subject.name }}
                                                        {% endif %}
                                                    </span>
                                                    <span class="amount-cell">
                                                        {% if entry.debit_amount > 0 %}
                                                        <span class="entry-debit">{{ "{:,}".format(entry.debit_amount) }}</span>
                                                        {% else %}
                                                        <span class="entry-credit">{{ "{:,}".format(entry.credit_amount) }}</span>
                                                        {% endif %}
                                                    </span>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('new_income_expense.transaction_detail', transaction_id=transaction.id) }}" 
                                               class="button is-small is-info">
                                                <i class="fas fa-eye"></i> 詳細
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                            {% else %}
                            <div class="notification is-info">
                                <p><i class="fas fa-info-circle"></i> 目前沒有支出記錄</p>
                                <p>
                                    <a href="{{ url_for('new_income_expense.new_expense_record') }}" class="button is-primary">
                                        <i class="fas fa-plus"></i> 新增第一筆支出記錄
                                    </a>
                                </p>
                            </div>
                            {% endif %}

                            <!-- 說明 -->
                            <div class="notification is-light mt-5">
                                <p><strong>新支出系統特色：</strong></p>
                                <ul>
                                    <li><strong>自動稅額處理：</strong>系統會自動產生進項稅額分錄</li>
                                    <li><strong>複式記帳：</strong>每筆交易都會產生平衡的借貸分錄</li>
                                    <li><strong>即時驗證：</strong>提交前自動檢查借貸平衡</li>
                                    <li><strong>完整追蹤：</strong>可查看每筆交易的詳細分錄</li>
                                </ul>
                                
                                <p class="mt-3"><strong>分錄邏輯：</strong></p>
                                <div class="content">
                                    <pre>
支出交易（含稅）：
借：費用科目（未稅金額）
借：進項稅額 1290（稅額）
    貸：銀行存款（含稅總額）
                                    </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
