<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>損益表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        .main-content {
            margin-left: 250px;
            padding: 20px;
            transition: margin-left 0.3s;
        }

        .filter-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .filter-section .label {
            color: white;
            font-weight: 600;
        }

        .filter-section .select select,
        .filter-section .input {
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .filter-section .select select:focus,
        .filter-section .input:focus {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .income-statement-table {
            background-color: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e8ed;
        }

        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            text-align: center;
            font-size: 1.1em;
            padding: 15px 10px;
        }

        .table-header th {
            border: none;
            color: white;
            font-weight: 600;
        }

        .category-header {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .category-header:hover {
            background: linear-gradient(135deg, #0984e3 0%, #74b9ff 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
        }

        .subcategory-header {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
            font-weight: 600;
            padding-left: 20px;
        }

        .item-row {
            padding-left: 40px;
            transition: background-color 0.2s ease;
        }

        .item-row:hover {
            background-color: #f8f9fa;
        }

        .total-row {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            font-weight: bold;
            border-top: 3px solid #00b894;
            font-size: 1.1em;
        }

        .subtotal-row {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
            font-weight: 600;
            border-top: 2px solid #fdcb6e;
        }

        .amount-cell {
            text-align: right;
            font-family: 'Courier New', monospace;
            font-weight: 600;
            padding: 12px 15px;
        }

        .negative-amount {
            color: #e74c3c;
            font-weight: bold;
        }

        .positive-amount {
            color: #27ae60;
            font-weight: bold;
        }

        .table tbody tr {
            border-bottom: 1px solid #e1e8ed;
        }

        .table tbody tr:last-child {
            border-bottom: none;
        }

        .collapsible-header {
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .collapsible-header:hover {
            background-color: #e3f2fd;
        }

        .collapse-icon {
            margin-right: 8px;
            transition: transform 0.2s;
        }

        .collapsed .collapse-icon {
            transform: rotate(-90deg);
        }

        .detail-row {
            display: table-row;
        }

        .collapsed .detail-row {
            display: none;
        }
    </style>
</head>

<body>
    <!-- 引入側邊欄 -->
    {% include 'sidebar.html' %}

    <!-- 主要內容 -->
    <div class="main-content">
        <!-- 頁面標題 -->
        <div class="hero is-primary is-small"
            style="border-radius: 12px; margin-bottom: 2rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
            <div class="hero-body">
                <div class="level">
                    <div class="level-left">
                        <div>
                            <h1 class="title is-2 has-text-white" style="margin-bottom: 0.5rem;">
                                <i class="fas fa-chart-line" style="margin-right: 15px;"></i>損益表
                            </h1>
                            <p class="subtitle is-5 has-text-white-ter">
                                <i class="fas fa-calendar-alt" style="margin-right: 8px;"></i>
                                {{ start_date }} 至 {{ end_date }}
                            </p>
                        </div>
                    </div>
                    <div class="level-right">
                        <div class="field is-grouped">
                            <div class="control">
                                <button class="button is-light is-medium" onclick="window.print()"
                                    style="border-radius: 8px;">
                                    <i class="fas fa-print"></i>
                                    <span>列印報表</span>
                                </button>
                            </div>
                            <div class="control">
                                <button class="button is-light is-medium" onclick="exportToExcel()"
                                    style="border-radius: 8px;">
                                    <i class="fas fa-file-excel"></i>
                                    <span>匯出 Excel</span>
                                </button>
                            </div>
                            <div class="control">
                                <button class="button is-light is-medium" onclick="toggleAllSections()"
                                    style="border-radius: 8px;">
                                    <i class="fas fa-expand-arrows-alt"></i>
                                    <span>全部展開/收合</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 篩選條件 -->
        <div class="filter-section">
            <form method="GET" action="/reports/income_statement">
                <div class="columns">
                    <div class="column is-3">
                        <div class="field">
                            <label class="label">期間</label>
                            <div class="control">
                                <div class="select is-fullwidth">
                                    <select name="period_type" onchange="updateDateRange(this.value)">
                                        <option value="custom">自訂期間</option>
                                        <option value="this_month">本月</option>
                                        <option value="last_month">上月</option>
                                        <option value="this_quarter">本季</option>
                                        <option value="this_year">本年度</option>
                                        <option value="last_year">去年度</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="field">
                            <label class="label">開始日期</label>
                            <div class="control">
                                <input class="input" type="date" name="start_date" value="{{ start_date }}" required>
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="field">
                            <label class="label">結束日期</label>
                            <div class="control">
                                <input class="input" type="date" name="end_date" value="{{ end_date }}" required>
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="field">
                            <label class="label">&nbsp;</label>
                            <div class="control">
                                <button type="submit" class="button is-primary is-fullwidth">
                                    <i class="fas fa-search"></i>
                                    <span>查詢</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 關鍵指標卡片 -->
        <div class="columns" style="margin-bottom: 2rem;">
            <div class="column is-3">
                <div class="card" style="border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                    <div class="card-content has-text-centered">
                        <div class="content">
                            <p class="title is-4 has-text-primary">
                                <i class="fas fa-chart-line"></i>
                            </p>
                            <p class="title is-5">營業收入</p>
                            <p
                                class="subtitle is-6 {% if income_statement.revenue.total >= 0 %}has-text-success{% else %}has-text-danger{% endif %}">
                                NT$ {{ "{:,}".format(income_statement.revenue.total) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column is-3">
                <div class="card" style="border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                    <div class="card-content has-text-centered">
                        <div class="content">
                            <p class="title is-4 has-text-warning">
                                <i class="fas fa-chart-pie"></i>
                            </p>
                            <p class="title is-5">營業毛利</p>
                            <p
                                class="subtitle is-6 {% if income_statement.gross_profit >= 0 %}has-text-success{% else %}has-text-danger{% endif %}">
                                NT$ {{ "{:,}".format(income_statement.gross_profit) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column is-3">
                <div class="card" style="border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                    <div class="card-content has-text-centered">
                        <div class="content">
                            <p class="title is-4 has-text-info">
                                <i class="fas fa-balance-scale"></i>
                            </p>
                            <p class="title is-5">營業利益</p>
                            <p
                                class="subtitle is-6 {% if income_statement.operating_income >= 0 %}has-text-success{% else %}has-text-danger{% endif %}">
                                NT$ {{ "{:,}".format(income_statement.operating_income) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column is-3">
                <div class="card" style="border-radius: 12px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);">
                    <div class="card-content has-text-centered">
                        <div class="content">
                            <p class="title is-4 has-text-success">
                                <i class="fas fa-trophy"></i>
                            </p>
                            <p class="title is-5">稅後淨利</p>
                            <p
                                class="subtitle is-6 {% if income_statement.net_income >= 0 %}has-text-success{% else %}has-text-danger{% endif %}">
                                NT$ {{ "{:,}".format(income_statement.net_income) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 損益表 -->
        <div class="income-statement-table">
            <table class="table is-fullwidth">
                <thead>
                    <tr class="table-header">
                        <th style="width: 40%; padding: 15px;">
                            <i class="fas fa-list-ul" style="margin-right: 8px;"></i>項目名稱
                        </th>
                        <th style="width: 15%; padding: 15px;">
                            <i class="fas fa-code" style="margin-right: 8px;"></i>會計科目
                        </th>
                        <th style="width: 30%; padding: 15px;">
                            <i class="fas fa-dollar-sign" style="margin-right: 8px;"></i>金額 (新台幣)
                            <br><small style="font-weight: normal; opacity: 0.8;">{{ start_date }} 至 {{ end_date
                                }}</small>
                        </th>
                        <th style="width: 15%; padding: 15px;">
                            <i class="fas fa-percentage" style="margin-right: 8px;"></i>百分比
                            <br><small style="font-weight: normal; opacity: 0.8;">佔營收比例</small>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 營業收入 -->
                    <tr class="collapsible-header category-header" onclick="toggleCollapse('revenue')">
                        <td>
                            <span class="collapse-icon">▼</span>營業收入
                        </td>
                        <td style="text-align: center;">-</td>
                        <td class="amount-cell positive-amount">{{ "{:,}".format(income_statement.revenue.total) }}</td>
                        <td class="amount-cell" style="text-align: center;"></td>
                    </tr>

                    <!-- 營業收入細項 -->
                    <tr class="revenue-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">銷貨收入</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="revenue-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">營業收入</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="revenue-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">服務收入</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="revenue-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">工程收入</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="revenue-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">銷貨退回</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="revenue-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">銷貨折讓</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="revenue-detail subtotal-row">
                        <td class="item-row"></td>
                        <td style="text-align: center; font-weight: bold;">營業收入總計</td>
                        <td class="amount-cell" style="font-weight: bold;">{{
                            "{:,}".format(income_statement.revenue.total) }}</td>
                        <td class="amount-cell" style="text-align: center; font-weight: bold;">100.0%</td>
                    </tr>

                    <!-- 營業成本 -->
                    <tr class="collapsible-header category-header" onclick="toggleCollapse('cost')">
                        <td>
                            <span class="collapse-icon">▼</span>營業成本
                        </td>
                        <td style="text-align: center;">-</td>
                        <td class="amount-cell negative-amount">({{ "{:,}".format(income_statement.cost_of_sales.total)
                            }})</td>
                        <td class="amount-cell" style="text-align: center; font-weight: bold;">{{
                            income_statement.cost_of_sales.percentage }}%</td>
                    </tr>

                    <!-- 營業成本細項 -->
                    <tr class="cost-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">銷貨成本</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="cost-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">進貨</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="cost-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">進貨-進貨轉商品存貨</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="cost-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">進貨退回</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="cost-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">進貨折讓</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="cost-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">其他成本</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="cost-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">勞務成本</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="cost-detail subtotal-row">
                        <td class="item-row"></td>
                        <td style="text-align: center; font-weight: bold;">營業成本總計</td>
                        <td class="amount-cell" style="font-weight: bold;">{{
                            "{:,}".format(income_statement.cost_of_sales.total) }}</td>
                        <td class="amount-cell" style="text-align: center; font-weight: bold;">{{
                            income_statement.cost_of_sales.percentage }}%</td>
                    </tr>

                    <!-- 營業毛利 -->
                    <tr class="subtotal-row">
                        <td style="font-weight: bold;">營業毛利</td>
                        <td style="text-align: center;">-</td>
                        <td
                            class="amount-cell {% if income_statement.gross_profit >= 0 %}positive-amount{% else %}negative-amount{% endif %}">
                            {{ "{:,}".format(income_statement.gross_profit) }}
                        </td>
                        <td class="amount-cell" style="text-align: center; font-weight: bold;">{{
                            income_statement.gross_profit_percentage }}%</td>
                    </tr>

                    <!-- 營業費用 -->
                    <tr class="collapsible-header category-header" onclick="toggleCollapse('expenses')">
                        <td>
                            <span class="collapse-icon">▼</span>營業費用
                        </td>
                        <td style="text-align: center;">-</td>
                        <td class="amount-cell negative-amount">({{
                            "{:,}".format(income_statement.operating_expenses.total) }})</td>
                        <td class="amount-cell" style="text-align: center;"></td>
                    </tr>

                    <!-- 營業費用細項 -->
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">薪資支出</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">薪資支出-公司提繳</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">薪資支出-自願提繳</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">薪資支出-員工酬勞</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">租金支出</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">文具用品</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">旅費</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">運費</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">修繕費</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">郵電費</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">廣告費</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">水電瓦斯費</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">保險費</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">保險費-健保費</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">保險費-勞保費</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">交際費</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">捐贈</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">稅捐</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">各項折舊</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="expenses-detail subtotal-row">
                        <td class="item-row"></td>
                        <td style="text-align: center; font-weight: bold;">營業費用總計</td>
                        <td class="amount-cell" style="font-weight: bold;">{{
                            "{:,}".format(income_statement.operating_expenses.total) }}</td>
                        <td class="amount-cell" style="text-align: center; font-weight: bold;">{{
                            income_statement.operating_expenses.percentage }}%</td>
                    </tr>

                    <!-- 營業利益 -->
                    <tr class="subtotal-row">
                        <td style="font-weight: bold;">營業利益</td>
                        <td style="text-align: center;">-</td>
                        <td
                            class="amount-cell {% if income_statement.operating_income >= 0 %}positive-amount{% else %}negative-amount{% endif %}">
                            {{ "{:,}".format(income_statement.operating_income) }}
                        </td>
                        <td class="amount-cell" style="text-align: center; font-weight: bold;">{{
                            income_statement.operating_income_percentage }}%</td>
                    </tr>

                    <!-- 營業外收入及支出 -->
                    <tr class="collapsible-header category-header" onclick="toggleCollapse('non-operating')">
                        <td>
                            <span class="collapse-icon">▼</span>營業外收入及支出
                        </td>
                        <td style="text-align: center;">-</td>
                        <td
                            class="amount-cell {% if income_statement.non_operating.total >= 0 %}positive-amount{% else %}negative-amount{% endif %}">
                            {{ "{:,}".format(income_statement.non_operating.total) }}
                        </td>
                        <td class="amount-cell" style="text-align: center;"></td>
                    </tr>

                    <!-- 營業外收入及支出細項 -->
                    <tr class="non-operating-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">利息收入</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="non-operating-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">利息費用</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="non-operating-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">兌換利益</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="non-operating-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">兌換損失</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="non-operating-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">處分資產利益</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="non-operating-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">處分資產損失</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="non-operating-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">其他收入</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="non-operating-detail detail-row">
                        <td class="item-row"></td>
                        <td style="text-align: center;">其他支出</td>
                        <td class="amount-cell">0</td>
                        <td class="amount-cell" style="text-align: center;">0.0%</td>
                    </tr>
                    <tr class="non-operating-detail subtotal-row">
                        <td class="item-row"></td>
                        <td style="text-align: center; font-weight: bold;">營業外收支淨額</td>
                        <td class="amount-cell" style="font-weight: bold;">{{
                            "{:,}".format(income_statement.non_operating.total) }}</td>
                        <td class="amount-cell" style="text-align: center; font-weight: bold;">{{
                            income_statement.non_operating.percentage }}%</td>
                    </tr>

                    <!-- 稅前淨利 -->
                    <tr class="subtotal-row">
                        <td style="font-weight: bold;">稅前淨利</td>
                        <td style="text-align: center;">-</td>
                        <td
                            class="amount-cell {% if income_statement.income_before_tax >= 0 %}positive-amount{% else %}negative-amount{% endif %}">
                            {{ "{:,}".format(income_statement.income_before_tax) }}
                        </td>
                        <td class="amount-cell" style="text-align: center; font-weight: bold;">{{
                            income_statement.income_before_tax_percentage }}%</td>
                    </tr>

                    <!-- 所得稅費用 -->
                    <tr>
                        <td>所得稅費用</td>
                        <td style="text-align: center;">-</td>
                        <td class="amount-cell negative-amount">({{ "{:,}".format(income_statement.income_tax) }})</td>
                        <td class="amount-cell" style="text-align: center;">{{
                            income_statement.income_tax_percentage }}%</td>
                    </tr>

                    <!-- 本期淨利 -->
                    <tr class="total-row">
                        <td style="font-weight: bold;">本期淨利</td>
                        <td style="text-align: center;">-</td>
                        <td
                            class="amount-cell {% if income_statement.net_income >= 0 %}positive-amount{% else %}negative-amount{% endif %}">
                            {{ "{:,}".format(income_statement.net_income) }}
                        </td>
                        <td class="amount-cell" style="text-align: center; font-weight: bold;">{{
                            income_statement.net_income_percentage }}%</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 更新日期範圍
        function updateDateRange(periodType) {
            const today = new Date();
            let startDate = new Date();
            let endDate = new Date();

            switch (periodType) {
                case 'this_month':
                    startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    endDate = today;
                    break;
                case 'last_month':
                    startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    endDate = new Date(today.getFullYear(), today.getMonth(), 0);
                    break;
                case 'this_quarter':
                    const quarter = Math.floor(today.getMonth() / 3);
                    startDate = new Date(today.getFullYear(), quarter * 3, 1);
                    endDate = today;
                    break;
                case 'this_year':
                    startDate = new Date(today.getFullYear(), 0, 1);
                    endDate = today;
                    break;
                case 'last_year':
                    startDate = new Date(today.getFullYear() - 1, 0, 1);
                    endDate = new Date(today.getFullYear() - 1, 11, 31);
                    break;
                default:
                    // 自訂期間，不做任何變更
                    return;
            }

            // 格式化日期為 YYYY-MM-DD
            document.querySelector('input[name="start_date"]').value = formatDate(startDate);
            document.querySelector('input[name="end_date"]').value = formatDate(endDate);
        }

        // 格式化日期
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // 切換展開/收合
        function toggleCollapse(sectionId) {
            const header = event.currentTarget;
            const icon = header.querySelector('.collapse-icon');
            const detailRows = document.querySelectorAll('.' + sectionId + '-detail');

            if (header.classList.contains('collapsed')) {
                // 展開
                header.classList.remove('collapsed');
                icon.textContent = '▼';
                detailRows.forEach(row => {
                    row.style.display = '';
                });
            } else {
                // 收合
                header.classList.add('collapsed');
                icon.textContent = '▶';
                detailRows.forEach(row => {
                    row.style.display = 'none';
                });
            }
        }

        // 全部展開/收合
        function toggleAllSections() {
            const headers = document.querySelectorAll('.collapsible-header');
            const isAnyCollapsed = Array.from(headers).some(header => header.classList.contains('collapsed'));

            headers.forEach(header => {
                const sectionId = header.querySelector('td').textContent.trim().split(' ')[1].toLowerCase();
                const icon = header.querySelector('.collapse-icon');
                const detailRows = document.querySelectorAll('.' + sectionId + '-detail');

                if (isAnyCollapsed) {
                    // 全部展開
                    header.classList.remove('collapsed');
                    icon.textContent = '▼';
                    detailRows.forEach(row => {
                        row.style.display = '';
                    });
                } else {
                    // 全部收合
                    header.classList.add('collapsed');
                    icon.textContent = '▶';
                    detailRows.forEach(row => {
                        row.style.display = 'none';
                    });
                }
            });
        }

        // 匯出 Excel
        function exportToExcel() {
            const table = document.querySelector('.income-statement-table table');
            const wb = XLSX.utils.table_to_book(table, { sheet: "損益表" });
            XLSX.writeFile(wb, `損益表_${document.querySelector('input[name="start_date"]').value}_${document.querySelector('input[name="end_date"]').value}.xlsx`);
        }
    </script>
</body>

</html>