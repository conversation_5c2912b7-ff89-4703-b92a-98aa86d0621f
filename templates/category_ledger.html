<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分類明細賬</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .sidebar {
            width: 200px;
            font-size: 20px;
        }

        .main-content {
            margin-top: 20px;
        }

        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .ledger-table {
            margin-top: 20px;
        }

        .amount-debit {
            color: #d73527;
        }

        .amount-credit {
            color: #28a745;
        }

        .balance-positive {
            color: #28a745;
            font-weight: bold;
        }

        .balance-negative {
            color: #d73527;
            font-weight: bold;
        }

        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="main-content">
                    <!-- 導航 -->
                    <div class="box mb-5">
                        <h2 class="subtitle">
                            <a href="javascript:history.back()"
                                style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                            <i class="fas fa-list-alt"></i> 分類明細賬
                        </h2>
                    </div>

                    <!-- 篩選條件 -->
                    <div class="filter-section">
                        <div class="columns">
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">會計科目</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="accountSelect">
                                                <option value="">請選擇科目</option>
                                                <option value="1111">現金</option>
                                                <option value="1112">銀行存款</option>
                                                <option value="1141">應收帳款</option>
                                                <option value="4111">銷貨收入</option>
                                                <option value="5111">銷貨成本</option>
                                                <option value="6111">薪資支出</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">起始日期</label>
                                    <div class="control">
                                        <input class="input" type="date" id="startDate" value="2024-01-01">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">結束日期</label>
                                    <div class="control">
                                        <input class="input" type="date" id="endDate" value="2024-12-31">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">&nbsp;</label>
                                    <div class="control">
                                        <button class="button is-primary is-fullwidth" onclick="searchLedger()">
                                            <span class="icon"><i class="fas fa-search"></i></span>
                                            <span>查詢</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 摘要資訊 -->
                    <div id="summarySection" class="is-hidden">
                        <div class="summary-card">
                            <div class="columns">
                                <div class="column">
                                    <h3 class="title is-5 has-text-white">科目：<span id="accountName"></span></h3>
                                    <p class="subtitle is-6 has-text-white">期間：<span id="periodRange"></span></p>
                                </div>
                                <div class="column has-text-right">
                                    <p class="title is-4 has-text-white">期末餘額</p>
                                    <p class="title is-3 has-text-white" id="endingBalance">NT$ 0</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 明細表格 -->
                    <div id="ledgerSection" class="is-hidden">
                        <div class="box">
                            <div class="table-container ledger-table">
                                <table class="table is-fullwidth is-striped is-hoverable">
                                    <thead>
                                        <tr class="has-background-primary has-text-white">
                                            <th>日期</th>
                                            <th>憑證號碼</th>
                                            <th>摘要</th>
                                            <th>借方</th>
                                            <th>貸方</th>
                                            <th>借方金額</th>
                                            <th>貸方金額</th>
                                            <th>餘額</th>
                                        </tr>
                                    </thead>
                                    <tbody id="ledgerTableBody">
                                        <!-- 動態載入內容 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 空狀態 -->
                    <div id="emptyState" class="empty-state">
                        <i class="fas fa-file-invoice"></i>
                        <h3 class="title is-4">尚未選擇查詢條件</h3>
                        <p class="subtitle is-6">請選擇會計科目和日期範圍後點擊查詢</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 查詢分類明細賬
        function searchLedger() {
            const accountCode = document.getElementById('accountSelect').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!accountCode) {
                alert('請選擇會計科目');
                return;
            }

            if (!startDate || !endDate) {
                alert('請選擇日期範圍');
                return;
            }

            // 隱藏空狀態，顯示載入中
            document.getElementById('emptyState').classList.add('is-hidden');

            // 模擬API調用
            setTimeout(() => {
                loadLedgerData(accountCode, startDate, endDate);
            }, 500);
        }

        // 載入明細賬資料
        function loadLedgerData(accountCode, startDate, endDate) {
            // 模擬資料
            const mockData = [
                {
                    date: '2024-01-01',
                    voucherNo: 'V001',
                    description: '期初餘額',
                    debitAccount: '',
                    creditAccount: '',
                    debitAmount: 0,
                    creditAmount: 0,
                    balance: 100000
                },
                {
                    date: '2024-01-15',
                    voucherNo: 'V002',
                    description: '銷售商品',
                    debitAccount: '1141應收帳款',
                    creditAccount: '4111銷貨收入',
                    debitAmount: 50000,
                    creditAmount: 0,
                    balance: 150000
                },
                {
                    date: '2024-01-20',
                    voucherNo: 'V003',
                    description: '收到貨款',
                    debitAccount: '1112銀行存款',
                    creditAccount: '1141應收帳款',
                    debitAmount: 0,
                    creditAmount: 30000,
                    balance: 120000
                }
            ];

            // 更新摘要資訊
            const accountName = getAccountName(accountCode);
            document.getElementById('accountName').textContent = accountName;
            document.getElementById('periodRange').textContent = `${startDate} ~ ${endDate}`;
            document.getElementById('endingBalance').textContent = `NT$ ${mockData[mockData.length - 1].balance.toLocaleString()}`;

            // 更新表格
            const tbody = document.getElementById('ledgerTableBody');
            tbody.innerHTML = '';

            mockData.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row.date}</td>
                    <td>${row.voucherNo}</td>
                    <td>${row.description}</td>
                    <td>${row.debitAccount}</td>
                    <td>${row.creditAccount}</td>
                    <td class="amount-debit">${row.debitAmount > 0 ? row.debitAmount.toLocaleString() : ''}</td>
                    <td class="amount-credit">${row.creditAmount > 0 ? row.creditAmount.toLocaleString() : ''}</td>
                    <td class="${row.balance >= 0 ? 'balance-positive' : 'balance-negative'}">${row.balance.toLocaleString()}</td>
                `;
                tbody.appendChild(tr);
            });

            // 顯示結果
            document.getElementById('summarySection').classList.remove('is-hidden');
            document.getElementById('ledgerSection').classList.remove('is-hidden');
        }

        // 取得科目名稱
        function getAccountName(code) {
            const accounts = {
                '1111': '現金',
                '1112': '銀行存款',
                '1141': '應收帳款',
                '4111': '銷貨收入',
                '5111': '銷貨成本',
                '6111': '薪資支出'
            };
            return accounts[code] || '未知科目';
        }

        // 頁面載入時設定預設日期
        document.addEventListener('DOMContentLoaded', function () {
            const today = new Date();
            const currentYear = today.getFullYear();
            document.getElementById('startDate').value = `${currentYear}-01-01`;
            document.getElementById('endDate').value = `${currentYear}-12-31`;
        });
    </script>
</body>

</html>