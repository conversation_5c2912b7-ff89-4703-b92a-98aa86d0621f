{% extends "base.html" %}

{% block title %}配置管理 - 印錢大師{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="columns">
        <div class="column">
            <!-- 標題 -->
            <nav class="breadcrumb" aria-label="breadcrumbs">
                <ul>
                    <li><a href="{{ url_for('admin.admin_panel') }}">系統管理</a></li>
                    <li class="is-active"><a href="#" aria-current="page">配置管理</a></li>
                </ul>
            </nav>

            <h1 class="title is-3">
                <span class="icon-text">
                    <span class="icon">
                        <i class="fas fa-cogs"></i>
                    </span>
                    <span>配置管理</span>
                </span>
            </h1>
            <p class="subtitle">管理系統配置和設定</p>

            <!-- 快速狀態概覽 -->
            <div class="notification is-light mb-5">
                <div class="columns is-mobile">
                    <div class="column">
                        <div class="has-text-centered">
                            <p class="heading">資料庫</p>
                            <p class="title is-5">
                                <span class="tag is-info">{{ config_summary.database.type }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="column">
                        <div class="has-text-centered">
                            <p class="heading">快取</p>
                            <p class="title is-5">
                                <span class="tag is-primary">{{ config_summary.cache.type }}</span>
                            </p>
                        </div>
                    </div>
                    <div class="column">
                        <div class="has-text-centered">
                            <p class="heading">安全</p>
                            <p class="title is-5">
                                {% if config_summary.security.secret_key_set %}
                                    <span class="tag is-success">已設定</span>
                                {% else %}
                                    <span class="tag is-danger">未設定</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="column">
                        <div class="has-text-centered">
                            <p class="heading">監控</p>
                            <p class="title is-5">
                                {% if config_summary.performance.monitoring_enabled %}
                                    <span class="tag is-success">啟用</span>
                                {% else %}
                                    <span class="tag is-warning">停用</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置驗證結果 -->
            <div class="card mb-5">
                <div class="card-header">
                    <p class="card-header-title">
                        <span class="icon">
                            <i class="fas fa-check-circle"></i>
                        </span>
                        配置驗證狀態
                    </p>
                </div>
                <div class="card-content">
                    <div class="content">
                        <div class="columns is-multiline">
                            {% for component, is_valid in validation_results.items() %}
                            <div class="column is-6">
                                <div class="box">
                                    <article class="media">
                                        <div class="media-left">
                                            <figure class="image is-32x32">
                                                {% if is_valid %}
                                                    <i class="fas fa-check-circle fa-lg has-text-success"></i>
                                                {% else %}
                                                    <i class="fas fa-times-circle fa-lg has-text-danger"></i>
                                                {% endif %}
                                            </figure>
                                        </div>
                                        <div class="media-content">
                                            <div class="content">
                                                <p>
                                                    <strong class="is-capitalized">{{ component }}</strong>
                                                    {% if is_valid %}
                                                        <span class="tag is-success is-small">正常</span>
                                                    {% else %}
                                                        <span class="tag is-danger is-small">異常</span>
                                                    {% endif %}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="media-right">
                                            <button class="button is-small" onclick="testConnection('{{ component }}')">
                                                <span class="icon">
                                                    <i class="fas fa-sync"></i>
                                                </span>
                                                <span>測試</span>
                                            </button>
                                        </div>
                                    </article>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置管理操作 -->
            <div class="card">
                <div class="card-header">
                    <p class="card-header-title">
                        <span class="icon">
                            <i class="fas fa-tools"></i>
                        </span>
                        配置管理操作
                    </p>
                </div>
                <div class="card-content">
                    <div class="columns is-multiline">
                        <!-- 查看配置 -->
                        <div class="column is-6">
                            <div class="box">
                                <h4 class="title is-5">查看配置</h4>
                                <p class="content">查看和瀏覽目前的系統配置</p>
                                <a href="{{ url_for('config_admin.view_config') }}" class="button is-info is-fullwidth">
                                    <span class="icon">
                                        <i class="fas fa-eye"></i>
                                    </span>
                                    <span>查看完整配置</span>
                                </a>
                            </div>
                        </div>

                        <!-- 匯出配置 -->
                        <div class="column is-6">
                            <div class="box">
                                <h4 class="title is-5">匯出配置</h4>
                                <p class="content">匯出配置為 YAML 或 JSON 格式</p>
                                <div class="field is-grouped">
                                    <div class="control">
                                        <button class="button is-primary" onclick="exportConfig('yaml')">
                                            <span class="icon">
                                                <i class="fas fa-download"></i>
                                            </span>
                                            <span>YAML</span>
                                        </button>
                                    </div>
                                    <div class="control">
                                        <button class="button is-primary" onclick="exportConfig('json')">
                                            <span class="icon">
                                                <i class="fas fa-download"></i>
                                            </span>
                                            <span>JSON</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 重新載入配置 -->
                        <div class="column is-6">
                            <div class="box">
                                <h4 class="title is-5">重新載入</h4>
                                <p class="content">重新載入系統配置</p>
                                <button class="button is-warning is-fullwidth" onclick="reloadConfig()">
                                    <span class="icon">
                                        <i class="fas fa-sync"></i>
                                    </span>
                                    <span>重新載入配置</span>
                                </button>
                            </div>
                        </div>

                        <!-- 備份配置 -->
                        <div class="column is-6">
                            <div class="box">
                                <h4 class="title is-5">備份配置</h4>
                                <p class="content">備份目前的配置到檔案</p>
                                <button class="button is-success is-fullwidth" onclick="backupConfig()">
                                    <span class="icon">
                                        <i class="fas fa-save"></i>
                                    </span>
                                    <span>建立備份</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置段落管理 -->
            <div class="card mt-5">
                <div class="card-header">
                    <p class="card-header-title">
                        <span class="icon">
                            <i class="fas fa-list"></i>
                        </span>
                        配置段落
                    </p>
                </div>
                <div class="card-content">
                    <div class="columns is-multiline">
                        {% for section_name, section_data in config_summary.items() %}
                        <div class="column is-4">
                            <div class="box has-text-centered">
                                <h5 class="title is-6 is-capitalized">{{ section_name }}</h5>
                                <p class="content is-small">
                                    {% if section_name == 'database' %}
                                        類型: {{ section_data.type }}<br>
                                        連接池: {{ section_data.pool_size }}
                                    {% elif section_name == 'cache' %}
                                        類型: {{ section_data.type }}<br>
                                        超時: {{ section_data.timeout }}s
                                    {% elif section_name == 'security' %}
                                        CSRF: {{ section_data.csrf_enabled }}<br>
                                        限速: {{ section_data.rate_limit_enabled }}
                                    {% elif section_name == 'logging' %}
                                        層級: {{ section_data.level }}<br>
                                        檔案: {{ section_data.file_logging }}
                                    {% elif section_name == 'performance' %}
                                        監控: {{ section_data.monitoring_enabled }}<br>
                                        閾值: {{ section_data.slow_threshold }}s
                                    {% endif %}
                                </p>
                                <div class="buttons is-centered">
                                    <a href="{{ url_for('config_admin.view_section', section_name=section_name) }}" 
                                       class="button is-small is-info">查看</a>
                                    <a href="{{ url_for('config_admin.edit_section', section_name=section_name) }}" 
                                       class="button is-small is-primary">編輯</a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模態框 -->
<div id="exportModal" class="modal">
    <div class="modal-background"></div>
    <div class="modal-card">
        <header class="modal-card-head">
            <p class="modal-card-title">匯出配置</p>
            <button class="delete" aria-label="close" onclick="closeModal('exportModal')"></button>
        </header>
        <section class="modal-card-body">
            <pre id="exportContent" style="max-height: 400px; overflow-y: auto;"></pre>
        </section>
        <footer class="modal-card-foot">
            <button class="button" onclick="closeModal('exportModal')">關閉</button>
            <button class="button is-primary" onclick="downloadConfig()">下載</button>
        </footer>
    </div>
</div>

<script>
let currentExportData = null;
let currentExportFormat = null;

// 測試連接
async function testConnection(type) {
    try {
        const response = await fetch('/admin/config/test_connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({type: type})
        });
        
        const result = await response.json();
        
        if (result.success) {
            bulmaToast.toast({
                message: result.message,
                type: 'is-success',
                duration: 3000
            });
        } else {
            bulmaToast.toast({
                message: result.message || '測試失敗',
                type: 'is-danger',
                duration: 5000
            });
        }
    } catch (error) {
        bulmaToast.toast({
            message: '測試連接時發生錯誤: ' + error.message,
            type: 'is-danger',
            duration: 5000
        });
    }
}

// 匯出配置
async function exportConfig(format) {
    try {
        const response = await fetch(`/admin/config/export?format=${format}`);
        const result = await response.json();
        
        if (result.error) {
            throw new Error(result.error);
        }
        
        currentExportData = result.config;
        currentExportFormat = format;
        
        document.getElementById('exportContent').textContent = result.config;
        document.getElementById('exportModal').classList.add('is-active');
        
    } catch (error) {
        bulmaToast.toast({
            message: '匯出配置失敗: ' + error.message,
            type: 'is-danger',
            duration: 5000
        });
    }
}

// 下載配置
function downloadConfig() {
    if (!currentExportData) return;
    
    const blob = new Blob([currentExportData], {
        type: currentExportFormat === 'json' ? 'application/json' : 'text/yaml'
    });
    
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `config.${currentExportFormat}`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    closeModal('exportModal');
}

// 重新載入配置
async function reloadConfig() {
    if (!confirm('確定要重新載入配置嗎？這可能會影響系統運行。')) {
        return;
    }
    
    try {
        const response = await fetch('/admin/config/reload', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            bulmaToast.toast({
                message: result.message,
                type: 'is-success',
                duration: 3000
            });
            
            // 重新載入頁面以顯示更新後的配置
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        bulmaToast.toast({
            message: '重新載入配置失敗: ' + error.message,
            type: 'is-danger',
            duration: 5000
        });
    }
}

// 備份配置
async function backupConfig() {
    try {
        const response = await fetch('/admin/config/backup', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            bulmaToast.toast({
                message: result.message + ': ' + result.backup_file,
                type: 'is-success',
                duration: 5000
            });
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        bulmaToast.toast({
            message: '備份配置失敗: ' + error.message,
            type: 'is-danger',
            duration: 5000
        });
    }
}

// 關閉模態框
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('is-active');
}

// 關閉模態框事件監聽
document.addEventListener('DOMContentLoaded', function() {
    // 模態框背景點擊關閉
    document.querySelectorAll('.modal-background').forEach(function(element) {
        element.addEventListener('click', function() {
            element.closest('.modal').classList.remove('is-active');
        });
    });
});
</script>
{% endblock %}