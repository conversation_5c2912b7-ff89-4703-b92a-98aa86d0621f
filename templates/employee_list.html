<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>員工管理列表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .main-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .employee-table th {
            background: #2563eb !important;
            color: #fff !important;
            text-align: center;
            font-weight: 600 !important;
            border-color: #2563eb !important;
        }

        .employee-table td {
            text-align: center;
            background: #f9fafb;
        }

        .employee-table .edit-btn {
            color: #2563eb;
            background: none;
            border: none;
            font-size: 1.2em;
            cursor: pointer;
        }

        .employee-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 0;
        }

        .right-panel {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2rem;
        }

        .page-btns {
            float: right;
        }

        .pagination {
            justify-content: center;
        }

        .search-label {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 0.5em;
        }

        .search-box {
            border: 2px solid #2563eb;
            border-radius: 12px;
            padding: 1.2em 1.5em 0.5em 1.5em;
            margin-bottom: 1.5em;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column is-6">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=薪資報酬"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        員工管理列表
                    </h1>
                </div>
                <div class="mb-4">
                    <div class="page-btns" style="float:right;">
                        <button class="button is-link is-light mr-2"><span class="icon"><i
                                    class="fas fa-book"></i></span>頁面指南</button>
                        <a class="button is-link mr-2">新增多位員工</a>
                        <a class="button is-link" href="{{ url_for('payroll.add_employee') }}">新增員工</a>
                    </div>
                </div>
                <div class="employee-card p-0">
                    <table class="table employee-table is-fullwidth mt-0 mb-0">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>職稱</th>
                                <th>姓名</th>
                                <th>員工編號</th>
                                <th>部門</th>
                                <th>到職日</th>
                                <th>離職日</th>
                                <th>編輯</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if employees %}
                            {% for employee in employees %}
                            <tr>
                                <td>{{ loop.index + (page - 1) * 20 }}</td>
                                <td>{{ employee.title or '-' }}</td>
                                <td>{{ employee.name }}</td>
                                <td>{{ employee.emp_id or '-' }}</td>
                                <td>{{ employee.department_name or '-' }}</td>
                                <td>{{ employee.onboard_date.strftime('%Y-%m-%d') if employee.onboard_date else '-' }}
                                </td>
                                <td>{{ employee.leave_date.strftime('%Y-%m-%d') if employee.leave_date else '-' }}</td>
                                <td>
                                    <a href="{{ url_for('payroll.edit_employee', employee_id=employee.id) }}"
                                        class="edit-btn" title="編輯"><i class="fas fa-pen"></i></a>
                                    <button class="edit-btn" onclick="deleteEmployee({{ employee.id }})" title="刪除"
                                        style="color: #e53e3e;"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                            {% endfor %}
                            {% else %}
                            <tr>
                                <td colspan="8" style="text-align: center; padding: 2rem; color: #888;">
                                    尚無員工資料
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
                {% if total_pages > 1 %}
                <nav class="pagination is-centered mt-4" role="navigation" aria-label="pagination">
                    {% if has_prev %}
                    <a class="pagination-previous"
                        href="?page={{ page - 1 }}{% if search %}&search={{ search }}{% endif %}">
                        <span class="icon"><i class="fas fa-angle-left"></i></span>
                    </a>
                    {% else %}
                    <a class="pagination-previous" disabled><span class="icon"><i
                                class="fas fa-angle-left"></i></span></a>
                    {% endif %}

                    <ul class="pagination-list">
                        {% for p in range(1, total_pages + 1) %}
                        {% if p == page %}
                        <li><a class="pagination-link is-current">{{ p }}</a></li>
                        {% else %}
                        <li><a class="pagination-link"
                                href="?page={{ p }}{% if search %}&search={{ search }}{% endif %}">{{ p }}</a></li>
                        {% endif %}
                        {% endfor %}
                    </ul>

                    {% if has_next %}
                    <a class="pagination-next"
                        href="?page={{ page + 1 }}{% if search %}&search={{ search }}{% endif %}">
                        <span class="icon"><i class="fas fa-angle-right"></i></span>
                    </a>
                    {% else %}
                    <a class="pagination-next" disabled><span class="icon"><i class="fas fa-angle-right"></i></span></a>
                    {% endif %}
                </nav>
                {% endif %}
                <div class="has-text-right mt-2 mb-5">共 {{ total_pages }} 頁　{{ total }}筆紀錄</div>
            </div>
            <div class="column is-3">
                <div class="right-panel">
                    <div class="search-label">篩選條件：</div>
                    <div class="field">
                        <div class="control">
                            <label class="checkbox"><input type="checkbox"> 在職中</label>
                        </div>
                        <div class="control mt-2">
                            <label class="checkbox"><input type="checkbox"> 已離職</label>
                        </div>
                    </div>
                    <div class="search-label mt-5">查詢</div>
                    <form method="GET">
                        <div class="field">
                            <div class="control">
                                <input class="input" type="text" name="search" value="{{ search }}"
                                    placeholder="姓名、員工編號、身份證號、部門">
                            </div>
                        </div>
                        <div class="field">
                            <div class="control">
                                <button class="button is-link is-fullwidth" type="submit">搜尋</button>
                            </div>
                        </div>
                        {% if search %}
                        <div class="field">
                            <div class="control">
                                <a class="button is-light is-fullwidth"
                                    href="{{ url_for('payroll.employee_list') }}">清除搜尋</a>
                            </div>
                        </div>
                        {% endif %}
                    </form>
                    <div class="field is-grouped mt-4">
                        <div class="control">
                            <button class="button is-light">清空條件</button>
                        </div>
                        <div class="control">
                            <button class="button is-link">查詢</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script>
        function deleteEmployee(employeeId) {
            if (confirm('確定要刪除這位員工嗎？此操作無法復原。')) {
                fetch(`/delete_employee/${employeeId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                    .then(response => {
                        if (response.ok) {
                            location.reload();
                        } else {
                            alert('刪除失敗，請稍後再試。');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('刪除失敗，請稍後再試。');
                    });
            }
        }

        // Flash messages
        {% with messages = get_flashed_messages(with_categories = true) %}
        {% if messages %}
        {% for category, message in messages %}
        {% if category == 'success' %}
        alert('✅ ' + '{{ message }}');
        {% elif category == 'error' %}
        alert('❌ ' + '{{ message }}');
        {% else %}
        alert('{{ message }}');
        {% endif %}
        {% endfor %}
        {% endif %}
        {% endwith %}
    </script>
</body>

</html>