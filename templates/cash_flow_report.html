<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>經營現金流量表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .cf-table th, .cf-table td { text-align: right; font-size: 1.05rem; }
        .cf-table th { background: #f5f6fa; color: #222; }
        .cf-table .group-row { background: #e5e7eb; font-weight: bold; text-align: left; }
        .cf-table .total-row { background: #e0e7ff; font-weight: bold; color: #2563eb; }
        .cf-table .amount-cell { font-family: '<PERSON><PERSON>', 'Consolas', monospace; letter-spacing: 1px; }
        .cf-table .negative { color: #e67e22; }
        .cf-table .positive { color: #2563eb; }
        .cf-table .sub-row { background: #f9fafb; }
        .cf-table .main-row { background: #f1f5f9; }
        .cf-table .border-bottom { border-bottom: 2px solid #b6b6b6; }
        .cf-table .border-top { border-top: 2px solid #b6b6b6; }
        .cf-table .left { text-align: left; }
        .cf-table .blue { color: #2563eb; }
        .cf-table .gray-bg { background: #f5f6fa; }
        .cf-toolbar { display: flex; justify-content: flex-end; gap: 0.5rem; margin-bottom: 1rem; }
        .cf-toolbar .button { font-size: 0.95rem; }
    </style>
</head>
<body>
<div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
    <div class="columns">
        <div class="column is-narrow">
            {% include 'sidebar.html' %}
        </div>
        <div class="column">
            <section class="section">
                <div class="level mb-3">
                    <div class="level-left">
                        <h1 class="title is-4 mb-0"><i class="fas fa-coins"></i> 經營現金流量表</h1>
                    </div>
                    <div class="level-right cf-toolbar">
                        <button class="button is-light"><span class="icon"><i class="fas fa-cog"></i></span> 顯示設定</button>
                        <button class="button is-light"><span class="icon"><i class="fas fa-list"></i></span> 列表</button>
                        <button class="button is-light"><span class="icon"><i class="fas fa-download"></i></span> 下載</button>
                    </div>
                </div>
                <div class="box">
                    <div class="mb-3">
                        <span class="has-text-weight-semibold">{{ year or '年度' }}年經營現金流量表</span>
                    </div>
                    <div class="table-container">
                        <table class="table is-fullwidth is-bordered cf-table">
                            <thead>
                                <tr>
                                    <th class="left">項目</th>
                                    {% for m in range(1, 13) %}
                                    <th>{{ m }}月</th>
                                    {% endfor %}
                                    <th>合計</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- 收入分組 -->
                                <tr class="group-row"><td colspan="14">收入</td></tr>
                                <tr class="sub-row">
                                    <td class="left">銷貨收入</td>
                                    {% for m in range(1, 13) %}
                                    <td class="amount-cell positive">{{ '{:,.0f}'.format(cash_flow.monthly[m].income or 0) }}</td>
                                    {% endfor %}
                                    <td class="amount-cell positive">{{ '{:,.0f}'.format(cash_flow.summary.total_income or 0) }}</td>
                                </tr>
                                <tr class="main-row border-bottom">
                                    <td class="left">收入合計</td>
                                    {% for m in range(1, 13) %}
                                    <td class="amount-cell positive">{{ '{:,.0f}'.format(cash_flow.monthly[m].income or 0) }}</td>
                                    {% endfor %}
                                    <td class="amount-cell positive">{{ '{:,.0f}'.format(cash_flow.summary.total_income or 0) }}</td>
                                </tr>
                                <!-- 付款分組 -->
                                <tr class="group-row"><td colspan="14">付款</td></tr>
                                <tr class="sub-row">
                                    <td class="left">營業費用</td>
                                    {% for m in range(1, 13) %}
                                    <td class="amount-cell negative">{{ '{:,.0f}'.format(cash_flow.monthly[m].expense or 0) }}</td>
                                    {% endfor %}
                                    <td class="amount-cell negative">{{ '{:,.0f}'.format(cash_flow.summary.total_expense or 0) }}</td>
                                </tr>
                                <tr class="main-row border-bottom">
                                    <td class="left">付款合計</td>
                                    {% for m in range(1, 13) %}
                                    <td class="amount-cell negative">{{ '{:,.0f}'.format(cash_flow.monthly[m].expense or 0) }}</td>
                                    {% endfor %}
                                    <td class="amount-cell negative">{{ '{:,.0f}'.format(cash_flow.summary.total_expense or 0) }}</td>
                                </tr>
                                <!-- 融資分組 -->
                                <tr class="group-row"><td colspan="14">融資</td></tr>
                                <tr class="sub-row">
                                    <td class="left">借款收入</td>
                                    {% for m in range(1, 13) %}
                                    <td class="amount-cell">0</td>
                                    {% endfor %}
                                    <td class="amount-cell">0</td>
                                </tr>
                                <tr class="sub-row">
                                    <td class="left">還款支出</td>
                                    {% for m in range(1, 13) %}
                                    <td class="amount-cell">0</td>
                                    {% endfor %}
                                    <td class="amount-cell">0</td>
                                </tr>
                                <tr class="main-row border-bottom">
                                    <td class="left">融資合計</td>
                                    {% for m in range(1, 13) %}
                                    <td class="amount-cell">0</td>
                                    {% endfor %}
                                    <td class="amount-cell">0</td>
                                </tr>
                                <!-- 開餘分組 -->
                                <tr class="group-row"><td colspan="14">開餘</td></tr>
                                <tr class="main-row border-bottom">
                                    <td class="left">期初現金餘額</td>
                                    {% for m in range(1, 13) %}
                                    <td class="amount-cell">{{ '{:,.0f}'.format(cash_flow.monthly[m].opening_balance or 0) }}</td>
                                    {% endfor %}
                                    <td class="amount-cell">{{ '{:,.0f}'.format(cash_flow.summary.opening_balance or 0) }}</td>
                                </tr>
                                <!-- 現金流合計 -->
                                <tr class="total-row border-top">
                                    <td class="left">現金流量合計</td>
                                    {% for m in range(1, 13) %}
                                    <td class="amount-cell blue">{{ '{:,.0f}'.format((cash_flow.monthly[m].income or 0) - (cash_flow.monthly[m].expense or 0)) }}</td>
                                    {% endfor %}
                                    <td class="amount-cell blue">{{ '{:,.0f}'.format((cash_flow.summary.total_income or 0) - (cash_flow.summary.total_expense or 0)) }}</td>
                                </tr>
                                <!-- 期末現金餘額 -->
                                <tr class="main-row border-bottom">
                                    <td class="left">期末現金餘額</td>
                                    {% for m in range(1, 13) %}
                                    <td class="amount-cell">{{ '{:,.0f}'.format(cash_flow.monthly[m].closing_balance or 0) }}</td>
                                    {% endfor %}
                                    <td class="amount-cell">{{ '{:,.0f}'.format(cash_flow.summary.closing_balance or 0) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>
</body>
</html>