<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>資產負債表 - 印錢大師</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .balance-sheet-table {
            border-collapse: collapse;
            width: 100%;
            font-size: 14px;
        }

        .balance-sheet-table th,
        .balance-sheet-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .balance-sheet-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .assets-header {
            background-color: #3273dc !important;
            color: white !important;
            text-align: center;
            font-weight: bold;
        }

        .liabilities-header {
            background-color: #ff7f00 !important;
            color: white !important;
            text-align: center;
            font-weight: bold;
        }

        .amount-cell {
            text-align: right;
        }

        .percentage-cell {
            text-align: right;
        }

        .subject-name {
            padding-left: 10px;
        }

        .collapsible-header {
            cursor: pointer;
            user-select: none;
        }

        .collapsible-header:hover {
            background-color: #f0f0f0;
        }

        .collapse-icon {
            margin-right: 5px;
            transition: transform 0.3s ease;
        }

        .collapsed .collapse-icon {
            transform: rotate(-90deg);
        }

        .subtotal-row {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <div class="columns">
            <!-- 左側邊欄 -->
            <div class="column is-narrow" style="width: 250px;">
                {% include 'sidebar.html' %}
            </div>

            <!-- 主要內容區域 -->
            <div class="column">
                <div class="content" style="margin-top: 20px;">
                    <!-- 標題區域 -->
                    <div class="level">
                        <div class="level-left">
                            <div class="level-item">
                                <h1 class="title is-4">資產負債表</h1>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <div class="field has-addons">
                                    <div class="control">
                                        <input class="input" type="date" id="report_date" value="{{ report_date }}">
                                    </div>
                                    <div class="control">
                                        <button class="button is-primary" onclick="updateReport()">
                                            <span>查詢</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 資產負債表 -->
                    <div class="columns">
                        <!-- 左側：資產 -->
                        <div class="column is-half">
                            <table class="balance-sheet-table">
                                <thead>
                                    <tr>
                                        <th class="assets-header" colspan="3">資產</th>
                                    </tr>
                                    <tr>
                                        <th>會計科目</th>
                                        <th>金額</th>
                                        <th>百分比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 流動資產 -->
                                    {% set current_assets_items =
                                    balance_sheet['assets']['current_assets']['cash'] +
                                    balance_sheet['assets']['current_assets']['petty_cash'] +
                                    balance_sheet['assets']['current_assets']['bank_deposits'] +
                                    balance_sheet['assets']['current_assets']['notes_receivable'] +
                                    balance_sheet['assets']['current_assets']['accounts_receivable'] +
                                    balance_sheet['assets']['current_assets']['other_receivables'] +
                                    balance_sheet['assets']['current_assets']['estimated_accounts_receivable'] +
                                    balance_sheet['assets']['current_assets']['financial_assets_fvpl'] +
                                    balance_sheet['assets']['current_assets']['financial_assets_fvoci'] +
                                    balance_sheet['assets']['current_assets']['financial_assets_cost'] +
                                    balance_sheet['assets']['current_assets']['merchandise_inventory'] +
                                    balance_sheet['assets']['current_assets']['raw_material_inventory'] +
                                    balance_sheet['assets']['current_assets']['finished_goods'] +
                                    balance_sheet['assets']['current_assets']['prepaid_expenses'] +
                                    balance_sheet['assets']['current_assets']['prepaid_purchases'] +
                                    balance_sheet['assets']['current_assets']['other_prepayments'] +
                                    balance_sheet['assets']['current_assets']['temporary_payments'] +
                                    balance_sheet['assets']['current_assets']['prepaid_taxes'] +
                                    balance_sheet['assets']['current_assets']['input_tax'] +
                                    balance_sheet['assets']['current_assets']['tax_credits'] +
                                    balance_sheet['assets']['current_assets']['other_current']
                                    %}

                                    {% if current_assets_items %}
                                    <!-- 流動資產標題行 -->
                                    <tr class="collapsible-header" style="background-color: #e8f4fd;"
                                        onclick="toggleCollapse('current-assets')">
                                        <td style="font-weight: bold; text-align: center; padding: 10px;">
                                            <span class="collapse-icon">▼</span>流動資產
                                        </td>
                                        <td class="amount-cell">{{
                                            "{:,}".format(balance_sheet['assets']['current_assets']['total']) }}</td>
                                        <td class="percentage-cell">{{
                                            "%.2f"|format((balance_sheet['assets']['current_assets']['total'] /
                                            balance_sheet['assets']['total_assets'] * 100) if
                                            balance_sheet['assets']['total_assets'] > 0 else 0) }}</td>
                                    </tr>
                                    <!-- 流動資產明細 -->
                                    {% for item in current_assets_items %}
                                        {% if item.balance != 0 %}
                                        <tr class="current-assets-detail">
                                            <td style="padding-left: 20px;">{{ item.code }}-{{ item.name }}</td>
                                            <td class="amount-cell">{{ "{:,}".format(item.balance) }}</td>
                                            <td class="percentage-cell">{{ "%.2f"|format(item.percentage) }}</td>
                                        </tr>
                                        {% endif %}
                                    {% endfor %}
                                    {% endif %}

                                    <!-- 非流動資產 -->
                                    {% set non_current_assets_items =
                                    balance_sheet['assets']['non_current_assets']['property_plant_equipment'] +
                                    balance_sheet['assets']['non_current_assets']['intangible_assets'] +
                                    balance_sheet['assets']['non_current_assets']['investments'] +
                                    balance_sheet['assets']['non_current_assets']['other_non_current'] %}

                                    {% if non_current_assets_items %}
                                    <!-- 非流動資產標題行 -->
                                    <tr class="collapsible-header" style="background-color: #e8f4fd;"
                                        onclick="toggleCollapse('non-current-assets')">
                                        <td style="font-weight: bold; text-align: center; padding: 10px;">
                                            <span class="collapse-icon">▼</span>非流動資產
                                        </td>
                                        <td class="amount-cell">{{
                                            "{:,}".format(balance_sheet['assets']['non_current_assets']['total']) }}
                                        </td>
                                        <td class="percentage-cell">{{
                                            "%.2f"|format((balance_sheet['assets']['non_current_assets']['total'] /
                                            balance_sheet['assets']['total_assets'] * 100) if
                                            balance_sheet['assets']['total_assets'] > 0 else 0) }}</td>
                                    </tr>
                                    <!-- 非流動資產明細 -->
                                    {% for item in non_current_assets_items %}
                                        {% if item.balance != 0 %}
                                        <tr class="non-current-assets-detail">
                                            <td style="padding-left: 20px;">{{ item.code }}-{{ item.name }}</td>
                                            <td class="amount-cell">{{ "{:,}".format(item.balance) }}</td>
                                            <td class="percentage-cell">{{ "%.2f"|format(item.percentage) }}</td>
                                        </tr>
                                        {% endif %}
                                    {% endfor %}
                                    {% endif %}

                                    <!-- 資產合計 -->
                                    <tr style="background-color: #d4edda; font-weight: bold; border-top: 2px solid #007bff;">
                                        <td style="text-align: center; padding: 10px;">資產合計</td>
                                        <td class="amount-cell" style="font-size: 1.1em;">{{
                                            "{:,}".format(balance_sheet['assets']['total_assets']) }}</td>
                                        <td class="percentage-cell" style="font-size: 1.1em;">100.00</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 右側：負債與權益 -->
                        <div class="column is-half">
                            <table class="balance-sheet-table">
                                <thead>
                                    <tr>
                                        <th class="liabilities-header" colspan="3">負債</th>
                                    </tr>
                                    <tr>
                                        <th>會計科目</th>
                                        <th>金額</th>
                                        <th>百分比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- 流動負債 -->
                                    {% set current_liabilities_items =
                                    balance_sheet['liabilities']['current_liabilities']['bank_loans'] +
                                    balance_sheet['liabilities']['current_liabilities']['notes_payable'] +
                                    balance_sheet['liabilities']['current_liabilities']['accounts_payable'] +
                                    balance_sheet['liabilities']['current_liabilities']['estimated_accounts_payable'] +
                                    balance_sheet['liabilities']['current_liabilities']['accrued_expenses'] +
                                    balance_sheet['liabilities']['current_liabilities']['taxes_payable'] +
                                    balance_sheet['liabilities']['current_liabilities']['benefits_payable'] +
                                    balance_sheet['liabilities']['current_liabilities']['other_payables'] +
                                    balance_sheet['liabilities']['current_liabilities']['advances_from_customers'] +
                                    balance_sheet['liabilities']['current_liabilities']['deferred_revenue'] +
                                    balance_sheet['liabilities']['current_liabilities']['other_advances'] +
                                    balance_sheet['liabilities']['current_liabilities']['temporary_receipts'] +
                                    balance_sheet['liabilities']['current_liabilities']['owners_current_account'] +
                                    balance_sheet['liabilities']['current_liabilities']['output_tax'] +
                                    balance_sheet['liabilities']['current_liabilities']['advances_received'] +
                                    balance_sheet['liabilities']['current_liabilities']['tax_payable'] +
                                    balance_sheet['liabilities']['current_liabilities']['other_current'] %}

                                    {% if current_liabilities_items %}
                                    <!-- 流動負債標題行 -->
                                    <tr class="collapsible-header" style="background-color: #fff2e6;"
                                        onclick="toggleCollapse('current-liabilities')">
                                        <td style="font-weight: bold; text-align: center; padding: 10px;">
                                            <span class="collapse-icon">▼</span>流動負債
                                        </td>
                                        <td class="amount-cell">{{
                                            "{:,}".format(balance_sheet['liabilities']['current_liabilities']['total'])
                                            }}</td>
                                        <td class="percentage-cell">{{
                                            "%.2f"|format((balance_sheet['liabilities']['current_liabilities']['total']
                                            / balance_sheet['assets']['total_assets'] * 100) if
                                            balance_sheet['assets']['total_assets'] > 0 else 0) }}</td>
                                    </tr>
                                    <!-- 流動負債明細 -->
                                    {% for item in current_liabilities_items %}
                                        {% if item.balance != 0 %}
                                        <tr class="current-liabilities-detail">
                                            <td style="padding-left: 20px;">{{ item.code }}-{{ item.name }}</td>
                                            <td class="amount-cell">{{ "{:,}".format(item.balance) }}</td>
                                            <td class="percentage-cell">{{ "%.2f"|format(item.percentage) }}</td>
                                        </tr>
                                        {% endif %}
                                    {% endfor %}
                                    {% endif %}

                                    <!-- 非流動負債 -->
                                    {% set non_current_liabilities_items =
                                    balance_sheet['liabilities']['non_current_liabilities']['long_term_debt'] +
                                    balance_sheet['liabilities']['non_current_liabilities']['other_non_current'] %}

                                    {% if non_current_liabilities_items %}
                                    <!-- 非流動負債標題行 -->
                                    <tr class="collapsible-header" style="background-color: #fff2e6;"
                                        onclick="toggleCollapse('non-current-liabilities')">
                                        <td style="font-weight: bold; text-align: center; padding: 10px;">
                                            <span class="collapse-icon">▼</span>非流動負債
                                        </td>
                                        <td class="amount-cell">{{
                                            "{:,}".format(balance_sheet['liabilities']['non_current_liabilities']['total'])
                                            }}</td>
                                        <td class="percentage-cell">{{
                                            "%.2f"|format((balance_sheet['liabilities']['non_current_liabilities']['total']
                                            / balance_sheet['assets']['total_assets'] * 100) if
                                            balance_sheet['assets']['total_assets'] > 0 else 0) }}</td>
                                    </tr>
                                    <!-- 非流動負債明細 -->
                                    {% for item in non_current_liabilities_items %}
                                        {% if item.balance != 0 %}
                                        <tr class="non-current-liabilities-detail">
                                            <td style="padding-left: 20px;">{{ item.code }}-{{ item.name }}</td>
                                            <td class="amount-cell">{{ "{:,}".format(item.balance) }}</td>
                                            <td class="percentage-cell">{{ "%.2f"|format(item.percentage) }}</td>
                                        </tr>
                                        {% endif %}
                                    {% endfor %}
                                    {% endif %}

                                    <!-- 負債合計 -->
                                    <tr
                                        style="background-color: #f0f8ff; border-top: 2px solid #ddd; border-bottom: 2px solid #ddd;">
                                        <td style="text-align: center; padding: 10px; font-weight: bold;">負債合計</td>
                                        <td class="amount-cell" style="font-size: 1.1em; font-weight: bold;">{{
                                            "{:,}".format(balance_sheet['liabilities']['total_liabilities']) }}</td>
                                        <td class="percentage-cell" style="font-size: 1.1em; font-weight: bold;">{{
                                            "%.2f"|format((balance_sheet['liabilities']['total_liabilities'] /
                                            balance_sheet['assets']['total_assets'] * 100) if
                                            balance_sheet['assets']['total_assets'] > 0 else 0) }}</td>
                                    </tr>

                                    <!-- 權益 -->
                                    {% set equity_items = balance_sheet['equity']['capital'] +
                                    balance_sheet['equity']['retained_earnings'] +
                                    balance_sheet['equity']['other_equity'] %}

                                    {% if equity_items %}
                                    <!-- 權益標題行 -->
                                    <tr class="collapsible-header" style="background-color: #fff2e6;"
                                        onclick="toggleCollapse('equity')">
                                        <td style="font-weight: bold; text-align: center; padding: 10px;">
                                            <span class="collapse-icon">▼</span>權益
                                        </td>
                                        <td class="amount-cell">{{
                                            "{:,}".format(balance_sheet['equity']['total_equity']) }}</td>
                                        <td class="percentage-cell">{{
                                            "%.2f"|format((balance_sheet['equity']['total_equity'] /
                                            balance_sheet['assets']['total_assets'] * 100) if
                                            balance_sheet['assets']['total_assets'] > 0 else 0) }}</td>
                                    </tr>
                                    <!-- 權益明細 -->
                                    {% for item in equity_items %}
                                        {% if item.balance != 0 %}
                                        <tr class="equity-detail">
                                            <td style="padding-left: 20px;">{{ item.code }}-{{ item.name }}</td>
                                            <td class="amount-cell">{{ "{:,}".format(item.balance) }}</td>
                                            <td class="percentage-cell">{{ "%.2f"|format(item.percentage) }}</td>
                                        </tr>
                                        {% endif %}
                                    {% endfor %}
                                    {% endif %}

                                    <!-- 負債與權益合計 -->
                                    <tr style="background-color: #d4edda; font-weight: bold; border-top: 2px solid #fd7e14;">
                                        <td style="text-align: center; padding: 10px;">負債與權益合計</td>
                                        <td class="amount-cell" style="font-size: 1.1em;">{{
                                            "{:,}".format(balance_sheet['liabilities']['total_liabilities'] +
                                            balance_sheet['equity']['total_equity']) }}</td>
                                        <td class="percentage-cell" style="font-size: 1.1em;">100.00</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateReport() {
            const reportDate = document.getElementById('report_date').value;
            if (reportDate) {
                window.location.href = `/reports/balance_sheet?report_date=${reportDate}`;
            }
        }

        function toggleCollapse(sectionId) {
            const detailRows = document.querySelectorAll('.' + sectionId + '-detail');
            const header = event.target.closest('tr');
            const icon = header.querySelector('.collapse-icon');

            detailRows.forEach(row => {
                if (row.style.display === 'none') {
                    row.style.display = '';
                    icon.textContent = '▼';
                    header.classList.remove('collapsed');
                } else {
                    row.style.display = 'none';
                    icon.textContent = '▶';
                    header.classList.add('collapsed');
                }
            });
        }
    </script>
</body>

</html>
