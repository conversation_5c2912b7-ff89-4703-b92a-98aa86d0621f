<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>總覽 - 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-theme.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <style>
        .dashboard-card {
            height: 100%;
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            box-shadow: 0 8px 16px rgba(10, 10, 10, 0.1);
            transform: translateY(-5px);
        }

        .card-header-icon {
            color: #3273dc;
        }

        .trend-up {
            color: #48c774;
        }

        .trend-down {
            color: #f14668;
        }

        .amount-positive {
            color: #48c774;
            font-weight: bold;
        }

        .amount-negative {
            color: #f14668;
            font-weight: bold;
        }

        .table-container {
            max-height: 300px;
            overflow-y: auto;
        }

        .chart-container {
            position: relative;
            height: 250px;
            width: 100%;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #f14668;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
        }

        .summary-icon {
            font-size: 2rem;
            margin-right: 1rem;
        }

        .summary-content {
            display: flex;
            align-items: center;
        }

        .summary-text {
            flex-grow: 1;
        }

        .summary-amount {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .summary-label {
            color: #7a7a7a;
            font-size: 0.9rem;
        }

        .overdue-tag {
            background-color: #f14668;
            color: white;
        }

        .due-soon-tag {
            background-color: #ffdd57;
            color: rgba(0, 0, 0, 0.7);
        }
    </style>
</head>

<body>
    <!-- 深色模式切換 -->
    <div class="theme-toggle" onclick="toggleTheme()">
        <i class="fas fa-moon" id="theme-icon"></i>
    </div>
    
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="mb-4">
                    <h1 class="title is-4">
                        <i class="fas fa-tachometer-alt mr-2"></i>總覽儀表板
                    </h1>
                    <p class="subtitle is-6">
                        <span id="current-date"></span> | 最近更新: <span id="last-updated"></span>
                    </p>
                </div>

                <!-- 帳戶餘額摘要 -->
                <div class="columns is-multiline">
                    <div class="column is-3">
                        <div class="summary-card-enhanced fade-in-up">
                            <div class="summary-content">
                                <span class="summary-icon has-text-info">
                                    <i class="fas fa-wallet"></i>
                                </span>
                                <div class="summary-text">
                                    <div class="animated-counter">{{ total_balance|default('NT$ 0') }}</div>
                                    <div class="summary-label">總資金餘額</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="summary-card-enhanced fade-in-up" style="animation-delay: 0.1s;">
                            <div class="summary-content">
                                <span class="summary-icon has-text-success">
                                    <i class="fas fa-hand-holding-usd"></i>
                                </span>
                                <div class="summary-text">
                                    <div class="animated-counter">{{ total_income|default('NT$ 0') }}</div>
                                    <div class="summary-label">本月收入</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="summary-card-enhanced fade-in-up" style="animation-delay: 0.2s;">
                            <div class="summary-content">
                                <span class="summary-icon has-text-danger">
                                    <i class="fas fa-shopping-cart"></i>
                                </span>
                                <div class="summary-text">
                                    <div class="animated-counter">{{ total_expense|default('NT$ 0') }}</div>
                                    <div class="summary-label">本月支出</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="column is-3">
                        <div class="summary-card-enhanced fade-in-up" style="animation-delay: 0.3s;">
                            <div class="summary-content">
                                <span class="summary-icon has-text-warning">
                                    <i class="fas fa-chart-line"></i>
                                </span>
                                <div class="summary-text">
                                    <div class="animated-counter">{{ net_income|default('NT$ 0') }}</div>
                                    <div class="summary-label">本月淨收入</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="columns">
                    <!-- 收入/支出趨勢圖 -->
                    <div class="column is-8">
                        <div class="modern-card fade-in-up" style="animation-delay: 0.4s;">
                            <header class="card-header" style="background: var(--primary-gradient); color: white; border: none; border-radius: 16px 16px 0 0;">
                                <p class="card-header-title" style="color: white;">
                                    <span class="icon">
                                        <i class="fas fa-chart-line"></i>
                                    </span>
                                    收入/支出趨勢
                                </p>
                                <div class="card-header-icon">
                                    <div class="select is-small">
                                        <select id="trend-period" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; border-radius: 8px;">
                                            <option value="week">本週</option>
                                            <option value="month" selected>本月</option>
                                            <option value="quarter">本季</option>
                                            <option value="year">本年</option>
                                        </select>
                                    </div>
                                </div>
                            </header>
                            <div class="card-content">
                                <div class="chart-container-modern">
                                    <canvas id="income-expense-chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 帳戶餘額 -->
                    <div class="column is-4">
                        <div class="modern-card fade-in-up" style="animation-delay: 0.5s;">
                            <header class="card-header" style="background: var(--info-gradient); color: white; border: none; border-radius: 16px 16px 0 0;">
                                <p class="card-header-title" style="color: white;">
                                    <span class="icon">
                                        <i class="fas fa-university"></i>
                                    </span>
                                    帳戶餘額
                                </p>
                                <a href="/account_details" class="card-header-icon btn-gradient-primary" style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 8px;">
                                    <span class="icon" style="color: white;">
                                        <i class="fas fa-external-link-alt"></i>
                                    </span>
                                </a>
                            </header>
                            <div class="card-content">
                                <div class="table-container">
                                    <table class="modern-table table is-fullwidth">
                                        <thead>
                                            <tr>
                                                <th>帳戶名稱</th>
                                                <th class="has-text-right">餘額</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for account in accounts %}
                                            <tr>
                                                <td>
                                                    <span class="icon-text">
                                                        <span class="icon">
                                                            {% if account.category == '現金' %}
                                                            <i class="fas fa-money-bill-wave"></i>
                                                            {% elif account.category == '銀行帳戶' %}
                                                            <i class="fas fa-university"></i>
                                                            {% else %}
                                                            <i class="fas fa-mobile-alt"></i>
                                                            {% endif %}
                                                        </span>
                                                        <span>{{ account.name }}</span>
                                                    </span>
                                                </td>
                                                <td class="has-text-right">
                                                    <span
                                                        class="{% if account.raw_balance >= 0 %}has-text-success{% else %}has-text-danger{% endif %}">
                                                        {{ account.balance }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% else %}
                                            <tr>
                                                <td colspan="2" class="has-text-centered">無帳戶資料</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="columns">
                    <!-- 最近的收支紀錄 -->
                    <div class="column is-6">
                        <div class="modern-card fade-in-up" style="animation-delay: 0.6s;">
                            <header class="card-header" style="background: var(--success-gradient); color: white; border: none; border-radius: 16px 16px 0 0;">
                                <p class="card-header-title" style="color: white;">
                                    <span class="icon">
                                        <i class="fas fa-history"></i>
                                    </span>
                                    最近的收支紀錄
                                </p>
                                <div class="card-header-icon">
                                    <a href="/income_list" class="btn-gradient-primary" style="font-size: 0.875rem; padding: 6px 12px;">
                                        <span>查看全部</span>
                                    </a>
                                </div>
                            </header>
                            <div class="card-content">
                                <div class="table-container">
                                    <table class="modern-table table is-fullwidth">
                                        <thead>
                                            <tr>
                                                <th>日期</th>
                                                <th>類型</th>
                                                <th>名稱</th>
                                                <th class="has-text-right">金額</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for record in recent_records %}
                                            <tr>
                                                <td>{{ record.date }}</td>
                                                <td>
                                                    <span
                                                        class="status-tag-modern {% if record.type == 'Income' %}is-success{% else %}is-danger{% endif %}">
                                                        {{ record.type }}
                                                    </span>
                                                </td>
                                                <td>{{ record.name }} <small class="has-text-grey">({{ record.subject
                                                        }})</small></td>
                                                <td class="has-text-right">
                                                    <span
                                                        class="{% if record.type == 'Income' %}amount-positive{% else %}amount-negative{% endif %}">
                                                        {{ record.amount }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% else %}
                                            <tr>
                                                <td colspan="4" class="has-text-centered">無最近交易記錄</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 待處理的應收/應付帳款 -->
                    <div class="column is-6">
                        <div class="modern-card fade-in-up" style="animation-delay: 0.7s;">
                            <header class="card-header" style="background: var(--warning-gradient); color: white; border: none; border-radius: 16px 16px 0 0;">
                                <p class="card-header-title" style="color: white; position: relative;">
                                    <span class="icon">
                                        <i class="fas fa-calendar-alt"></i>
                                    </span>
                                    待處理的應收/應付帳款
                                    <span class="notification-badge-modern">{{ pending_payments|length }}</span>
                                </p>
                                <div class="card-header-icon">
                                    <a href="/ac_delay_list" class="btn-gradient-primary" style="font-size: 0.875rem; padding: 6px 12px;">
                                        <span>查看全部</span>
                                    </a>
                                </div>
                            </header>
                            <div class="card-content">
                                <div class="table-container">
                                    <table class="modern-table table is-fullwidth">
                                        <thead>
                                            <tr>
                                                <th>對象</th>
                                                <th>類型</th>
                                                <th>到期日</th>
                                                <th class="has-text-right">金額</th>
                                                <th>狀態</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for payment in pending_payments %}
                                            <tr>
                                                <td>{{ payment.identity }}</td>
                                                <td>
                                                    <span
                                                        class="status-tag-modern {% if payment.type == '應收' %}is-info{% else %}is-warning{% endif %}">
                                                        {{ payment.type }}
                                                    </span>
                                                </td>
                                                <td>{{ payment.due_date }}</td>
                                                <td class="has-text-right">{{ payment.amount }}</td>
                                                <td>
                                                    {% if payment.is_overdue %}
                                                    <span class="status-tag-modern is-danger">已逾期</span>
                                                    {% else %}
                                                    <span class="status-tag-modern is-warning">即將到期</span>
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% else %}
                                            <tr>
                                                <td colspan="5" class="has-text-centered">無待處理帳款</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 設置當前日期和最後更新時間
            const now = new Date();
            const dateOptions = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
            const timeOptions = { hour: '2-digit', minute: '2-digit' };

            document.getElementById('current-date').textContent = now.toLocaleDateString('zh-TW', dateOptions);
            document.getElementById('last-updated').textContent = now.toLocaleTimeString('zh-TW', timeOptions);

            // 初始化收入/支出趨勢圖
            const ctx = document.getElementById('income-expense-chart').getContext('2d');

            // 假設的數據 - 實際應用中應從後端獲取
            const chartData = {
                labels: {{ chart_labels| safe |default ('[]')
        }},
            incomeData: {{ chart_income_data| safe |default('[]') }},
            expenseData: {{ chart_expense_data| safe |default('[]') }}
            };

        const incomeExpenseChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: chartData.labels,
                datasets: [
                    {
                        label: '收入',
                        data: chartData.incomeData,
                        borderColor: '#48c774',
                        backgroundColor: 'rgba(72, 199, 116, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '支出',
                        data: chartData.expenseData,
                        borderColor: '#f14668',
                        backgroundColor: 'rgba(241, 70, 104, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function (value) {
                                return 'NT$ ' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // 切換趨勢圖時間範圍
        document.getElementById('trend-period').addEventListener('change', function () {
            // 實際應用中應從後端獲取新數據
            // 這裡僅作為示例
            const period = this.value;
            // 向後端請求數據
            fetch(`/api/income_expense_trend?period=${period}`)
                .then(response => response.json())
                .then(data => {
                    // 更新圖表數據
                    incomeExpenseChart.data.labels = data.labels;
                    incomeExpenseChart.data.datasets[0].data = data.income_data;
                    incomeExpenseChart.data.datasets[1].data = data.expense_data;
                    incomeExpenseChart.update();
                })
                .catch(error => console.error('獲取趨勢數據失敗:', error));
        });
        });
        
        // 深色模式切換功能
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');
            const currentTheme = body.getAttribute('data-theme');
            
            if (currentTheme === 'dark') {
                body.removeAttribute('data-theme');
                themeIcon.className = 'fas fa-moon';
                localStorage.setItem('theme', 'light');
            } else {
                body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'fas fa-sun';
                localStorage.setItem('theme', 'dark');
            }
        }
        
        // 載入保存的主題
        function loadSavedTheme() {
            const savedTheme = localStorage.getItem('theme');
            const themeIcon = document.getElementById('theme-icon');
            
            if (savedTheme === 'dark') {
                document.body.setAttribute('data-theme', 'dark');
                themeIcon.className = 'fas fa-sun';
            }
        }
        
        // 數值動畫效果
        function animateCounters() {
            const counters = document.querySelectorAll('.animated-counter');
            
            counters.forEach(counter => {
                const target = counter.innerText.replace(/[^\d.-]/g, '');
                const targetNumber = parseFloat(target) || 0;
                const duration = 2000; // 2秒動畫
                const step = targetNumber / (duration / 16); // 60fps
                let current = 0;
                let increment = step;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (Math.abs(current) >= Math.abs(targetNumber)) {
                        current = targetNumber;
                        clearInterval(timer);
                    }
                    
                    const formattedValue = counter.innerText.replace(target, Math.floor(current).toLocaleString());
                    counter.innerText = formattedValue;
                }, 16);
            });
        }
        
        // 載入完成後執行
        loadSavedTheme();
        setTimeout(animateCounters, 500); // 延遲500ms開始數值動畫
        
        // 為所有按鈕添加波紋效果
        document.addEventListener('click', function(e) {
            if (e.target.closest('.btn-gradient-primary, .summary-card-enhanced, .modern-card')) {
                const element = e.target.closest('.btn-gradient-primary, .summary-card-enhanced, .modern-card');
                
                // 創建波紋效果
                const ripple = document.createElement('div');
                ripple.style.position = 'absolute';
                ripple.style.borderRadius = '50%';
                ripple.style.background = 'rgba(255, 255, 255, 0.3)';
                ripple.style.pointerEvents = 'none';
                ripple.style.transform = 'scale(0)';
                ripple.style.animation = 'ripple 0.6s linear';
                ripple.style.left = (e.clientX - element.getBoundingClientRect().left - 10) + 'px';
                ripple.style.top = (e.clientY - element.getBoundingClientRect().top - 10) + 'px';
                ripple.style.width = ripple.style.height = '20px';
                
                if (element.style.position !== 'relative') {
                    element.style.position = 'relative';
                }
                element.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }
        });
    </script>
    
    <style>
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
</body>

</html>