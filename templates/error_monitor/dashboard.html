<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>錯誤監控儀表板 | 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-card {
            height: 100%;
            transition: transform 0.2s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .error-item {
            border-left: 4px solid #ff6b6b;
            padding-left: 1rem;
            margin-bottom: 0.5rem;
        }

        .refresh-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
        }
    </style>
</head>

<body>
    <div class="container is-fluid" style="padding: 2rem;">
        <h1 class="title has-text-centered">印錢大師</h1>
        <!-- 標題區域 -->
        <div class="level">
            <div class="level-left">
                <div class="level-item">
                    <h1 class="title is-3">
                        <span class="icon-text">
                            <span class="icon has-text-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                            </span>
                            <span>錯誤監控儀表板</span>
                        </span>
                    </h1>
                </div>
            </div>
            <div class="level-right">
                <div class="level-item">
                    <div class="field has-addons">
                        <div class="control">
                            <button class="button is-info" onclick="refreshData()">
                                <span class="icon">
                                    <i class="fas fa-sync-alt"></i>
                                </span>
                                <span>刷新數據</span>
                            </button>
                        </div>
                        <div class="control">
                            <button class="button is-warning" onclick="exportReport()">
                                <span class="icon">
                                    <i class="fas fa-download"></i>
                                </span>
                                <span>導出報告</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 統計卡片 -->
        <div class="columns">
            <div class="column is-3">
                <div class="card dashboard-card has-background-danger-light">
                    <div class="card-content has-text-centered">
                        <div class="stat-number has-text-danger" id="totalErrors">-</div>
                        <p class="subtitle is-6">總錯誤數</p>
                    </div>
                </div>
            </div>
            <div class="column is-3">
                <div class="card dashboard-card has-background-warning-light">
                    <div class="card-content has-text-centered">
                        <div class="stat-number has-text-warning" id="errors24h">-</div>
                        <p class="subtitle is-6">24小時錯誤</p>
                    </div>
                </div>
            </div>
            <div class="column is-3">
                <div class="card dashboard-card has-background-info-light">
                    <div class="card-content has-text-centered">
                        <div class="stat-number has-text-info" id="errors1h">-</div>
                        <p class="subtitle is-6">1小時錯誤</p>
                    </div>
                </div>
            </div>
            <div class="column is-3">
                <div class="card dashboard-card has-background-success-light">
                    <div class="card-content has-text-centered">
                        <div class="stat-number has-text-success" id="errorRate">-</div>
                        <p class="subtitle is-6">錯誤率/小時</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 圖表區域 -->
        <div class="columns">
            <div class="column is-8">
                <div class="card">
                    <div class="card-header">
                        <p class="card-header-title">錯誤趨勢圖</p>
                        <div class="card-header-icon">
                            <div class="select is-small">
                                <select id="trendHours" onchange="updateTrends()">
                                    <option value="24">24小時</option>
                                    <option value="48">48小時</option>
                                    <option value="168">7天</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="chart-container">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="column is-4">
                <div class="card">
                    <div class="card-header">
                        <p class="card-header-title">錯誤類型分布</p>
                    </div>
                    <div class="card-content">
                        <div class="chart-container">
                            <canvas id="typeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近錯誤列表 -->
        <div class="columns">
            <div class="column">
                <div class="card">
                    <div class="card-header">
                        <p class="card-header-title">最近錯誤</p>
                        <div class="card-header-icon">
                            <button class="button is-small is-outlined" onclick="viewAllErrors()">
                                查看全部
                            </button>
                        </div>
                    </div>
                    <div class="card-content">
                        <div id="recentErrors">
                            <div class="has-text-centered">
                                <span class="icon is-large">
                                    <i class="fas fa-spinner fa-pulse"></i>
                                </span>
                                <p>載入中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 刷新按鈕 -->
    <button class="button is-primary is-large refresh-btn" onclick="refreshData()" title="刷新數據">
        <span class="icon">
            <i class="fas fa-sync-alt"></i>
        </span>
    </button>

    <script>
        let trendChart = null;
        let typeChart = null;

        // 初始化頁面
        document.addEventListener('DOMContentLoaded', function () {
            refreshData();
            setInterval(refreshData, 30000); // 每30秒自動刷新
        });

        async function refreshData() {
            try {
                // 獲取錯誤摘要
                const summaryResponse = await fetch('/admin/errors/api/summary');
                const summaryData = await summaryResponse.json();

                if (summaryData.success) {
                    updateSummaryCards(summaryData.data);
                    updateRecentErrors(summaryData.data.recent_errors);
                    updateTypeChart(summaryData.data.error_types);
                }

                // 獲取趨勢數據
                await updateTrends();

            } catch (error) {
                console.error('刷新數據失敗:', error);
                showNotification('刷新數據失敗', 'is-danger');
            }
        }

        function updateSummaryCards(data) {
            document.getElementById('totalErrors').textContent = data.total_errors || 0;
            document.getElementById('errors24h').textContent = data.errors_24h || 0;
            document.getElementById('errors1h').textContent = data.errors_1h || 0;
            document.getElementById('errorRate').textContent = (data.error_rate_24h || 0).toFixed(1);
        }

        function updateRecentErrors(errors) {
            const container = document.getElementById('recentErrors');

            if (!errors || errors.length === 0) {
                container.innerHTML = '<p class="has-text-grey">暫無錯誤記錄</p>';
                return;
            }

            const html = errors.map(error => `
                <div class="error-item">
                    <div class="level is-mobile">
                        <div class="level-left">
                            <div class="level-item">
                                <div>
                                    <p class="has-text-weight-semibold">${error.error_type}</p>
                                    <p class="is-size-7 has-text-grey">${error.message}</p>
                                </div>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <span class="tag is-light is-small">
                                    ${new Date(error.timestamp).toLocaleString('zh-TW')}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        async function updateTrends() {
            const hours = document.getElementById('trendHours').value;

            try {
                const response = await fetch(`/admin/errors/api/trends?hours=${hours}`);
                const data = await response.json();

                if (data.success) {
                    updateTrendChart(data.data.time_series);
                }
            } catch (error) {
                console.error('更新趨勢圖失敗:', error);
            }
        }

        function updateTrendChart(timeSeriesData) {
            const ctx = document.getElementById('trendChart').getContext('2d');

            if (trendChart) {
                trendChart.destroy();
            }

            trendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: timeSeriesData.map(item => new Date(item.timestamp).toLocaleString('zh-TW', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit'
                    })),
                    datasets: [{
                        label: '錯誤數量',
                        data: timeSeriesData.map(item => item.count),
                        borderColor: '#ff6b6b',
                        backgroundColor: 'rgba(255, 107, 107, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function updateTypeChart(errorTypes) {
            const ctx = document.getElementById('typeChart').getContext('2d');

            if (typeChart) {
                typeChart.destroy();
            }

            const labels = Object.keys(errorTypes);
            const data = Object.values(errorTypes);

            if (labels.length === 0) {
                return;
            }

            typeChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
                            '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function exportReport() {
            window.open('/admin/errors/api/export', '_blank');
        }

        function viewAllErrors() {
            window.location.href = '/admin/errors/list';
        }

        function showNotification(message, type = 'is-info') {
            // 簡單的通知實現
            const notification = document.createElement('div');
            notification.className = `notification ${type} is-light`;
            notification.innerHTML = `
                <button class="delete" onclick="this.parentElement.remove()"></button>
                ${message}
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>

</html>