<!DOCTYPE html>\n<html lang=\"zh-TW\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>性能監控儀表板</title>\n    \n    <!-- <PERSON>ulma CSS -->\n    <link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css\">\n    <!-- Font Awesome -->\n    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n    <!-- Chart.js -->\n    <script src=\"https://cdn.jsdelivr.net/npm/chart.js\"></script>\n    \n    <style>\n        .metric-card {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n        }\n        \n        .chart-container {\n            position: relative;\n            height: 300px;\n            margin: 20px 0;\n        }\n        \n        .alert-item {\n            border-left: 4px solid #ff3860;\n            padding-left: 15px;\n        }\n        \n        .alert-item.warning {\n            border-left-color: #ffdd57;\n        }\n        \n        .alert-item.success {\n            border-left-color: #48c774;\n        }\n        \n        .real-time-indicator {\n            display: inline-block;\n            width: 10px;\n            height: 10px;\n            border-radius: 50%;\n            background-color: #48c774;\n            animation: pulse 2s infinite;\n        }\n        \n        @keyframes pulse {\n            0% { opacity: 1; }\n            50% { opacity: 0.5; }\n            100% { opacity: 1; }\n        }\n        \n        .refresh-btn {\n            position: fixed;\n            bottom: 20px;\n            right: 20px;\n            z-index: 999;\n        }\n    </style>\n</head>\n<body>\n    <nav class=\"navbar is-dark\" role=\"navigation\">\n        <div class=\"navbar-brand\">\n            <a class=\"navbar-item\" href=\"/\">\n                <i class=\"fas fa-chart-line\"></i>\n                <span class=\"ml-2\">性能監控儀表板</span>\n            </a>\n        </div>\n        \n        <div class=\"navbar-menu\">\n            <div class=\"navbar-end\">\n                <div class=\"navbar-item\">\n                    <span class=\"real-time-indicator\"></span>\n                    <span class=\"ml-2\">即時監控中</span>\n                </div>\n                <div class=\"navbar-item\">\n                    <button class=\"button is-primary\" onclick=\"exportMetrics()\">\n                        <i class=\"fas fa-download\"></i>\n                        <span>導出數據</span>\n                    </button>\n                </div>\n            </div>\n        </div>\n    </nav>

    <h1 class="title has-text-centered mt-4">印錢大師</h1>\n\n    <div class=\"container is-fluid mt-4\">\n        <!-- 概覽卡片 -->\n        <div class=\"columns\">\n            <div class=\"column\">\n                <div class=\"card metric-card\">\n                    <div class=\"card-content has-text-centered\">\n                        <div class=\"title is-4 has-text-white\">\n                            <span id=\"avg-response-time\">載入中...</span>ms\n                        </div>\n                        <div class=\"subtitle is-6 has-text-white-bis\">\n                            平均響應時間\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"column\">\n                <div class=\"card metric-card\">\n                    <div class=\"card-content has-text-centered\">\n                        <div class=\"title is-4 has-text-white\">\n                            <span id=\"total-requests\">載入中...</span>\n                        </div>\n                        <div class=\"subtitle is-6 has-text-white-bis\">\n                            總請求數\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"column\">\n                <div class=\"card metric-card\">\n                    <div class=\"card-content has-text-centered\">\n                        <div class=\"title is-4 has-text-white\">\n                            <span id=\"memory-usage\">載入中...</span>MB\n                        </div>\n                        <div class=\"subtitle is-6 has-text-white-bis\">\n                            記憶體使用\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"column\">\n                <div class=\"card metric-card\">\n                    <div class=\"card-content has-text-centered\">\n                        <div class=\"title is-4 has-text-white\">\n                            <span id=\"pool-utilization\">載入中...</span>%\n                        </div>\n                        <div class=\"subtitle is-6 has-text-white-bis\">\n                            連接池使用率\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div class=\"columns\">\n            <!-- API 性能圖表 -->\n            <div class=\"column is-half\">\n                <div class=\"card\">\n                    <header class=\"card-header\">\n                        <p class=\"card-header-title\">\n                            <i class=\"fas fa-tachometer-alt mr-2\"></i>\n                            API 響應時間趨勢\n                        </p>\n                    </header>\n                    <div class=\"card-content\">\n                        <div class=\"chart-container\">\n                            <canvas id=\"apiResponseChart\"></canvas>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- 系統資源圖表 -->\n            <div class=\"column is-half\">\n                <div class=\"card\">\n                    <header class=\"card-header\">\n                        <p class=\"card-header-title\">\n                            <i class=\"fas fa-server mr-2\"></i>\n                            系統資源使用\n                        </p>\n                    </header>\n                    <div class=\"card-content\">\n                        <div class=\"chart-container\">\n                            <canvas id=\"systemResourceChart\"></canvas>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <div class=\"columns\">\n            <!-- 慢查詢列表 -->\n            <div class=\"column is-half\">\n                <div class=\"card\">\n                    <header class=\"card-header\">\n                        <p class=\"card-header-title\">\n                            <i class=\"fas fa-exclamation-triangle mr-2\"></i>\n                            慢查詢監控\n                        </p>\n                    </header>\n                    <div class=\"card-content\">\n                        <div id=\"slow-queries-list\">\n                            載入中...\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- 優化建議 -->\n            <div class=\"column is-half\">\n                <div class=\"card\">\n                    <header class=\"card-header\">\n                        <p class=\"card-header-title\">\n                            <i class=\"fas fa-lightbulb mr-2\"></i>\n                            優化建議\n                        </p>\n                    </header>\n                    <div class=\"card-content\">\n                        <div id=\"optimization-suggestions\">\n                            載入中...\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- 快取統計 -->\n        <div class=\"card\">\n            <header class=\"card-header\">\n                <p class=\"card-header-title\">\n                    <i class=\"fas fa-memory mr-2\"></i>\n                    快取統計\n                </p>\n                <div class=\"card-header-icon\">\n                    <button class=\"button is-small\" onclick=\"clearCache()\">\n                        <i class=\"fas fa-trash\"></i>\n                        <span>清除快取</span>\n                    </button>\n                </div>\n            </header>\n            <div class=\"card-content\">\n                <div class=\"columns\">\n                    <div class=\"column\">\n                        <div class=\"chart-container\" style=\"height: 200px;\">\n                            <canvas id=\"cacheHitRateChart\"></canvas>\n                        </div>\n                    </div>\n                    <div class=\"column\">\n                        <div id=\"cache-stats-details\">\n                            載入中...\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- 浮動刷新按鈕 -->\n    <button class=\"button is-primary is-large refresh-btn\" onclick=\"refreshAll()\">\n        <i class=\"fas fa-sync-alt\"></i>\n    </button>\n\n    <script>\n        // 全局變數\n        let charts = {};\n        let realtimeData = {\n            timestamps: [],\n            apiResponseTimes: [],\n            memoryUsage: [],\n            cpuUsage: []\n        };\n        \n        // 初始化\n        document.addEventListener('DOMContentLoaded', function() {\n            initializeCharts();\n            loadInitialData();\n            startRealTimeUpdates();\n        });\n        \n        // 初始化圖表\n        function initializeCharts() {\n            // API 響應時間圖表\n            const apiCtx = document.getElementById('apiResponseChart').getContext('2d');\n            charts.apiResponse = new Chart(apiCtx, {\n                type: 'line',\n                data: {\n                    labels: [],\n                    datasets: [{\n                        label: '平均響應時間 (ms)',\n                        data: [],\n                        borderColor: '#3273dc',\n                        backgroundColor: 'rgba(50, 115, 220, 0.1)',\n                        fill: true\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    scales: {\n                        y: {\n                            beginAtZero: true,\n                            title: {\n                                display: true,\n                                text: '響應時間 (ms)'\n                            }\n                        }\n                    }\n                }\n            });\n            \n            // 系統資源圖表\n            const systemCtx = document.getElementById('systemResourceChart').getContext('2d');\n            charts.systemResource = new Chart(systemCtx, {\n                type: 'doughnut',\n                data: {\n                    labels: ['已使用記憶體', '可用記憶體'],\n                    datasets: [{\n                        data: [0, 100],\n                        backgroundColor: ['#ff3860', '#48c774']\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false\n                }\n            });\n            \n            // 快取命中率圖表\n            const cacheCtx = document.getElementById('cacheHitRateChart').getContext('2d');\n            charts.cacheHitRate = new Chart(cacheCtx, {\n                type: 'bar',\n                data: {\n                    labels: ['命中', '未命中'],\n                    datasets: [{\n                        label: '快取統計',\n                        data: [0, 0],\n                        backgroundColor: ['#48c774', '#ff3860']\n                    }]\n                },\n                options: {\n                    responsive: true,\n                    maintainAspectRatio: false,\n                    scales: {\n                        y: {\n                            beginAtZero: true\n                        }\n                    }\n                }\n            });\n        }\n        \n        // 載入初始數據\n        async function loadInitialData() {\n            try {\n                await Promise.all([\n                    loadPerformanceOverview(),\n                    loadOptimizationSuggestions(),\n                    loadCacheStats(),\n                    loadSlowQueries()\n                ]);\n            } catch (error) {\n                console.error('載入初始數據失敗:', error);\n            }\n        }\n        \n        // 載入性能概覽\n        async function loadPerformanceOverview() {\n            try {\n                const response = await fetch('/api/performance/overview');\n                const result = await response.json();\n                \n                if (result.success) {\n                    const data = result.data;\n                    \n                    // 更新概覽卡片\n                    document.getElementById('avg-response-time').textContent = \n                        (data.api_performance.overall_stats?.avg_response_time * 1000).toFixed(0) || 'N/A';\n                    document.getElementById('total-requests').textContent = \n                        data.api_performance.total_requests || 0;\n                    document.getElementById('memory-usage').textContent = \n                        data.memory_usage.current_memory_mb?.toFixed(0) || 'N/A';\n                    document.getElementById('pool-utilization').textContent = \n                        ((data.connection_pool.metrics?.utilization || 0) * 100).toFixed(0);\n                    \n                    // 更新系統資源圖表\n                    if (data.memory_usage.system_memory_percent) {\n                        const memoryPercent = data.memory_usage.system_memory_percent;\n                        charts.systemResource.data.datasets[0].data = [memoryPercent, 100 - memoryPercent];\n                        charts.systemResource.update();\n                    }\n                }\n            } catch (error) {\n                console.error('載入性能概覽失敗:', error);\n            }\n        }\n        \n        // 載入優化建議\n        async function loadOptimizationSuggestions() {\n            try {\n                const response = await fetch('/api/performance/optimization-suggestions');\n                const result = await response.json();\n                \n                if (result.success) {\n                    const suggestions = result.data.suggestions;\n                    const container = document.getElementById('optimization-suggestions');\n                    \n                    if (suggestions.length === 0) {\n                        container.innerHTML = '<div class=\"has-text-success\"><i class=\"fas fa-check-circle\"></i> 目前沒有優化建議</div>';\n                    } else {\n                        container.innerHTML = suggestions.map(s => \n                            `<div class=\"alert-item ${s.severity} mb-3\">\n                                <strong>${s.category}</strong>: ${s.message}\n                                <br><small class=\"has-text-grey\">建議動作: ${s.action}</small>\n                            </div>`\n                        ).join('');\n                    }\n                }\n            } catch (error) {\n                console.error('載入優化建議失敗:', error);\n            }\n        }\n        \n        // 載入快取統計\n        async function loadCacheStats() {\n            try {\n                const response = await fetch('/api/performance/cache-stats');\n                const result = await response.json();\n                \n                if (result.success) {\n                    const data = result.data;\n                    \n                    // 更新快取命中率圖表\n                    if (data.basic_cache) {\n                        charts.cacheHitRate.data.datasets[0].data = [\n                            data.basic_cache.hits || 0,\n                            data.basic_cache.misses || 0\n                        ];\n                        charts.cacheHitRate.update();\n                    }\n                    \n                    // 更新快取詳細統計\n                    const detailsContainer = document.getElementById('cache-stats-details');\n                    detailsContainer.innerHTML = `\n                        <div class=\"content\">\n                            <h6>基本快取</h6>\n                            <p>命中率: ${data.basic_cache?.hit_rate || 0}%</p>\n                            <p>快取大小: ${data.basic_cache?.size || 0}</p>\n                            \n                            <h6>分層快取</h6>\n                            <p>L1 命中: ${data.tiered_cache?.l1_hits || 0}</p>\n                            <p>L2 命中: ${data.tiered_cache?.l2_hits || 0}</p>\n                            \n                            <h6>熱門快取鍵</h6>\n                            <ul>\n                                ${(data.popular_keys || []).slice(0, 5).map(key => `<li>${key}</li>`).join('')}\n                            </ul>\n                        </div>\n                    `;\n                }\n            } catch (error) {\n                console.error('載入快取統計失敗:', error);\n            }\n        }\n        \n        // 載入慢查詢\n        async function loadSlowQueries() {\n            try {\n                const response = await fetch('/api/performance/query-analysis');\n                const result = await response.json();\n                \n                if (result.success && result.data.performance_summary) {\n                    const slowQueries = result.data.performance_summary.slow_requests || [];\n                    const container = document.getElementById('slow-queries-list');\n                    \n                    if (slowQueries.length === 0) {\n                        container.innerHTML = '<div class=\"has-text-success\"><i class=\"fas fa-check-circle\"></i> 沒有發現慢查詢</div>';\n                    } else {\n                        container.innerHTML = slowQueries.slice(0, 5).map(query => \n                            `<div class=\"alert-item mb-3\">\n                                <strong>${query.endpoint}</strong> (${query.method})\n                                <br><small>響應時間: ${(query.response_time * 1000).toFixed(0)}ms</small>\n                                <br><small class=\"has-text-grey\">${query.timestamp}</small>\n                            </div>`\n                        ).join('');\n                    }\n                }\n            } catch (error) {\n                console.error('載入慢查詢失敗:', error);\n            }\n        }\n        \n        // 開始即時更新\n        function startRealTimeUpdates() {\n            setInterval(async () => {\n                try {\n                    const response = await fetch('/api/performance/realtime-stats');\n                    const result = await response.json();\n                    \n                    if (result.success) {\n                        const data = result.data;\n                        const timestamp = new Date(data.timestamp);\n                        \n                        // 更新即時數據\n                        realtimeData.timestamps.push(timestamp.toLocaleTimeString());\n                        realtimeData.memoryUsage.push(data.memory.usage_mb);\n                        \n                        // 保持最近20個數據點\n                        if (realtimeData.timestamps.length > 20) {\n                            realtimeData.timestamps.shift();\n                            realtimeData.memoryUsage.shift();\n                        }\n                        \n                        // 更新記憶體卡片\n                        document.getElementById('memory-usage').textContent = \n                            data.memory.usage_mb.toFixed(0);\n                        document.getElementById('pool-utilization').textContent = \n                            (data.connection_pool.utilization * 100).toFixed(0);\n                    }\n                } catch (error) {\n                    console.error('即時更新失敗:', error);\n                }\n            }, 10000); // 每10秒更新一次\n        }\n        \n        // 刷新所有數據\n        async function refreshAll() {\n            const refreshBtn = document.querySelector('.refresh-btn i');\n            refreshBtn.classList.add('fa-spin');\n            \n            try {\n                await loadInitialData();\n            } finally {\n                refreshBtn.classList.remove('fa-spin');\n            }\n        }\n        \n        // 清除快取\n        async function clearCache() {\n            try {\n                const response = await fetch('/api/performance/clear-cache');\n                const result = await response.json();\n                \n                if (result.success) {\n                    alert('快取已清除');\n                    await loadCacheStats();\n                } else {\n                    alert('清除快取失敗: ' + result.error);\n                }\n            } catch (error) {\n                alert('清除快取失敗: ' + error.message);\n            }\n        }\n        \n        // 導出指標\n        async function exportMetrics() {\n            try {\n                const response = await fetch('/api/performance/export?format=json');\n                const result = await response.json();\n                \n                if (result.success) {\n                    const dataStr = JSON.stringify(result.data, null, 2);\n                    const dataBlob = new Blob([dataStr], {type: 'application/json'});\n                    const url = URL.createObjectURL(dataBlob);\n                    const link = document.createElement('a');\n                    link.href = url;\n                    link.download = `performance_metrics_${new Date().toISOString().slice(0, 19)}.json`;\n                    link.click();\n                    URL.revokeObjectURL(url);\n                }\n            } catch (error) {\n                alert('導出失敗: ' + error.message);\n            }\n        }\n    </script>\n</body>\n</html>"