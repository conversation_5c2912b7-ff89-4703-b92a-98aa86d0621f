<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>專案/部門別分析</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar {
            width: 200px;
            font-size: 20px;
        }
        .main-content {
            margin-top: 20px;
        }
        .analysis-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #3273dc;
        }
        .metric-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }
        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .data-table {
            margin-top: 20px;
        }
        .positive {
            color: #28a745;
        }
        .negative {
            color: #dc3545;
        }
        .tab-content {
            display: none;
        }
        .tab-content.is-active {
            display: block;
        }
        .department-card, .project-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: box-shadow 0.3s;
        }
        .department-card:hover, .project-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .progress-bar {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="main-content">
                    <!-- 導航 -->
                    <div class="box mb-5">
                        <h2 class="subtitle">
                            <a href="javascript:history.back()" style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                            <i class="fas fa-sitemap"></i> 專案/部門別分析
                        </h2>
                    </div>

                    <!-- 篩選條件 -->
                    <div class="filter-section">
                        <div class="columns">
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">分析類型</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="analysisType" onchange="switchAnalysisType()">
                                                <option value="department">部門別分析</option>
                                                <option value="project">專案別分析</option>
                                                <option value="both">綜合分析</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">起始日期</label>
                                    <div class="control">
                                        <input class="input" type="date" id="startDate" value="2024-01-01">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">結束日期</label>
                                    <div class="control">
                                        <input class="input" type="date" id="endDate" value="2024-12-31">
                                    </div>
                                </div>
                            </div>
                            <div class="column is-3">
                                <div class="field">
                                    <label class="label">&nbsp;</label>
                                    <div class="control">
                                        <button class="button is-primary is-fullwidth" onclick="generateAnalysis()">
                                            <span class="icon"><i class="fas fa-chart-bar"></i></span>
                                            <span>生成分析</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 總覽指標 -->
                    <div class="analysis-header">
                        <h3 class="title is-4 has-text-white">總覽指標</h3>
                        <div class="columns">
                            <div class="column">
                                <div class="metric-card">
                                    <div class="metric-label">活躍部門數</div>
                                    <div class="metric-value" id="activeDepartments">0</div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="metric-card">
                                    <div class="metric-label">進行中專案</div>
                                    <div class="metric-value" id="activeProjects">0</div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="metric-card">
                                    <div class="metric-label">總收入</div>
                                    <div class="metric-value positive" id="totalRevenue">NT$ 0</div>
                                </div>
                            </div>
                            <div class="column">
                                <div class="metric-card">
                                    <div class="metric-label">總支出</div>
                                    <div class="metric-value negative" id="totalExpense">NT$ 0</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分析標籤 -->
                    <div class="tabs is-boxed">
                        <ul>
                            <li class="is-active" id="departmentTab">
                                <a onclick="showTab('department')">
                                    <span class="icon is-small"><i class="fas fa-building"></i></span>
                                    <span>部門分析</span>
                                </a>
                            </li>
                            <li id="projectTab">
                                <a onclick="showTab('project')">
                                    <span class="icon is-small"><i class="fas fa-project-diagram"></i></span>
                                    <span>專案分析</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 部門分析內容 -->
                    <div id="departmentContent" class="tab-content is-active">
                        <div class="columns">
                            <div class="column is-half">
                                <div class="box">
                                    <h4 class="subtitle">部門收支分布</h4>
                                    <div class="chart-container">
                                        <canvas id="departmentChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-half">
                                <div class="box">
                                    <h4 class="subtitle">部門績效排名</h4>
                                    <div id="departmentRanking">
                                        <!-- 動態載入部門排名 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 部門詳細資料 -->
                        <div class="box">
                            <h4 class="subtitle">
                                <i class="fas fa-table"></i> 部門明細
                            </h4>
                            <div class="table-container data-table">
                                <table class="table is-fullwidth is-striped is-hoverable">
                                    <thead>
                                        <tr class="has-background-primary has-text-white">
                                            <th>部門名稱</th>
                                            <th>收入</th>
                                            <th>支出</th>
                                            <th>淨利潤</th>
                                            <th>利潤率</th>
                                            <th>預算執行率</th>
                                        </tr>
                                    </thead>
                                    <tbody id="departmentTable">
                                        <!-- 動態載入內容 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 專案分析內容 -->
                    <div id="projectContent" class="tab-content">
                        <div class="columns">
                            <div class="column is-half">
                                <div class="box">
                                    <h4 class="subtitle">專案收支分布</h4>
                                    <div class="chart-container">
                                        <canvas id="projectChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-half">
                                <div class="box">
                                    <h4 class="subtitle">專案進度概覽</h4>
                                    <div id="projectProgress">
                                        <!-- 動態載入專案進度 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 專案詳細資料 -->
                        <div class="box">
                            <h4 class="subtitle">
                                <i class="fas fa-table"></i> 專案明細
                            </h4>
                            <div class="table-container data-table">
                                <table class="table is-fullwidth is-striped is-hoverable">
                                    <thead>
                                        <tr class="has-background-primary has-text-white">
                                            <th>專案名稱</th>
                                            <th>負責部門</th>
                                            <th>預算</th>
                                            <th>實際成本</th>
                                            <th>進度</th>
                                            <th>狀態</th>
                                        </tr>
                                    </thead>
                                    <tbody id="projectTable">
                                        <!-- 動態載入內容 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let departmentChart = null;
        let projectChart = null;

        // 從後端傳來的真實資料
        let realData = null;
        {% if analysis_data %}
        realData = {{ analysis_data | tojson }};
        {% endif %}

        // 切換分析類型
        function switchAnalysisType() {
            const type = document.getElementById('analysisType').value;
            if (type === 'department') {
                showTab('department');
            } else if (type === 'project') {
                showTab('project');
            }
        }

        // 切換標籤
        function showTab(tabName) {
            // 隱藏所有內容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('is-active');
            });
            
            // 移除所有標籤的活躍狀態
            document.querySelectorAll('.tabs li').forEach(tab => {
                tab.classList.remove('is-active');
            });
            
            // 顯示選中的內容和標籤
            document.getElementById(tabName + 'Content').classList.add('is-active');
            document.getElementById(tabName + 'Tab').classList.add('is-active');
        }

        // 生成分析
        function generateAnalysis() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!startDate || !endDate) {
                alert('請選擇分析期間');
                return;
            }

            loadAnalysisData(startDate, endDate);
        }

        // 載入分析資料
        function loadAnalysisData(startDate, endDate) {
            // 使用後端真實資料或預設資料
            const data = realData || {
                overview: {
                    active_departments: 0,
                    active_projects: 0,
                    total_revenue: 0,
                    total_expense: 0
                },
                departments: [],
                projects: []
            };

            updateOverview(data.overview);
            updateDepartmentAnalysis(data.departments);
            updateProjectAnalysis(data.projects);
        }

        // 更新總覽指標
        function updateOverview(overview) {
            document.getElementById('activeDepartments').textContent = overview.active_departments;
            document.getElementById('activeProjects').textContent = overview.active_projects;
            document.getElementById('totalRevenue').textContent = `NT$ ${overview.total_revenue.toLocaleString()}`;
            document.getElementById('totalExpense').textContent = `NT$ ${overview.total_expense.toLocaleString()}`;
        }

        // 更新部門分析
        function updateDepartmentAnalysis(departments) {
            if (!departments || departments.length === 0) {
                // 如果沒有部門資料，顯示空狀態
                document.getElementById('departmentRanking').innerHTML = '<div class="notification is-info">目前沒有部門資料</div>';
                document.getElementById('departmentTable').innerHTML = '<tr><td colspan="6" class="has-text-centered">無資料</td></tr>';
                return;
            }

            // 更新圖表
            const deptCtx = document.getElementById('departmentChart').getContext('2d');
            if (departmentChart) departmentChart.destroy();
            
            departmentChart = new Chart(deptCtx, {
                type: 'bar',
                data: {
                    labels: departments.map(d => d.name),
                    datasets: [{
                        label: '收入',
                        data: departments.map(d => d.revenue),
                        backgroundColor: '#28a745'
                    }, {
                        label: '支出',
                        data: departments.map(d => d.expense),
                        backgroundColor: '#dc3545'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'NT$ ' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });

            // 更新排名
            const ranking = document.getElementById('departmentRanking');
            const sortedDepts = [...departments].sort((a, b) => b.profit - a.profit);
            ranking.innerHTML = sortedDepts.map((dept, index) => `
                <div class="department-card">
                    <div class="level">
                        <div class="level-left">
                            <div class="level-item">
                                <span class="tag is-primary">${index + 1}</span>
                                <strong style="margin-left: 10px;">${dept.name}</strong>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <span class="${dept.profit >= 0 ? 'positive' : 'negative'}">NT$ ${dept.profit.toLocaleString()}</span>
                            </div>
                        </div>
                    </div>
                    <progress class="progress is-primary" value="${Math.abs(dept.margin)}" max="100">${dept.margin}%</progress>
                </div>
            `).join('');

            // 更新表格
            const tbody = document.getElementById('departmentTable');
            tbody.innerHTML = departments.map(dept => `
                <tr>
                    <td><strong>${dept.name}</strong></td>
                    <td class="positive">NT$ ${dept.revenue.toLocaleString()}</td>
                    <td class="negative">NT$ ${dept.expense.toLocaleString()}</td>
                    <td class="${dept.profit >= 0 ? 'positive' : 'negative'}">NT$ ${dept.profit.toLocaleString()}</td>
                    <td>${dept.margin.toFixed ? dept.margin.toFixed(1) : dept.margin}%</td>
                    <td>
                        <progress class="progress is-small ${dept.budget_rate >= 90 ? 'is-success' : dept.budget_rate >= 80 ? 'is-warning' : 'is-danger'}" 
                                  value="${dept.budget_rate}" max="100">${dept.budget_rate}%</progress>
                    </td>
                </tr>
            `).join('');
        }

        // 更新專案分析
        function updateProjectAnalysis(projects) {
            if (!projects || projects.length === 0) {
                // 如果沒有專案資料，顯示空狀態
                document.getElementById('projectProgress').innerHTML = '<div class="notification is-info">目前沒有專案資料</div>';
                document.getElementById('projectTable').innerHTML = '<tr><td colspan="6" class="has-text-centered">無資料</td></tr>';
                return;
            }

            // 更新圖表
            const projCtx = document.getElementById('projectChart').getContext('2d');
            if (projectChart) projectChart.destroy();
            
            projectChart = new Chart(projCtx, {
                type: 'doughnut',
                data: {
                    labels: projects.map(p => p.name),
                    datasets: [{
                        data: projects.map(p => p.actual_cost || p.budget || 1),
                        backgroundColor: ['#3273dc', '#28a745', '#ffdd57', '#ff3860', '#00d1b2', '#ff6b35', '#7b68ee', '#20b2aa', '#ff69b4', '#32cd32']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 更新進度
            const progress = document.getElementById('projectProgress');
            progress.innerHTML = projects.map(project => `
                <div class="project-card">
                    <div class="level">
                        <div class="level-left">
                            <div class="level-item">
                                <strong>${project.name}</strong>
                            </div>
                        </div>
                        <div class="level-right">
                            <div class="level-item">
                                <span class="tag ${project.status === '已完成' ? 'is-success' : project.status === '進行中' ? 'is-info' : project.status === '暫停' ? 'is-warning' : 'is-danger'}">${project.status}</span>
                            </div>
                        </div>
                    </div>
                    <progress class="progress is-primary progress-bar" value="${project.progress}" max="100">${project.progress}%</progress>
                </div>
            `).join('');

            // 更新表格
            const tbody = document.getElementById('projectTable');
            tbody.innerHTML = projects.map(project => `
                <tr>
                    <td><strong>${project.name}</strong></td>
                    <td>${project.department}</td>
                    <td>NT$ ${project.budget.toLocaleString()}</td>
                    <td class="${project.actual_cost <= project.budget ? 'positive' : 'negative'}">NT$ ${project.actual_cost.toLocaleString()}</td>
                    <td>
                        <progress class="progress is-small is-primary" value="${project.progress}" max="100">${project.progress}%</progress>
                    </td>
                    <td>
                        <span class="tag ${project.status === '已完成' ? 'is-success' : project.status === '進行中' ? 'is-info' : project.status === '暫停' ? 'is-warning' : 'is-danger'}">${project.status}</span>
                    </td>
                </tr>
            `).join('');
        }

        // 頁面載入時自動載入資料
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const currentYear = today.getFullYear();
            document.getElementById('startDate').value = `${currentYear}-01-01`;
            document.getElementById('endDate').value = `${currentYear}-12-31`;
            
            // 自動載入真實資料
            loadAnalysisData();
        });
    </script>
</body>
</html>
