<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>對象類別管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .table thead th {
            background-color: #3273dc !important;
            color: white !important;
            font-weight: 600 !important;
            border-color: #3273dc !important;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/payment_identity_list"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        對象類別管理
                    </h1>
                </div>
                <div class="box">
                    <div class="level">
                        <div class="level-left">
                        </div>
                        <div class="level-right">
                            <button class="button is-warning is-rounded" id="addTypeBtn">
                                <span class="icon"><i class="fas fa-plus"></i></span>
                                <span>新增對象類別</span>
                            </button>
                        </div>
                    </div>

                    <table class="table is-fullwidth is-striped is-hoverable">
                        <thead>
                            <tr>
                                <th>類別名稱</th>
                                <th>描述</th>
                                <th>使用次數</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for type in types %}
                            <tr>
                                <td>{{ type.name }}</td>
                                <td>{{ type.description }}</td>
                                <td>{{ type.usage_count }}</td>
                                <td>
                                    <div class="buttons are-small">
                                        <button class="button is-info is-small edit-type-btn" data-id="{{ type.id }}"
                                            data-name="{{ type.name }}" data-description="{{ type.description }}">
                                            <span class="icon">
                                                <i class="fas fa-pen"></i>
                                            </span>
                                            <span>編輯</span>
                                        </button>
                                        {% if type.usage_count == 0 %}
                                        <button class="button is-danger is-small delete-type-btn"
                                            data-id="{{ type.id }}">
                                            <span class="icon">
                                                <i class="fas fa-trash"></i>
                                            </span>
                                            <span>刪除</span>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增對象類別的模態框 -->
    <div class="modal" id="typeModal">
        <div class="modal-background"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title" id="modalTitle">新增對象類別</p>
                <button class="delete" aria-label="close"></button>
            </header>
            <form id="typeForm" method="post">
                <input type="hidden" name="type_id" id="typeId">
                <section class="modal-card-body">
                    <div class="field">
                        <label class="label">類別名稱</label>
                        <div class="control">
                            <input class="input" type="text" name="name" id="typeName" required placeholder="請輸入類別名稱">
                        </div>
                    </div>
                    <div class="field">
                        <label class="label">描述</label>
                        <div class="control">
                            <textarea class="textarea" name="description" id="typeDescription"></textarea>
                        </div>
                    </div>
                </section>
                <footer class="modal-card-foot">
                    <button type="submit" class="button is-success">儲存</button>
                    <button type="button" class="button cancel-btn">取消</button>
                </footer>
            </form>
        </div>
    </div>

    <!-- 刪除確認模態框 -->
    <div class="modal" id="deleteModal">
        <div class="modal-background"></div>
        <div class="modal-card">
            <header class="modal-card-head">
                <p class="modal-card-title">確認刪除</p>
                <button class="delete" aria-label="close"></button>
            </header>
            <section class="modal-card-body">
                <p>確定要刪除這個對象類別嗎？此操作無法撤銷。</p>
            </section>
            <footer class="modal-card-foot">
                <form id="deleteForm" method="post">
                    <input type="hidden" name="type_id" id="deleteTypeId">
                    <button type="submit" class="button is-danger">確認刪除</button>
                    <button type="button" class="button cancel-btn">取消</button>
                </form>
            </footer>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 新增按鈕點擊事件
            document.getElementById('addTypeBtn').addEventListener('click', function () {
                document.getElementById('modalTitle').textContent = '新增對象類別';
                document.getElementById('typeForm').action = '/payment_identity_type/add_type';
                document.getElementById('typeId').value = '';
                document.getElementById('typeName').value = '';
                document.getElementById('typeDescription').value = '';
                document.getElementById('typeModal').classList.add('is-active');
            });

            // 編輯按鈕點擊事件
            document.querySelectorAll('.edit-type-btn').forEach(function (btn) {
                btn.addEventListener('click', function () {
                    document.getElementById('modalTitle').textContent = '編輯對象類別';
                    document.getElementById('typeForm').action = '/payment_identity_type/edit';
                    document.getElementById('typeId').value = this.dataset.id;
                    document.getElementById('typeName').value = this.dataset.name;
                    document.getElementById('typeDescription').value = this.dataset.description;
                    document.getElementById('typeModal').classList.add('is-active');
                });
            });

            // 刪除按鈕點擊事件
            document.querySelectorAll('.delete-type-btn').forEach(function (btn) {
                btn.addEventListener('click', function () {
                    document.getElementById('deleteTypeId').value = this.dataset.id;
                    document.getElementById('deleteForm').action = '/payment_identity_type/delete';
                    document.getElementById('deleteModal').classList.add('is-active');
                });
            });

            // 關閉模態框
            document.querySelectorAll('.modal .delete, .modal .cancel-btn').forEach(function (el) {
                el.addEventListener('click', function () {
                    document.querySelectorAll('.modal').forEach(function (modal) {
                        modal.classList.remove('is-active');
                    });
                });
            });

            // 點擊模態框背景關閉
            document.querySelectorAll('.modal-background').forEach(function (bg) {
                bg.addEventListener('click', function () {
                    document.querySelectorAll('.modal').forEach(function (modal) {
                        modal.classList.remove('is-active');
                    });
                });
            });
        });
    </script>
</body>

</html>