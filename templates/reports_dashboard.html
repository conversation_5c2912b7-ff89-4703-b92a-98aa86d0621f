<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>財務報表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 導航 -->
                <div class="box mb-5">
                    <h2 class="subtitle">
                        <a href="/"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        <i class="fas fa-chart-bar"></i> 財務報表
                    </h2>
                </div>

                <!-- 報表選單 -->
                <div class="columns is-multiline">
                    <!-- 月度報表 -->
                    <div class="column is-half">
                        <div class="box">
                            <div class="media">
                                <div class="media-left">
                                    <figure class="image is-64x64">
                                        <i class="fas fa-calendar-alt fa-3x has-text-primary"></i>
                                    </figure>
                                </div>
                                <div class="media-content">
                                    <h3 class="title is-5">月度報表</h3>
                                    <p class="subtitle is-6">查看特定月份的收支統計和分析</p>
                                    <a href="/reports/monthly" class="button is-primary">
                                        <span class="icon"><i class="fas fa-eye"></i></span>
                                        <span>查看報表</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 年度報表 -->
                    <div class="column is-half">
                        <div class="box">
                            <div class="media">
                                <div class="media-left">
                                    <figure class="image is-64x64">
                                        <i class="fas fa-chart-line fa-3x has-text-success"></i>
                                    </figure>
                                </div>
                                <div class="media-content">
                                    <h3 class="title is-5">年度報表</h3>
                                    <p class="subtitle is-6">查看年度收支趨勢和統計分析</p>
                                    <a href="/reports/yearly" class="button is-success">
                                        <span class="icon"><i class="fas fa-eye"></i></span>
                                        <span>查看報表</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 帳戶對帳單 -->
                    <div class="column is-half">
                        <div class="box">
                            <div class="media">
                                <div class="media-left">
                                    <figure class="image is-64x64">
                                        <i class="fas fa-university fa-3x has-text-info"></i>
                                    </figure>
                                </div>
                                <div class="media-content">
                                    <h3 class="title is-5">帳戶對帳單</h3>
                                    <p class="subtitle is-6">查看特定帳戶的交易明細</p>
                                    <a href="/reports/account_statement" class="button is-info">
                                        <span class="icon"><i class="fas fa-eye"></i></span>
                                        <span>查看報表</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 現金流量報表 -->
                    <div class="column is-half">
                        <div class="box">
                            <div class="media">
                                <div class="media-left">
                                    <figure class="image is-64x64">
                                        <i class="fas fa-exchange-alt fa-3x has-text-warning"></i>
                                    </figure>
                                </div>
                                <div class="media-content">
                                    <h3 class="title is-5">現金流量報表</h3>
                                    <p class="subtitle is-6">分析現金流入流出情況</p>
                                    <a href="/reports/cash_flow" class="button is-warning">
                                        <span class="icon"><i class="fas fa-eye"></i></span>
                                        <span>查看報表</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 付款狀態報表 -->
                    <div class="column is-half">
                        <div class="box">
                            <div class="media">
                                <div class="media-left">
                                    <figure class="image is-64x64">
                                        <i class="fas fa-clock fa-3x has-text-danger"></i>
                                    </figure>
                                </div>
                                <div class="media-content">
                                    <h3 class="title is-5">付款狀態報表</h3>
                                    <p class="subtitle is-6">查看逾期和即將到期的付款</p>
                                    <a href="/reports/payment_status" class="button is-danger">
                                        <span class="icon"><i class="fas fa-eye"></i></span>
                                        <span>查看報表</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分類明細賬 -->
                    <div class="column is-half">
                        <div class="box">
                            <div class="media">
                                <div class="media-left">
                                    <figure class="image is-64x64">
                                        <i class="fas fa-list-alt fa-3x has-text-link"></i>
                                    </figure>
                                </div>
                                <div class="media-content">
                                    <h3 class="title is-5">分類明細賬</h3>
                                    <p class="subtitle is-6">查看各會計科目的詳細交易記錄</p>
                                    <a href="/reports/category_ledger" class="button is-link">
                                        <span class="icon"><i class="fas fa-eye"></i></span>
                                        <span>查看報表</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快取管理 -->
                    <div class="column is-half">
                        <div class="box">
                            <div class="media">
                                <div class="media-left">
                                    <figure class="image is-64x64">
                                        <i class="fas fa-database fa-3x has-text-grey"></i>
                                    </figure>
                                </div>
                                <div class="media-content">
                                    <h3 class="title is-5">快取管理</h3>
                                    <p class="subtitle is-6">查看和管理系統快取狀態</p>
                                    <button id="cacheStatsBtn" class="button is-light">
                                        <span class="icon"><i class="fas fa-info-circle"></i></span>
                                        <span>查看狀態</span>
                                    </button>
                                    <button id="refreshCacheBtn" class="button is-light">
                                        <span class="icon"><i class="fas fa-sync"></i></span>
                                        <span>重新整理</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快取狀態顯示 -->
                <div id="cacheStatsBox" class="box is-hidden">
                    <h3 class="subtitle">
                        <i class="fas fa-database"></i> 快取狀態
                    </h3>
                    <div id="cacheStatsContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 查看快取狀態
        document.getElementById('cacheStatsBtn').addEventListener('click', function () {
            fetch('/api/reports/cache_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCacheStats(data.data);
                        document.getElementById('cacheStatsBox').classList.remove('is-hidden');
                    } else {
                        alert('取得快取狀態失敗: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('取得快取狀態時發生錯誤');
                });
        });

        // 重新整理快取
        document.getElementById('refreshCacheBtn').addEventListener('click', function () {
            if (confirm('確定要重新整理所有快取嗎？')) {
                fetch('/api/reports/refresh_cache', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({})
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('快取已重新整理');
                            // 重新載入快取狀態
                            document.getElementById('cacheStatsBtn').click();
                        } else {
                            alert('重新整理快取失敗: ' + data.error);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('重新整理快取時發生錯誤');
                    });
            }
        });

        // 顯示快取狀態
        function displayCacheStats(stats) {
            const container = document.getElementById('cacheStatsContent');

            let html = `
                <div class="notification is-info is-light">
                    <strong>總快取項目：${stats.total_cached_items}</strong>
                </div>
                <div class="table-container">
                    <table class="table is-fullwidth is-striped">
                        <thead>
                            <tr>
                                <th>快取鍵</th>
                                <th>項目數量</th>
                                <th>快取時間</th>
                                <th>剩餘時間</th>
                                <th>狀態</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            Object.entries(stats.cache_details).forEach(([key, detail]) => {
                const statusClass = detail.is_valid ? 'tag is-success' : 'tag is-danger';
                const statusText = detail.is_valid ? '有效' : '已過期';

                html += `
                    <tr>
                        <td><code>${key}</code></td>
                        <td>${detail.item_count}</td>
                        <td>${new Date(detail.cached_at).toLocaleString()}</td>
                        <td>${detail.expires_in_seconds}秒</td>
                        <td><span class="${statusClass}">${statusText}</span></td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = html;
        }
    </script>
</body>

</html>