<!DOCTYPE html>
<html>
<head>
  <title>系統使用流程圖</title>
  <script type="module">
    import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
    mermaid.initialize({ startOnLoad: true });
  </script>
</head>
<body>
  <h1 style="text-align: center;">印錢大師</h1>
  <h2>系統使用流程圖</h2>
  <pre class="mermaid">
    flowchart TD
      subgraph 先期設定
        A[填寫公司基本資料]
        B[填寫帳戶設定]
        C[開帳設定]
      end
      subgraph 基礎設定
        D[會計科目設定]
        E[部門管理]
        F[專案管理]
        G[收支對象管理]
      end
      A --> B
      B --> C
      C --> 基礎設定
      click A "http://127.0.0.1:5000/basic_info" "前往公司基本資料"
      click B "http://127.0.0.1:5000/account_setting" "前往帳戶設定"
      click C "http://127.0.0.1:5000/opening_setting" "前往開帳設定"
      click D "http://127.0.0.1:5000/accounting/subject_manage" "前往會計科目設定"
      click E "http://127.0.0.1:5000/department_manage" "前往部門管理"
      click F "http://127.0.0.1:5000/project_manage" "前往專案管理"
      click G "http://127.0.0.1:5000/payment_identity_list" "前往收支對象管理"
  </pre>
</body>
</html>