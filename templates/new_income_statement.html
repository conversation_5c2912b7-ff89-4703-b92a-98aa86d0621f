<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <title>損益表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .statement-table th, .statement-table td { text-align: center; font-size: 1.05rem; }
        .statement-table th { background: #e5e7eb; }
        .statement-table .group-row { background: #f5f6fa; font-weight: bold; }
        .statement-table .total-row { background: #f0f0f0; font-weight: bold; color: #2563eb; }
        .subject-code { color: #888; font-size: 0.95em; margin-left: 0.2em; }
        .amount-cell { font-family: 'Menlo', 'Consolas', monospace; letter-spacing: 1px; }
        .negative { color: #e67e22; }
        .positive { color: #2563eb; }
    </style>
</head>
<body>
<section class="section">
    <div class="container" style="max-width: 1100px;">
        <h1 class="title is-4 mb-4">損益表</h1>
        <div class="box">
            <div class="mb-3">
                <span>期間：{{ start_date }} ~ {{ end_date }}</span>
            </div>
            <div class="table-container">
                <table class="table is-fullwidth is-striped statement-table">
                    <thead>
                        <tr>
                            <th style="width: 200px;">科目名稱</th>
                            <th style="width: 80px;">科目代號</th>
                            <th style="width: 150px;">金額</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 營業收入 -->
                        <tr class="group-row"><td colspan="3">營業收入</td></tr>
                        {% if income_statement.revenue and income_statement.revenue.items %}
                        {% for item in income_statement.revenue.items %}
                        <tr>
                            <td>{{ item.name }}</td>
                            <td class="subject-code">{{ item.code }}</td>
                            <td class="amount-cell positive">{{ '{:,.0f}'.format(item.amount or 0) }}</td>
                        </tr>
                        {% endfor %}
                        <tr class="total-row">
                            <td colspan="2">營業收入合計</td>
                            <td class="amount-cell positive">{{ '{:,.0f}'.format(income_statement.revenue.total or 0) }}</td>
                        </tr>
                        {% else %}
                        <tr><td colspan="3">無營業收入資料</td></tr>
                        {% endif %}
                        <!-- 營業費用 -->
                        <tr class="group-row"><td colspan="3">營業費用</td></tr>
                        {% if income_statement.operating_expenses and income_statement.operating_expenses.items %}
                        {% for item in income_statement.operating_expenses.items %}
                        <tr>
                            <td>{{ item.name }}</td>
                            <td class="subject-code">{{ item.code }}</td>
                            <td class="amount-cell negative">{{ '{:,.0f}'.format(item.amount or 0) }}</td>
                        </tr>
                        {% endfor %}
                        <tr class="total-row">
                            <td colspan="2">營業費用合計</td>
                            <td class="amount-cell negative">{{ '{:,.0f}'.format(income_statement.operating_expenses.total or 0) }}</td>
                        </tr>
                        {% else %}
                        <tr><td colspan="3">無營業費用資料</td></tr>
                        {% endif %}
                        <!-- 營業利益 -->
                        <tr class="group-row"><td colspan="3">營業利益</td></tr>
                        <tr class="total-row">
                            <td colspan="2">營業利益</td>
                            <td class="amount-cell positive">{{ '{:,.0f}'.format(income_statement.operating_profit or 0) }}</td>
                        </tr>
                        <!-- 稅前淨利 -->
                        <tr class="group-row"><td colspan="3">稅前淨利</td></tr>
                        <tr class="total-row">
                            <td colspan="2">稅前淨利</td>
                            <td class="amount-cell positive">{{ '{:,.0f}'.format(income_statement.profit_before_tax or 0) }}</td>
                        </tr>
                        <!-- 本期淨利 -->
                        <tr class="group-row"><td colspan="3">本期淨利</td></tr>
                        <tr class="total-row">
                            <td colspan="2">本期淨利</td>
                            <td class="amount-cell positive">{{ '{:,.0f}'.format(income_statement.net_profit or 0) }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>
</body>
</html> 