<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>儀表板 - {{ tenant.name }} - 印錢大師</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
        }
        
        .welcome-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 2rem;
            backdrop-filter: blur(10px);
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .plan-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.875rem;
        }
        
        .plan-basic {
            background: #e0f2fe;
            color: #0277bd;
        }
        
        .plan-standard {
            background: #fff3e0;
            color: #ef6c00;
        }
        
        .plan-premium {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .plan-enterprise {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .trial-alert {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .quick-action {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .quick-action:hover {
            border-color: #667eea;
            background: #f8faff;
        }
        
        .quick-action-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="container">
            <div class="welcome-card">
                <div class="columns is-vcentered">
                    <div class="column">
                        <h1 class="title is-2 has-text-white">
                            歡迎回來！{{ session.get('username') }}
                        </h1>
                        <p class="subtitle is-4 has-text-white">
                            {{ tenant.name }}
                            <span class="plan-badge plan-{{ tenant.plan_level.value.lower() }}">
                                {{ tenant.plan_level.value }}版
                            </span>
                        </p>
                    </div>
                    <div class="column is-narrow">
                        <div class="has-text-right">
                            <p class="has-text-white">
                                <i class="fas fa-calendar-alt"></i>
                                {{ tenant.created_at.strftime('%Y年%m月%d日') }} 加入
                            </p>
                            {% if tenant.status.value == 'trial' %}
                            <p class="has-text-white">
                                <i class="fas fa-clock"></i>
                                試用到期：{{ tenant.trial_end_date.strftime('%Y年%m月%d日') }}
                            </p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container" style="margin-top: -1rem;">
        {% if tenant.status.value == 'trial' %}
        <div class="trial-alert">
            <div class="columns is-vcentered">
                <div class="column">
                    <h3 class="title is-5 has-text-white">
                        <i class="fas fa-gift"></i>
                        您正在使用免費試用版
                    </h3>
                    <p class="has-text-white">
                        試用期剩餘 <strong>{{ (tenant.trial_end_date - today()).days }}</strong> 天
                    </p>
                </div>
                <div class="column is-narrow">
                    <a href="{{ url_for('tenant.plan_info') }}" class="button is-white is-rounded">
                        <span class="icon">
                            <i class="fas fa-arrow-up"></i>
                        </span>
                        <span>升級方案</span>
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="section">
            <!-- 統計卡片 -->
            <div class="columns">
                <div class="column is-3">
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <p class="has-text-grey">本月收入</p>
                        <small class="has-text-grey">NT$ 0</small>
                    </div>
                </div>
                <div class="column is-3">
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <p class="has-text-grey">本月支出</p>
                        <small class="has-text-grey">NT$ 0</small>
                    </div>
                </div>
                <div class="column is-3">
                    <div class="stat-card">
                        <div class="stat-number">0</div>
                        <p class="has-text-grey">總帳戶數</p>
                        <small class="has-text-grey">銀行、現金帳戶</small>
                    </div>
                </div>
                <div class="column is-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ tenant.users|length or 1 }}</div>
                        <p class="has-text-grey">用戶數</p>
                        <small class="has-text-grey">最多{{ tenant.max_users }}個</small>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="box">
                <h2 class="title is-4">
                    <i class="fas fa-bolt"></i>
                    快速開始
                </h2>
                <div class="columns">
                    <div class="column">
                        <div class="quick-action" onclick="window.location='{{ url_for('income_expense.income_record') }}'">
                            <div class="quick-action-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <p class="has-text-weight-semibold">新增帳務</p>
                            <small class="has-text-grey">記錄收入或支出</small>
                        </div>
                    </div>
                    <div class="column">
                        <div class="quick-action" onclick="window.location='{{ url_for('account.account_setting') }}'">
                            <div class="quick-action-icon">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <p class="has-text-weight-semibold">帳戶設定</p>
                            <small class="has-text-grey">管理銀行帳戶</small>
                        </div>
                    </div>
                    <div class="column">
                        <div class="quick-action" onclick="window.location='{{ url_for('main.transaction_details') }}'">
                            <div class="quick-action-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <p class="has-text-weight-semibold">交易明細</p>
                            <small class="has-text-grey">查看所有記錄</small>
                        </div>
                    </div>
                    {% if tenant.plan_level.value in ['STANDARD', 'PREMIUM', 'ENTERPRISE'] %}
                    <div class="column">
                        <div class="quick-action" onclick="window.location='{{ url_for('reports.balance_sheet') }}'">
                            <div class="quick-action-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <p class="has-text-weight-semibold">財務報表</p>
                            <small class="has-text-grey">資產負債表</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 方案資訊 -->
            <div class="columns">
                <div class="column is-8">
                    <div class="box">
                        <h2 class="title is-4">
                            <i class="fas fa-info-circle"></i>
                            使用狀況
                        </h2>
                        <div class="content">
                            <div class="columns">
                                <div class="column">
                                    <label class="label">用戶使用量</label>
                                    <progress class="progress is-primary" 
                                              value="{{ tenant.users|length or 1 }}" 
                                              max="{{ tenant.max_users }}">
                                        {{ ((tenant.users|length or 1) / tenant.max_users * 100)|round|int }}%
                                    </progress>
                                    <small>{{ tenant.users|length or 1 }} / {{ tenant.max_users }} 個用戶</small>
                                </div>
                                <div class="column">
                                    <label class="label">儲存空間</label>
                                    <progress class="progress is-info" value="10" max="{{ tenant.max_storage_mb }}">
                                        1%
                                    </progress>
                                    <small>10 MB / {{ tenant.max_storage_mb }} MB</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="column is-4">
                    <div class="box">
                        <h2 class="title is-4">
                            <i class="fas fa-crown"></i>
                            我的方案
                        </h2>
                        <div class="content">
                            <p class="has-text-weight-semibold">
                                {{ tenant.plan_level.value }}版
                            </p>
                            {% if tenant.status.value == 'trial' %}
                            <p class="has-text-grey">
                                試用期至 {{ tenant.trial_end_date.strftime('%Y年%m月%d日') }}
                            </p>
                            {% endif %}
                            <a href="{{ url_for('tenant.plan_info') }}" class="button is-primary is-fullwidth mt-3">
                                <span class="icon">
                                    <i class="fas fa-eye"></i>
                                </span>
                                <span>查看方案詳情</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加一些互動效果
        document.addEventListener('DOMContentLoaded', function() {
            // 動畫顯示統計數字
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = parseInt(stat.textContent);
                let currentValue = 0;
                const increment = finalValue / 30;
                
                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        stat.textContent = finalValue;
                        clearInterval(timer);
                    } else {
                        stat.textContent = Math.floor(currentValue);
                    }
                }, 50);
            });
        });
    </script>
</body>
</html>