<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>會計科目明細</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .sidebar {
            width: 200px;
            font-size: 20px;
        }
        .main-content {
            margin-top: 20px;
        }
        .filter-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .filter-row {
            margin-bottom: 15px;
        }
        .filter-label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .button-group {
            text-align: right;
            margin-top: 20px;
        }
        .detail-table {
            margin-top: 20px;
        }
        .amount-debit {
            color: #d73527;
        }
        .amount-credit {
            color: #28a745;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <div class="main-content">
                    <!-- 標題區域 -->
                    <div class="header-section">
                        <div class="level">
                            <div class="level-left">
                                <div class="level-item">
                                    <div>
                                        <h2 class="title is-4 has-text-white">
                                            <a href="javascript:history.back()" style="color:white;text-decoration:none;margin-right:10px;">←</a>
                                            會計科目明細
                                        </h2>
                                        <p class="subtitle is-6 has-text-white">查詢條件</p>
                                    </div>
                                </div>
                            </div>
                            <div class="level-right">
                                <div class="level-item">
                                    <button class="button is-info is-outlined" style="color:white;border-color:white;">
                                        <span class="icon"><i class="fas fa-question-circle"></i></span>
                                        <span>操作說明</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 篩選條件 -->
                    <div class="filter-section">
                        <!-- 第一行 -->
                        <div class="columns filter-row">
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">科目</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="subjectSelect">
                                                <option value="">請選擇會計科目</option>
                                                {% for category in category_order %}
                                                    {% if category_data[category] %}
                                                        <optgroup label="{{ category }}">
                                                            {% for subject in category_data[category] %}
                                                                <option value="{{ subject.code }}">{{ subject.code }} {{ subject.name }}</option>
                                                                {% for child in subject.children %}
                                                                    <option value="{{ child.code }}">&nbsp;&nbsp;&nbsp;&nbsp;{{ child.code }} {{ child.name }}</option>
                                                                {% endfor %}
                                                            {% endfor %}
                                                        </optgroup>
                                                    {% endif %}
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">月份</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="monthSelect">
                                                <option value="">1-12月 全部</option>
                                                <option value="1">1月</option>
                                                <option value="2">2月</option>
                                                <option value="3">3月</option>
                                                <option value="4">4月</option>
                                                <option value="5">5月</option>
                                                <option value="6">6月</option>
                                                <option value="7">7月</option>
                                                <option value="8">8月</option>
                                                <option value="9">9月</option>
                                                <option value="10">10月</option>
                                                <option value="11">11月</option>
                                                <option value="12">12月</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-1">
                                <div class="field">
                                    <label class="filter-label">年度</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="yearSelect">
                                                <!-- 動態產生年度選項 -->
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">對象</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="targetSelect">
                                                <option value="">請選擇收支對象</option>
                                                {% for identity in payment_identity_data %}
                                                <option value="{{ identity.id }}">{{ identity.name }}{% if identity.tax_id %} ({{ identity.tax_id }}){% endif %}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">部門</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="departmentSelect">
                                                <option value="">請選擇部門</option>
                                                {% for dept in department_data %}
                                                <option value="{{ dept.id }}">{{ dept.name }} ({{ dept.code }})</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">日期區間設定</label>
                                    <div class="control">
                                        <input class="input" type="text" value="2024/07/01 - 2024/07/17" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 第二行 -->
                        <div class="columns filter-row">
                            <!-- 移除「會計科目明細」欄位 -->
                            <div class="column is-2">
                                <div class="field">
                                    <label class="filter-label">會計科目分類</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select id="subjectCategorySelect">
                                                <option value="">請選擇會計科目分類</option>
                                                <option value="資產">資產</option>
                                                <option value="負債">負債</option>
                                                <option value="權益">權益</option>
                                                <option value="營業收入">營業收入</option>
                                                <option value="營業成本">營業成本</option>
                                                <option value="營業費用">營業費用</option>
                                                <option value="營業外收益及費損">營業外收益及費損</option>
                                                <option value="所得稅">所得稅</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="column is-4">
                                <div class="field">
                                    <label class="filter-label">會計科目關鍵字</label>
                                    <div class="control">
                                        <input class="input" type="text" placeholder="請輸入會計科目關鍵字" id="keywordInput">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 按鈕區域 -->
                        <div class="button-group">
                            <button class="button is-light" onclick="clearFilters()">
                                <span class="icon"><i class="fas fa-eraser"></i></span>
                                <span>清除條件</span>
                            </button>
                            <button class="button is-primary" onclick="searchData()">
                                <span class="icon"><i class="fas fa-search"></i></span>
                                <span>查詢</span>
                            </button>
                        </div>
                    </div>

                    <!-- 結果表格 -->
                    <div id="resultSection" class="is-hidden">
                        <div class="box">
                            <div class="table-container detail-table">
                                <table class="table is-fullwidth is-striped is-hoverable">
                                    <thead>
                                        <tr class="has-background-primary has-text-white">
                                            <th>日期</th>
                                            <th>科目代碼</th>
                                            <th>科目名稱</th>
                                            <th>摘要</th>
                                            <th>借方金額</th>
                                            <th>貸方金額</th>
                                            <th>餘額</th>
                                            <th>對象</th>
                                            <!-- <th>專案</th> -->
                                            <!-- <th>部門</th> -->
                                        </tr>
                                    </thead>
                                    <tbody id="resultTableBody">
                                        <!-- 動態載入內容 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 空狀態 -->
                    <div id="emptyState" class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3 class="title is-4">請設定查詢條件</h3>
                        <p class="subtitle is-6">選擇會計科目和其他條件後點擊查詢</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 動態產生年度選單
        (function() {
            const yearSelect = document.getElementById('yearSelect');
            const thisYear = new Date().getFullYear();
            let html = '';
            for (let y = thisYear; y >= thisYear - 3; y--) {
                html += `<option value="${y}"${y === thisYear ? ' selected' : ''}>${y}</option>`;
            }
            yearSelect.innerHTML = html;
        })();

        // 查詢資料
        function searchData() {
            const subject = document.getElementById('subjectSelect').value;
            const category = document.getElementById('subjectCategorySelect').value;
            const target = document.getElementById('targetSelect').value;
            const year = document.getElementById('yearSelect').value;
            const month = document.getElementById('monthSelect').value;
            const department = document.getElementById('departmentSelect').value;
            
            if (!subject && !category) {
                alert('請選擇會計科目或分類');
                return;
            }

            // 隱藏空狀態
            document.getElementById('emptyState').classList.add('is-hidden');
            
            // 顯示載入中狀態
            const tbody = document.getElementById('resultTableBody');
            tbody.innerHTML = '<tr><td colspan="8" class="has-text-centered">載入中...</td></tr>';
            
            // 構建查詢參數
            const params = new URLSearchParams({});
            if (subject) {
                params.append('subject_code', subject);
            }
            if (category) {
                params.append('subject_category', category);
            }
            if (target) {
                params.append('payment_identity_id', target);
            }
            if (year) {
                params.append('year', year);
            }
            if (month) {
                params.append('month', month);
            }
            if (department) {
                params.append('department_id', department);
            }
            
            // 發送 AJAX 請求查詢科目明細
            fetch(`/api/reports/subject_detail?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadRealData(data.data);
                    } else {
                        tbody.innerHTML = '<tr><td colspan="8" class="has-text-centered has-text-danger">查詢失敗：' + data.error + '</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('查詢錯誤:', error);
                    tbody.innerHTML = '<tr><td colspan="8" class="has-text-centered has-text-danger">查詢發生錯誤</td></tr>';
                });
        }

        // 載入真實資料
        function loadRealData(data) {
            const tbody = document.getElementById('resultTableBody');
            tbody.innerHTML = '';

            if (!data || data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="has-text-centered">無資料</td></tr>';
                return;
            }

            data.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row.date || ''}</td>
                    <td>${row.subject_code || ''}</td>
                    <td>${row.subject_name || ''}</td>
                    <td>${row.description || ''}</td>
                    <td class="amount-debit">${row.debit_amount > 0 ? row.debit_amount.toLocaleString() : ''}</td>
                    <td class="amount-credit">${row.credit_amount > 0 ? row.credit_amount.toLocaleString() : ''}</td>
                    <td>${row.balance ? row.balance.toLocaleString() : ''}</td>
                    <td>${row.payment_identity || ''}</td>
                    <!-- <td>${row.project || ''}</td> -->
                    <!-- <td>${row.department || ''}</td> -->
                `;
                tbody.appendChild(tr);
            });

            // 顯示結果
            document.getElementById('resultSection').classList.remove('is-hidden');
        }

        // 載入模擬資料（保留作為備用）
        function loadMockData() {
            const mockData = [
                {
                    date: '2024-07-01',
                    code: '1111',
                    name: '現金',
                    description: '期初餘額',
                    debit: 100000,
                    credit: 0,
                    balance: 100000,
                    target: '',
                    project: '',
                    department: ''
                },
                {
                    date: '2024-07-05',
                    code: '1111',
                    name: '現金',
                    description: '銷售收入',
                    debit: 50000,
                    credit: 0,
                    balance: 150000,
                    target: '客戶A',
                    project: '專案A',
                    department: '業務部'
                },
                {
                    date: '2024-07-10',
                    code: '1111',
                    name: '現金',
                    description: '支付費用',
                    debit: 0,
                    credit: 20000,
                    balance: 130000,
                    target: '供應商B',
                    project: '專案B',
                    department: '財務部'
                }
            ];

            const tbody = document.getElementById('resultTableBody');
            tbody.innerHTML = '';

            mockData.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row.date}</td>
                    <td>${row.code}</td>
                    <td>${row.name}</td>
                    <td>${row.description}</td>
                    <td class="amount-debit">${row.debit > 0 ? row.debit.toLocaleString() : ''}</td>
                    <td class="amount-credit">${row.credit > 0 ? row.credit.toLocaleString() : ''}</td>
                    <td>${row.balance.toLocaleString()}</td>
                    <td>${row.target}</td>
                    <td>${row.project}</td>
                    <td>${row.department}</td>
                `;
                tbody.appendChild(tr);
            });

            // 顯示結果
            document.getElementById('resultSection').classList.remove('is-hidden');
        }

        // 清除條件
        function clearFilters() {
            document.getElementById('subjectSelect').value = '';
            document.getElementById('monthSelect').value = '';
            document.getElementById('yearSelect').value = '2024';
            document.getElementById('targetSelect').value = '';
            document.getElementById('projectSelect').value = '';
            document.getElementById('subjectDetailSelect').value = '';
            document.getElementById('subjectCategorySelect').value = '';
            document.getElementById('keywordInput').value = '';
            document.getElementById('departmentSelect').value = '';
            
            // 隱藏結果，顯示空狀態
            document.getElementById('resultSection').classList.add('is-hidden');
            document.getElementById('emptyState').classList.remove('is-hidden');
        }
    </script>
</body>
</html>
