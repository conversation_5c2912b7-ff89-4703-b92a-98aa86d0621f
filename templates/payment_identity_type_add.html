<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>新增對象類別</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/payment_identity_type_manage"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        新增對象類別
                    </h1>
                </div>
                <div class="box">
                    <h2 class="subtitle is-5">對象類別資訊</h2>
                    <form method="post">
                        <div class="field">
                            <label class="label">類別名稱</label>
                            <div class="control">
                                <input class="input" type="text" name="name" value="" required>
                            </div>
                        </div>
                        <div class="field">
                            <label class="label">描述</label>
                            <div class="control">
                                <textarea class="textarea" name="description"></textarea>
                            </div>
                        </div>
                        <div class="field">
                            <button class="button is-primary" type="submit">儲存</button>
                            <a href="/payment_identity_type_manage" class="button is-light">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>

</html>