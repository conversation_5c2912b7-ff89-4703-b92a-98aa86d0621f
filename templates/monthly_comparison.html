<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <title>年度損益表（月度比較）</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .statement-table th, .statement-table td { text-align: center; font-size: 1.05rem; }
        .statement-table th { background: #e5e7eb; }
        .statement-table .group-row { background: #f5f6fa; font-weight: bold; }
        .statement-table .total-row { background: #f0f0f0; font-weight: bold; color: #2563eb; }
        .amount-cell { font-family: 'Menlo', 'Consolas', monospace; letter-spacing: 1px; }
        .negative { color: #e67e22; }
        .positive { color: #2563eb; }
    </style>
</head>
<body>
<section class="section">
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <h1 class="title is-4 mb-4">年度損益表（月度比較）</h1>
                <div class="box">
                    <div class="mb-3">
                        <span>年度：{{ year }}</span>
                    </div>
                    <div class="table-container">
                        <table class="table is-fullwidth is-striped statement-table">
                            <thead>
                                <tr>
                                    <th>項目</th>
                                    {% for m in monthly_data %}
                                    <th>{{ m.month }}月</th>
                                    {% endfor %}
                                    <th>合計</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="group-row"><td colspan="14">營業收入</td></tr>
                                <tr>
                                    <td>營業收入</td>
                                    {% for m in monthly_data %}
                                    <td class="amount-cell positive">{{ '{:,.0f}'.format(m.revenue or 0) }}</td>
                                    {% endfor %}
                                    <td class="amount-cell positive">{{ '{:,.0f}'.format(year_totals.revenue or 0) }}</td>
                                </tr>
                                <tr class="group-row"><td colspan="14">營業費用</td></tr>
                                <tr>
                                    <td>營業費用</td>
                                    {% for m in monthly_data %}
                                    <td class="amount-cell negative">{{ '{:,.0f}'.format(m.expenses or 0) }}</td>
                                    {% endfor %}
                                    <td class="amount-cell negative">{{ '{:,.0f}'.format(year_totals.expenses or 0) }}</td>
                                </tr>
                                <tr class="group-row"><td colspan="14">本期淨利</td></tr>
                                <tr>
                                    <td>本期淨利</td>
                                    {% for m in monthly_data %}
                                    <td class="amount-cell positive">{{ '{:,.0f}'.format(m.profit or 0) }}</td>
                                    {% endfor %}
                                    <td class="amount-cell positive">{{ '{:,.0f}'.format(year_totals.profit or 0) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
</body>
</html> 