<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>403 - 權限不足 | 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .error-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            margin: 2rem;
        }

        .error-icon {
            font-size: 5rem;
            color: #ff6b6b;
            margin-bottom: 1rem;
        }

        .error-title {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .error-message {
            color: #7f8c8d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .back-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }

        .back-button:hover {
            transform: translateY(-2px);
            color: white;
        }
    </style>
</head>

<body>
    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-lock"></i>
            </div>
            <h1 class="title is-2 error-title">403</h1>
            <h2 class="subtitle is-4 error-title">權限不足</h2>
            <p class="error-message">
                很抱歉，您沒有權限訪問此頁面。<br>
                請聯繫系統管理員或確認您的登入狀態。
            </p>
            <div class="buttons is-centered">
                <button class="button back-button" onclick="goBack()">
                    <span class="icon">
                        <i class="fas fa-arrow-left"></i>
                    </span>
                    <span>返回上頁</span>
                </button>
                <a href="/" class="button is-outlined">
                    <span class="icon">
                        <i class="fas fa-home"></i>
                    </span>
                    <span>回到首頁</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            if (document.referrer && document.referrer !== window.location.href) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }
    </script>
</body>

</html>