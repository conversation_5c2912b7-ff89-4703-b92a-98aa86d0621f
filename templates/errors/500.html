<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - 伺服器錯誤 | 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
        }

        .error-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            margin: 2rem;
        }

        .error-icon {
            font-size: 5rem;
            color: #e17055;
            margin-bottom: 1rem;
        }

        .error-title {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .error-message {
            color: #7f8c8d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .back-button {
            background: linear-gradient(45deg, #ff7675, #d63031);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }

        .back-button:hover {
            transform: translateY(-2px);
            color: white;
        }

        .error-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: left;
        }

        .report-button {
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-top: 1rem;
        }

        .report-button:hover {
            background: #5f3dc4;
            color: white;
        }
    </style>
</head>

<body>
    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h1 class="title is-2 error-title">500</h1>
            <h2 class="subtitle is-4 error-title">伺服器內部錯誤</h2>
            <p class="error-message">
                很抱歉，伺服器發生了內部錯誤。<br>
                我們的技術團隊已經收到通知，正在處理這個問題。
            </p>

            <div class="error-details">
                <p><strong>可能的原因：</strong></p>
                <ul style="text-align: left; margin-left: 1rem;">
                    <li>資料庫連接問題</li>
                    <li>系統資源不足</li>
                    <li>程式碼執行異常</li>
                    <li>第三方服務暫時不可用</li>
                </ul>
            </div>

            <div class="error-details">
                <p><strong>建議的解決方案：</strong></p>
                <ul style="text-align: left; margin-left: 1rem;">
                    <li>等待幾分鐘後重新嘗試</li>
                    <li>檢查網路連接是否正常</li>
                    <li>清除瀏覽器快取</li>
                    <li>如果問題持續，請聯繫系統管理員</li>
                </ul>
            </div>

            <div class="buttons is-centered">
                <button class="button back-button" onclick="retryPage()">
                    <span class="icon">
                        <i class="fas fa-redo"></i>
                    </span>
                    <span>重新嘗試</span>
                </button>
                <button class="button back-button" onclick="goBack()">
                    <span class="icon">
                        <i class="fas fa-arrow-left"></i>
                    </span>
                    <span>返回上頁</span>
                </button>
                <a href="/" class="button is-outlined">
                    <span class="icon">
                        <i class="fas fa-home"></i>
                    </span>
                    <span>回到首頁</span>
                </a>
            </div>

            <div class="content is-small" style="margin-top: 2rem;">
                <p class="has-text-grey">
                    錯誤時間: <span id="errorTime"></span><br>
                    如果問題持續發生，請記錄此時間並聯繫技術支援
                </p>
                <button class="button report-button is-small" onclick="reportError()">
                    <span class="icon is-small">
                        <i class="fas fa-bug"></i>
                    </span>
                    <span>回報問題</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        // 顯示錯誤時間
        document.getElementById('errorTime').textContent = new Date().toLocaleString('zh-TW');
        
        function retryPage() {
            window.location.reload();
        }
        
        function goBack() {
            if (document.referrer && document.referrer !== window.location.href) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }
        
        function reportError() {
            const errorInfo = {
                time: new Date().toISOString(),
                url: window.location.href,
                userAgent: navigator.userAgent,
                referrer: document.referrer
            };
            
            // 這裡可以發送錯誤報告到後端
            alert('錯誤報告已記錄，感謝您的回饋！\n\n錯誤時間: ' + errorInfo.time);
        }
        
        // 自動重試機制（可選）
        let retryCount = 0;
        const maxRetries = 3;
        
        function autoRetry() {
            if (retryCount < maxRetries) {
                retryCount++;
                setTimeout(() => {
                    console.log(`自動重試第 ${retryCount} 次...`);
                    window.location.reload();
                }, 5000 * retryCount); // 遞增延遲時間
            }
        }
        
        // 可以根據需要啟用自動重試
        // autoRetry();
    </script>
</body>

</html>