<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - 頁面不存在 | 會計系統</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .error-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            margin: 2rem;
        }

        .error-icon {
            font-size: 5rem;
            color: #fdcb6e;
            margin-bottom: 1rem;
        }

        .error-title {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .error-message {
            color: #7f8c8d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .back-button {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }

        .back-button:hover {
            transform: translateY(-2px);
            color: white;
        }

        .search-box {
            margin: 1rem 0;
        }
    </style>
</head>

<body>
    <div class="error-container">
        <div class="error-card">
            <div class="error-icon">
                <i class="fas fa-search"></i>
            </div>
            <h1 class="title is-2 error-title">404</h1>
            <h2 class="subtitle is-4 error-title">頁面不存在</h2>
            <p class="error-message">
                很抱歉，您要找的頁面不存在。<br>
                可能是網址輸入錯誤，或頁面已被移動。
            </p>

            <div class="field search-box">
                <div class="control has-icons-left">
                    <input class="input" type="text" placeholder="搜尋功能..." id="searchInput">
                    <span class="icon is-small is-left">
                        <i class="fas fa-search"></i>
                    </span>
                </div>
            </div>

            <div class="buttons is-centered">
                <button class="button back-button" onclick="goBack()">
                    <span class="icon">
                        <i class="fas fa-arrow-left"></i>
                    </span>
                    <span>返回上頁</span>
                </button>
                <a href="/" class="button is-outlined">
                    <span class="icon">
                        <i class="fas fa-home"></i>
                    </span>
                    <span>回到首頁</span>
                </a>
            </div>

            <div class="content is-small" style="margin-top: 2rem;">
                <p><strong>常用功能：</strong></p>
                <div class="tags are-medium">
                    <a href="/?main=收支管理" class="tag is-link">收支管理</a>
                    <a href="/?main=帳戶管理" class="tag is-info">帳戶管理</a>
                    <a href="/?main=我的報表" class="tag is-success">報表查詢</a>
                    <a href="/?main=設定" class="tag is-warning">系統設定</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            if (document.referrer && document.referrer !== window.location.href) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }

        // 簡單的搜尋功能
        document.getElementById('searchInput').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                const searchTerm = this.value.toLowerCase();
                if (searchTerm.includes('收支') || searchTerm.includes('記帳')) {
                    window.location.href = '/?main=收支管理';
                } else if (searchTerm.includes('帳戶') || searchTerm.includes('銀行')) {
                    window.location.href = '/?main=帳戶管理';
                } else if (searchTerm.includes('報表') || searchTerm.includes('統計')) {
                    window.location.href = '/?main=我的報表';
                } else if (searchTerm.includes('設定') || searchTerm.includes('配置')) {
                    window.location.href = '/?main=設定';
                } else {
                    window.location.href = '/';
                }
            }
        });
    </script>
</body>

</html>