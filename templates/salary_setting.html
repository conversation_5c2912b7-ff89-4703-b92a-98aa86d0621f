<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <title>薪資設定</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <style>
        .main-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .tab-bar {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
        }

        .tab-active {
            color: #2563eb;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 0.2em;
        }

        .tab-inactive {
            color: #bfc7d1;
            margin-left: 1.2em;
        }

        .setting-card {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 2px 8px #eee;
            padding: 2.5rem 2rem;
            max-width: 700px;
            margin: 0 auto;
        }

        .setting-label {
            font-weight: bold;
            font-size: 1.1em;
            margin-bottom: 0.5em;
        }

        .setting-desc {
            color: #444;
            font-size: 1em;
            margin-bottom: 1.5em;
        }

        .is-required:after {
            content: '*';
            color: #e53e3e;
            margin-left: 0.2em;
        }

        .radio-group {
            display: flex;
            gap: 2em;
            align-items: center;
        }

        .form-btns {
            display: flex;
            justify-content: flex-end;
            gap: 1.5em;
            margin-top: 2.5em;
        }
    </style>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 頁面標題 -->
                <div class="mb-4">
                    <h1 class="title is-4">
                        <a href="/?main=薪資報酬"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        薪資設定 <span class="icon has-text-info" title="說明"><i class="fas fa-question-circle"></i></span>
                    </h1>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                {% for category, message in messages %}
                <div
                    class="notification {% if category == 'success' %}is-success{% elif category == 'error' %}is-danger{% else %}is-info{% endif %} is-light">
                    <button class="delete"></button>
                    {{ message }}
                </div>
                {% endfor %}
                {% endif %}
                {% endwith %}
                <div class="tab-bar mb-4">
                    <span class="tab-active">發薪設定</span>
                    <span class="tab-inactive">薪資項目</span>
                </div>
                <div class="setting-card">
                    <form method="POST">
                        <div class="columns">
                            <div class="column is-6">
                                <div class="field mb-4">
                                    <label class="label is-required">設定發薪日</label>
                                    <div class="control">
                                        <input class="input" type="number" name="payday" placeholder="發薪日"
                                            value="{{ current_setting.payday if current_setting else 10 }}" min="1"
                                            max="31">
                                    </div>
                                    <p class="help">每月幾號發薪（1-31）</p>
                                </div>
                            </div>
                            <div class="column is-6">
                                <div class="field mb-4">
                                    <label class="label is-required">資金帳戶</label>
                                    <div class="control">
                                        <div class="select is-fullwidth">
                                            <select name="fund_account_id" required>
                                                <option value="">請選擇發薪用的銀行帳戶</option>
                                                {% for account in bank_accounts %}
                                                <option value="{{ account.id }}" {% if current_setting and
                                                    current_setting.fund_account_id==account.id %}selected {% elif not
                                                    current_setting and account.is_default %}selected {% endif %}>
                                                    {{ account.display_name }}
                                                    {% if account.is_default %} (預設){% endif %}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    {% if not bank_accounts %}
                                    <p class="help has-text-danger">尚未設定銀行帳戶，請先到<a href="/account/bank"
                                            class="has-text-link">帳戶設定</a>新增銀行帳戶</p>
                                    {% else %}
                                    <p class="help">選擇用於發放薪資的銀行帳戶</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="columns">
                            <div class="column is-6">
                                <div class="field mb-4">
                                    <label class="label is-required">每月幾日計算基準</label>
                                    <div class="radio-group mt-2">
                                        <label class="radio">
                                            <input type="radio" name="days_type" value="calendar" {% if not
                                                current_setting or current_setting.days_type=='calendar' %}checked{%
                                                endif %}> 日曆天數
                                        </label>
                                        <label class="radio">
                                            <input type="radio" name="days_type" value="fixed" {% if current_setting and
                                                current_setting.days_type=='fixed' %}checked{% endif %}> 固定天數(30天)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-btns">
                            <button class="button is-light" type="button">取消</button>
                            <button class="button is-link" type="submit">儲存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script>
        // 處理通知關閉
        document.addEventListener('DOMContentLoaded', function () {
            // 關閉通知
            const deleteButtons = document.querySelectorAll('.notification .delete');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function () {
                    this.parentElement.remove();
                });
            });

            // 自動關閉成功通知
            const successNotifications = document.querySelectorAll('.notification.is-success');
            successNotifications.forEach(notification => {
                setTimeout(() => {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 3000);
            });

            // 表單驗證
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function (e) {
                    const payday = document.querySelector('input[name="payday"]').value;
                    const fundAccount = document.querySelector('select[name="fund_account_id"]').value;

                    if (!payday || payday < 1 || payday > 31) {
                        e.preventDefault();
                        alert('請輸入有效的發薪日（1-31）');
                        return;
                    }

                    if (!fundAccount) {
                        e.preventDefault();
                        alert('請選擇資金帳戶');
                        return;
                    }
                });
            }
        });
    </script>
</body>

</html>