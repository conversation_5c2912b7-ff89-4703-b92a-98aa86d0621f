<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>年度報表</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>

<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            <div class="column">
                <!-- 導航 -->
                <div class="box mb-5">
                    <h2 class="subtitle">
                        <a href="/?main=我的報表"
                            style="color:#222;text-decoration:none;font-size:1.2rem;vertical-align:middle;margin-right:0.5rem;">←</a>
                        <i class="fas fa-chart-line"></i> 年度報表 - {{ year }}年
                    </h2>

                    <!-- 年份選擇 -->
                    <form method="GET" class="field is-grouped">
                        <div class="control">
                            <div class="select">
                                <select name="year">
                                    {% for y in range(2020, 2030) %}
                                    <option value="{{ y }}" {% if y==year %}selected{% endif %}>{{ y }}年</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="control">
                            <button type="submit" class="button is-primary">
                                <span class="icon"><i class="fas fa-search"></i></span>
                                <span>查詢</span>
                            </button>
                        </div>
                    </form>
                </div>

                {% if report %}
                <!-- 年度統計摘要 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-chart-pie"></i> {{ year }}年度統計摘要
                    </h3>
                    <div class="columns">
                        <div class="column">
                            <div class="notification is-success">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ "{:,}".format(report.yearly_total.get('收入',
                                                    {}).get('total', 0)) }}</p>
                                                <p class="subtitle">年度收入</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-arrow-up fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <p class="has-text-weight-semibold">筆數: {{ report.yearly_total.get('收入',
                                    {}).get('count', 0) }}</p>
                            </div>
                        </div>

                        <div class="column">
                            <div class="notification is-danger">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                <p class="title">{{ "{:,}".format(report.yearly_total.get('支出',
                                                    {}).get('total', 0)) }}</p>
                                                <p class="subtitle">年度支出</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-arrow-down fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <p class="has-text-weight-semibold">筆數: {{ report.yearly_total.get('支出',
                                    {}).get('count', 0) }}</p>
                            </div>
                        </div>

                        <div class="column">
                            <div class="notification is-info">
                                <div class="level">
                                    <div class="level-left">
                                        <div class="level-item">
                                            <div>
                                                {% set net_amount = report.yearly_total.get('收入', {}).get('total', 0)
                                                - report.yearly_total.get('支出', {}).get('total', 0) %}
                                                <p class="title">{{ "{:,}".format(net_amount) }}</p>
                                                <p class="subtitle">年度淨收支</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="level-right">
                                        <div class="level-item">
                                            <i class="fas fa-balance-scale fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                                <p class="has-text-weight-semibold">
                                    {% if net_amount > 0 %}
                                    <span class="has-text-success">年度盈餘</span>
                                    {% elif net_amount < 0 %} <span class="has-text-danger">年度虧損</span>
                                        {% else %}
                                        <span class="has-text-grey">收支平衡</span>
                                        {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 月度趨勢圖表 -->
                {% if report.monthly_trend %}
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-chart-line"></i> {{ year }}年月度趨勢
                    </h3>
                    <canvas id="trendChart" width="400" height="200"></canvas>
                </div>
                {% endif %}

                <!-- 年度收支圖表 -->
                <div class="box mb-5">
                    <h3 class="subtitle">
                        <i class="fas fa-chart-bar"></i> 年度收支對比
                    </h3>
                    <canvas id="yearlyChart" width="400" height="200"></canvas>
                </div>

                {% else %}
                <!-- 無資料提示 -->
                <div class="box">
                    <div class="notification is-warning">
                        <h4 class="title is-4">
                            <i class="fas fa-exclamation-triangle"></i> 無資料
                        </h4>
                        <p>{{ year }}年沒有收支記錄，請選擇其他年份或先新增收支記錄。</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        {% if report %}
        // 年度收支對比圖表
        const yearlyCtx = document.getElementById('yearlyChart').getContext('2d');
        const yearlyChart = new Chart(yearlyCtx, {
            type: 'doughnut',
            data: {
                labels: ['收入', '支出'],
                datasets: [{
                    data: [
                        {{ report.yearly_total.get('收入', {}).get('total', 0) }},
                {{ report.yearly_total.get('支出', {}).get('total', 0) }}
                    ],
        backgroundColor: [
            'rgba(72, 187, 120, 0.8)',
            'rgba(245, 101, 101, 0.8)'
        ],
            borderColor: [
                'rgba(72, 187, 120, 1)',
                'rgba(245, 101, 101, 1)'
            ],
                borderWidth: 2
                }]
            },
        options: {
            responsive: true,
                plugins: {
                title: {
                    display: true,
                        text: '{{ year }}年收支比例'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
        });

        {% if report.monthly_trend %}
        // 月度趨勢圖表
        const trendCtx = document.getElementById('trendChart').getContext('2d');
        const months = [];
        const incomeData = [];
        const expenseData = [];

        // 準備12個月的資料
        for (let i = 1; i <= 12; i++) {
            months.push(i + '月');
            const monthData = {{ report.monthly_trend | tojson
        }};
        incomeData.push(monthData[i] ? (monthData[i]['收入'] || 0) : 0);
        expenseData.push(monthData[i] ? (monthData[i]['支出'] || 0) : 0);
        }

        const trendChart = new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: '收入',
                    data: incomeData,
                    borderColor: 'rgba(72, 187, 120, 1)',
                    backgroundColor: 'rgba(72, 187, 120, 0.1)',
                    tension: 0.4
                }, {
                    label: '支出',
                    data: expenseData,
                    borderColor: 'rgba(245, 101, 101, 1)',
                    backgroundColor: 'rgba(245, 101, 101, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function (value) {
                                return value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '{{ year }}年月度收支趨勢'
                    }
                }
            }
        });
        {% endif %}
        {% endif %}
    </script>
</body>

</html>