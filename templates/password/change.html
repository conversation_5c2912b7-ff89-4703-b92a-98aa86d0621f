<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>變更密碼</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <style>
        .password-strength {
            margin-top: 0.5rem;
        }
        
        .strength-bar {
            height: 6px;
            border-radius: 3px;
            background: #e0e0e0;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 3px;
        }
        
        .strength-very-weak { background: #d32f2f; }
        .strength-weak { background: #f57c00; }
        .strength-medium { background: #fbc02d; }
        .strength-strong { background: #388e3c; }
        .strength-very-strong { background: #1976d2; }
        
        .requirements-list {
            margin-top: 1rem;
        }
        
        .requirement-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .requirement-icon {
            margin-right: 0.5rem;
            width: 16px;
        }
        
        .requirement-met {
            color: #00d1b2;
        }
        
        .requirement-unmet {
            color: #ff3860;
        }
        
        .password-input-group {
            position: relative;
        }
        
        .password-toggle {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #999;
        }
        
        .recommendations {
            background: #f8f9fa;
            border-left: 4px solid #0066cc;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container is-fluid">
        <h1 class="title has-text-centered">印錢大師</h1>
        
        <div class="columns">
            <div class="column is-narrow">
                {% include 'sidebar.html' %}
            </div>
            
            <div class="column">
                <div class="container" style="max-width: 600px; margin: 0 auto;">
                    <!-- 頁面標題 -->
                    <div class="mb-5">
                        <h1 class="title is-4">
                            <i class="fas fa-key mr-2"></i>
                            變更密碼
                        </h1>
                        <p class="subtitle is-6">為了保護您的帳戶安全，請使用強密碼</p>
                    </div>
                    
                    <!-- 錯誤訊息 -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="notification is-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }}">
                                    <button class="delete" onclick="this.parentElement.remove()"></button>
                                    {{ message }}
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <!-- 密碼變更表單 -->
                    <div class="card">
                        <div class="card-content">
                            <form method="post" id="changePasswordForm">
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                                
                                <!-- 當前密碼 -->
                                <div class="field">
                                    <label class="label">當前密碼 <span class="has-text-danger">*</span></label>
                                    <div class="control password-input-group">
                                        <input class="input" type="password" name="current_password" id="current_password" 
                                               placeholder="請輸入當前密碼" required>
                                        <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 新密碼 -->
                                <div class="field">
                                    <label class="label">新密碼 <span class="has-text-danger">*</span></label>
                                    <div class="control password-input-group">
                                        <input class="input" type="password" name="new_password" id="new_password" 
                                               placeholder="請輸入新密碼" required onkeyup="checkPasswordStrength()">
                                        <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    
                                    <!-- 密碼強度指示器 -->
                                    <div class="password-strength" id="passwordStrength" style="display: none;">
                                        <div class="level">
                                            <div class="level-left">
                                                <span class="level-item">密碼強度:</span>
                                                <span class="level-item" id="strengthText">檢測中...</span>
                                            </div>
                                            <div class="level-right">
                                                <span class="level-item" id="strengthScore">0/100</span>
                                            </div>
                                        </div>
                                        <div class="strength-bar">
                                            <div class="strength-fill" id="strengthFill" style="width: 0%;"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 確認新密碼 -->
                                <div class="field">
                                    <label class="label">確認新密碼 <span class="has-text-danger">*</span></label>
                                    <div class="control password-input-group">
                                        <input class="input" type="password" name="confirm_password" id="confirm_password" 
                                               placeholder="請再次輸入新密碼" required onkeyup="checkPasswordMatch()">
                                        <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <p class="help" id="matchHelp"></p>
                                </div>
                                
                                <!-- 密碼要求 -->
                                <div class="requirements-list" id="passwordRequirements">
                                    <h6 class="title is-6">密碼要求：</h6>
                                    <div class="requirement-item">
                                        <span class="requirement-icon"><i class="fas fa-circle requirement-unmet"></i></span>
                                        <span>至少 8 個字符</span>
                                    </div>
                                    <div class="requirement-item">
                                        <span class="requirement-icon"><i class="fas fa-circle requirement-unmet"></i></span>
                                        <span>包含大寫字母</span>
                                    </div>
                                    <div class="requirement-item">
                                        <span class="requirement-icon"><i class="fas fa-circle requirement-unmet"></i></span>
                                        <span>包含小寫字母</span>
                                    </div>
                                    <div class="requirement-item">
                                        <span class="requirement-icon"><i class="fas fa-circle requirement-unmet"></i></span>
                                        <span>包含數字</span>
                                    </div>
                                    <div class="requirement-item">
                                        <span class="requirement-icon"><i class="fas fa-circle requirement-unmet"></i></span>
                                        <span>包含特殊字符 (!@#$%^&* 等)</span>
                                    </div>
                                </div>
                                
                                <!-- 密碼建議 -->
                                {% if password_recommendations %}
                                <div class="recommendations">
                                    <h6 class="title is-6">
                                        <i class="fas fa-lightbulb mr-2"></i>
                                        密碼改善建議：
                                    </h6>
                                    <ul>
                                        {% for recommendation in password_recommendations %}
                                        <li>{{ recommendation }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                                
                                <!-- 提交按鈕 -->
                                <div class="field is-grouped mt-5">
                                    <div class="control">
                                        <button class="button is-primary" type="submit" id="submitBtn">
                                            <span class="icon">
                                                <i class="fas fa-key"></i>
                                            </span>
                                            <span>變更密碼</span>
                                        </button>
                                    </div>
                                    <div class="control">
                                        <a class="button is-light" href="{{ url_for('main.index') }}">
                                            <span class="icon">
                                                <i class="fas fa-times"></i>
                                            </span>
                                            <span>取消</span>
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- 密碼安全提示 -->
                    <div class="notification is-info mt-4">
                        <h6 class="title is-6">
                            <i class="fas fa-shield-alt mr-2"></i>
                            密碼安全提示：
                        </h6>
                        <ul>
                            <li>請使用包含大小寫字母、數字和特殊字符的複雜密碼</li>
                            <li>不要在多個網站使用相同的密碼</li>
                            <li>定期更換密碼，建議 90 天更新一次</li>
                            <li>不要與他人分享您的密碼</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let strengthTimeout;
        let currentStrengthData = null;
        
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }
        
        function checkPasswordStrength() {
            const password = document.getElementById('new_password').value;
            const strengthDiv = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthDiv.style.display = 'none';
                updateRequirements(password);
                return;
            }
            
            strengthDiv.style.display = 'block';
            
            // 防抖處理，避免過於頻繁的請求
            clearTimeout(strengthTimeout);
            strengthTimeout = setTimeout(() => {
                fetch('/password/api/check-strength', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ password: password })
                })
                .then(response => response.json())
                .then(data => {
                    currentStrengthData = data;
                    updateStrengthDisplay(data);
                    updateRequirements(password);
                })
                .catch(error => {
                    console.error('密碼強度檢查錯誤:', error);
                });
            }, 300);
        }
        
        function updateStrengthDisplay(data) {
            const strengthText = document.getElementById('strengthText');
            const strengthScore = document.getElementById('strengthScore');
            const strengthFill = document.getElementById('strengthFill');
            const submitBtn = document.getElementById('submitBtn');
            
            strengthText.textContent = data.strength;
            strengthScore.textContent = `${data.score}/100`;
            strengthFill.style.width = `${data.score}%`;
            
            // 移除所有強度類
            strengthFill.className = 'strength-fill';
            
            // 添加對應的強度類
            if (data.score >= 80) {
                strengthFill.classList.add('strength-very-strong');
            } else if (data.score >= 60) {
                strengthFill.classList.add('strength-strong');
            } else if (data.score >= 40) {
                strengthFill.classList.add('strength-medium');
            } else if (data.score >= 20) {
                strengthFill.classList.add('strength-weak');
            } else {
                strengthFill.classList.add('strength-very-weak');
            }
            
            // 根據密碼有效性啟用/禁用提交按鈕
            submitBtn.disabled = !data.is_valid;
            if (!data.is_valid) {
                submitBtn.classList.add('is-loading');
                submitBtn.title = '密碼不符合安全要求';
            } else {
                submitBtn.classList.remove('is-loading');
                submitBtn.title = '';
            }
        }
        
        function updateRequirements(password) {
            const requirements = document.querySelectorAll('.requirement-item');
            
            // 檢查各項要求
            const checks = [
                password.length >= 8,  // 長度
                /[A-Z]/.test(password),  // 大寫字母
                /[a-z]/.test(password),  // 小寫字母
                /\d/.test(password),     // 數字
                /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)  // 特殊字符
            ];
            
            requirements.forEach((item, index) => {
                const icon = item.querySelector('.requirement-icon i');
                const text = item.querySelector('span:last-child');
                
                if (checks[index]) {
                    icon.className = 'fas fa-check-circle requirement-met';
                    text.classList.add('requirement-met');
                    text.classList.remove('requirement-unmet');
                } else {
                    icon.className = 'fas fa-circle requirement-unmet';
                    text.classList.add('requirement-unmet');
                    text.classList.remove('requirement-met');
                }
            });
        }
        
        function checkPasswordMatch() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const matchHelp = document.getElementById('matchHelp');
            const confirmInput = document.getElementById('confirm_password');
            
            if (confirmPassword.length === 0) {
                matchHelp.textContent = '';
                confirmInput.classList.remove('is-danger', 'is-success');
                return;
            }
            
            if (newPassword === confirmPassword) {
                matchHelp.textContent = '✓ 密碼匹配';
                matchHelp.className = 'help has-text-success';
                confirmInput.classList.remove('is-danger');
                confirmInput.classList.add('is-success');
            } else {
                matchHelp.textContent = '✗ 密碼不匹配';
                matchHelp.className = 'help has-text-danger';
                confirmInput.classList.remove('is-success');
                confirmInput.classList.add('is-danger');
            }
        }
        
        // 表單提交驗證
        document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('新密碼與確認密碼不匹配');
                return false;
            }
            
            if (currentStrengthData && !currentStrengthData.is_valid) {
                e.preventDefault();
                alert('密碼不符合安全要求，請修改後再試');
                return false;
            }
        });
    </script>
</body>
</html>