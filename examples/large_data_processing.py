#!/usr/bin/env python3
"""
大量資料處理範例
展示如何使用批次處理工具處理大量資料
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db
from model import Money, Transaction, Account
from utils.database.batch_processor import BatchProcessor, ChunkProcessor, stream_large_dataset
from utils.database.bulk_operations import BulkOperations, DataAggregator
from datetime import datetime, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== 範例 1: 批次處理交易記錄 ====================

def example_batch_processing():
    """批次處理大量交易記錄"""
    print("\n" + "="*60)
    print("範例 1: 批次處理交易記錄")
    print("="*60)
    
    with get_db() as db:
        # 查詢所有需要處理的記錄
        query = db.query(Money).filter(Money.is_deleted == False)
        
        # 定義處理函數
        def process_batch(batch):
            """處理一批交易記錄"""
            for record in batch:
                # 這裡執行實際的處理邏輯
                # 例如：計算稅額、更新狀態等
                if record.total and not record.tax:
                    record.tax = record.total * 0.05  # 計算 5% 稅額
            
            # 批次提交更新
            db.commit()
            print(f"  處理了 {len(batch)} 筆記錄")
        
        # 使用批次處理器
        processor = BatchProcessor(batch_size=100)
        stats = processor.process_in_batches(query, process_batch)
        
        print(f"\n處理統計:")
        print(f"  總處理數: {stats['total_processed']} 筆")
        print(f"  批次數: {stats['batches_completed']}")
        print(f"  錯誤數: {stats['errors']}")

# ==================== 範例 2: 串流處理（節省記憶體） ====================

def example_stream_processing():
    """串流處理大量資料（記憶體效率最佳）"""
    print("\n" + "="*60)
    print("範例 2: 串流處理（節省記憶體）")
    print("="*60)
    
    with get_db() as db:
        query = db.query(Money).filter(Money.money_type == '收入')
        
        total_income = 0
        record_count = 0
        
        # 使用串流處理，一次只載入少量記錄到記憶體
        for record in stream_large_dataset(query, batch_size=50):
            total_income += record.total or 0
            record_count += 1
            
            # 每處理 100 筆顯示進度
            if record_count % 100 == 0:
                print(f"  已處理 {record_count} 筆記錄...")
        
        print(f"\n統計結果:")
        print(f"  總收入記錄: {record_count} 筆")
        print(f"  總收入金額: ${total_income:,.2f}")

# ==================== 範例 3: 批次插入新資料 ====================

def example_bulk_insert():
    """批次插入大量新資料"""
    print("\n" + "="*60)
    print("範例 3: 批次插入新資料")
    print("="*60)
    
    # 準備要插入的資料
    new_records = []
    base_date = datetime.now()
    
    for i in range(1000):
        new_records.append({
            'name': f'批次交易 {i+1}',
            'total': 1000 + i,
            'tax': (1000 + i) * 0.05,
            'money_type': '收入' if i % 2 == 0 else '支出',
            'subject_code': '4110' if i % 2 == 0 else '5110',
            'a_time': base_date - timedelta(days=i),
            'created_at': datetime.now(),
            'created_by': 'batch_import',
            'is_deleted': False
        })
    
    with get_db() as db:
        # 使用批次插入（比逐筆插入快 10-100 倍）
        inserted = BulkOperations.bulk_insert_mappings(
            db, Money, new_records, chunk_size=200
        )
        
        print(f"\n批次插入完成:")
        print(f"  成功插入: {inserted} 筆記錄")

# ==================== 範例 4: 按日期分塊處理 ====================

def example_chunk_processing():
    """按日期分塊處理超大資料集"""
    print("\n" + "="*60)
    print("範例 4: 按日期分塊處理")
    print("="*60)
    
    with get_db() as db:
        def process_chunk(chunk_data):
            """處理一個日期分塊的資料"""
            # 計算這個分塊的統計資訊
            total_amount = sum(record.total or 0 for record in chunk_data)
            avg_amount = total_amount / len(chunk_data) if chunk_data else 0
            
            print(f"  分塊統計 - 記錄數: {len(chunk_data)}, "
                  f"總金額: ${total_amount:,.2f}, "
                  f"平均金額: ${avg_amount:,.2f}")
        
        # 按月份分塊處理資料
        stats = ChunkProcessor.process_by_date_chunks(
            db, Money, 'a_time', process_chunk, chunk_days=30
        )
        
        print(f"\n分塊處理統計:")
        print(f"  處理分塊數: {stats['chunks_processed']}")
        print(f"  總記錄數: {stats['total_records']}")

# ==================== 範例 5: 資料聚合優化 ====================

def example_data_aggregation():
    """在資料庫層級進行聚合（高效能）"""
    print("\n" + "="*60)
    print("範例 5: 資料聚合優化")
    print("="*60)
    
    with get_db() as db:
        # 在資料庫層級進行聚合，而不是載入所有資料到應用層
        results = DataAggregator.aggregate_in_database(
            db, Money,
            group_by=['money_type', 'subject_code'],
            aggregations={
                'total': 'sum',
                'id': 'count',
                'tax': 'avg'
            }
        )
        
        print("\n聚合結果（前 10 筆）:")
        for i, result in enumerate(results[:10], 1):
            print(f"  {i}. 類型: {result['money_type']}, "
                  f"科目: {result['subject_code']}, "
                  f"總金額: ${result.get('total_sum', 0):,.2f}, "
                  f"記錄數: {result.get('id_count', 0)}, "
                  f"平均稅額: ${result.get('tax_avg', 0):.2f}")

# ==================== 範例 6: 並行處理 ====================

def example_parallel_processing():
    """使用多執行緒並行處理資料"""
    print("\n" + "="*60)
    print("範例 6: 並行處理")
    print("="*60)
    
    with get_db() as db:
        query = db.query(Money).filter(Money.is_deleted == False)
        
        processed_count = 0
        
        def process_record(record):
            """處理單筆記錄"""
            nonlocal processed_count
            # 模擬一些處理工作
            if record.total:
                # 執行某些計算或驗證
                _ = record.total * 1.1
            processed_count += 1
        
        # 使用 4 個執行緒並行處理
        processor = BatchProcessor(batch_size=100)
        stats = processor.parallel_process(query, process_record, num_workers=4)
        
        print(f"\n並行處理統計:")
        print(f"  成功處理: {stats['processed']} 筆")
        print(f"  錯誤: {stats['errors']} 筆")

# ==================== 範例 7: UPSERT 操作 ====================

def example_upsert():
    """批次 UPSERT（插入或更新）"""
    print("\n" + "="*60)
    print("範例 7: UPSERT 操作")
    print("="*60)
    
    # 準備資料（可能包含新記錄和需要更新的記錄）
    accounts_data = [
        {
            'code': 'ACC001',
            'name': '現金帳戶',
            'category': '現金',
            'init_amount': 10000,
            'is_deleted': False
        },
        {
            'code': 'ACC002',
            'name': '銀行帳戶',
            'category': '銀行帳戶',
            'init_amount': 50000,
            'is_deleted': False
        }
    ]
    
    with get_db() as db:
        # 執行 UPSERT（如果 code 已存在則更新，否則插入）
        BulkOperations.upsert_records(
            db, Account, accounts_data, unique_key='code'
        )
        
        print("\nUPSERT 操作完成")
        print(f"  處理記錄數: {len(accounts_data)}")

# ==================== 主程式 ====================

def main():
    """執行所有範例"""
    print("\n" + "="*70)
    print(" 大量資料處理效能優化範例 ")
    print("="*70)
    
    examples = [
        ("批次處理", example_batch_processing),
        ("串流處理", example_stream_processing),
        # ("批次插入", example_bulk_insert),  # 會實際插入資料，謹慎使用
        ("分塊處理", example_chunk_processing),
        ("資料聚合", example_data_aggregation),
        ("並行處理", example_parallel_processing),
        # ("UPSERT", example_upsert),  # 會修改資料，謹慎使用
    ]
    
    for name, func in examples:
        try:
            func()
        except Exception as e:
            print(f"\n❌ {name} 範例執行失敗: {e}")
    
    print("\n" + "="*70)
    print(" 所有範例執行完成 ")
    print("="*70)
    
    print("\n💡 效能優化建議:")
    print("1. 使用批次處理而不是逐筆處理")
    print("2. 使用串流處理節省記憶體")
    print("3. 在資料庫層級進行聚合")
    print("4. 使用並行處理提升速度")
    print("5. 使用 UPSERT 減少查詢次數")
    print("6. 定期監控和優化慢查詢")

if __name__ == "__main__":
    main()