"""
設定路由輔助函數
提取重複的邏輯以減少代碼重複
"""

from flask import render_template, redirect, url_for, flash, request
from data.menu_data import menu
from database import get_db
from datetime import datetime
from typing import Dict, Any, List, Optional


def get_menu_context(selected_menu: str = '設定') -> Dict[str, Any]:
    """
    獲取標準菜單上下文
    
    Args:
        selected_menu: 選中的菜單項
        
    Returns:
        包含菜單資訊的字典
    """
    return {
        'sidebar_items': list(menu.keys()),
        'selected': selected_menu
    }


def render_settings_template(template_name: str, selected_menu: str = '設定', **kwargs) -> str:
    """
    渲染設定頁面模板
    
    Args:
        template_name: 模板名稱
        selected_menu: 選中的菜單項
        **kwargs: 額外的模板變數
        
    Returns:
        渲染的模板內容
    """
    context = get_menu_context(selected_menu)
    context.update(kwargs)
    return render_template(template_name, **context)


def redirect_with_flash(endpoint: str, message: str, category: str = 'success'):
    """
    重導向並顯示 flash 訊息
    
    Args:
        endpoint: 重導向的端點
        message: flash 訊息
        category: 訊息類別
        
    Returns:
        重導向回應
    """
    flash(message, category)
    return redirect(url_for(endpoint))


def parse_date(val: str) -> Optional[datetime]:
    """
    將字串日期轉換為 datetime 物件
    
    Args:
        val: 日期字串
        
    Returns:
        datetime 物件或 None
    """
    if val:
        try:
            return datetime.strptime(val, '%Y-%m-%d')
        except Exception:
            return None
    return None


def format_account_display(account) -> str:
    """
    格式化帳戶顯示名稱
    
    Args:
        account: 帳戶物件
        
    Returns:
        格式化的帳戶顯示名稱
    """
    account_display = account.name
    
    if account.bank_name:
        account_display += f" ({account.bank_name}"
        if account.account_number:
            # 只顯示帳號後4位
            masked_number = ("*" * (len(account.account_number) - 4) + 
                           account.account_number[-4:] 
                           if len(account.account_number) > 4 
                           else account.account_number)
            account_display += f" {masked_number}"
        account_display += ")"
    
    return account_display


def get_bank_accounts_data() -> List[Dict[str, Any]]:
    """
    獲取銀行帳戶列表資料
    
    Returns:
        銀行帳戶資料列表
    """
    from model import Account
    
    with get_db() as db:
        bank_accounts = db.query(Account).filter_by(category='銀行帳戶').order_by(
            Account.is_default.desc(), 
            Account.name.asc()
        ).all()
        
        accounts_data = []
        for account in bank_accounts:
            accounts_data.append({
                'id': account.id,
                'name': account.name,
                'display_name': format_account_display(account),
                'is_default': account.is_default,
                'bank_name': account.bank_name,
                'account_number': account.account_number
            })
        
        return accounts_data


def get_departments_data() -> List[Dict[str, Any]]:
    """
    獲取部門列表資料
    
    Returns:
        部門資料列表
    """
    from model import Department
    
    with get_db() as db:
        departments = db.query(Department).filter_by(is_active=True).order_by(Department.name.asc()).all()
        
        departments_data = []
        for dept in departments:
            departments_data.append({
                'id': dept.id,
                'name': dept.name,
                'code': dept.code or '',
                'note': dept.note or '',  # 使用 note 而不是 description
                'is_active': dept.is_active,
                'parent_id': dept.parent_id
            })
        
        return departments_data


def get_projects_data() -> List[Dict[str, Any]]:
    """
    獲取專案列表資料
    
    Returns:
        專案資料列表
    """
    from model import Project, Department
    
    with get_db() as db:
        projects = db.query(Project).join(
            Department, Project.department_id == Department.id, isouter=True
        ).order_by(Project.created_at.desc()).all()
        
        projects_data = []
        for project in projects:
            projects_data.append({
                'id': project.id,
                'name': project.name,
                'description': project.description or '',
                'budget': project.budget or 0,
                'start_date': project.start_date.strftime('%Y-%m-%d') if project.start_date else '',
                'end_date': project.end_date.strftime('%Y-%m-%d') if project.end_date else '',
                'department_name': project.department.name if project.department else '',
                'status': project.status,
                'created_at': project.created_at.strftime('%Y-%m-%d') if project.created_at else ''
            })
        
        return projects_data


def get_payment_identities_data(page: int = 1, per_page: int = 50) -> Dict[str, Any]:
    """
    獲取收支對象列表資料
    
    Args:
        page: 頁碼
        per_page: 每頁數量
        
    Returns:
        包含收支對象資料和分頁資訊的字典
    """
    from model import PaymentIdentity, PaymentIdentityType
    from sqlalchemy import desc
    
    with get_db() as db:
        # 建立基礎查詢
        query = db.query(PaymentIdentity).join(
            PaymentIdentityType, 
            PaymentIdentity.type_id == PaymentIdentityType.id, 
            isouter=True
        ).order_by(desc(PaymentIdentity.created_at))
        
        # 分頁
        total = query.count()
        identities = query.offset((page - 1) * per_page).limit(per_page).all()
        
        # 轉換為字典格式
        identities_data = []
        for identity in identities:
            identities_data.append({
                'id': identity.id,
                'name': identity.name,
                'tax_id': identity.tax_id or '',
                'contact_person': identity.contact or '',  # 使用 contact 而不是 contact_person
                'phone': identity.mobile or '',  # 使用 mobile 而不是 phone
                'email': '',  # PaymentIdentity 沒有 email 欄位
                'address': '',  # PaymentIdentity 沒有 address 欄位
                'type_name': identity.identity_type.name if identity.identity_type else '一般對象',
                'created_at': identity.created_at.strftime('%Y-%m-%d') if identity.created_at else ''
            })
        
        return {
            'identities': identities_data,
            'total': total,
            'page': page,
            'per_page': per_page,
            'has_prev': page > 1,
            'has_next': (page * per_page) < total,
            'prev_page': page - 1 if page > 1 else None,
            'next_page': page + 1 if (page * per_page) < total else None
        }


def validate_form_data(form_fields: Dict[str, str], required_fields: List[str]) -> List[str]:
    """
    驗證表單資料
    
    Args:
        form_fields: 表單欄位字典
        required_fields: 必需欄位列表
        
    Returns:
        錯誤訊息列表
    """
    errors = []
    
    for field in required_fields:
        if not form_fields.get(field, '').strip():
            # 將字段名轉換為中文
            field_names = {
                'name': '名稱',
                'tax_id': '統一編號',
                'contact_person': '聯絡人',
                'phone': '電話',
                'email': '電子郵件',
                'address': '地址',
                'description': '描述',
                'budget': '預算'
            }
            field_display = field_names.get(field, field)
            errors.append(f'{field_display}不能為空')
    
    return errors


def handle_form_errors(errors: List[str], redirect_endpoint: str):
    """
    處理表單錯誤
    
    Args:
        errors: 錯誤訊息列表
        redirect_endpoint: 重導向端點
        
    Returns:
        重導向回應
    """
    for error in errors:
        flash(error, 'error')
    return redirect(url_for(redirect_endpoint))


class SettingsFormHandler:
    """設定表單處理器"""
    
    @staticmethod
    def handle_department_form(form_data: Dict[str, str], department=None):
        """
        處理部門表單
        
        Args:
            form_data: 表單資料
            department: 部門物件（編輯時使用）
            
        Returns:
            (success, errors, department) 元組
        """
        from model import Department
        
        errors = validate_form_data(form_data, ['name'])
        if errors:
            return False, errors, department
        
        try:
            with get_db() as db:
                if department is None:
                    # 新增部門
                    department = Department(
                        name=form_data['name'].strip(),
                        note=form_data.get('description', '').strip(),
                        is_active=True,
                        created_at=datetime.now()
                    )
                    db.add(department)
                else:
                    # 更新部門
                    department.name = form_data['name'].strip()
                    department.note = form_data.get('description', '').strip()
                
                db.commit()
                return True, [], department
                
        except Exception as e:
            return False, [f'儲存失敗: {str(e)}'], department
    
    @staticmethod
    def handle_project_form(form_data: Dict[str, str], project=None):
        """
        處理專案表單
        
        Args:
            form_data: 表單資料
            project: 專案物件（編輯時使用）
            
        Returns:
            (success, errors, project) 元組
        """
        from model import Project
        
        errors = validate_form_data(form_data, ['name', 'department_id'])
        if errors:
            return False, errors, project
        
        try:
            with get_db() as db:
                if project is None:
                    # 新增專案
                    project = Project(
                        name=form_data['name'].strip(),
                        description=form_data.get('description', '').strip(),
                        department_id=int(form_data['department_id']),
                        budget=float(form_data.get('budget', 0) or 0),
                        start_date=parse_date(form_data.get('start_date')),
                        end_date=parse_date(form_data.get('end_date')),
                        status='進行中',
                        created_at=datetime.now()
                    )
                    db.add(project)
                else:
                    # 更新專案
                    project.name = form_data['name'].strip()
                    project.description = form_data.get('description', '').strip()
                    project.department_id = int(form_data['department_id'])
                    project.budget = float(form_data.get('budget', 0) or 0)
                    project.start_date = parse_date(form_data.get('start_date'))
                    project.end_date = parse_date(form_data.get('end_date'))
                
                db.commit()
                return True, [], project
                
        except Exception as e:
            return False, [f'儲存失敗: {str(e)}'], project