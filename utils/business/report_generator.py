"""
報表生成器
提供各種財務報表的生成功能
"""
from sqlalchemy import func, extract
from model import Money, Account, AccountSubject, PaymentIdentity
from database import get_db
from datetime import datetime, timedelta
from typing import Dict

class ReportGenerator:
    """報表生成器"""
    
    @staticmethod
    def generate_monthly_report(year: int, month: int) -> Dict:
        """
        生成月度財務報表
        
        Args:
            year (int): 年份
            month (int): 月份
            
        Returns:
            Dict: 月度報表資料
        """
        with get_db() as db:
            # 收支統計
            summary = db.query(
                Money.money_type,
                func.sum(Money.total).label('total'),
                func.count(Money.id).label('count'),
                func.avg(Money.total).label('average')
            ).filter(
                extract('year', Money.a_time) == year,
                extract('month', Money.a_time) == month
            ).group_by(Money.money_type).all()
            
            # 按會計科目統計
            by_subject = db.query(
                AccountSubject.name,
                AccountSubject.code,
                Money.money_type,
                func.sum(Money.total).label('total'),
                func.count(Money.id).label('count')
            ).join(AccountSubject).filter(
                extract('year', Money.a_time) == year,
                extract('month', Money.a_time) == month
            ).group_by(
                AccountSubject.name,
                AccountSubject.code,
                Money.money_type
            ).order_by(func.sum(Money.total).desc()).all()
            
            # 按帳戶統計
            by_account = db.query(
                Account.name,
                Account.category,
                Money.money_type,
                func.sum(Money.total).label('total'),
                func.count(Money.id).label('count')
            ).join(Account).filter(
                extract('year', Money.a_time) == year,
                extract('month', Money.a_time) == month
            ).group_by(
                Account.name,
                Account.category,
                Money.money_type
            ).order_by(func.sum(Money.total).desc()).all()
            
            # 按收支對象統計
            by_identity = db.query(
                PaymentIdentity.name,
                PaymentIdentity.type,
                Money.money_type,
                func.sum(Money.total).label('total'),
                func.count(Money.id).label('count')
            ).join(PaymentIdentity).filter(
                extract('year', Money.a_time) == year,
                extract('month', Money.a_time) == month
            ).group_by(
                PaymentIdentity.name,
                PaymentIdentity.type,
                Money.money_type
            ).order_by(func.sum(Money.total).desc()).limit(20).all()
            
            # 整理資料
            report_data = {
                'period': {
                    'year': year,
                    'month': month,
                    'period_name': f'{year}年{month}月'
                },
                'summary': {
                    result.money_type: {
                        'total': result.total or 0,
                        'count': result.count,
                        'average': round(result.average or 0, 2)
                    }
                    for result in summary
                },
                'by_subject': [
                    {
                        'subject_name': result.name,
                        'subject_code': result.code,
                        'money_type': result.money_type,
                        'total': result.total or 0,
                        'count': result.count
                    }
                    for result in by_subject
                ],
                'by_account': [
                    {
                        'account_name': result.name,
                        'account_category': result.category,
                        'money_type': result.money_type,
                        'total': result.total or 0,
                        'count': result.count
                    }
                    for result in by_account
                ],
                'by_identity': [
                    {
                        'identity_name': result.name,
                        'identity_type': result.type,
                        'money_type': result.money_type,
                        'total': result.total or 0,
                        'count': result.count
                    }
                    for result in by_identity
                ]
            }
            
            # 計算淨收入
            income_total = report_data['summary'].get('收入', {}).get('total', 0)
            expense_total = report_data['summary'].get('支出', {}).get('total', 0)
            report_data['net_income'] = income_total - expense_total
            
            return report_data
    
    @staticmethod
    def generate_yearly_report(year: int) -> Dict:
        """
        生成年度財務報表
        
        Args:
            year (int): 年份
            
        Returns:
            Dict: 年度報表資料
        """
        with get_db() as db:
            # 按月份統計
            monthly_summary = db.query(
                extract('month', Money.a_time).label('month'),
                Money.money_type,
                func.sum(Money.total).label('total'),
                func.count(Money.id).label('count')
            ).filter(
                extract('year', Money.a_time) == year
            ).group_by(
                extract('month', Money.a_time),
                Money.money_type
            ).order_by(extract('month', Money.a_time)).all()
            
            # 年度總計
            yearly_total = db.query(
                Money.money_type,
                func.sum(Money.total).label('total'),
                func.count(Money.id).label('count'),
                func.avg(Money.total).label('average')
            ).filter(
                extract('year', Money.a_time) == year
            ).group_by(Money.money_type).all()
            
            # 最大支出項目
            top_expenses = db.query(
                Money.name,
                Money.total,
                Money.a_time,
                AccountSubject.name.label('subject_name')
            ).join(AccountSubject).filter(
                extract('year', Money.a_time) == year,
                Money.money_type == '支出'
            ).order_by(Money.total.desc()).limit(10).all()
            
            # 整理月度資料
            monthly_data = {}
            for result in monthly_summary:
                month = int(result.month)
                if month not in monthly_data:
                    monthly_data[month] = {}
                monthly_data[month][result.money_type] = {
                    'total': result.total or 0,
                    'count': result.count
                }
            
            # 確保每個月都有資料
            for month in range(1, 13):
                if month not in monthly_data:
                    monthly_data[month] = {}
                for money_type in ['收入', '支出']:
                    if money_type not in monthly_data[month]:
                        monthly_data[month][money_type] = {'total': 0, 'count': 0}
            
            return {
                'year': year,
                'monthly_data': monthly_data,
                'yearly_total': {
                    result.money_type: {
                        'total': result.total or 0,
                        'count': result.count,
                        'average': round(result.average or 0, 2)
                    }
                    for result in yearly_total
                },
                'top_expenses': [
                    {
                        'name': result.name,
                        'total': result.total,
                        'date': result.a_time.strftime('%Y-%m-%d') if result.a_time else '',
                        'subject': result.subject_name
                    }
                    for result in top_expenses
                ]
            }
    
    @staticmethod
    def generate_account_statement(account_id: int, start_date: datetime, end_date: datetime) -> Dict:
        """
        生成帳戶對帳單
        
        Args:
            account_id (int): 帳戶ID
            start_date (datetime): 開始日期
            end_date (datetime): 結束日期
            
        Returns:
            Dict: 對帳單資料
        """
        with get_db() as db:
            # 取得帳戶資訊
            account = db.query(Account).filter(Account.id == account_id).first()
            if not account:
                return {}
            
            # 期初餘額（開始日期之前的餘額）
            opening_transactions = db.query(Money).filter(
                Money.account_id == account_id,
                Money.a_time < start_date.date()
            ).all()
            
            opening_balance = account.init_amount or 0
            for trans in opening_transactions:
                money_type = getattr(trans, 'money_type', '')
                total = getattr(trans, 'total', 0)
                if money_type == '收入':
                    opening_balance += total
                elif money_type == '支出':
                    opening_balance -= total
            
            # 期間交易
            period_transactions = db.query(Money).filter(
                Money.account_id == account_id,
                Money.a_time >= start_date.date(),
                Money.a_time <= end_date.date()
            ).order_by(Money.a_time, Money.id).all()
            
            # 計算期末餘額
            closing_balance = opening_balance
            transaction_details = []
            
            for trans in period_transactions:
                money_type = getattr(trans, 'money_type', '')
                total = getattr(trans, 'total', 0)
                a_time = getattr(trans, 'a_time', None)
                name = getattr(trans, 'name', '')
                note = getattr(trans, 'note', '')

                if money_type == '收入':
                    closing_balance += total
                    amount = total
                elif money_type == '支出':
                    closing_balance -= total
                    amount = -total
                else:
                    amount = 0

                transaction_details.append({
                    'date': a_time.strftime('%Y-%m-%d') if a_time else '',
                    'name': name,
                    'type': money_type,
                    'amount': amount,
                    'balance': closing_balance,
                    'note': note or ''
                })
            
            # 期間統計
            period_income = sum([getattr(t, 'total', 0) for t in period_transactions if getattr(t, 'money_type', '') == '收入'])
            period_expense = sum([getattr(t, 'total', 0) for t in period_transactions if getattr(t, 'money_type', '') == '支出'])
            
            return {
                'account': {
                    'id': account.id,
                    'name': account.name,
                    'category': account.category,
                    'bank_name': account.bank_name,
                    'account_number': account.account_number
                },
                'period': {
                    'start_date': start_date.date(),
                    'end_date': end_date.date()
                },
                'balances': {
                    'opening': opening_balance,
                    'closing': closing_balance,
                    'change': closing_balance - opening_balance
                },
                'period_summary': {
                    'income': period_income,
                    'expense': period_expense,
                    'net': period_income - period_expense,
                    'transaction_count': len(period_transactions)
                },
                'transactions': transaction_details
            }
    
    @staticmethod
    def generate_cash_flow_report(start_date: datetime, end_date: datetime) -> Dict:
        """
        生成現金流量報表
        
        Args:
            start_date (datetime): 開始日期
            end_date (datetime): 結束日期
            
        Returns:
            Dict: 現金流量報表
        """
        with get_db() as db:
            # 按帳戶分類統計
            account_flows = db.query(
                Account.name,
                Account.category,
                Money.money_type,
                func.sum(Money.total).label('total')
            ).join(Account).filter(
                Money.a_time >= start_date.date(),
                Money.a_time <= end_date.date()
            ).group_by(
                Account.name,
                Account.category,
                Money.money_type
            ).all()
            
            # 按日期統計
            daily_flows = db.query(
                Money.a_time,
                Money.money_type,
                func.sum(Money.total).label('total')
            ).filter(
                Money.a_time >= start_date.date(),
                Money.a_time <= end_date.date()
            ).group_by(
                Money.a_time,
                Money.money_type
            ).order_by(Money.a_time).all()
            
            # 整理帳戶資料
            account_data = {}
            for result in account_flows:
                key = f"{result.name} ({result.category})"
                if key not in account_data:
                    account_data[key] = {'收入': 0, '支出': 0}
                account_data[key][result.money_type] = result.total or 0
            
            # 整理日期資料
            daily_data = {}
            for result in daily_flows:
                date_str = result.a_time.strftime('%Y-%m-%d')
                if date_str not in daily_data:
                    daily_data[date_str] = {'收入': 0, '支出': 0}
                daily_data[date_str][result.money_type] = result.total or 0
            
            # 計算累計現金流
            cumulative_flow = 0
            daily_cumulative = []
            for date_str in sorted(daily_data.keys()):
                daily_net = daily_data[date_str]['收入'] - daily_data[date_str]['支出']
                cumulative_flow += daily_net
                daily_cumulative.append({
                    'date': date_str,
                    'income': daily_data[date_str]['收入'],
                    'expense': daily_data[date_str]['支出'],
                    'net': daily_net,
                    'cumulative': cumulative_flow
                })
            
            return {
                'period': {
                    'start_date': start_date.date(),
                    'end_date': end_date.date()
                },
                'by_account': [
                    {
                        'account': account,
                        'income': data['收入'],
                        'expense': data['支出'],
                        'net': data['收入'] - data['支出']
                    }
                    for account, data in account_data.items()
                ],
                'daily_flow': daily_cumulative,
                'summary': {
                    'total_income': sum([data['收入'] for data in account_data.values()]),
                    'total_expense': sum([data['支出'] for data in account_data.values()]),
                    'net_flow': cumulative_flow
                }
            }
    
    @staticmethod
    def generate_payment_status_report() -> Dict:
        """
        生成付款狀態報表
        
        Returns:
            Dict: 付款狀態報表
        """
        today = datetime.now().date()
        
        with get_db() as db:
            # 逾期未付款
            overdue = db.query(Money).filter(
                Money.should_paid_date < today,
                Money.is_paid == False
            ).order_by(Money.should_paid_date).all()
            
            # 即將到期（7天內）
            upcoming = db.query(Money).filter(
                Money.should_paid_date >= today,
                Money.should_paid_date <= today + timedelta(days=7),
                Money.is_paid == False
            ).order_by(Money.should_paid_date).all()
            
            # 本月已付款
            current_month_paid = db.query(Money).filter(
                extract('year', Money.paid_date) == today.year,
                extract('month', Money.paid_date) == today.month,
                Money.is_paid == True
            ).all()
            
            return {
                'report_date': today,
                'overdue_payments': [
                    {
                        'id': getattr(record, 'id', 0),
                        'name': getattr(record, 'name', ''),
                        'total': getattr(record, 'total', 0),
                        'should_paid_date': (lambda d: d.strftime('%Y-%m-%d') if d else '')(getattr(record, 'should_paid_date', None)),
                        'days_overdue': (lambda d: (today - d).days if d else 0)(getattr(record, 'should_paid_date', None))
                    }
                    for record in overdue
                ],
                'upcoming_payments': [
                    {
                        'id': getattr(record, 'id', 0),
                        'name': getattr(record, 'name', ''),
                        'total': getattr(record, 'total', 0),
                        'should_paid_date': (lambda d: d.strftime('%Y-%m-%d') if d else '')(getattr(record, 'should_paid_date', None)),
                        'days_until_due': (lambda d: (d - today).days if d else 0)(getattr(record, 'should_paid_date', None))
                    }
                    for record in upcoming
                ],
                'current_month_paid': [
                    {
                        'id': getattr(record, 'id', 0),
                        'name': getattr(record, 'name', ''),
                        'total': getattr(record, 'total', 0),
                        'paid_date': (lambda d: d.strftime('%Y-%m-%d') if d else '')(getattr(record, 'paid_date', None))
                    }
                    for record in current_month_paid
                ],
                'summary': {
                    'overdue_count': len(overdue),
                    'overdue_amount': sum([r.total for r in overdue]),
                    'upcoming_count': len(upcoming),
                    'upcoming_amount': sum([r.total for r in upcoming]),
                    'paid_this_month_count': len(current_month_paid),
                    'paid_this_month_amount': sum([r.total for r in current_month_paid])
                }
            }