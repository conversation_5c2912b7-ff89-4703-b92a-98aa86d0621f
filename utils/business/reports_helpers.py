"""
報表路由輔助函數
提取重複的邏輯以減少代碼重複
"""

from flask import request, jsonify
from datetime import datetime, date
from functools import wraps
from typing import Dict, Any, Optional, Callable


def get_year_param(default_current: bool = True) -> int:
    """
    統一獲取年份參數
    
    Args:
        default_current: 是否默認使用當前年份
        
    Returns:
        年份整數
    """
    if default_current:
        return request.args.get('year', datetime.now().year, type=int)
    else:
        return request.args.get('year', type=int)


def get_month_param(default_current: bool = True) -> int:
    """
    統一獲取月份參數
    
    Args:
        default_current: 是否默認使用當前月份
        
    Returns:
        月份整數
    """
    if default_current:
        return request.args.get('month', datetime.now().month, type=int)
    else:
        return request.args.get('month', type=int)


def get_date_range_params() -> tuple[Optional[str], Optional[str]]:
    """
    獲取日期範圍參數
    
    Returns:
        (start_date, end_date) 元組
    """
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    return start_date, end_date


def get_account_id_param(required: bool = False) -> Optional[int]:
    """
    獲取帳戶ID參數
    
    Args:
        required: 是否必需
        
    Returns:
        帳戶ID或None
    """
    account_id = request.args.get('account_id', type=int)
    if required and not account_id:
        raise ValueError('缺少帳戶ID參數')
    return account_id


def get_common_filter_params() -> Dict[str, Any]:
    """
    獲取常用的過濾參數
    
    Returns:
        包含常用參數的字典
    """
    return {
        'subject_code': request.args.get('subject_code'),
        'subject_category': request.args.get('subject_category'), 
        'payment_identity_id': request.args.get('payment_identity_id', type=int),
        'department_id': request.args.get('department_id', type=int),
        'year': request.args.get('year', type=int),
        'month': request.args.get('month', type=int)
    }


def create_success_response(data: Any) -> Dict[str, Any]:
    """
    創建成功的API響應
    
    Args:
        data: 響應資料
        
    Returns:
        標準化的成功響應
    """
    return {
        'success': True,
        'data': data
    }


def create_error_response(error: str) -> Dict[str, Any]:
    """
    創建錯誤的API響應
    
    Args:
        error: 錯誤訊息
        
    Returns:
        標準化的錯誤響應
    """
    return {
        'success': False,
        'error': error
    }


def api_error_handler(func: Callable) -> Callable:
    """
    API錯誤處理裝飾器
    統一處理API路由的錯誤
    
    Args:
        func: 要裝飾的函數
        
    Returns:
        裝飾後的函數
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            # 如果函數返回的不是jsonify對象，則包裝成成功響應
            if not hasattr(result, 'status_code'):
                result = jsonify(create_success_response(result))
            return result
        except ValueError as ve:
            return jsonify(create_error_response(str(ve))), 400
        except Exception as e:
            return jsonify(create_error_response(str(e))), 500
    
    return wrapper


def get_default_month_range():
    """
    獲取預設的月份範圍（本月）
    
    Returns:
        (start_date, end_date) 字符串元組
    """
    now = datetime.now()
    start_date = f"{now.year}-{now.month:02d}-01"
    
    # 計算本月最後一天
    if now.month == 12:
        next_month = now.replace(year=now.year + 1, month=1, day=1)
    else:
        next_month = now.replace(month=now.month + 1, day=1)
    
    from datetime import timedelta
    last_day = next_month - timedelta(days=1)
    end_date = f"{now.year}-{now.month:02d}-{last_day.day:02d}"
    
    return start_date, end_date


class ReportParamsExtractor:
    """
    報表參數提取器類
    提供統一的參數提取和驗證
    """
    
    @staticmethod
    def extract_monthly_params() -> Dict[str, int]:
        """提取月度報表參數"""
        return {
            'year': get_year_param(),
            'month': get_month_param()
        }
    
    @staticmethod
    def extract_yearly_params() -> Dict[str, int]:
        """提取年度報表參數"""
        return {
            'year': get_year_param()
        }
    
    @staticmethod
    def extract_account_statement_params() -> Dict[str, Any]:
        """提取帳戶報表參數"""
        start_date, end_date = get_date_range_params()
        
        # 如果沒有提供日期範圍，使用本月
        if not start_date or not end_date:
            start_date, end_date = get_default_month_range()
        
        return {
            'account_id': get_account_id_param(),
            'start_date': start_date,
            'end_date': end_date
        }
    
    @staticmethod  
    def extract_top_expenses_params() -> Dict[str, Any]:
        """提取熱門支出參數"""
        return {
            'limit': request.args.get('limit', 10, type=int),
            'year': get_year_param(default_current=False),
            'month': get_month_param(default_current=False)
        }


def validate_required_params(params: Dict[str, Any], required: list) -> None:
    """
    驗證必需的參數
    
    Args:
        params: 參數字典
        required: 必需的參數名稱列表
        
    Raises:
        ValueError: 當缺少必需參數時
    """
    missing = [param for param in required if not params.get(param)]
    if missing:
        raise ValueError(f'缺少必需參數: {", ".join(missing)}')


# 快捷函數
def get_monthly_params() -> Dict[str, int]:
    """快捷獲取月度參數"""
    return ReportParamsExtractor.extract_monthly_params()


def get_yearly_params() -> Dict[str, int]:
    """快捷獲取年度參數"""
    return ReportParamsExtractor.extract_yearly_params()


def api_success(data: Any):
    """快捷創建成功響應"""
    return jsonify(create_success_response(data))


def api_error(error: str, status_code: int = 500):
    """快捷創建錯誤響應"""
    return jsonify(create_error_response(error)), status_code