"""
銀行資料輔助函數
替代 data/bank_data.py 中的函數，從資料庫獲取銀行資料
"""

from database import engine
from sqlalchemy import text

def get_head_offices():
    """
    獲取所有總行
    返回格式: {code: name, ...}
    """
    with engine.connect() as conn:
        result = conn.execute(text("SELECT code, name FROM bank_head_offices ORDER BY code"))
        return {row.code: row.name for row in result}

def get_branches(head_code):
    """
    獲取指定總行的所有分行
    返回格式: [{"code": code, "name": name}, ...]
    """
    with engine.connect() as conn:
        result = conn.execute(
            text("SELECT code, name FROM bank_branches WHERE head_office_code = :head_code ORDER BY code"),
            {"head_code": head_code}
        )
        return [{"code": row.code, "name": row.name} for row in result]

def get_branch_info(branch_code):
    """
    獲取指定分行的詳細資訊
    返回格式: {"code": code, "name": name, "head_office": head_code, "head_office_name": head_name}
    """
    with engine.connect() as conn:
        result = conn.execute(
            text("""
                SELECT 
                    b.code, 
                    b.name, 
                    b.head_office_code,
                    h.name as head_office_name
                FROM bank_branches b
                JOIN bank_head_offices h ON b.head_office_code = h.code
                WHERE b.code = :branch_code
            """),
            {"branch_code": branch_code}
        )
        
        row = result.first()
        if not row:
            return None
            
        return {
            "code": row.code,
            "name": row.name, 
            "head_office": row.head_office_code,
            "head_office_name": row.head_office_name
        }