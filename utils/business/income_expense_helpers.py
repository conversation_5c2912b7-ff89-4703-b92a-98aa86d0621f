"""
income_expense.py 的輔助函數
將重複邏輯提取到這裡以減少主文件行數
"""

from flask import current_app
from model import Transaction
from datetime import datetime
from sqlalchemy import asc, desc

def parse_date(val):
    """解析日期字符串"""
    if val:
        try:
            return datetime.strptime(val, '%Y-%m-%d')
        except Exception:
            return None
    return None

def parse_date_range(daterange_str):
    """解析日期範圍字符串 '2023-01-01 to 2023-12-31'"""
    if not daterange_str:
        return None, None
    
    try:
        if ' to ' in daterange_str:
            parts = daterange_str.split(' to ')
            if len(parts) == 2:
                start_date = parse_date(parts[0].strip())
                end_date = parse_date(parts[1].strip())
                return start_date, end_date
    except Exception as e:
        current_app.logger.warning(f"日期範圍解析失敗: {daterange_str}, 錯誤: {str(e)}")
    
    return None, None

def apply_transaction_filters(query, filters):
    """統一的交易篩選邏輯"""
    from utils.web.debug_helpers import debug_log
    
    # 篩選條件映射表
    filter_conditions = [
        ('start_date', lambda q, v: q.filter(Transaction.transaction_date >= v)),
        ('end_date', lambda q, v: q.filter(Transaction.transaction_date <= v)),
        ('real_start_date', lambda q, v: q.filter(Transaction.invoice_date >= v)),
        ('real_end_date', lambda q, v: q.filter(Transaction.invoice_date <= v)),
        ('expected_start_date', lambda q, v: q.filter(Transaction.should_paid_date >= v)),
        ('expected_end_date', lambda q, v: q.filter(Transaction.should_paid_date <= v)),
        ('payment_identity', lambda q, v: q.filter(Transaction.payment_identity_id == v)),
        ('account', lambda q, v: q.filter(Transaction.account_id == v)),
        ('keyword', lambda q, v: q.filter(Transaction.description.contains(v))),
        ('subject_code', lambda q, v: q.filter(Transaction.subject_code == v)),
        ('project', lambda q, v: q.filter(Transaction.project_id == v)),
        ('target', lambda q, v: q.filter(Transaction.description.contains(v))),
        ('note', lambda q, v: q.filter(Transaction.note.contains(v))),
    ]
    
    # 應用所有篩選條件
    for filter_key, filter_func in filter_conditions:
        value = filters.get(filter_key)
        if value:
            query = filter_func(query, value)
            debug_log(f"應用篩選 {filter_key}", value)
    
    # 處理金額範圍
    amount_min = filters.get('amount_min')
    amount_max = filters.get('amount_max')
    
    if amount_min:
        try:
            min_amount = int(amount_min)
            query = query.filter(Transaction.total_amount >= min_amount)
            debug_log("應用最低金額篩選", min_amount)
        except ValueError:
            current_app.logger.warning(f"最低金額格式錯誤: {amount_min}")
    
    if amount_max:
        try:
            max_amount = int(amount_max)
            query = query.filter(Transaction.total_amount <= max_amount)
            debug_log("應用最高金額篩選", max_amount)
        except ValueError:
            current_app.logger.warning(f"最高金額格式錯誤: {amount_max}")
    
    # 特殊篩選邏輯
    voucher_type = filters.get('voucher_type')
    if voucher_type:
        if voucher_type == 'with_invoice':
            query = query.filter(Transaction.invoice_number.isnot(None))
        elif voucher_type == 'without_invoice':
            query = query.filter(Transaction.invoice_number.is_(None))
        debug_log("應用憑證類型篩選", voucher_type)
    
    attachment = filters.get('attachment')
    if attachment:
        if attachment == 'with_attachment':
            query = query.filter(Transaction.image_path.isnot(None))
        elif attachment == 'without_attachment':
            query = query.filter(Transaction.image_path.is_(None))
        debug_log("應用附件篩選", attachment)
    
    transaction_status = filters.get('transaction_status')
    if transaction_status:
        if transaction_status == 'paid':
            query = query.filter(Transaction.is_paid == True)
        elif transaction_status == 'unpaid':
            query = query.filter(Transaction.is_paid == False)
        debug_log("應用交易狀態篩選", transaction_status)
    
    # 根據交易對象類型查詢
    payment_identity_type = filters.get('payment_identity_type')
    if payment_identity_type:
        from model import PaymentIdentity
        from database import get_db
        
        with get_db() as db:
            identity_ids = [
                i.id for i in db.query(PaymentIdentity).filter(
                    PaymentIdentity.type_id == payment_identity_type
                ).all()
            ]
            if identity_ids:
                query = query.filter(Transaction.payment_identity_id.in_(identity_ids))
            else:
                query = query.filter(Transaction.payment_identity_id == -1)  # 不會有資料
        debug_log("應用交易對象類型篩選", payment_identity_type)
    
    return query

def extract_filter_params(request_args):
    """從 request 參數中提取篩選條件"""
    filters = {}
    
    # 日期範圍處理
    payment_daterange = request_args.get('payment_daterange')
    if payment_daterange:
        start_date, end_date = parse_date_range(payment_daterange)
        if start_date and end_date:
            filters['start_date'] = start_date
            filters['end_date'] = end_date
    
    voucher_daterange = request_args.get('voucher_daterange')
    if voucher_daterange:
        start_date, end_date = parse_date_range(voucher_daterange)
        if start_date and end_date:
            filters['real_start_date'] = start_date
            filters['real_end_date'] = end_date
    
    expected_daterange = request_args.get('expected_daterange')
    if expected_daterange:
        start_date, end_date = parse_date_range(expected_daterange)
        if start_date and end_date:
            filters['expected_start_date'] = start_date
            filters['expected_end_date'] = end_date
    
    # 其他參數
    simple_params = [
        'payment_identity', 'project', 'account', 'subject_code', 
        'keyword', 'target', 'note', 'voucher_type', 'attachment',
        'transaction_status', 'payment_identity_type', 'amount_min', 'amount_max'
    ]
    
    for param in simple_params:
        value = request_args.get(param)
        if value:
            filters[param] = value
    
    return filters

def log_query_info(query, filters):
    """記錄查詢資訊"""
    from utils.web.debug_helpers import debug_log
    
    total_count = query.count()
    current_app.logger.info(f"交易查詢 - 篩選條件: {len(filters)} 個，結果: {total_count} 筆")
    
    filter_summary = {k: v for k, v in filters.items() if v}
    debug_log("詳細篩選條件", filter_summary)
    
    return total_count

def get_order_clause(order_by):
    """獲取排序子句"""
    order_mapping = {
        'a_time_desc': desc(Transaction.transaction_date),
        'a_time_asc': asc(Transaction.transaction_date),
        'voucher_date_desc': desc(Transaction.invoice_date),
        'voucher_date_asc': asc(Transaction.invoice_date),
        'invoice_number_desc': desc(Transaction.invoice_number),
        'invoice_number_asc': asc(Transaction.invoice_number),
        'created_at_desc': desc(Transaction.created_at),
        'created_at_asc': asc(Transaction.created_at),
        'amount_desc': desc(Transaction.total_amount),
        'amount_asc': asc(Transaction.total_amount),
        'prepay_date_asc': asc(Transaction.should_paid_date),
        'prepay_date_desc': desc(Transaction.should_paid_date),
    }
    return order_mapping.get(order_by, desc(Transaction.transaction_date))