"""
審計功能輔助模組
提供審計欄位的統一處理功能
"""
from model import get_taiwan_time

class AuditHelper:
    """審計功能輔助類"""
    
    @staticmethod
    def set_create_audit(record, username):
        """設定建立時的審計欄位"""
        if hasattr(record, 'created_by'):
            record.created_by = username
        if hasattr(record, 'created_at') and record.created_at is None:
            record.created_at = get_taiwan_time()
        if hasattr(record, 'version'):
            record.version = 1
        if hasattr(record, 'is_deleted'):
            record.is_deleted = False
    
    @staticmethod
    def set_update_audit(record, username):
        """設定更新時的審計欄位"""
        if hasattr(record, 'updated_by'):
            record.updated_by = username
        if hasattr(record, 'updated_at'):
            record.updated_at = get_taiwan_time()
        if hasattr(record, 'version'):
            record.version = (record.version or 0) + 1
    
    @staticmethod
    def set_delete_audit(record, username):
        """設定軟刪除的審計欄位"""
        if hasattr(record, 'is_deleted'):
            record.is_deleted = True
        if hasattr(record, 'deleted_at'):
            record.deleted_at = get_taiwan_time()
        if hasattr(record, 'deleted_by'):
            record.deleted_by = username
    
    @staticmethod
    def get_audit_info(record):
        """取得記錄的審計資訊"""
        audit_info = {}
        
        if hasattr(record, 'created_by'):
            audit_info['created_by'] = record.created_by
        if hasattr(record, 'created_at'):
            audit_info['created_at'] = record.created_at
        if hasattr(record, 'updated_by'):
            audit_info['updated_by'] = record.updated_by
        if hasattr(record, 'updated_at'):
            audit_info['updated_at'] = record.updated_at
        if hasattr(record, 'version'):
            audit_info['version'] = record.version
        if hasattr(record, 'is_deleted'):
            audit_info['is_deleted'] = record.is_deleted
        if hasattr(record, 'deleted_by'):
            audit_info['deleted_by'] = record.deleted_by
        if hasattr(record, 'deleted_at'):
            audit_info['deleted_at'] = record.deleted_at
            
        return audit_info

def get_current_user():
    """取得當前用戶名稱 - 這裡需要根據你的認證系統調整"""
    # 暫時返回預設用戶，實際使用時需要整合你的用戶認證系統
    from flask import session, request
    
    # 嘗試從 session 取得用戶
    if 'username' in session:
        return session['username']
    
    # 嘗試從 request 取得用戶（如果有 API 認證）
    # 注意：Flask request 默認沒有 user 屬性，這裡是為了支持自定義認證系統
    try:
        if hasattr(request, 'user') and getattr(request, 'user', None):
            return getattr(request, 'user')
    except AttributeError:
        pass
    
    # 預設用戶
    return 'system'

def apply_audit_filter(query, model_class):
    """為查詢添加軟刪除過濾"""
    if hasattr(model_class, 'is_deleted'):
        return query.filter(not model_class.is_deleted)
    return query