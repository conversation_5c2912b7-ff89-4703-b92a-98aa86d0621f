"""
租戶隔離工具函數
提供租戶過濾、權限檢查等功能
整合安全機制以確保完整的租戶隔離
"""

from functools import wraps
from flask import session, request, abort, current_app, g
from flask_login import current_user
from sqlalchemy import and_
from typing import Optional, Type, Any, Callable
import logging

# 導入安全機制
try:
    from utils.security.tenant_security import (
        TenantAccessControl,
        TenantAuditLogger,
        TenantSessionManager,
        TenantRateLimiter,
        validate_tenant_input
    )
    SECURITY_ENABLED = True
except ImportError:
    SECURITY_ENABLED = False
    logging.warning("租戶安全機制模組未載入，使用基本功能")

logger = logging.getLogger(__name__)

def get_current_tenant_id() -> Optional[int]:
    """
    取得當前用戶的租戶ID
    
    Returns:
        int: 租戶ID，如果無法取得則返回None
    """
    try:
        # 優先從 current_user 取得
        if hasattr(current_user, 'tenant_id') and current_user.is_authenticated:
            return current_user.tenant_id
        
        # 從 session 取得備用
        return session.get('tenant_id')
        
    except Exception as e:
        logger.warning(f"無法取得租戶ID: {e}")
        return None

def require_tenant_access(f: Callable) -> Callable:
    """
    裝飾器：確保用戶有租戶存取權限
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        tenant_id = get_current_tenant_id()
        if not tenant_id:
            logger.warning(f"用戶 {current_user.get_id() if current_user.is_authenticated else 'unknown'} 嘗試無租戶存取")
            abort(403, "無租戶存取權限")
        
        # 將租戶ID添加到請求上下文
        request.tenant_id = tenant_id
        return f(*args, **kwargs)
    
    return decorated_function

def add_tenant_filter(query, model_class: Type, tenant_id: Optional[int] = None):
    """
    為查詢添加租戶過濾條件
    
    Args:
        query: SQLAlchemy查詢物件
        model_class: 模型類別
        tenant_id: 租戶ID，如果不提供則自動取得
    
    Returns:
        過濾後的查詢物件
    """
    if tenant_id is None:
        tenant_id = get_current_tenant_id()
    
    if not tenant_id:
        logger.warning(f"無法為 {model_class.__name__} 添加租戶過濾 - 無租戶ID")
        return query
    
    # 檢查模型是否有 tenant_id 欄位
    if hasattr(model_class, 'tenant_id'):
        return query.filter(model_class.tenant_id == tenant_id)
    else:
        logger.warning(f"模型 {model_class.__name__} 缺少 tenant_id 欄位")
        return query

def safe_get_by_id(model_class: Type, record_id: int, tenant_id: Optional[int] = None):
    """
    安全地根據ID取得記錄，自動添加租戶過濾
    
    Args:
        model_class: 模型類別
        record_id: 記錄ID
        tenant_id: 租戶ID，如果不提供則自動取得
    
    Returns:
        記錄物件或None
    """
    from database import get_db
    
    if tenant_id is None:
        tenant_id = get_current_tenant_id()
    
    if not tenant_id:
        logger.warning(f"無法安全取得 {model_class.__name__} ID:{record_id} - 無租戶ID")
        return None
    
    db = next(get_db())
    
    try:
        if hasattr(model_class, 'tenant_id'):
            return db.query(model_class).filter(
                and_(
                    model_class.id == record_id,
                    model_class.tenant_id == tenant_id
                )
            ).first()
        else:
            logger.warning(f"模型 {model_class.__name__} 缺少 tenant_id 欄位")
            return db.query(model_class).filter(model_class.id == record_id).first()
    
    except Exception as e:
        logger.error(f"安全取得記錄失敗 - {model_class.__name__} ID:{record_id}, 錯誤: {e}")
        return None

def check_record_tenant_access(model_class: Type, record_id: int, tenant_id: Optional[int] = None) -> bool:
    """
    檢查記錄是否屬於指定租戶
    
    Args:
        model_class: 模型類別
        record_id: 記錄ID
        tenant_id: 租戶ID，如果不提供則自動取得
    
    Returns:
        bool: 是否有存取權限
    """
    record = safe_get_by_id(model_class, record_id, tenant_id)
    return record is not None

class TenantQueryBuilder:
    """租戶查詢建構器 - 提供便利的租戶隔離查詢方法"""
    
    def __init__(self, db_session, tenant_id: Optional[int] = None):
        self.db = db_session
        self.tenant_id = tenant_id or get_current_tenant_id()
    
    def query(self, model_class: Type):
        """
        建立有租戶過濾的查詢
        
        Args:
            model_class: 模型類別
        
        Returns:
            過濾後的查詢物件
        """
        base_query = self.db.query(model_class)
        return add_tenant_filter(base_query, model_class, self.tenant_id)
    
    def get_by_id(self, model_class: Type, record_id: int):
        """安全地根據ID取得記錄"""
        return safe_get_by_id(model_class, record_id, self.tenant_id)
    
    def count(self, model_class: Type) -> int:
        """計算租戶記錄總數"""
        try:
            query = self.query(model_class)
            return query.count()
        except Exception as e:
            logger.error(f"計算 {model_class.__name__} 記錄數失敗: {e}")
            return 0
    
    def exists(self, model_class: Type, record_id: int) -> bool:
        """檢查記錄是否存在於租戶中"""
        return self.get_by_id(model_class, record_id) is not None

def log_tenant_access(action: str, resource: str, resource_id: Optional[int] = None):
    """
    記錄租戶存取日誌
    
    Args:
        action: 動作 (查詢、建立、更新、刪除)
        resource: 資源名稱
        resource_id: 資源ID（選用）
    """
    tenant_id = get_current_tenant_id()
    user_id = current_user.get_id() if current_user.is_authenticated else 'anonymous'
    
    log_message = f"租戶存取 - 用戶:{user_id}, 租戶:{tenant_id}, 動作:{action}, 資源:{resource}"
    if resource_id:
        log_message += f", 資源ID:{resource_id}"
    
    logger.info(log_message)

def validate_tenant_data(data: dict, model_class: Type, tenant_id: Optional[int] = None) -> dict:
    """
    驗證並確保資料包含正確的租戶ID
    
    Args:
        data: 要驗證的資料
        model_class: 模型類別
        tenant_id: 租戶ID，如果不提供則自動取得
    
    Returns:
        驗證後的資料
    """
    if tenant_id is None:
        tenant_id = get_current_tenant_id()
    
    if not tenant_id:
        raise ValueError("無法驗證租戶資料 - 無租戶ID")
    
    # 如果模型有 tenant_id 欄位，確保資料包含正確的租戶ID
    if hasattr(model_class, 'tenant_id'):
        data['tenant_id'] = tenant_id
    
    return data

# 常用的租戶過濾查詢方法
def get_tenant_money_records(db_session, limit: Optional[int] = None):
    """取得租戶的交易記錄"""
    from model import Money
    
    builder = TenantQueryBuilder(db_session)
    query = builder.query(Money).order_by(Money.created_at.desc())
    
    if limit:
        query = query.limit(limit)
    
    return query.all()

def get_tenant_payment_identities(db_session):
    """取得租戶的收支對象"""
    from model import PaymentIdentity
    
    builder = TenantQueryBuilder(db_session)
    return builder.query(PaymentIdentity).all()

def get_tenant_departments(db_session):
    """取得租戶的部門"""
    from model import Department
    
    builder = TenantQueryBuilder(db_session)
    return builder.query(Department).all()

def get_tenant_projects(db_session):
    """取得租戶的專案"""
    from model import Project
    
    builder = TenantQueryBuilder(db_session)
    return builder.query(Project).all()