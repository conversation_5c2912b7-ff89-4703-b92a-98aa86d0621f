"""
格式化工具函數
提供金額、日期等數據的格式化功能
"""

from decimal import Decimal
from typing import Optional, Union
from datetime import datetime, date

def format_money(amount: Union[int, float, Decimal, None], currency_symbol: str = "$", show_symbol: bool = True) -> str:
    """
    格式化金額顯示
    
    Args:
        amount: 金額數值
        currency_symbol: 貨幣符號，預設為$
        show_symbol: 是否顯示貨幣符號
        
    Returns:
        格式化後的金額字串
    """
    if amount is None:
        return "0"
    
    try:
        # 轉換為數值
        if isinstance(amount, str):
            amount = float(amount)
        
        # 格式化為千分位
        formatted_amount = f"{amount:,.0f}"
        
        if show_symbol:
            return f"{currency_symbol}{formatted_amount}"
        else:
            return formatted_amount
            
    except (ValueError, TypeError):
        return "0"

def format_percentage(value: Union[int, float, None], decimal_places: int = 2) -> str:
    """
    格式化百分比顯示
    
    Args:
        value: 數值
        decimal_places: 小數位數
        
    Returns:
        格式化後的百分比字串
    """
    if value is None:
        return "0%"
        
    try:
        return f"{value:.{decimal_places}f}%"
    except (ValueError, TypeError):
        return "0%"

def format_date(date_value: Union[datetime, date, None], format_string: str = "%Y-%m-%d") -> str:
    """
    格式化日期顯示
    
    Args:
        date_value: 日期值
        format_string: 格式化字串
        
    Returns:
        格式化後的日期字串
    """
    if date_value is None:
        return ""
        
    try:
        if isinstance(date_value, str):
            # 嘗試解析字串格式的日期
            date_value = datetime.strptime(date_value, "%Y-%m-%d").date()
        
        if isinstance(date_value, datetime):
            return date_value.strftime(format_string)
        elif isinstance(date_value, date):
            return date_value.strftime(format_string)
        else:
            return str(date_value)
            
    except (ValueError, AttributeError):
        return str(date_value) if date_value else ""

def format_taiwan_date(date_value: Union[datetime, date, None]) -> str:
    """
    格式化為台灣民國年份格式
    
    Args:
        date_value: 日期值
        
    Returns:
        民國年份格式的日期字串
    """
    if date_value is None:
        return ""
        
    try:
        if isinstance(date_value, str):
            date_value = datetime.strptime(date_value, "%Y-%m-%d").date()
        
        if isinstance(date_value, datetime):
            date_value = date_value.date()
            
        # 轉換為民國年
        taiwan_year = date_value.year - 1911
        return f"{taiwan_year:03d}/{date_value.month:02d}/{date_value.day:02d}"
        
    except (ValueError, AttributeError):
        return str(date_value) if date_value else ""

def format_number(value: Union[int, float, None], decimal_places: int = 0) -> str:
    """
    格式化數字顯示（千分位）
    
    Args:
        value: 數值
        decimal_places: 小數位數
        
    Returns:
        格式化後的數字字串
    """
    if value is None:
        return "0"
        
    try:
        if decimal_places > 0:
            return f"{value:,.{decimal_places}f}"
        else:
            return f"{value:,.0f}"
    except (ValueError, TypeError):
        return "0"

def format_file_size(bytes_size: Optional[int]) -> str:
    """
    格式化檔案大小顯示
    
    Args:
        bytes_size: 檔案大小（位元組）
        
    Returns:
        格式化後的檔案大小字串
    """
    if bytes_size is None:
        return "0 B"
        
    try:
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_size < 1024.0:
                return f"{bytes_size:.1f} {unit}"
            bytes_size /= 1024.0
        return f"{bytes_size:.1f} PB"
    except (ValueError, TypeError):
        return "0 B"

def truncate_text(text: Optional[str], max_length: int = 50, suffix: str = "...") -> str:
    """
    截斷文字並添加省略號
    
    Args:
        text: 原始文字
        max_length: 最大長度
        suffix: 省略符號
        
    Returns:
        截斷後的文字
    """
    if not text:
        return ""
        
    if len(text) <= max_length:
        return text
        
    return text[:max_length - len(suffix)] + suffix