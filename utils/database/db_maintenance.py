"""
資料庫維護和清理機制
定期執行資料清理、備份、歸檔等維護任務
"""

import logging
import time
import threading
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from database import engine, get_db
from sqlalchemy import text
from utils.database.db_analyzer import db_analyzer
from utils.database.db_optimizer import db_optimizer

logger = logging.getLogger(__name__)

class DatabaseMaintenanceManager:
    """資料庫維護管理器"""
    
    def __init__(self):
        self.running = False
        self.maintenance_thread = None
        self.tasks_history = []
        self.max_history = 100
        
    def start_maintenance_scheduler(self):
        """啟動維護排程器"""
        if self.running:
            return
            
        self.running = True
        
        # 設定排程任務
        self._setup_scheduled_tasks()
        
        # 啟動背景執行緒
        def run_scheduler():
            while self.running:
                try:
                    schedule.run_pending()
                    time.sleep(60)  # 每分鐘檢查一次
                except Exception as e:
                    logger.error(f"Maintenance scheduler error: {str(e)}")
                    time.sleep(60)
        
        self.maintenance_thread = threading.Thread(target=run_scheduler, daemon=True)
        self.maintenance_thread.start()
        
        logger.info("Database maintenance scheduler started")
    
    def stop_maintenance_scheduler(self):
        """停止維護排程器"""
        self.running = False
        if self.maintenance_thread:
            self.maintenance_thread.join(timeout=5)
        logger.info("Database maintenance scheduler stopped")
    
    def _setup_scheduled_tasks(self):
        """設置排程任務"""
        # 每日任務
        schedule.every().day.at("02:00").do(self._daily_maintenance)
        
        # 每週任務
        schedule.every().sunday.at("03:00").do(self._weekly_maintenance)
        
        # 每月任務
        schedule.every().month.do(self._monthly_maintenance)
        
        logger.info("Scheduled maintenance tasks configured")
    
    def _daily_maintenance(self):
        """每日維護任務"""
        logger.info("Starting daily maintenance...")
        
        tasks_completed = []
        tasks_failed = []
        
        try:
            # 1. 清理過期會話
            result = self._clean_expired_sessions()
            if result['success']:
                tasks_completed.append(f"清理過期會話: {result['cleaned_count']}個")
            else:
                tasks_failed.append(f"清理過期會話失敗: {result['error']}")
            
            # 2. 清理舊日誌
            result = self._clean_old_logs()
            if result['success']:
                tasks_completed.append(f"清理舊日誌: {result['cleaned_files']}個檔案")
            else:
                tasks_failed.append(f"清理舊日誌失敗: {result['error']}")
            
            # 3. 檢查資料完整性
            result = self._check_data_integrity()
            if result['success']:
                if result['issues_found'] == 0:
                    tasks_completed.append("資料完整性檢查: 正常")
                else:
                    tasks_failed.append(f"資料完整性檢查: 發現{result['issues_found']}個問題")
            else:
                tasks_failed.append(f"資料完整性檢查失敗: {result['error']}")
            
            # 記錄維護歷史
            self._record_maintenance_history('daily', tasks_completed, tasks_failed)
            
        except Exception as e:
            logger.error(f"Daily maintenance failed: {str(e)}")
            self._record_maintenance_history('daily', [], [f"維護失敗: {str(e)}"])
        
        logger.info("Daily maintenance completed")
    
    def _weekly_maintenance(self):
        """每週維護任務"""
        logger.info("Starting weekly maintenance...")
        
        tasks_completed = []
        tasks_failed = []
        
        try:
            # 1. 更新表統計信息
            result = db_optimizer.update_table_statistics(dry_run=False)
            if not result['errors']:
                tasks_completed.append(f"更新統計信息: {len(result['analyzed_tables'])}個表")
            else:
                tasks_failed.append(f"更新統計信息失敗: {len(result['errors'])}個錯誤")
            
            # 2. 重建索引
            result = self._rebuild_indexes()
            if result['success']:
                tasks_completed.append(f"重建索引: {result['rebuilt_count']}個")
            else:
                tasks_failed.append(f"重建索引失敗: {result['error']}")
            
            # 3. 生成健康報告
            result = self._generate_health_report()
            if result['success']:
                tasks_completed.append("生成健康報告: 完成")
            else:
                tasks_failed.append(f"生成健康報告失敗: {result['error']}")
            
            # 記錄維護歷史
            self._record_maintenance_history('weekly', tasks_completed, tasks_failed)
            
        except Exception as e:
            logger.error(f"Weekly maintenance failed: {str(e)}")
            self._record_maintenance_history('weekly', [], [f"維護失敗: {str(e)}"])
        
        logger.info("Weekly maintenance completed")
    
    def _monthly_maintenance(self):
        """每月維護任務"""
        logger.info("Starting monthly maintenance...")
        
        tasks_completed = []
        tasks_failed = []
        
        try:
            # 1. VACUUM 資料庫
            result = db_optimizer.vacuum_database(dry_run=False)
            if result['vacuum_executed']:
                tasks_completed.append(f"VACUUM 資料庫: 節省{result['space_saved_mb']}MB")
            else:
                tasks_failed.append("VACUUM 資料庫失敗")
            
            # 2. 歸檔舊資料
            result = self._archive_old_data()
            if result['success']:
                tasks_completed.append(f"歸檔舊資料: {result['archived_records']}筆")
            else:
                tasks_failed.append(f"歸檔舊資料失敗: {result['error']}")
            
            # 3. 完整性能分析
            result = self._comprehensive_performance_analysis()
            if result['success']:
                tasks_completed.append("完整性能分析: 完成")
            else:
                tasks_failed.append(f"完整性能分析失敗: {result['error']}")
            
            # 記錄維護歷史
            self._record_maintenance_history('monthly', tasks_completed, tasks_failed)
            
        except Exception as e:
            logger.error(f"Monthly maintenance failed: {str(e)}")
            self._record_maintenance_history('monthly', [], [f"維護失敗: {str(e)}"])
        
        logger.info("Monthly maintenance completed")
    
    def _clean_expired_sessions(self, days_old: int = 30) -> Dict[str, Any]:
        """清理過期會話"""
        try:
            cutoff_timestamp = time.time() - (days_old * 24 * 60 * 60)
            
            with engine.connect() as conn:
                # 計算要清理的會話數
                count_result = conn.execute(text("""
                    SELECT COUNT(*) FROM user_sessions 
                    WHERE created_at < :cutoff_timestamp
                """), {"cutoff_timestamp": cutoff_timestamp})
                
                count = count_result.scalar() or 0
                
                if count > 0:
                    # 執行刪除
                    conn.execute(text("""
                        DELETE FROM user_sessions 
                        WHERE created_at < :cutoff_timestamp
                    """), {"cutoff_timestamp": cutoff_timestamp})
                    conn.commit()
                
                return {
                    'success': True,
                    'cleaned_count': count
                }
                
        except Exception as e:
            logger.error(f"Failed to clean expired sessions: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _clean_old_logs(self, days_old: int = 30) -> Dict[str, Any]:
        """清理舊日誌檔案"""
        try:
            import os
            import glob
            
            logs_dir = 'logs'
            if not os.path.exists(logs_dir):
                return {'success': True, 'cleaned_files': 0}
            
            cutoff_time = time.time() - (days_old * 24 * 60 * 60)
            cleaned_count = 0
            
            # 尋找舊日誌檔案
            log_files = glob.glob(os.path.join(logs_dir, '*.log.*'))  # 輪轉的日誌檔案
            
            for log_file in log_files:
                try:
                    if os.path.getmtime(log_file) < cutoff_time:
                        os.remove(log_file)
                        cleaned_count += 1
                        logger.info(f"Removed old log file: {log_file}")
                except Exception as e:
                    logger.error(f"Failed to remove log file {log_file}: {str(e)}")
            
            return {
                'success': True,
                'cleaned_files': cleaned_count
            }
            
        except Exception as e:
            logger.error(f"Failed to clean old logs: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _check_data_integrity(self) -> Dict[str, Any]:
        """檢查資料完整性"""
        try:
            integrity_analysis = db_analyzer.check_data_integrity()
            
            return {
                'success': True,
                'issues_found': integrity_analysis.get('total_issues', 0),
                'issues': integrity_analysis.get('issues', [])
            }
            
        except Exception as e:
            logger.error(f"Failed to check data integrity: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _rebuild_indexes(self) -> Dict[str, Any]:
        """重建索引"""
        try:
            rebuilt_count = 0
            
            with engine.connect() as conn:
                # 獲取所有索引（SQLite）
                result = conn.execute(text("""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND name NOT LIKE 'sqlite_%'
                """))
                
                indexes = [row[0] for row in result.fetchall()]
                
                for index_name in indexes:
                    try:
                        conn.execute(text(f"REINDEX {index_name}"))
                        rebuilt_count += 1
                    except Exception as e:
                        logger.warning(f"Failed to rebuild index {index_name}: {str(e)}")
                
                conn.commit()
            
            return {
                'success': True,
                'rebuilt_count': rebuilt_count
            }
            
        except Exception as e:
            logger.error(f"Failed to rebuild indexes: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _generate_health_report(self) -> Dict[str, Any]:
        """生成健康報告"""
        try:
            from utils.database.db_analyzer import get_database_health_summary
            
            health_summary = get_database_health_summary()
            
            # 這裡可以將報告保存到檔案或發送郵件
            report_content = f"""
資料庫健康報告
生成時間: {datetime.now().isoformat()}

健康分數: {health_summary.get('health_score', 0)}
狀態: {health_summary.get('status', 'unknown')}
資料庫大小: {health_summary.get('database_size_mb', 0):.2f}MB
性能問題數: {health_summary.get('performance_issues', 0)}
"""
            
            # 保存報告到檔案
            import os
            reports_dir = 'reports'
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
            
            report_filename = f"db_health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            report_path = os.path.join(reports_dir, report_filename)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            logger.info(f"Health report generated: {report_path}")
            
            return {
                'success': True,
                'report_path': report_path
            }
            
        except Exception as e:
            logger.error(f"Failed to generate health report: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _archive_old_data(self, months_old: int = 12) -> Dict[str, Any]:
        """歸檔舊資料"""
        try:
            # 這裡可以實現將舊交易記錄移到歷史表
            # 目前僅作為示例，不實際執行
            
            cutoff_date = datetime.now() - timedelta(days=months_old * 30)
            archived_count = 0
            
            # 示例：歸檔舊的 money 記錄
            # 實際實現時需要考慮外鍵關係和資料完整性
            
            return {
                'success': True,
                'archived_records': archived_count
            }
            
        except Exception as e:
            logger.error(f"Failed to archive old data: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _comprehensive_performance_analysis(self) -> Dict[str, Any]:
        """完整性能分析"""
        try:
            analysis_result = db_analyzer.generate_optimization_plan()
            
            # 記錄分析結果
            logger.info(f"Comprehensive analysis completed: {analysis_result}")
            
            return {
                'success': True,
                'analysis_result': analysis_result
            }
            
        except Exception as e:
            logger.error(f"Failed to perform comprehensive analysis: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _record_maintenance_history(self, task_type: str, 
                                   completed_tasks: List[str], 
                                   failed_tasks: List[str]):
        """記錄維護歷史"""
        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'task_type': task_type,
            'completed_tasks': completed_tasks,
            'failed_tasks': failed_tasks,
            'success': len(failed_tasks) == 0
        }
        
        self.tasks_history.append(history_entry)
        
        # 保持歷史記錄在限制範圍內
        if len(self.tasks_history) > self.max_history:
            self.tasks_history = self.tasks_history[-self.max_history:]
        
        # 記錄到日誌
        if completed_tasks:
            logger.info(f"{task_type} maintenance completed tasks: {', '.join(completed_tasks)}")
        if failed_tasks:
            logger.error(f"{task_type} maintenance failed tasks: {', '.join(failed_tasks)}")
    
    def get_maintenance_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """獲取維護歷史"""
        return self.tasks_history[-limit:]
    
    def manual_maintenance(self, task_type: str) -> Dict[str, Any]:
        """手動執行維護任務"""
        try:
            if task_type == 'daily':
                self._daily_maintenance()
            elif task_type == 'weekly':
                self._weekly_maintenance()
            elif task_type == 'monthly':
                self._monthly_maintenance()
            else:
                return {
                    'success': False,
                    'error': f'Unknown task type: {task_type}'
                }
            
            return {
                'success': True,
                'message': f'{task_type} maintenance completed'
            }
            
        except Exception as e:
            logger.error(f"Manual {task_type} maintenance failed: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

# 全局維護管理器實例
maintenance_manager = DatabaseMaintenanceManager()

def init_database_maintenance():
    """初始化資料庫維護系統"""
    maintenance_manager.start_maintenance_scheduler()
    logger.info("Database maintenance system initialized")

def get_maintenance_status() -> Dict[str, Any]:
    """獲取維護系統狀態"""
    return {
        'running': maintenance_manager.running,
        'recent_history': maintenance_manager.get_maintenance_history(10)
    }