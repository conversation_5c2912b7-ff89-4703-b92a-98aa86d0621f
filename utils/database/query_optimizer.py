"""
智能查詢優化器
分析查詢性能，提供優化建議，自動調整查詢策略
"""

import time
import logging
import threading
from typing import Dict, List, Any, Optional, Callable, Tuple
from collections import defaultdict, deque
from functools import wraps
import re
from sqlalchemy import event, text
from sqlalchemy.engine import Engine
from database import engine, get_db

logger = logging.getLogger(__name__)

class QueryAnalyzer:
    """
    查詢分析器
    分析 SQL 查詢性能並提供優化建議
    """
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.query_history = deque(maxlen=max_history)
        self.slow_queries = deque(maxlen=100)
        self.query_patterns = defaultdict(list)
        self.optimization_suggestions = {}
        
        self._lock = threading.RLock()
        self.total_queries = 0
        self.total_time = 0
        
        # 查詢類型分類
        self.query_types = {
            'SELECT': [],
            'INSERT': [],
            'UPDATE': [],
            'DELETE': []
        }
    
    def analyze_query(self, query: str, execution_time: float, 
                     rows_affected: int = 0) -> Dict[str, Any]:
        """
        分析單個查詢
        
        Args:
            query: SQL 查詢字符串
            execution_time: 執行時間（秒）
            rows_affected: 影響的行數
            
        Returns:
            分析結果
        """
        with self._lock:
            # 基本統計
            self.total_queries += 1
            self.total_time += execution_time
            
            # 查詢分類
            query_type = self._classify_query(query)
            normalized_query = self._normalize_query(query)
            
            # 記錄查詢信息
            query_info = {
                'query': query,
                'normalized_query': normalized_query,
                'execution_time': execution_time,
                'rows_affected': rows_affected,
                'query_type': query_type,
                'timestamp': time.time(),
                'is_slow': execution_time > 1.0
            }
            
            self.query_history.append(query_info)
            self.query_types[query_type].append(execution_time)
            
            # 記錄慢查詢
            if execution_time > 1.0:
                self.slow_queries.append(query_info)
                logger.warning(f"Slow query detected: {execution_time:.3f}s - {query[:100]}...")
            
            # 模式分析
            self._analyze_query_pattern(normalized_query, execution_time)
            
            # 生成優化建議
            suggestions = self._generate_optimization_suggestions(query_info)
            
            return {
                'query_type': query_type,
                'execution_time': execution_time,
                'is_slow': query_info['is_slow'],
                'suggestions': suggestions,
                'pattern_id': self._get_pattern_id(normalized_query)
            }
    
    def _classify_query(self, query: str) -> str:
        """
        分類查詢類型
        
        Args:
            query: SQL 查詢
            
        Returns:
            查詢類型
        """
        query_upper = query.strip().upper()
        
        if query_upper.startswith('SELECT'):
            return 'SELECT'
        elif query_upper.startswith('INSERT'):
            return 'INSERT'
        elif query_upper.startswith('UPDATE'):
            return 'UPDATE'
        elif query_upper.startswith('DELETE'):
            return 'DELETE'
        else:
            return 'OTHER'
    
    def _normalize_query(self, query: str) -> str:
        """
        標準化查詢（移除參數值）
        
        Args:
            query: 原始查詢
            
        Returns:
            標準化查詢
        """
        # 替換數值參數
        normalized = re.sub(r'\b\d+\b', '?', query)
        # 替換字符串參數
        normalized = re.sub(r"'[^']*'", '?', normalized)
        # 替換日期參數
        normalized = re.sub(r'\b\d{4}-\d{2}-\d{2}\b', '?', normalized)
        
        return normalized.strip()
    
    def _analyze_query_pattern(self, normalized_query: str, execution_time: float):
        """
        分析查詢模式
        
        Args:
            normalized_query: 標準化查詢
            execution_time: 執行時間
        """
        pattern_id = self._get_pattern_id(normalized_query)
        self.query_patterns[pattern_id].append(execution_time)
    
    def _get_pattern_id(self, normalized_query: str) -> str:
        """
        獲取查詢模式 ID
        
        Args:
            normalized_query: 標準化查詢
            
        Returns:
            模式 ID
        """
        import hashlib
        return hashlib.md5(normalized_query.encode()).hexdigest()[:8]
    
    def _generate_optimization_suggestions(self, query_info: Dict[str, Any]) -> List[str]:
        """
        生成優化建議
        
        Args:
            query_info: 查詢信息
            
        Returns:
            優化建議列表
        """
        suggestions = []
        query = query_info['query'].upper()
        execution_time = query_info['execution_time']
        
        # 慢查詢建議
        if execution_time > 2.0:
            suggestions.append("考慮添加適當的索引來提升查詢性能")
            
        if execution_time > 5.0:
            suggestions.append("這是一個極慢查詢，需要立即優化")
        
        # SELECT 查詢建議
        if query_info['query_type'] == 'SELECT':
            if 'SELECT *' in query:
                suggestions.append("避免使用 SELECT *，明確指定需要的欄位")
            
            if 'ORDER BY' in query and 'LIMIT' not in query:
                suggestions.append("ORDER BY 查詢建議添加 LIMIT 以提升性能")
            
            if 'JOIN' in query and execution_time > 1.0:
                suggestions.append("複雜 JOIN 查詢可考慮使用索引優化")
        
        # INSERT 查詢建議
        elif query_info['query_type'] == 'INSERT':
            if query_info['rows_affected'] == 1 and execution_time > 0.5:
                suggestions.append("單條 INSERT 耗時過長，檢查表結構或索引")
        
        # UPDATE/DELETE 建議
        elif query_info['query_type'] in ['UPDATE', 'DELETE']:
            if 'WHERE' not in query:
                suggestions.append("UPDATE/DELETE 查詢應該包含 WHERE 條件")
            
            if query_info['rows_affected'] > 1000:
                suggestions.append("大量數據更新建議分批進行")
        
        return suggestions
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        獲取性能摘要
        
        Args:
            hours: 統計時間範圍（小時）
            
        Returns:
            性能摘要
        """
        with self._lock:
            if not self.query_history:
                return {'message': 'No query history available'}
            
            cutoff_time = time.time() - (hours * 3600)
            recent_queries = [
                q for q in self.query_history 
                if q['timestamp'] > cutoff_time
            ]
            
            if not recent_queries:
                return {'message': f'No queries in the last {hours} hours'}
            
            # 計算統計指標
            execution_times = [q['execution_time'] for q in recent_queries]
            slow_queries = [q for q in recent_queries if q['is_slow']]
            
            # 按查詢類型統計
            type_stats = defaultdict(list)
            for q in recent_queries:
                type_stats[q['query_type']].append(q['execution_time'])
            
            type_summary = {}
            for query_type, times in type_stats.items():
                if times:
                    type_summary[query_type] = {
                        'count': len(times),
                        'avg_time': sum(times) / len(times),
                        'max_time': max(times),
                        'slow_count': len([t for t in times if t > 1.0])
                    }
            
            return {
                'period_hours': hours,
                'total_queries': len(recent_queries),
                'avg_execution_time': sum(execution_times) / len(execution_times),
                'max_execution_time': max(execution_times),
                'slow_queries_count': len(slow_queries),
                'slow_queries_rate': len(slow_queries) / len(recent_queries) * 100,
                'by_query_type': type_summary,
                'top_slow_patterns': self._get_top_slow_patterns(5)
            }
    
    def _get_top_slow_patterns(self, limit: int) -> List[Dict[str, Any]]:
        """
        獲取最慢的查詢模式
        
        Args:
            limit: 返回數量限制
            
        Returns:
            最慢模式列表
        """
        pattern_stats = {}
        
        for pattern_id, times in self.query_patterns.items():
            if times:
                pattern_stats[pattern_id] = {
                    'pattern_id': pattern_id,
                    'count': len(times),
                    'avg_time': sum(times) / len(times),
                    'max_time': max(times),
                    'total_time': sum(times)
                }
        
        # 按平均執行時間排序
        sorted_patterns = sorted(
            pattern_stats.values(),
            key=lambda x: x['avg_time'],
            reverse=True
        )
        
        return sorted_patterns[:limit]
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """
        獲取優化報告
        
        Returns:
            優化報告
        """
        with self._lock:
            report = {
                'total_queries_analyzed': self.total_queries,
                'total_execution_time': self.total_time,
                'avg_query_time': self.total_time / self.total_queries if self.total_queries > 0 else 0,
                'slow_queries_count': len(self.slow_queries),
                'patterns_identified': len(self.query_patterns),
                'recommendations': []
            }
            
            # 生成總體建議
            if len(self.slow_queries) / self.total_queries > 0.1:  # 超過10%慢查詢
                report['recommendations'].append({
                    'priority': 'high',
                    'category': 'performance',
                    'description': '慢查詢比例過高，建議進行索引優化'
                })
            
            # 查詢類型建議
            for query_type, times in self.query_types.items():
                if times and len(times) > 10:
                    avg_time = sum(times) / len(times)
                    if avg_time > 0.5:
                        report['recommendations'].append({
                            'priority': 'medium',
                            'category': query_type.lower(),
                            'description': f'{query_type} 查詢平均耗時 {avg_time:.3f}s，建議優化'
                        })
            
            return report

class SmartQueryOptimizer:
    """
    智能查詢優化器
    基於查詢分析結果自動應用優化策略
    """
    
    def __init__(self, analyzer: QueryAnalyzer):
        self.analyzer = analyzer
        self.optimization_rules = {}
        self.applied_optimizations = defaultdict(int)
        self._lock = threading.RLock()
        
        # 初始化優化規則
        self._init_optimization_rules()
    
    def _init_optimization_rules(self):
        """初始化優化規則"""
        self.optimization_rules = {
            'add_limit_to_order_by': {
                'pattern': r'ORDER BY .+ (?!LIMIT)',
                'suggestion': 'ADD LIMIT',
                'priority': 'medium'
            },
            'avoid_select_star': {
                'pattern': r'SELECT \*',
                'suggestion': 'SPECIFY COLUMNS',
                'priority': 'low'
            },
            'add_where_clause': {
                'pattern': r'(UPDATE|DELETE) .+ (?!WHERE)',
                'suggestion': 'ADD WHERE CLAUSE',
                'priority': 'high'
            }
        }
    
    def suggest_query_optimization(self, query: str, 
                                 execution_time: float) -> Dict[str, Any]:
        """
        建議查詢優化
        
        Args:
            query: SQL 查詢
            execution_time: 執行時間
            
        Returns:
            優化建議
        """
        suggestions = []
        
        # 應用優化規則
        for rule_name, rule in self.optimization_rules.items():
            if re.search(rule['pattern'], query, re.IGNORECASE):
                suggestions.append({
                    'rule': rule_name,
                    'suggestion': rule['suggestion'],
                    'priority': rule['priority']
                })
        
        # 基於執行時間的建議
        if execution_time > 2.0:
            suggestions.append({
                'rule': 'slow_query',
                'suggestion': 'CONSIDER INDEXING',
                'priority': 'high'
            })
        
        return {
            'query': query,
            'execution_time': execution_time,
            'suggestions': suggestions,
            'optimization_score': self._calculate_optimization_score(suggestions)
        }
    
    def _calculate_optimization_score(self, suggestions: List[Dict[str, Any]]) -> int:
        """
        計算優化分數
        
        Args:
            suggestions: 建議列表
            
        Returns:
            優化分數 (0-100)
        """
        if not suggestions:
            return 100  # 沒有建議表示已經很好
        
        priority_weights = {'high': 30, 'medium': 20, 'low': 10}
        total_penalty = sum(priority_weights.get(s['priority'], 10) for s in suggestions)
        
        return max(0, 100 - total_penalty)
    
    def get_optimization_history(self) -> Dict[str, Any]:
        """
        獲取優化歷史
        
        Returns:
            優化歷史報告
        """
        with self._lock:
            return {
                'applied_optimizations': dict(self.applied_optimizations),
                'total_suggestions': sum(self.applied_optimizations.values()),
                'rules_count': len(self.optimization_rules)
            }

def setup_query_monitoring():
    """
    設置查詢監控
    使用 SQLAlchemy 事件系統監控所有查詢
    """
    query_analyzer = QueryAnalyzer()
    query_optimizer = SmartQueryOptimizer(query_analyzer)
    
    @event.listens_for(Engine, "before_cursor_execute")
    def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        context._query_start_time = time.time()
    
    @event.listens_for(Engine, "after_cursor_execute")  
    def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
        if hasattr(context, '_query_start_time'):
            execution_time = time.time() - context._query_start_time
            rows_affected = cursor.rowcount if hasattr(cursor, 'rowcount') else 0
            
            # 分析查詢
            analysis_result = query_analyzer.analyze_query(
                statement, execution_time, rows_affected
            )
            
            # 記錄到性能基準測試
            try:
                from utils.performance.performance_benchmarking import benchmark_manager
                benchmark_manager.record_database_query(
                    analysis_result['query_type'], 
                    execution_time, 
                    rows_affected
                )
            except ImportError:
                pass  # 如果沒有基準測試模組，忽略
    
    logger.info("Query monitoring setup completed")
    
    return query_analyzer, query_optimizer

def optimize_query(func: Callable) -> Callable:
    """
    查詢優化裝飾器
    
    Args:
        func: 要優化的函數
        
    Returns:
        優化後的函數
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 如果執行時間較長，記錄優化建議
            if execution_time > 1.0:
                logger.warning(f"Function {func.__name__} took {execution_time:.3f}s to execute")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Function {func.__name__} failed after {execution_time:.3f}s: {str(e)}")
            raise
    
    return wrapper

# 全局查詢分析器和優化器
query_analyzer = None
query_optimizer = None

def init_query_optimization():
    """初始化查詢優化系統"""
    global query_analyzer, query_optimizer
    
    query_analyzer, query_optimizer = setup_query_monitoring()
    
    logger.info("Query optimization system initialized")

def get_query_performance_report() -> Dict[str, Any]:
    """
    獲取查詢性能報告
    
    Returns:
        性能報告
    """
    if query_analyzer is None:
        return {'error': 'Query analyzer not initialized'}
    
    return {
        'performance_summary': query_analyzer.get_performance_summary(),
        'optimization_report': query_analyzer.get_optimization_report(),
        'optimization_history': query_optimizer.get_optimization_history() if query_optimizer else {}
    }