"""
資料庫連接池監控
監控連接池使用情況，提供效能優化建議
"""

import logging
import time
from typing import Dict, Any, Optional
from datetime import datetime
import threading

logger = logging.getLogger(__name__)

class ConnectionPoolMonitor:
    """
    連接池監控器
    監控資料庫連接池的使用情況並提供告警
    """
    
    def __init__(self, engine, alert_threshold: float = 0.8):
        """
        初始化監控器
        
        Args:
            engine: SQLAlchemy 引擎
            alert_threshold: 告警閾值（使用率超過此值時告警）
        """
        self.engine = engine
        self.alert_threshold = alert_threshold
        self.metrics_history = []
        self.max_history_size = 1000
        self._monitoring = False
        self._monitor_thread = None
    
    def get_pool_metrics(self) -> Dict[str, Any]:
        """
        獲取連接池當前指標
        
        Returns:
            包含連接池狀態的字典
        """
        pool = self.engine.pool
        
        # 獲取基本指標
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'size': pool.size(),              # 連接池大小
            'checked_out': pool.checkedout(), # 已檢出的連接數
            'overflow': pool.overflow(),      # 溢出連接數
            'total': pool.size() + pool.overflow(),  # 總連接數
        }
        
        # 計算使用率
        if metrics['size'] > 0:
            metrics['utilization'] = metrics['checked_out'] / metrics['size']
        else:
            metrics['utilization'] = 0
        
        # 計算總使用率（包含溢出）
        if metrics['total'] > 0:
            metrics['total_utilization'] = metrics['checked_out'] / metrics['total']
        else:
            metrics['total_utilization'] = 0
        
        # 添加狀態標記
        metrics['status'] = self._get_status(metrics['utilization'])
        
        return metrics
    
    def _get_status(self, utilization: float) -> str:
        """
        根據使用率獲取狀態
        
        Args:
            utilization: 連接池使用率
        
        Returns:
            狀態字符串
        """
        if utilization < 0.5:
            return 'healthy'
        elif utilization < self.alert_threshold:
            return 'warning'
        else:
            return 'critical'
    
    def check_pool_health(self) -> Dict[str, Any]:
        """
        檢查連接池健康狀況
        
        Returns:
            健康檢查結果
        """
        metrics = self.get_pool_metrics()
        
        health_report = {
            'healthy': metrics['status'] == 'healthy',
            'metrics': metrics,
            'recommendations': []
        }
        
        # 根據指標提供建議
        if metrics['utilization'] > self.alert_threshold:
            health_report['recommendations'].append(
                f"連接池使用率過高 ({metrics['utilization']:.1%})，建議增加 pool_size"
            )
            logger.warning(
                f"Connection pool utilization high: {metrics['utilization']:.1%}"
            )
        
        if metrics['overflow'] > 0:
            health_report['recommendations'].append(
                f"正在使用溢出連接 ({metrics['overflow']})，可能需要調整 pool_size"
            )
        
        if metrics['total_utilization'] > 0.9:
            health_report['recommendations'].append(
                "總連接數接近上限，建議增加 max_overflow 或優化查詢"
            )
            logger.error(
                f"Connection pool near capacity: {metrics['total_utilization']:.1%}"
            )
        
        # 記錄歷史
        self._record_metrics(metrics)
        
        return health_report
    
    def _record_metrics(self, metrics: Dict[str, Any]):
        """記錄指標到歷史"""
        self.metrics_history.append(metrics)
        
        # 限制歷史記錄大小
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size:]
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        獲取統計信息
        
        Returns:
            連接池使用統計
        """
        if not self.metrics_history:
            return {'error': 'No metrics available'}
        
        utilizations = [m['utilization'] for m in self.metrics_history]
        checked_outs = [m['checked_out'] for m in self.metrics_history]
        
        return {
            'current': self.get_pool_metrics(),
            'statistics': {
                'avg_utilization': sum(utilizations) / len(utilizations),
                'max_utilization': max(utilizations),
                'min_utilization': min(utilizations),
                'avg_connections': sum(checked_outs) / len(checked_outs),
                'max_connections': max(checked_outs),
                'samples': len(self.metrics_history)
            }
        }
    
    def start_monitoring(self, interval: int = 60):
        """
        開始定期監控
        
        Args:
            interval: 監控間隔（秒）
        """
        if self._monitoring:
            logger.warning("Monitoring already started")
            return
        
        self._monitoring = True
        
        def monitor_loop():
            while self._monitoring:
                try:
                    health = self.check_pool_health()
                    
                    # 只在有問題時記錄
                    if not health['healthy']:
                        logger.warning(
                            f"Pool health check failed: {health['recommendations']}"
                        )
                    
                    time.sleep(interval)
                except Exception as e:
                    logger.error(f"Error in pool monitoring: {str(e)}")
                    time.sleep(interval)
        
        self._monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info(f"Connection pool monitoring started (interval: {interval}s)")
    
    def stop_monitoring(self):
        """停止監控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Connection pool monitoring stopped")
    
    def get_optimization_suggestions(self) -> list:
        """
        獲取優化建議
        
        Returns:
            優化建議列表
        """
        stats = self.get_statistics()
        suggestions = []
        
        if 'statistics' in stats:
            stat = stats['statistics']
            
            # 基於平均使用率的建議
            if stat['avg_utilization'] > 0.7:
                suggestions.append({
                    'type': 'pool_size',
                    'severity': 'high',
                    'message': '平均連接池使用率過高，建議增加 pool_size',
                    'current_value': self.engine.pool.size(),
                    'suggested_value': int(self.engine.pool.size() * 1.5)
                })
            
            # 基於最大連接數的建議
            if stat['max_connections'] >= self.engine.pool.size():
                suggestions.append({
                    'type': 'max_overflow',
                    'severity': 'medium',
                    'message': '曾達到連接池上限，建議增加 max_overflow',
                    'current_value': self.engine.pool._max_overflow,
                    'suggested_value': int(self.engine.pool._max_overflow * 1.5)
                })
            
            # 基於使用率波動的建議
            if stat['max_utilization'] - stat['min_utilization'] > 0.5:
                suggestions.append({
                    'type': 'optimization',
                    'severity': 'low',
                    'message': '連接池使用率波動較大，建議優化查詢或實施連接池預熱'
                })
        
        return suggestions

# 全局監控實例（需要在應用啟動時初始化）
pool_monitor: Optional[ConnectionPoolMonitor] = None

def init_pool_monitor(engine, start_monitoring: bool = True):
    """
    初始化連接池監控
    
    Args:
        engine: SQLAlchemy 引擎
        start_monitoring: 是否立即開始監控
    
    Returns:
        ConnectionPoolMonitor 實例
    """
    global pool_monitor
    
    pool_monitor = ConnectionPoolMonitor(engine)
    
    if start_monitoring:
        pool_monitor.start_monitoring()
    
    logger.info("Connection pool monitor initialized")
    return pool_monitor

def get_pool_status() -> Dict[str, Any]:
    """
    獲取連接池狀態（便捷函數）
    
    Returns:
        連接池狀態信息
    """
    if pool_monitor:
        return pool_monitor.check_pool_health()
    else:
        return {'error': 'Pool monitor not initialized'}

def get_pool_suggestions() -> list:
    """
    獲取優化建議（便捷函數）
    
    Returns:
        優化建議列表
    """
    if pool_monitor:
        return pool_monitor.get_optimization_suggestions()
    else:
        return []