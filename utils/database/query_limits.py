"""
查詢限制工具
提供智能的查詢結果限制和分頁功能
"""
import time
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from sqlalchemy.orm import Query
from sqlalchemy.sql import func
from flask import request
try:
    from flask import flash
except ImportError:
    # 如果無法導入 flash（比如在測試環境中），提供一個空實現
    def flash(message, category=None):
        pass

logger = logging.getLogger(__name__)

class QueryLimits:
    """查詢限制配置"""
    
    # 預設限制
    DEFAULT_LIMIT = 1000
    MAX_LIMIT = 5000
    
    # 不同場景的限制
    DASHBOARD_LIMIT = 50         # 儀表板顯示
    SEARCH_LIMIT = 200          # 搜索結果
    REPORT_LIMIT = 10000        # 報表查詢
    API_LIMIT = 1000            # API響應
    
    # 分頁設定
    DEFAULT_PAGE_SIZE = 50
    MAX_PAGE_SIZE = 200
    
    # 性能閾值
    MAX_QUERY_TIME = 2.0        # 最大查詢時間（秒）
    WARNING_THRESHOLD = 1.0     # 警告閾值（秒）

class SafeQueryExecutor:
    """安全查詢執行器"""
    
    def __init__(self, query_limits: QueryLimits = None):
        self.limits = query_limits or QueryLimits()
        self.stats = {'queries': 0, 'limited': 0, 'warnings': 0}
    
    def safe_all(self, query: Query, limit: int = None, context: str = "general") -> List[Any]:
        """
        安全執行 .all() 查詢，自動應用限制
        
        Args:
            query: SQLAlchemy 查詢對象
            limit: 自定義限制，若為 None 則使用預設
            context: 查詢上下文 (dashboard, search, report, api)
        
        Returns:
            查詢結果列表
        """
        # 確定限制數量
        if limit is None:
            limit = self._get_context_limit(context)
        
        # 應用安全限制
        limited_query = self._apply_safe_limit(query, limit)
        
        # 執行查詢並監控性能
        return self._execute_with_monitoring(limited_query, context)
    
    def safe_paginate(self, query: Query, page: int = 1, per_page: int = None, 
                     count_query: bool = True) -> Dict[str, Any]:
        """
        安全分頁查詢
        
        Args:
            query: SQLAlchemy 查詢對象
            page: 頁碼（從1開始）
            per_page: 每頁記錄數
            count_query: 是否執行計數查詢
        
        Returns:
            分頁結果字典
        """
        if per_page is None:
            per_page = self.limits.DEFAULT_PAGE_SIZE
        elif per_page > self.limits.MAX_PAGE_SIZE:
            per_page = self.limits.MAX_PAGE_SIZE
            try:
                flash(f'每頁顯示數量已限制為 {per_page} 筆', 'warning')
            except RuntimeError:
                # 在沒有 Flask 請求上下文時忽略 flash
                logger.warning(f'每頁顯示數量已限制為 {per_page} 筆')
        
        # 計算偏移量
        offset = (page - 1) * per_page
        
        # 獲取總記錄數（可選）
        total = None
        if count_query:
            start_time = time.time()
            total = query.count()
            count_time = time.time() - start_time
            
            # 如果計數查詢太慢，記錄警告
            if count_time > self.limits.WARNING_THRESHOLD:
                logger.warning(f"慢計數查詢: {count_time:.3f}s")
                self.stats['warnings'] += 1
        
        # 獲取當前頁數據
        paginated_query = query.offset(offset).limit(per_page)
        items = self._execute_with_monitoring(paginated_query, "pagination")
        
        # 計算分頁信息
        has_prev = page > 1
        has_next = len(items) == per_page
        
        return {
            'items': items,
            'total': total,
            'page': page,
            'per_page': per_page,
            'has_prev': has_prev,
            'has_next': has_next,
            'prev_num': page - 1 if has_prev else None,
            'next_num': page + 1 if has_next else None,
            'pages': (total + per_page - 1) // per_page if total else None
        }
    
    def adaptive_limit(self, query: Query, max_time: float = None, 
                      initial_sample: int = 100) -> List[Any]:
        """
        自適應限制查詢
        根據查詢性能動態調整結果數量
        
        Args:
            query: SQLAlchemy 查詢對象
            max_time: 最大允許執行時間
            initial_sample: 初始採樣數量
        
        Returns:
            查詢結果列表
        """
        if max_time is None:
            max_time = self.limits.MAX_QUERY_TIME
        
        # 執行初始採樣
        start_time = time.time()
        sample_query = query.limit(initial_sample)
        sample_results = sample_query.all()
        sample_time = time.time() - start_time
        
        # 如果採樣就很慢，直接返回採樣結果的一部分
        if sample_time > max_time:
            reduced_size = max(10, int(initial_sample * max_time / sample_time))
            logger.warning(f"查詢過慢，縮減結果至 {reduced_size} 筆")
            self.stats['limited'] += 1
            return sample_results[:reduced_size]
        
        # 如果採樣很快，估算安全的限制數量
        if sample_time > 0:
            estimated_safe_limit = int(initial_sample * max_time / sample_time)
            safe_limit = min(estimated_safe_limit, self.limits.MAX_LIMIT)
        else:
            safe_limit = self.limits.MAX_LIMIT
        
        # 如果採樣結果已經足夠，直接返回
        if len(sample_results) < initial_sample:
            return sample_results
        
        # 否則執行完整查詢
        return self.safe_all(query, limit=safe_limit, context="adaptive")
    
    def get_stats(self) -> Dict[str, int]:
        """獲取查詢統計信息"""
        return self.stats.copy()
    
    def reset_stats(self):
        """重置統計信息"""
        self.stats = {'queries': 0, 'limited': 0, 'warnings': 0}
    
    def _get_context_limit(self, context: str) -> int:
        """根據上下文獲取適當的限制"""
        context_limits = {
            'dashboard': self.limits.DASHBOARD_LIMIT,
            'search': self.limits.SEARCH_LIMIT,
            'report': self.limits.REPORT_LIMIT,
            'api': self.limits.API_LIMIT,
        }
        return context_limits.get(context, self.limits.DEFAULT_LIMIT)
    
    def _apply_safe_limit(self, query: Query, limit: int) -> Query:
        """應用安全限制到查詢"""
        # 檢查是否已有限制
        try:
            if hasattr(query.statement, 'limit') and query.statement.limit is not None:
                existing_limit = query.statement.limit
                if callable(existing_limit):
                    # 如果是方法，嘗試獲取實際值
                    try:
                        existing_limit = existing_limit()
                    except:
                        existing_limit = None
                
                if existing_limit is not None and existing_limit <= limit:
                    return query  # 現有限制更嚴格，保持不變
        except Exception:
            # 如果檢查限制失敗，繼續應用新限制
            pass
        
        # 應用限制
        if limit > self.limits.MAX_LIMIT:
            limit = self.limits.MAX_LIMIT
            self.stats['limited'] += 1
            try:
                flash(f'查詢結果已限制為 {limit} 筆，如需更多請使用分頁', 'info')
            except RuntimeError:
                # 在沒有 Flask 請求上下文時忽略 flash
                logger.info(f'查詢結果已限制為 {limit} 筆，如需更多請使用分頁')
        
        return query.limit(limit)
    
    def _execute_with_monitoring(self, query: Query, context: str) -> List[Any]:
        """執行查詢並監控性能"""
        start_time = time.time()
        
        try:
            results = query.all()
            execution_time = time.time() - start_time
            
            # 記錄統計
            self.stats['queries'] += 1
            
            # 性能警告
            if execution_time > self.limits.WARNING_THRESHOLD:
                logger.warning(
                    f"慢查詢 ({context}): {execution_time:.3f}s, "
                    f"返回 {len(results)} 筆記錄"
                )
                self.stats['warnings'] += 1
            
            # 記錄調試信息
            logger.debug(
                f"查詢執行 ({context}): {execution_time:.3f}s, "
                f"{len(results)} 筆記錄"
            )
            
            return results
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                f"查詢失敗 ({context}): {execution_time:.3f}s, "
                f"錯誤: {str(e)}"
            )
            raise

# 全域安全查詢執行器實例
safe_query = SafeQueryExecutor()

# 便利函數
def safe_all(query: Query, limit: int = None, context: str = "general") -> List[Any]:
    """便利函數：安全執行查詢"""
    return safe_query.safe_all(query, limit, context)

def safe_paginate(query: Query, page: int = 1, per_page: int = None) -> Dict[str, Any]:
    """便利函數：安全分頁查詢"""
    return safe_query.safe_paginate(query, page, per_page)

def adaptive_query(query: Query, max_time: float = 2.0) -> List[Any]:
    """便利函數：自適應查詢"""
    return safe_query.adaptive_limit(query, max_time)

# Flask 請求參數解析
def get_pagination_params(default_per_page: int = 50) -> Tuple[int, int]:
    """從請求參數獲取分頁參數"""
    try:
        # 檢查是否在 Flask 請求上下文中
        if request:
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', default_per_page))
        else:
            page = 1
            per_page = default_per_page
    except RuntimeError:
        # 沒有請求上下文時使用預設值
        page = 1
        per_page = default_per_page
    except (ValueError, TypeError):
        page = 1
        per_page = default_per_page
    
    # 驗證參數
    if page < 1:
        page = 1
    if per_page < 1:
        per_page = default_per_page
    elif per_page > QueryLimits.MAX_PAGE_SIZE:
        per_page = QueryLimits.MAX_PAGE_SIZE
        
    return page, per_page