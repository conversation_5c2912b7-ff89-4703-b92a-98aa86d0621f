"""
查詢輔助類
提供常用的資料庫查詢方法，不修改原有模型結構
"""
from sqlalchemy import func, extract
from model import Money, Account, AccountSubject, PaymentIdentity
from database import get_db
from datetime import datetime, timedelta

class MoneyQueryHelper:
    """收支記錄查詢輔助類"""
    
    @staticmethod
    def get_monthly_summary(year, month):
        """
        月度收支匯總
        
        Args:
            year (int): 年份
            month (int): 月份
            
        Returns:
            list: [{'money_type': '收入', 'total_amount': 10000, 'count': 5}, ...]
        """
        with get_db() as db:
            results = db.query(
                Money.money_type,
                func.sum(Money.total).label('total_amount'),
                func.count(Money.id).label('count')
            ).filter(
                extract('year', Money.a_time) == year,
                extract('month', Money.a_time) == month,
                Money.is_deleted == False
            ).group_by(Money.money_type).all()
            
            return [
                {
                    'money_type': result.money_type,
                    'total_amount': result.total_amount or 0,
                    'count': result.count
                }
                for result in results
            ]
    
    @staticmethod
    def get_account_balance(account_id):
        """
        計算帳戶餘額
        
        Args:
            account_id (int): 帳戶ID
            
        Returns:
            int: 帳戶餘額（收入-支出）
        """
        with get_db() as db:
            # 計算收入總額
            income = db.query(func.sum(Money.total)).filter(
                Money.account_id == account_id,
                Money.money_type == '收入',
                Money.is_deleted == False
            ).scalar() or 0
            
            # 計算支出總額
            expense = db.query(func.sum(Money.total)).filter(
                Money.account_id == account_id,
                Money.money_type == '支出',
                Money.is_deleted == False
            ).scalar() or 0
            
            return income - expense
    
    @staticmethod
    def get_yearly_summary(year):
        """
        年度收支匯總
        
        Args:
            year (int): 年份
            
        Returns:
            dict: {'monthly_data': [...], 'yearly_total': {...}}
        """
        with get_db() as db:
            # 按月份統計
            monthly_results = db.query(
                extract('month', Money.a_time).label('month'),
                Money.money_type,
                func.sum(Money.total).label('total_amount')
            ).filter(
                extract('year', Money.a_time) == year,
                Money.is_deleted == False
            ).group_by(
                extract('month', Money.a_time),
                Money.money_type
            ).all()
            
            # 年度總計
            yearly_results = db.query(
                Money.money_type,
                func.sum(Money.total).label('total_amount')
            ).filter(
                extract('year', Money.a_time) == year,
                Money.is_deleted == False
            ).group_by(Money.money_type).all()
            
            # 整理月度資料
            monthly_data = {}
            for result in monthly_results:
                month = int(result.month)
                if month not in monthly_data:
                    monthly_data[month] = {}
                monthly_data[month][result.money_type] = result.total_amount or 0
            
            # 整理年度資料
            yearly_total = {}
            for result in yearly_results:
                yearly_total[result.money_type] = result.total_amount or 0
            
            return {
                'monthly_data': monthly_data,
                'yearly_total': yearly_total
            }
    
    @staticmethod
    def get_top_expenses(limit=10, year=None, month=None):
        """
        取得最大支出記錄
        
        Args:
            limit (int): 限制筆數
            year (int, optional): 年份篩選
            month (int, optional): 月份篩選
            
        Returns:
            list: 支出記錄列表
        """
        with get_db() as db:
            query = db.query(Money).filter(
                Money.money_type == '支出',
                Money.is_deleted == False
            )
            
            if year:
                query = query.filter(extract('year', Money.a_time) == year)
            if month:
                query = query.filter(extract('month', Money.a_time) == month)
            
            return query.order_by(Money.total.desc()).limit(limit).all()

class AccountQueryHelper:
    """帳戶查詢輔助類"""
    
    @staticmethod
    def get_active_accounts():
        """取得所有啟用的帳戶"""
        with get_db() as db:
            return db.query(Account).filter(
                Account.is_deleted == False
            ).order_by(Account.name).all()
    
    @staticmethod
    def get_default_account():
        """取得預設帳戶"""
        with get_db() as db:
            return db.query(Account).filter(
                Account.is_default,
                Account.is_deleted == False
            ).first()
    
    @staticmethod
    def get_account_transactions(account_id, days=30):
        """
        取得帳戶最近交易記錄
        
        Args:
            account_id (int): 帳戶ID
            days (int): 天數
            
        Returns:
            list: 交易記錄列表
        """
        cutoff_date = datetime.now().date() - timedelta(days=days)
        
        with get_db() as db:
            return db.query(Money).filter(
                Money.account_id == account_id,
                Money.a_time >= cutoff_date,
                Money.is_deleted == False
            ).order_by(Money.a_time.desc()).all()

class SubjectQueryHelper:
    """會計科目查詢輔助類"""
    
    @staticmethod
    def get_subject_by_code(code):
        """根據代碼取得會計科目"""
        with get_db() as db:
            return db.query(AccountSubject).filter(
                AccountSubject.code == code
            ).first()
    
    @staticmethod
    def get_top_level_subjects():
        """取得頂層會計科目"""
        with get_db() as db:
            return db.query(AccountSubject).filter(
                AccountSubject.parent_id.is_(None)
            ).order_by(AccountSubject.code).all()
    
    @staticmethod
    def get_subject_usage_stats(year=None):
        """
        取得會計科目使用統計
        
        Args:
            year (int, optional): 年份篩選
            
        Returns:
            list: 科目使用統計
        """
        with get_db() as db:
            query = db.query(
                AccountSubject.name,
                AccountSubject.code,
                func.count(Money.id).label('usage_count'),
                func.sum(Money.total).label('total_amount')
            ).join(Money).filter(
                not Money.is_deleted
            )
            
            if year:
                query = query.filter(extract('year', Money.a_time) == year)
            
            return query.group_by(
                AccountSubject.name,
                AccountSubject.code
            ).order_by(func.count(Money.id).desc()).all()

class PaymentIdentityQueryHelper:
    """收支對象查詢輔助類"""
    
    @staticmethod
    def get_active_identities():
        """取得啟用的收支對象"""
        with get_db() as db:
            return db.query(PaymentIdentity).filter(
                PaymentIdentity.is_active
            ).order_by(PaymentIdentity.name).all()
    
    @staticmethod
    def search_by_name_or_tax_id(keyword):
        """
        根據名稱或統編搜尋收支對象
        
        Args:
            keyword (str): 搜尋關鍵字
            
        Returns:
            list: 符合條件的收支對象
        """
        with get_db() as db:
            return db.query(PaymentIdentity).filter(
                (PaymentIdentity.name.like(f'%{keyword}%')) |
                (PaymentIdentity.tax_id.like(f'%{keyword}%')),
                PaymentIdentity.is_active
            ).all()
    
    @staticmethod
    def get_identity_transaction_summary(identity_id, year=None):
        """
        取得收支對象交易匯總
        
        Args:
            identity_id (int): 收支對象ID
            year (int, optional): 年份篩選
            
        Returns:
            dict: 交易匯總資料
        """
        with get_db() as db:
            query = db.query(
                Money.money_type,
                func.sum(Money.total).label('total_amount'),
                func.count(Money.id).label('count')
            ).filter(
                Money.payment_identity_id == identity_id,
                Money.is_deleted == False
            )
            
            if year:
                query = query.filter(extract('year', Money.a_time) == year)
            
            results = query.group_by(Money.money_type).all()
            
            return {
                result.money_type: {
                    'total_amount': result.total_amount or 0,
                    'count': result.count
                }
                for result in results
            }