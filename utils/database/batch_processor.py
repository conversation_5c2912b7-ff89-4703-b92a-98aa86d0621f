"""
批次處理工具
用於高效處理大量資料
"""
import logging
from typing import List, Any, Callable, Generator, Optional, Dict
from sqlalchemy.orm import Query, Session
from sqlalchemy import and_, or_
import time
from datetime import datetime

logger = logging.getLogger(__name__)

class BatchProcessor:
    """批次處理器"""
    
    DEFAULT_BATCH_SIZE = 1000
    DEFAULT_COMMIT_INTERVAL = 100
    
    def __init__(self, batch_size: int = DEFAULT_BATCH_SIZE):
        """
        初始化批次處理器
        
        Args:
            batch_size: 每批次處理的記錄數量
        """
        self.batch_size = batch_size
        self.stats = {
            'total_processed': 0,
            'batches_completed': 0,
            'errors': 0,
            'start_time': None,
            'end_time': None
        }
    
    def process_in_batches(self, query: Query, 
                          processor: Callable[[List[Any]], None],
                          batch_size: int = None) -> Dict:
        """
        批次處理查詢結果
        
        Args:
            query: SQLAlchemy 查詢物件
            processor: 處理函數，接收一批記錄作為參數
            batch_size: 批次大小（覆蓋預設值）
        
        Returns:
            處理統計資訊
        """
        batch_size = batch_size or self.batch_size
        self.stats['start_time'] = datetime.now()
        
        try:
            offset = 0
            while True:
                # 獲取一批資料
                batch = query.offset(offset).limit(batch_size).all()
                
                if not batch:
                    break
                
                # 處理這批資料
                try:
                    processor(batch)
                    self.stats['total_processed'] += len(batch)
                    self.stats['batches_completed'] += 1
                    
                    # 記錄進度
                    if self.stats['batches_completed'] % 10 == 0:
                        logger.info(
                            f"已處理 {self.stats['total_processed']} 筆記錄 "
                            f"({self.stats['batches_completed']} 批次)"
                        )
                except Exception as e:
                    logger.error(f"批次處理錯誤: {e}")
                    self.stats['errors'] += 1
                
                offset += batch_size
                
                # 如果批次小於預期大小，說明已經到最後了
                if len(batch) < batch_size:
                    break
        
        finally:
            self.stats['end_time'] = datetime.now()
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            
            logger.info(
                f"批次處理完成: {self.stats['total_processed']} 筆記錄, "
                f"{self.stats['batches_completed']} 批次, "
                f"耗時 {duration:.2f} 秒"
            )
        
        return self.stats
    
    def stream_results(self, query: Query, batch_size: int = None) -> Generator:
        """
        串流處理查詢結果（記憶體效率最佳）
        
        Args:
            query: SQLAlchemy 查詢物件
            batch_size: 批次大小
        
        Yields:
            單筆記錄
        """
        batch_size = batch_size or self.batch_size
        offset = 0
        
        while True:
            batch = query.offset(offset).limit(batch_size).all()
            
            if not batch:
                break
            
            for record in batch:
                yield record
            
            offset += batch_size
            
            if len(batch) < batch_size:
                break
    
    def bulk_insert(self, db: Session, model_class: Any, 
                   data_list: List[Dict], commit_interval: int = None) -> int:
        """
        批次插入資料
        
        Args:
            db: 資料庫會話
            model_class: 模型類別
            data_list: 要插入的資料列表
            commit_interval: 提交間隔
        
        Returns:
            成功插入的記錄數
        """
        commit_interval = commit_interval or self.DEFAULT_COMMIT_INTERVAL
        inserted_count = 0
        
        try:
            for i, data in enumerate(data_list):
                obj = model_class(**data)
                db.add(obj)
                inserted_count += 1
                
                # 定期提交以避免記憶體過度使用
                if (i + 1) % commit_interval == 0:
                    db.commit()
                    logger.debug(f"已插入 {inserted_count} 筆記錄")
            
            # 提交剩餘的記錄
            db.commit()
            logger.info(f"批次插入完成: {inserted_count} 筆記錄")
            
        except Exception as e:
            db.rollback()
            logger.error(f"批次插入失敗: {e}")
            raise
        
        return inserted_count
    
    def bulk_update(self, db: Session, query: Query,
                   update_func: Callable[[Any], None],
                   batch_size: int = None,
                   commit_interval: int = None) -> int:
        """
        批次更新資料
        
        Args:
            db: 資料庫會話
            query: 要更新的記錄查詢
            update_func: 更新函數，接收單筆記錄作為參數
            batch_size: 批次大小
            commit_interval: 提交間隔
        
        Returns:
            更新的記錄數
        """
        batch_size = batch_size or self.batch_size
        commit_interval = commit_interval or self.DEFAULT_COMMIT_INTERVAL
        updated_count = 0
        
        try:
            for i, record in enumerate(self.stream_results(query, batch_size)):
                update_func(record)
                updated_count += 1
                
                # 定期提交
                if (i + 1) % commit_interval == 0:
                    db.commit()
                    logger.debug(f"已更新 {updated_count} 筆記錄")
            
            # 提交剩餘的更新
            db.commit()
            logger.info(f"批次更新完成: {updated_count} 筆記錄")
            
        except Exception as e:
            db.rollback()
            logger.error(f"批次更新失敗: {e}")
            raise
        
        return updated_count
    
    def parallel_process(self, query: Query, processor: Callable,
                        num_workers: int = 4) -> Dict:
        """
        並行處理資料（使用多執行緒）
        
        Args:
            query: 查詢物件
            processor: 處理函數
            num_workers: 工作執行緒數量
        
        Returns:
            處理統計資訊
        """
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import threading
        
        lock = threading.Lock()
        results = {'processed': 0, 'errors': 0}
        
        def process_batch(batch):
            """處理一批資料"""
            try:
                for record in batch:
                    processor(record)
                
                with lock:
                    results['processed'] += len(batch)
                    
            except Exception as e:
                logger.error(f"並行處理錯誤: {e}")
                with lock:
                    results['errors'] += 1
        
        # 使用執行緒池並行處理
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = []
            
            # 分批提交任務
            offset = 0
            while True:
                batch = query.offset(offset).limit(self.batch_size).all()
                
                if not batch:
                    break
                
                future = executor.submit(process_batch, batch)
                futures.append(future)
                
                offset += self.batch_size
                
                if len(batch) < self.batch_size:
                    break
            
            # 等待所有任務完成
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    logger.error(f"任務執行失敗: {e}")
                    results['errors'] += 1
        
        logger.info(
            f"並行處理完成: {results['processed']} 筆成功, "
            f"{results['errors']} 筆錯誤"
        )
        
        return results

class ChunkProcessor:
    """分塊處理器（用於超大資料集）"""
    
    @staticmethod
    def process_by_date_chunks(db: Session, model_class: Any,
                              date_field: str, processor: Callable,
                              chunk_days: int = 30) -> Dict:
        """
        按日期分塊處理資料
        
        Args:
            db: 資料庫會話
            model_class: 模型類別
            date_field: 日期欄位名稱
            processor: 處理函數
            chunk_days: 每塊的天數
        
        Returns:
            處理統計資訊
        """
        from datetime import timedelta
        
        stats = {'chunks_processed': 0, 'total_records': 0}
        
        # 獲取日期範圍
        date_column = getattr(model_class, date_field)
        min_date = db.query(func.min(date_column)).scalar()
        max_date = db.query(func.max(date_column)).scalar()
        
        if not min_date or not max_date:
            return stats
        
        current_date = min_date
        
        while current_date <= max_date:
            chunk_end = current_date + timedelta(days=chunk_days)
            
            # 查詢這個日期範圍的資料
            chunk_query = db.query(model_class).filter(
                and_(
                    date_column >= current_date,
                    date_column < chunk_end
                )
            )
            
            # 處理這個分塊
            chunk_data = chunk_query.all()
            if chunk_data:
                processor(chunk_data)
                stats['chunks_processed'] += 1
                stats['total_records'] += len(chunk_data)
                
                logger.info(
                    f"處理日期分塊 {current_date.date()} 到 {chunk_end.date()}: "
                    f"{len(chunk_data)} 筆記錄"
                )
            
            current_date = chunk_end
        
        return stats
    
    @staticmethod
    def process_by_id_chunks(db: Session, model_class: Any,
                            processor: Callable, chunk_size: int = 10000) -> Dict:
        """
        按 ID 分塊處理資料（適合有連續 ID 的表）
        
        Args:
            db: 資料庫會話
            model_class: 模型類別
            processor: 處理函數
            chunk_size: 每塊的記錄數
        
        Returns:
            處理統計資訊
        """
        stats = {'chunks_processed': 0, 'total_records': 0}
        
        # 獲取 ID 範圍
        min_id = db.query(func.min(model_class.id)).scalar()
        max_id = db.query(func.max(model_class.id)).scalar()
        
        if not min_id or not max_id:
            return stats
        
        current_id = min_id
        
        while current_id <= max_id:
            chunk_end_id = current_id + chunk_size
            
            # 查詢這個 ID 範圍的資料
            chunk_query = db.query(model_class).filter(
                and_(
                    model_class.id >= current_id,
                    model_class.id < chunk_end_id
                )
            )
            
            # 處理這個分塊
            chunk_data = chunk_query.all()
            if chunk_data:
                processor(chunk_data)
                stats['chunks_processed'] += 1
                stats['total_records'] += len(chunk_data)
                
                logger.info(
                    f"處理 ID 分塊 {current_id} 到 {chunk_end_id}: "
                    f"{len(chunk_data)} 筆記錄"
                )
            
            current_id = chunk_end_id
        
        return stats

# 全域批次處理器實例
batch_processor = BatchProcessor()

# 便利函數
def process_large_dataset(query: Query, processor: Callable, batch_size: int = 1000) -> Dict:
    """處理大型資料集"""
    return batch_processor.process_in_batches(query, processor, batch_size)

def stream_large_dataset(query: Query, batch_size: int = 1000) -> Generator:
    """串流處理大型資料集"""
    return batch_processor.stream_results(query, batch_size)

def bulk_insert_records(db: Session, model_class: Any, data_list: List[Dict]) -> int:
    """批次插入記錄"""
    return batch_processor.bulk_insert(db, model_class, data_list)

def bulk_update_records(db: Session, query: Query, update_func: Callable) -> int:
    """批次更新記錄"""
    return batch_processor.bulk_update(db, query, update_func)