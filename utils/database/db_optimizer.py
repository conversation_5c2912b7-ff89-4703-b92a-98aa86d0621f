"""
資料庫優化器
自動執行資料庫性能優化，包括索引優化、查詢優化、資料清理等
"""

import logging
import time
from typing import Dict, List, Any, Tuple, Optional
from sqlalchemy import text, MetaData
from database import engine, get_db
from utils.database.db_analyzer import db_analyzer

logger = logging.getLogger(__name__)

class DatabaseOptimizer:
    """資料庫優化器"""
    
    def __init__(self):
        self.engine = engine
        
    def add_missing_indexes(self, dry_run: bool = True) -> Dict[str, Any]:
        """為缺少索引的表添加必要索引"""
        results = {
            'added_indexes': [],
            'errors': [],
            'dry_run': dry_run
        }
        
        try:
            # 根據分析結果定義需要添加的索引
            index_definitions = {
                'bank_head_offices': [
                    "CREATE INDEX IF NOT EXISTS ix_bank_head_offices_code ON bank_head_offices(code)",
                    "CREATE INDEX IF NOT EXISTS ix_bank_head_offices_name ON bank_head_offices(name)"
                ],
                'permissions': [
                    "CREATE INDEX IF NOT EXISTS ix_permissions_name ON permissions(name)",
                    "CREATE INDEX IF NOT EXISTS ix_permissions_resource ON permissions(resource_type)",
                    "CREATE INDEX IF NOT EXISTS ix_permissions_active ON permissions(is_active)"
                ],
                'roles': [
                    "CREATE INDEX IF NOT EXISTS ix_roles_name ON roles(name)",
                    "CREATE INDEX IF NOT EXISTS ix_roles_active ON roles(is_active)"
                ],
                'user_roles': [
                    "CREATE INDEX IF NOT EXISTS ix_user_roles_user_id ON user_roles(user_id)",
                    "CREATE INDEX IF NOT EXISTS ix_user_roles_role_id ON user_roles(role_id)"
                ],
                'user_sessions': [
                    "CREATE INDEX IF NOT EXISTS ix_user_sessions_user_id ON user_sessions(user_id)",
                    "CREATE INDEX IF NOT EXISTS ix_user_sessions_created_at ON user_sessions(created_at)",
                    "CREATE INDEX IF NOT EXISTS ix_user_sessions_active ON user_sessions(is_active)"
                ]
            }
            
            with self.engine.connect() as conn:
                for table, indexes in index_definitions.items():
                    for index_sql in indexes:
                        try:
                            if dry_run:
                                logger.info(f"[DRY RUN] Would execute: {index_sql}")
                                results['added_indexes'].append({
                                    'table': table,
                                    'sql': index_sql,
                                    'status': 'would_create'
                                })
                            else:
                                start_time = time.time()
                                conn.execute(text(index_sql))
                                conn.commit()
                                execution_time = time.time() - start_time
                                
                                logger.info(f"Successfully created index: {index_sql}")
                                results['added_indexes'].append({
                                    'table': table,
                                    'sql': index_sql,
                                    'status': 'created',
                                    'execution_time': execution_time
                                })
                                
                        except Exception as e:
                            error_msg = f"Failed to create index for {table}: {str(e)}"
                            logger.error(error_msg)
                            results['errors'].append({
                                'table': table,
                                'sql': index_sql,
                                'error': str(e)
                            })
            
            return results
            
        except Exception as e:
            logger.error(f"Error in add_missing_indexes: {str(e)}")
            results['errors'].append({'general_error': str(e)})
            return results
    
    def optimize_bank_tables(self, dry_run: bool = True) -> Dict[str, Any]:
        """優化銀行相關表（最大的表）"""
        results = {
            'optimizations': [],
            'errors': [],
            'dry_run': dry_run
        }
        
        try:
            optimizations = [
                # 為 bank_branches 添加複合索引
                "CREATE INDEX IF NOT EXISTS ix_bank_branches_head_office_name ON bank_branches(head_office_code, name)",
                
                # 分析表統計信息（SQLite）
                "ANALYZE bank_branches",
                "ANALYZE bank_head_offices",
                
                # 重建索引（SQLite）
                "REINDEX bank_branches",
                "REINDEX bank_head_offices"
            ]
            
            with self.engine.connect() as conn:
                for sql in optimizations:
                    try:
                        if dry_run:
                            logger.info(f"[DRY RUN] Would execute: {sql}")
                            results['optimizations'].append({
                                'sql': sql,
                                'status': 'would_execute'
                            })
                        else:
                            start_time = time.time()
                            conn.execute(text(sql))
                            conn.commit()
                            execution_time = time.time() - start_time
                            
                            logger.info(f"Successfully executed: {sql}")
                            results['optimizations'].append({
                                'sql': sql,
                                'status': 'executed',
                                'execution_time': execution_time
                            })
                            
                    except Exception as e:
                        error_msg = f"Failed to execute {sql}: {str(e)}"
                        logger.error(error_msg)
                        results['errors'].append({
                            'sql': sql,
                            'error': str(e)
                        })
            
            return results
            
        except Exception as e:
            logger.error(f"Error in optimize_bank_tables: {str(e)}")
            results['errors'].append({'general_error': str(e)})
            return results
    
    def clean_old_sessions(self, days_old: int = 30, dry_run: bool = True) -> Dict[str, Any]:
        """清理舊的用戶會話"""
        results = {
            'cleaned_sessions': 0,
            'errors': [],
            'dry_run': dry_run
        }
        
        try:
            cutoff_date = time.time() - (days_old * 24 * 60 * 60)
            
            with self.engine.connect() as conn:
                # 先查詢要刪除的記錄數量
                count_sql = """
                    SELECT COUNT(*) FROM user_sessions 
                    WHERE created_at < :cutoff_date OR (is_active = 0 AND updated_at < :cutoff_date)
                """
                
                if dry_run:
                    result = conn.execute(text(count_sql), {"cutoff_date": cutoff_date})
                    count = result.scalar()
                    results['cleaned_sessions'] = count
                    logger.info(f"[DRY RUN] Would clean {count} old sessions")
                else:
                    # 先計數
                    result = conn.execute(text(count_sql), {"cutoff_date": cutoff_date})
                    count = result.scalar()
                    
                    # 執行刪除
                    delete_sql = """
                        DELETE FROM user_sessions 
                        WHERE created_at < :cutoff_date OR (is_active = 0 AND updated_at < :cutoff_date)
                    """
                    conn.execute(text(delete_sql), {"cutoff_date": cutoff_date})
                    conn.commit()
                    
                    results['cleaned_sessions'] = count
                    logger.info(f"Successfully cleaned {count} old sessions")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in clean_old_sessions: {str(e)}")
            results['errors'].append({'general_error': str(e)})
            return results
    
    def vacuum_database(self, dry_run: bool = True) -> Dict[str, Any]:
        """執行資料庫 VACUUM 操作來回收空間和優化性能"""
        results = {
            'vacuum_executed': False,
            'size_before_mb': 0,
            'size_after_mb': 0,
            'space_saved_mb': 0,
            'errors': [],
            'dry_run': dry_run
        }
        
        try:
            # 獲取 VACUUM 前的資料庫大小
            size_analysis = db_analyzer.analyze_database_size()
            results['size_before_mb'] = size_analysis.get('database_file_size_mb', 0)
            
            if dry_run:
                logger.info("[DRY RUN] Would execute VACUUM operation")
                results['vacuum_executed'] = False
            else:
                with self.engine.connect() as conn:
                    # SQLite VACUUM 不能在事務中執行
                    conn = conn.execution_options(autocommit=True)
                    conn.execute(text("VACUUM"))
                    
                    logger.info("Successfully executed VACUUM operation")
                    results['vacuum_executed'] = True
                    
                    # 獲取 VACUUM 後的資料庫大小
                    size_analysis_after = db_analyzer.analyze_database_size()
                    results['size_after_mb'] = size_analysis_after.get('database_file_size_mb', 0)
                    results['space_saved_mb'] = results['size_before_mb'] - results['size_after_mb']
            
            return results
            
        except Exception as e:
            logger.error(f"Error in vacuum_database: {str(e)}")
            results['errors'].append({'general_error': str(e)})
            return results
    
    def update_table_statistics(self, dry_run: bool = True) -> Dict[str, Any]:
        """更新表統計信息以改善查詢計畫"""
        results = {
            'analyzed_tables': [],
            'errors': [],
            'dry_run': dry_run
        }
        
        try:
            # 獲取所有表名
            size_analysis = db_analyzer.analyze_database_size()
            tables = list(size_analysis.get('table_sizes', {}).keys())
            
            with self.engine.connect() as conn:
                for table in tables:
                    try:
                        if dry_run:
                            logger.info(f"[DRY RUN] Would analyze table: {table}")
                            results['analyzed_tables'].append({
                                'table': table,
                                'status': 'would_analyze'
                            })
                        else:
                            start_time = time.time()
                            conn.execute(text(f"ANALYZE {table}"))
                            execution_time = time.time() - start_time
                            
                            logger.info(f"Successfully analyzed table: {table}")
                            results['analyzed_tables'].append({
                                'table': table,
                                'status': 'analyzed',
                                'execution_time': execution_time
                            })
                            
                    except Exception as e:
                        error_msg = f"Failed to analyze table {table}: {str(e)}"
                        logger.error(error_msg)
                        results['errors'].append({
                            'table': table,
                            'error': str(e)
                        })
                
                if not dry_run:
                    conn.commit()
            
            return results
            
        except Exception as e:
            logger.error(f"Error in update_table_statistics: {str(e)}")
            results['errors'].append({'general_error': str(e)})
            return results
    
    def run_comprehensive_optimization(self, dry_run: bool = True) -> Dict[str, Any]:
        """執行全面的資料庫優化"""
        results = {
            'start_time': time.time(),
            'dry_run': dry_run,
            'operations': {}
        }
        
        try:
            logger.info(f"Starting comprehensive database optimization (dry_run={dry_run})")
            
            # 1. 添加缺失的索引
            logger.info("Step 1: Adding missing indexes...")
            results['operations']['add_indexes'] = self.add_missing_indexes(dry_run)
            
            # 2. 優化銀行表
            logger.info("Step 2: Optimizing bank tables...")
            results['operations']['optimize_bank_tables'] = self.optimize_bank_tables(dry_run)
            
            # 3. 清理舊會話
            logger.info("Step 3: Cleaning old sessions...")
            results['operations']['clean_sessions'] = self.clean_old_sessions(30, dry_run)
            
            # 4. 更新統計信息
            logger.info("Step 4: Updating table statistics...")
            results['operations']['update_statistics'] = self.update_table_statistics(dry_run)
            
            # 5. VACUUM 資料庫
            logger.info("Step 5: Vacuuming database...")
            results['operations']['vacuum'] = self.vacuum_database(dry_run)
            
            results['end_time'] = time.time()
            results['total_time'] = results['end_time'] - results['start_time']
            
            # 計算總體結果
            total_errors = sum(len(op.get('errors', [])) for op in results['operations'].values())
            results['success'] = total_errors == 0
            results['total_errors'] = total_errors
            
            logger.info(f"Comprehensive optimization completed in {results['total_time']:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"Error in comprehensive optimization: {str(e)}")
            results['error'] = str(e)
            results['success'] = False
            return results
    
    def get_optimization_recommendations(self) -> Dict[str, Any]:
        """基於當前資料庫狀況提供優化建議"""
        try:
            size_analysis = db_analyzer.analyze_database_size()
            performance_analysis = db_analyzer.analyze_performance_bottlenecks()
            
            recommendations = {
                'immediate': [],
                'short_term': [],
                'long_term': [],
                'maintenance': []
            }
            
            # 立即建議
            if performance_analysis.get('bottlenecks'):
                recommendations['immediate'].append({
                    'action': '執行索引優化',
                    'description': '添加缺失的索引以提升查詢性能',
                    'method': 'add_missing_indexes',
                    'priority': 'high'
                })
            
            # 短期建議
            recommendations['short_term'].append({
                'action': '優化大表性能',
                'description': '為 bank_branches 和 bank_head_offices 添加複合索引',
                'method': 'optimize_bank_tables',
                'priority': 'medium'
            })
            
            # 長期建議
            db_size = size_analysis.get('database_file_size_mb', 0)
            if db_size > 50:  # 如果資料庫超過50MB
                recommendations['long_term'].append({
                    'action': '實施資料歸檔',
                    'description': f'資料庫大小 {db_size}MB，考慮歸檔舊資料',
                    'method': 'archive_old_data',
                    'priority': 'low'
                })
            
            # 維護建議
            recommendations['maintenance'].extend([
                {
                    'action': '定期清理會話',
                    'description': '每月清理超過30天的舊會話記錄',
                    'method': 'clean_old_sessions',
                    'frequency': 'monthly'
                },
                {
                    'action': '更新統計信息',
                    'description': '每週更新表統計信息以優化查詢計畫',
                    'method': 'update_table_statistics',
                    'frequency': 'weekly'
                },
                {
                    'action': 'VACUUM 操作',
                    'description': '每月執行 VACUUM 回收空間',
                    'method': 'vacuum_database',
                    'frequency': 'monthly'
                }
            ])
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error getting optimization recommendations: {str(e)}")
            return {'error': str(e)}

# 全局優化器實例
db_optimizer = DatabaseOptimizer()

def optimize_database(dry_run: bool = True) -> Dict[str, Any]:
    """執行資料庫優化的便捷函數"""
    return db_optimizer.run_comprehensive_optimization(dry_run)

def get_optimization_plan() -> Dict[str, Any]:
    """獲取優化計畫的便捷函數"""
    return db_optimizer.get_optimization_recommendations()