"""
資料庫分析工具
分析資料庫性能、索引使用情況、資料分布等
"""

import logging
import sqlite3
from typing import Dict, List, Any, Tuple
from database import engine
from sqlalchemy import text, inspect
from collections import defaultdict
import os
import time

logger = logging.getLogger(__name__)

class DatabaseAnalyzer:
    """資料庫分析器"""
    
    def __init__(self):
        self.engine = engine
        self.inspector = inspect(engine)
        
    def analyze_database_size(self) -> Dict[str, Any]:
        """分析資料庫大小和表大小"""
        try:
            # 獲取資料庫檔案大小
            db_uri = str(self.engine.url)
            if 'sqlite:///' in db_uri:
                db_path = db_uri.replace('sqlite:///', '')
                db_file_size = os.path.getsize(db_path) if os.path.exists(db_path) else 0
            else:
                db_file_size = 0
            
            table_sizes = {}
            total_records = 0
            
            # 獲取所有表名
            tables = self.inspector.get_table_names()
            
            with self.engine.connect() as conn:
                for table in tables:
                    try:
                        # 計算每個表的記錄數
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.scalar()
                        table_sizes[table] = count
                        total_records += count
                    except Exception as e:
                        logger.error(f"Error counting records in table {table}: {str(e)}")
                        table_sizes[table] = 0
            
            return {
                'database_file_size_bytes': db_file_size,
                'database_file_size_mb': round(db_file_size / 1024 / 1024, 2),
                'total_tables': len(tables),
                'total_records': total_records,
                'table_sizes': table_sizes,
                'largest_tables': sorted(table_sizes.items(), key=lambda x: x[1], reverse=True)[:5]
            }
            
        except Exception as e:
            logger.error(f"Error analyzing database size: {str(e)}")
            return {'error': str(e)}
    
    def analyze_indexes(self) -> Dict[str, Any]:
        """分析索引使用情況"""
        try:
            tables = self.inspector.get_table_names()
            index_info = {}
            
            for table in tables:
                indexes = self.inspector.get_indexes(table)
                foreign_keys = self.inspector.get_foreign_keys(table)
                
                index_info[table] = {
                    'indexes': indexes,
                    'foreign_keys': foreign_keys,
                    'index_count': len(indexes),
                    'fk_count': len(foreign_keys)
                }
            
            # 統計
            total_indexes = sum(info['index_count'] for info in index_info.values())
            total_fks = sum(info['fk_count'] for info in index_info.values())
            
            return {
                'total_indexes': total_indexes,
                'total_foreign_keys': total_fks,
                'table_index_info': index_info,
                'tables_without_indexes': [
                    table for table, info in index_info.items() 
                    if info['index_count'] == 0
                ]
            }
            
        except Exception as e:
            logger.error(f"Error analyzing indexes: {str(e)}")
            return {'error': str(e)}
    
    def analyze_query_patterns(self) -> Dict[str, Any]:
        """分析常用查詢模式（基於現有查詢優化器的數據）"""
        try:
            from utils.database.query_optimizer import query_analyzer
            
            if not query_analyzer:
                return {'message': 'Query analyzer not available'}
            
            # 獲取查詢性能報告
            performance_summary = query_analyzer.get_performance_summary(24)  # 最近24小時
            optimization_report = query_analyzer.get_optimization_report()
            
            # 分析慢查詢模式
            slow_patterns = []
            for query_info in query_analyzer.slow_queries:
                slow_patterns.append({
                    'query': query_info['query'][:200],  # 只取前200字符
                    'execution_time': query_info['execution_time'],
                    'timestamp': query_info['timestamp']
                })
            
            return {
                'performance_summary': performance_summary,
                'optimization_report': optimization_report,
                'slow_queries': slow_patterns[-10:],  # 最近10個慢查詢
                'query_history_count': len(query_analyzer.query_history)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing query patterns: {str(e)}")
            return {'error': str(e)}
    
    def check_data_integrity(self) -> Dict[str, Any]:
        """檢查資料完整性"""
        try:
            integrity_issues = []
            
            with self.engine.connect() as conn:
                # 檢查外鍵約束
                result = conn.execute(text("PRAGMA foreign_key_check"))
                fk_violations = result.fetchall()
                
                if fk_violations:
                    integrity_issues.append({
                        'type': 'foreign_key_violations',
                        'count': len(fk_violations),
                        'violations': [dict(row) for row in fk_violations[:5]]  # 只取前5個
                    })
                
                # 檢查NULL值在NOT NULL欄位
                tables = self.inspector.get_table_names()
                for table in tables:
                    columns = self.inspector.get_columns(table)
                    for column in columns:
                        if not column['nullable'] and column['name'] != 'id':
                            try:
                                result = conn.execute(text(f"SELECT COUNT(*) FROM {table} WHERE {column['name']} IS NULL"))
                                null_count = result.scalar()
                                if null_count > 0:
                                    integrity_issues.append({
                                        'type': 'null_in_not_null_column',
                                        'table': table,
                                        'column': column['name'],
                                        'count': null_count
                                    })
                            except Exception:
                                continue  # 跳過可能的語法錯誤
                
                # 檢查軟刪除一致性
                for table in ['account', 'money', 'payment_identity']:
                    try:
                        result = conn.execute(text(f"""
                            SELECT COUNT(*) FROM {table} 
                            WHERE is_deleted = 1 AND deleted_at IS NULL
                        """))
                        inconsistent_count = result.scalar()
                        if inconsistent_count > 0:
                            integrity_issues.append({
                                'type': 'soft_delete_inconsistency',
                                'table': table,
                                'count': inconsistent_count
                            })
                    except Exception:
                        continue  # 表可能沒有這些欄位
            
            return {
                'total_issues': len(integrity_issues),
                'issues': integrity_issues
            }
            
        except Exception as e:
            logger.error(f"Error checking data integrity: {str(e)}")
            return {'error': str(e)}
    
    def analyze_performance_bottlenecks(self) -> Dict[str, Any]:
        """分析性能瓶頸"""
        try:
            bottlenecks = []
            recommendations = []
            
            # 分析大表
            size_analysis = self.analyze_database_size()
            if 'table_sizes' in size_analysis:
                large_tables = [
                    table for table, size in size_analysis['table_sizes'].items() 
                    if size > 10000  # 超過1萬筆記錄
                ]
                
                if large_tables:
                    bottlenecks.append({
                        'type': 'large_tables',
                        'tables': large_tables,
                        'description': '大型表可能影響查詢性能'
                    })
                    recommendations.append('考慮對大型表實施分區或歸檔策略')
            
            # 分析索引缺失
            index_analysis = self.analyze_indexes()
            if 'tables_without_indexes' in index_analysis:
                tables_without_idx = index_analysis['tables_without_indexes']
                if tables_without_idx:
                    bottlenecks.append({
                        'type': 'missing_indexes',
                        'tables': tables_without_idx,
                        'description': '缺少索引的表可能查詢較慢'
                    })
                    recommendations.append('為經常查詢的欄位添加索引')
            
            # 檢查查詢性能
            query_analysis = self.analyze_query_patterns()
            if 'slow_queries' in query_analysis and query_analysis['slow_queries']:
                slow_count = len(query_analysis['slow_queries'])
                bottlenecks.append({
                    'type': 'slow_queries',
                    'count': slow_count,
                    'description': f'發現 {slow_count} 個慢查詢'
                })
                recommendations.append('優化慢查詢的SQL語句和索引')
            
            return {
                'bottlenecks': bottlenecks,
                'recommendations': recommendations,
                'severity': 'high' if len(bottlenecks) >= 3 else 'medium' if len(bottlenecks) >= 1 else 'low'
            }
            
        except Exception as e:
            logger.error(f"Error analyzing performance bottlenecks: {str(e)}")
            return {'error': str(e)}
    
    def generate_optimization_plan(self) -> Dict[str, Any]:
        """生成資料庫優化計畫"""
        try:
            # 綜合所有分析結果
            size_analysis = self.analyze_database_size()
            index_analysis = self.analyze_indexes()
            integrity_analysis = self.check_data_integrity()
            performance_analysis = self.analyze_performance_bottlenecks()
            query_analysis = self.analyze_query_patterns()
            
            optimization_plan = {
                'immediate_actions': [],      # 立即執行
                'short_term_actions': [],     # 短期執行（1週內）
                'long_term_actions': [],      # 長期執行（1個月內）
                'monitoring_actions': []      # 持續監控
            }
            
            # 立即執行的優化
            if integrity_analysis.get('total_issues', 0) > 0:
                optimization_plan['immediate_actions'].append({
                    'action': '修復資料完整性問題',
                    'priority': 'critical',
                    'description': f"發現 {integrity_analysis['total_issues']} 個資料完整性問題"
                })
            
            # 短期執行的優化
            if performance_analysis.get('bottlenecks'):
                for bottleneck in performance_analysis['bottlenecks']:
                    if bottleneck['type'] == 'missing_indexes':
                        optimization_plan['short_term_actions'].append({
                            'action': '添加缺失的索引',
                            'priority': 'high',
                            'description': f"為表 {bottleneck['tables']} 添加必要索引"
                        })
                    elif bottleneck['type'] == 'slow_queries':
                        optimization_plan['short_term_actions'].append({
                            'action': '優化慢查詢',
                            'priority': 'high',
                            'description': f"優化 {bottleneck['count']} 個慢查詢"
                        })
            
            # 長期執行的優化
            if size_analysis.get('database_file_size_mb', 0) > 100:  # 超過100MB
                optimization_plan['long_term_actions'].append({
                    'action': '實施資料歸檔策略',
                    'priority': 'medium',
                    'description': f"資料庫大小 {size_analysis['database_file_size_mb']}MB，考慮歸檔舊資料"
                })
            
            # 監控動作
            optimization_plan['monitoring_actions'].append({
                'action': '定期性能監控',
                'frequency': 'weekly',
                'description': '每週執行資料庫性能分析'
            })
            
            optimization_plan['monitoring_actions'].append({
                'action': '查詢性能追蹤',
                'frequency': 'daily',
                'description': '持續監控慢查詢和性能指標'
            })
            
            return {
                'analysis_summary': {
                    'database_size_mb': size_analysis.get('database_file_size_mb', 0),
                    'total_tables': size_analysis.get('total_tables', 0),
                    'total_records': size_analysis.get('total_records', 0),
                    'total_indexes': index_analysis.get('total_indexes', 0),
                    'integrity_issues': integrity_analysis.get('total_issues', 0),
                    'performance_bottlenecks': len(performance_analysis.get('bottlenecks', []))
                },
                'optimization_plan': optimization_plan,
                'estimated_impact': self._estimate_optimization_impact(optimization_plan)
            }
            
        except Exception as e:
            logger.error(f"Error generating optimization plan: {str(e)}")
            return {'error': str(e)}
    
    def _estimate_optimization_impact(self, plan: Dict[str, Any]) -> Dict[str, str]:
        """估計優化影響"""
        immediate_count = len(plan['immediate_actions'])
        short_term_count = len(plan['short_term_actions'])
        long_term_count = len(plan['long_term_actions'])
        
        if immediate_count > 0:
            return {
                'performance_improvement': '20-50%',
                'stability_improvement': 'High',
                'implementation_effort': 'Medium'
            }
        elif short_term_count > 0:
            return {
                'performance_improvement': '10-30%',
                'stability_improvement': 'Medium',
                'implementation_effort': 'Low'
            }
        else:
            return {
                'performance_improvement': '5-10%',
                'stability_improvement': 'Low',
                'implementation_effort': 'Very Low'
            }

# 全局分析器實例
db_analyzer = DatabaseAnalyzer()

def run_complete_database_analysis() -> Dict[str, Any]:
    """執行完整的資料庫分析"""
    return db_analyzer.generate_optimization_plan()

def get_database_health_summary() -> Dict[str, Any]:
    """獲取資料庫健康摘要"""
    try:
        size_analysis = db_analyzer.analyze_database_size()
        performance_analysis = db_analyzer.analyze_performance_bottlenecks()
        
        # 計算健康分數 (0-100)
        health_score = 100
        
        # 根據性能瓶頸扣分
        bottleneck_count = len(performance_analysis.get('bottlenecks', []))
        health_score -= min(bottleneck_count * 15, 60)  # 最多扣60分
        
        # 根據資料庫大小調整
        db_size_mb = size_analysis.get('database_file_size_mb', 0)
        if db_size_mb > 500:  # 超過500MB
            health_score -= 10
        elif db_size_mb > 200:  # 超過200MB
            health_score -= 5
        
        # 確保分數在0-100之間
        health_score = max(0, min(100, health_score))
        
        return {
            'health_score': health_score,
            'status': 'excellent' if health_score >= 90 else 
                     'good' if health_score >= 70 else 
                     'fair' if health_score >= 50 else 'poor',
            'database_size_mb': db_size_mb,
            'performance_issues': bottleneck_count,
            'last_checked': time.time()
        }
        
    except Exception as e:
        logger.error(f"Error getting database health summary: {str(e)}")
        return {
            'health_score': 0,
            'status': 'unknown',
            'error': str(e),
            'last_checked': time.time()
        }