"""
大量資料操作優化工具
提供高效的批次操作方法
"""
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text, bindparam
from sqlalchemy.dialects.sqlite import insert as sqlite_insert
from sqlalchemy.dialects.postgresql import insert as pg_insert
import time

logger = logging.getLogger(__name__)

class BulkOperations:
    """大量資料操作優化"""
    
    @staticmethod
    def bulk_insert_mappings(db: Session, model_class: Any, 
                           mappings: List[Dict], chunk_size: int = 1000):
        """
        使用 bulk_insert_mappings 批次插入（最快的插入方法）
        
        Args:
            db: 資料庫會話
            model_class: 模型類別
            mappings: 資料字典列表
            chunk_size: 每次插入的記錄數
        """
        total_count = len(mappings)
        inserted = 0
        
        try:
            # 分批插入
            for i in range(0, total_count, chunk_size):
                chunk = mappings[i:i + chunk_size]
                db.bulk_insert_mappings(model_class, chunk)
                inserted += len(chunk)
                
                # 定期提交
                if inserted % (chunk_size * 10) == 0:
                    db.commit()
                    logger.info(f"已插入 {inserted}/{total_count} 筆記錄")
            
            db.commit()
            logger.info(f"批次插入完成: {inserted} 筆記錄")
            return inserted
            
        except Exception as e:
            db.rollback()
            logger.error(f"批次插入失敗: {e}")
            raise
    
    @staticmethod
    def bulk_update_mappings(db: Session, model_class: Any, 
                           mappings: List[Dict], chunk_size: int = 1000):
        """
        使用 bulk_update_mappings 批次更新
        
        Args:
            db: 資料庫會話
            model_class: 模型類別
            mappings: 資料字典列表（必須包含主鍵）
            chunk_size: 每次更新的記錄數
        """
        total_count = len(mappings)
        updated = 0
        
        try:
            # 分批更新
            for i in range(0, total_count, chunk_size):
                chunk = mappings[i:i + chunk_size]
                db.bulk_update_mappings(model_class, chunk)
                updated += len(chunk)
                
                # 定期提交
                if updated % (chunk_size * 10) == 0:
                    db.commit()
                    logger.info(f"已更新 {updated}/{total_count} 筆記錄")
            
            db.commit()
            logger.info(f"批次更新完成: {updated} 筆記錄")
            return updated
            
        except Exception as e:
            db.rollback()
            logger.error(f"批次更新失敗: {e}")
            raise
    
    @staticmethod
    def execute_bulk_insert_sql(db: Session, table_name: str, 
                               data_list: List[Dict]) -> int:
        """
        使用原生 SQL 執行批次插入（最高效能）
        
        Args:
            db: 資料庫會話
            table_name: 表名
            data_list: 資料列表
        
        Returns:
            插入的記錄數
        """
        if not data_list:
            return 0
        
        # 獲取欄位名稱
        columns = list(data_list[0].keys())
        
        # 建立 SQL 語句
        placeholders = ', '.join([f':{col}' for col in columns])
        columns_str = ', '.join(columns)
        
        sql = text(f"""
            INSERT INTO {table_name} ({columns_str})
            VALUES ({placeholders})
        """)
        
        try:
            # 執行批次插入
            result = db.execute(sql, data_list)
            db.commit()
            
            inserted_count = result.rowcount
            logger.info(f"原生 SQL 批次插入完成: {inserted_count} 筆記錄")
            return inserted_count
            
        except Exception as e:
            db.rollback()
            logger.error(f"原生 SQL 批次插入失敗: {e}")
            raise
    
    @staticmethod
    def upsert_records(db: Session, model_class: Any, 
                      records: List[Dict], unique_key: str):
        """
        批次 UPSERT（插入或更新）操作
        
        Args:
            db: 資料庫會話
            model_class: 模型類別
            records: 記錄列表
            unique_key: 唯一鍵欄位名稱
        """
        # 檢查資料庫類型
        dialect = db.bind.dialect.name
        
        if dialect == 'sqlite':
            # SQLite 使用 INSERT OR REPLACE
            stmt = sqlite_insert(model_class.__table__)
            stmt = stmt.on_conflict_do_update(
                index_elements=[unique_key],
                set_={
                    col.name: col 
                    for col in stmt.excluded 
                    if col.name != unique_key
                }
            )
        elif dialect == 'postgresql':
            # PostgreSQL 使用 INSERT ... ON CONFLICT
            stmt = pg_insert(model_class.__table__)
            stmt = stmt.on_conflict_do_update(
                index_elements=[unique_key],
                set_={
                    col.name: stmt.excluded[col.name]
                    for col in model_class.__table__.columns
                    if col.name != unique_key
                }
            )
        else:
            # 其他資料庫使用傳統方法
            return BulkOperations._upsert_fallback(
                db, model_class, records, unique_key
            )
        
        try:
            db.execute(stmt, records)
            db.commit()
            logger.info(f"UPSERT 操作完成: {len(records)} 筆記錄")
            
        except Exception as e:
            db.rollback()
            logger.error(f"UPSERT 操作失敗: {e}")
            raise
    
    @staticmethod
    def _upsert_fallback(db: Session, model_class: Any, 
                        records: List[Dict], unique_key: str):
        """
        UPSERT 的備用實現（適用於不支援的資料庫）
        """
        inserted = 0
        updated = 0
        
        for record in records:
            unique_value = record.get(unique_key)
            if not unique_value:
                continue
            
            # 查詢是否存在
            existing = db.query(model_class).filter_by(
                **{unique_key: unique_value}
            ).first()
            
            if existing:
                # 更新
                for key, value in record.items():
                    setattr(existing, key, value)
                updated += 1
            else:
                # 插入
                new_record = model_class(**record)
                db.add(new_record)
                inserted += 1
        
        db.commit()
        logger.info(f"UPSERT 完成: {inserted} 筆插入, {updated} 筆更新")
        return inserted + updated

class PerformanceOptimizer:
    """效能優化器"""
    
    @staticmethod
    def optimize_large_query(query, use_yield: bool = True):
        """
        優化大型查詢
        
        Args:
            query: SQLAlchemy 查詢
            use_yield: 是否使用 yield_per 優化
        
        Returns:
            優化後的查詢
        """
        if use_yield:
            # 使用 yield_per 減少記憶體使用
            query = query.yield_per(1000)
        
        # 啟用查詢結果串流
        query = query.enable_eagerloads(False)
        
        return query
    
    @staticmethod
    def measure_performance(func):
        """
        裝飾器：測量函數執行時間
        """
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            duration = end_time - start_time
            logger.info(
                f"{func.__name__} 執行時間: {duration:.3f} 秒"
            )
            
            return result
        return wrapper
    
    @staticmethod
    def get_query_plan(db: Session, query):
        """
        獲取查詢執行計劃（用於優化分析）
        
        Args:
            db: 資料庫會話
            query: SQLAlchemy 查詢
        
        Returns:
            查詢執行計劃
        """
        # 獲取 SQL 語句
        sql = str(query.statement.compile(
            compile_kwargs={"literal_binds": True}
        ))
        
        # 根據資料庫類型獲取執行計劃
        dialect = db.bind.dialect.name
        
        if dialect == 'sqlite':
            explain_sql = f"EXPLAIN QUERY PLAN {sql}"
        elif dialect == 'postgresql':
            explain_sql = f"EXPLAIN ANALYZE {sql}"
        elif dialect == 'mysql':
            explain_sql = f"EXPLAIN {sql}"
        else:
            return "不支援的資料庫類型"
        
        try:
            result = db.execute(text(explain_sql))
            plan = [dict(row) for row in result]
            return plan
        except Exception as e:
            logger.error(f"獲取查詢計劃失敗: {e}")
            return None

class DataAggregator:
    """資料聚合優化器"""
    
    @staticmethod
    def aggregate_in_database(db: Session, model_class: Any,
                            group_by: List[str], 
                            aggregations: Dict[str, str]) -> List[Dict]:
        """
        在資料庫層級進行聚合（比在應用層聚合快得多）
        
        Args:
            db: 資料庫會話
            model_class: 模型類別
            group_by: 分組欄位列表
            aggregations: 聚合操作字典 {"欄位": "函數"}
                        例如: {"amount": "sum", "id": "count"}
        
        Returns:
            聚合結果列表
        """
        from sqlalchemy import func
        
        # 建立查詢
        query = db.query()
        
        # 添加分組欄位
        for field in group_by:
            column = getattr(model_class, field)
            query = query.add_columns(column)
        
        # 添加聚合欄位
        for field, agg_func in aggregations.items():
            column = getattr(model_class, field)
            
            if agg_func == 'sum':
                agg_column = func.sum(column).label(f'{field}_sum')
            elif agg_func == 'count':
                agg_column = func.count(column).label(f'{field}_count')
            elif agg_func == 'avg':
                agg_column = func.avg(column).label(f'{field}_avg')
            elif agg_func == 'max':
                agg_column = func.max(column).label(f'{field}_max')
            elif agg_func == 'min':
                agg_column = func.min(column).label(f'{field}_min')
            else:
                continue
            
            query = query.add_columns(agg_column)
        
        # 設定 FROM 子句
        query = query.select_from(model_class)
        
        # 添加 GROUP BY
        for field in group_by:
            column = getattr(model_class, field)
            query = query.group_by(column)
        
        # 執行查詢
        results = query.all()
        
        # 轉換為字典格式
        result_dicts = []
        for row in results:
            result_dict = {}
            for i, field in enumerate(group_by):
                result_dict[field] = row[i]
            
            # 添加聚合結果
            offset = len(group_by)
            for j, (field, agg_func) in enumerate(aggregations.items()):
                result_dict[f'{field}_{agg_func}'] = row[offset + j]
            
            result_dicts.append(result_dict)
        
        return result_dicts

# 匯出便利函數
def bulk_insert(db: Session, model: Any, data: List[Dict]) -> int:
    """快速批次插入"""
    return BulkOperations.bulk_insert_mappings(db, model, data)

def bulk_update(db: Session, model: Any, data: List[Dict]) -> int:
    """快速批次更新"""
    return BulkOperations.bulk_update_mappings(db, model, data)

def upsert(db: Session, model: Any, data: List[Dict], key: str) -> int:
    """批次 UPSERT 操作"""
    BulkOperations.upsert_records(db, model, data, key)
    return len(data)