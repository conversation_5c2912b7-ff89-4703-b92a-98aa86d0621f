"""
配置管理工具
提供配置驗證、遷移和管理功能
"""

import os
import json
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from config.advanced_config import ConfigManager, get_config

logger = logging.getLogger(__name__)

class ConfigValidator:
    """配置驗證器"""
    
    @staticmethod
    def validate_database_connection(config_manager: ConfigManager) -> bool:
        """驗證資料庫連接"""
        try:
            from sqlalchemy import create_engine
            
            uri = config_manager.get_database_uri()
            engine = create_engine(uri, echo=False)
            
            # 測試連接
            with engine.connect() as conn:
                conn.execute('SELECT 1')
            
            logger.info("資料庫連接驗證成功")
            return True
            
        except Exception as e:
            logger.error(f"資料庫連接驗證失敗: {e}")
            return False
    
    @staticmethod
    def validate_redis_connection(config_manager: ConfigManager) -> bool:
        """驗證 Redis 連接"""
        if config_manager.cache.type != 'redis':
            return True  # 不使用 Redis 時跳過驗證
        
        try:
            import redis
            
            r = redis.Redis(
                host=config_manager.cache.redis_host,
                port=config_manager.cache.redis_port,
                db=config_manager.cache.redis_db,
                password=config_manager.cache.redis_password or None,
                socket_timeout=5
            )
            
            # 測試連接
            r.ping()
            
            logger.info("Redis 連接驗證成功")
            return True
            
        except ImportError:
            logger.warning("Redis 模組未安裝，跳過 Redis 驗證")
            return True
        except Exception as e:
            logger.error(f"Redis 連接驗證失敗: {e}")
            return False
    
    @staticmethod
    def validate_mail_config(config_manager: ConfigManager) -> bool:
        """驗證郵件配置"""
        if not config_manager.mail.smtp_server:
            return True  # 未配置郵件時跳過驗證
        
        try:
            import smtplib
            from email.mime.text import MIMEText
            
            mail_config = config_manager.mail
            
            # 測試 SMTP 連接
            if mail_config.use_ssl:
                server = smtplib.SMTP_SSL(mail_config.smtp_server, mail_config.smtp_port)
            else:
                server = smtplib.SMTP(mail_config.smtp_server, mail_config.smtp_port)
                if mail_config.use_tls:
                    server.starttls()
            
            if mail_config.username and mail_config.password:
                server.login(mail_config.username, mail_config.password)
            
            server.quit()
            
            logger.info("郵件配置驗證成功")
            return True
            
        except Exception as e:
            logger.error(f"郵件配置驗證失敗: {e}")
            return False

class ConfigMigrator:
    """配置遷移工具"""
    
    @staticmethod
    def migrate_from_old_config():
        """從舊的 config.py 遷移到新配置系統"""
        try:
            from config.config import Config as OldConfig
            
            # 讀取舊配置
            old_config_dict = {}
            if hasattr(OldConfig, 'get_config_dict'):
                old_config_dict = OldConfig.get_config_dict()
            
            # 創建新配置
            new_config = ConfigManager()
            
            # 映射舊配置到新配置
            mapping = {
                'SECRET_KEY': ('security', 'secret_key'),
                'DEBUG': ('logging', 'level', lambda x: 'DEBUG' if x else 'INFO'),
                'ITEMS_PER_PAGE': ('api', 'items_per_page'),
                'MAX_ITEMS_PER_PAGE': ('api', 'max_items_per_page'),
                'UPLOAD_FOLDER': ('files', 'upload_folder'),
                'MAX_CONTENT_LENGTH': ('files', 'max_content_length'),
                'LOG_LEVEL': ('logging', 'level'),
                'LOG_MAX_BYTES': ('logging', 'max_bytes'),
                'LOG_BACKUP_COUNT': ('logging', 'backup_count'),
                'CACHE_DEFAULT_TIMEOUT': ('cache', 'default_timeout'),
            }
            
            for old_key, mapping_info in mapping.items():
                if old_key in old_config_dict:
                    value = old_config_dict[old_key]
                    
                    section_name = mapping_info[0]
                    attr_name = mapping_info[1]
                    converter = mapping_info[2] if len(mapping_info) > 2 else lambda x: x
                    
                    section = getattr(new_config, section_name)
                    setattr(section, attr_name, converter(value))
            
            # 儲存到配置檔案
            config_path = Path('config') / 'migrated_config.yaml'
            new_config.save_config(str(config_path))
            
            logger.info(f"配置遷移完成，已儲存到: {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"配置遷移失敗: {e}")
            return False

class ConfigManager:
    """配置管理工具"""
    
    @staticmethod
    def export_config(format: str = 'yaml', include_sensitive: bool = False) -> str:
        """匯出目前配置"""
        try:
            config = get_config()
            data = config.to_dict(include_sensitive=include_sensitive)
            
            if format.lower() == 'json':
                return json.dumps(data, indent=2, ensure_ascii=False)
            elif format.lower() in ['yml', 'yaml']:
                return yaml.dump(data, default_flow_style=False, 
                               allow_unicode=True, indent=2)
            else:
                raise ValueError(f'不支援的格式: {format}')
                
        except Exception as e:
            logger.error(f"匯出配置失敗: {e}")
            raise
    
    @staticmethod
    def get_config_summary() -> Dict[str, Any]:
        """獲取配置摘要"""
        try:
            config = get_config()
            
            summary = {
                'database': {
                    'type': config.database.type,
                    'host': config.database.host if config.database.type != 'sqlite' else 'N/A',
                    'pool_size': config.database.pool_size
                },
                'cache': {
                    'type': config.cache.type,
                    'timeout': config.cache.default_timeout
                },
                'security': {
                    'csrf_enabled': config.security.csrf_enabled,
                    'rate_limit_enabled': config.security.rate_limit_enabled,
                    'secret_key_set': bool(config.security.secret_key)
                },
                'logging': {
                    'level': config.logging.level,
                    'file_logging': config.logging.enable_file_logging
                },
                'performance': {
                    'monitoring_enabled': config.performance.enable_monitoring,
                    'slow_threshold': config.performance.slow_request_threshold
                }
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"獲取配置摘要失敗: {e}")
            return {}
    
    @staticmethod
    def validate_all_configs() -> Dict[str, bool]:
        """驗證所有配置"""
        config = get_config()
        results = {}
        
        try:
            # 基本配置驗證
            config.validate_config()
            results['basic'] = True
        except Exception as e:
            logger.error(f"基本配置驗證失敗: {e}")
            results['basic'] = False
        
        # 資料庫連接驗證
        results['database'] = ConfigValidator.validate_database_connection(config)
        
        # Redis 連接驗證
        results['redis'] = ConfigValidator.validate_redis_connection(config)
        
        # 郵件配置驗證
        results['mail'] = ConfigValidator.validate_mail_config(config)
        
        return results
    
    @staticmethod
    def create_env_file_from_config():
        """從當前配置創建 .env 檔案"""
        try:
            config = get_config()
            
            env_content = [
                "# 印錢大師會計系統 - 自動生成的環境變數配置",
                f"# 生成時間: {datetime.now().isoformat()}",
                "",
                "# ===================",
                "# 基本設定",
                "# ===================",
                f"SECRET_KEY={config.security.secret_key}",
                "",
                "# ===================", 
                "# 資料庫設定",
                "# ===================",
                f"DB_TYPE={config.database.type}",
                f"DB_HOST={config.database.host}",
                f"DB_PORT={config.database.port}",
                f"DB_NAME={config.database.name}",
                f"DB_POOL_SIZE={config.database.pool_size}",
                "",
                "# ===================",
                "# 快取設定", 
                "# ===================",
                f"CACHE_TYPE={config.cache.type}",
                f"CACHE_DEFAULT_TIMEOUT={config.cache.default_timeout}",
                "",
                "# ===================",
                "# 安全設定",
                "# ===================",
                f"CSRF_ENABLED={config.security.csrf_enabled}",
                f"RATE_LIMIT_ENABLED={config.security.rate_limit_enabled}",
                "",
                "# ===================",
                "# 日誌設定",
                "# ===================",
                f"LOG_LEVEL={config.logging.level}",
                f"LOG_MAX_BYTES={config.logging.max_bytes}",
                "",
                "# ===================",
                "# API 設定",
                "# ===================",
                f"ITEMS_PER_PAGE={config.api.items_per_page}",
                f"MAX_ITEMS_PER_PAGE={config.api.max_items_per_page}",
            ]
            
            env_path = Path('.env.generated')
            with open(env_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(env_content))
            
            logger.info(f"環境變數檔案已生成: {env_path}")
            return str(env_path)
            
        except Exception as e:
            logger.error(f"生成環境變數檔案失敗: {e}")
            raise

def print_config_status():
    """打印配置狀態"""
    try:
        print("\n🔧 配置管理系統狀態")
        print("=" * 50)
        
        # 配置摘要
        summary = ConfigManager.get_config_summary()
        
        print(f"📊 資料庫: {summary['database']['type']} (連接池: {summary['database']['pool_size']})")
        print(f"💾 快取: {summary['cache']['type']} (超時: {summary['cache']['timeout']}s)")
        print(f"🔐 安全: CSRF={summary['security']['csrf_enabled']}, 限速={summary['security']['rate_limit_enabled']}")
        print(f"📝 日誌: {summary['logging']['level']} (檔案: {summary['logging']['file_logging']})")
        print(f"⚡ 效能監控: {summary['performance']['monitoring_enabled']}")
        
        # 驗證結果
        print("\n✅ 配置驗證結果:")
        validation_results = ConfigManager.validate_all_configs()
        
        for component, is_valid in validation_results.items():
            status = "✅ 通過" if is_valid else "❌ 失敗"
            print(f"  {component:>10}: {status}")
        
        print("\n" + "=" * 50)
        
    except Exception as e:
        print(f"❌ 無法獲取配置狀態: {e}")

if __name__ == '__main__':
    # 命令行工具
    import sys
    from datetime import datetime
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'status':
            print_config_status()
        elif command == 'migrate':
            ConfigMigrator.migrate_from_old_config()
        elif command == 'validate':
            results = ConfigManager.validate_all_configs()
            all_valid = all(results.values())
            print("✅ 所有配置驗證通過" if all_valid else "❌ 部分配置驗證失敗")
            sys.exit(0 if all_valid else 1)
        elif command == 'export':
            format_type = sys.argv[2] if len(sys.argv) > 2 else 'yaml'
            print(ConfigManager.export_config(format_type))
        else:
            print("可用命令: status, migrate, validate, export")
    else:
        print_config_status()