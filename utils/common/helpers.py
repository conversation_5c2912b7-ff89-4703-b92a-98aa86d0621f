"""
通用工具函數模組
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timezone, timedelta
import re

def format_currency(amount: float) -> str:
    """格式化貨幣顯示"""
    if amount is None:
        return "0"
    return f"{amount:,.0f}"

def format_date(date_obj: date) -> str:
    """格式化日期顯示"""
    if date_obj is None:
        return ""
    return date_obj.strftime("%Y-%m-%d")

def validate_tax_id(tax_id: str) -> bool:
    """驗證統一編號"""
    if not tax_id or len(tax_id) != 8:
        return False
    
    # 統一編號驗證邏輯
    weights = [1, 2, 1, 2, 1, 2, 4, 1]
    total = 0
    
    for i, char in enumerate(tax_id):
        if not char.isdigit():
            return False
        digit = int(char)
        product = digit * weights[i]
        total += (product // 10) + (product % 10)
    
    return total % 10 == 0

def validate_phone(phone: str) -> bool:
    """驗證電話號碼"""
    if not phone:
        return False
    # 台灣電話號碼格式驗證
    pattern = r'^(\+886|0)?[2-9]\d{7,8}$'
    return bool(re.match(pattern, phone))

def validate_email(email: str) -> bool:
    """驗證電子郵件"""
    if not email:
        return False
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def get_menu_data() -> Dict[str, Any]:
    """取得選單資料"""
    from data.menu_data import menu
    return menu

def get_sidebar_items() -> List[str]:
    """取得側邊欄項目"""
    from data.menu_data import menu
    return list(menu.keys())

def get_template_context(selected: Optional[str] = None) -> Dict[str, Any]:
    """取得模板通用上下文"""
    context: Dict[str, Any] = {
        'sidebar_items': get_sidebar_items()
    }
    if selected:
        context['selected'] = selected
        context['submenus'] = get_menu_data().get(selected, {})
    return context

def render_with_menu(template_name: str, selected_menu: str, **context):
    """
    渲染模板並自動注入選單資料
    
    Args:
        template_name (str): 模板名稱
        selected_menu (str): 選中的選單項目
        **context: 其他模板上下文資料
        
    Returns:
        渲染後的模板
    """
    from flask import render_template
    
    menu_context = get_template_context(selected_menu)
    full_context = {**menu_context, **context}
    
    return render_template(template_name, **full_context)

def format_account_code(code: str) -> str:
    """格式化會計科目代碼"""
    if not code:
        return ""
    # 每4位數加一個空格
    return " ".join([code[i:i+4] for i in range(0, len(code), 4)])

def validate_account_code(code: str) -> bool:
    """驗證會計科目代碼"""
    if not code:
        return False
    # 只允許數字和字母
    return bool(re.match(r'^[A-Za-z0-9]+$', code))

def get_current_year() -> int:
    """取得目前年度"""
    return datetime.now(timezone(timedelta(hours=8))).year

def get_fiscal_year() -> int:
    """取得會計年度（假設會計年度從1月1日開始）"""
    return get_current_year() 