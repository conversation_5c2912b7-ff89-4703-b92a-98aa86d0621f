"""
統一錯誤處理工具
"""
import logging
import traceback
from functools import wraps
from flask import jsonify, flash, redirect, request, render_template
from datetime import datetime
import os

logger = logging.getLogger(__name__)

# 自定義異常類別
class AccountingSystemError(Exception):
    """會計系統基礎異常"""
    def __init__(self, message, error_code=None, details=None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = datetime.now()

class DatabaseError(AccountingSystemError):
    """資料庫相關錯誤"""
    pass

class ValidationError(AccountingSystemError):
    """資料驗證錯誤"""
    pass

class BusinessLogicError(AccountingSystemError):
    """業務邏輯錯誤"""
    pass

class AuthenticationError(AccountingSystemError):
    """認證錯誤"""
    pass

class PermissionError(AccountingSystemError):
    """權限錯誤"""
    pass

def handle_database_error(func):
    """資料庫操作錯誤處理裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except DatabaseError as e:
            logger.error(f"資料庫錯誤 in {func.__name__}: {e.message}", extra={
                'error_code': e.error_code,
                'details': e.details,
                'function': func.__name__,
                'args': str(args),
                'kwargs': str(kwargs)
            })
            return _handle_error_response(e.message, 500)
        except Exception as e:
            logger.error(f"未預期的資料庫錯誤 in {func.__name__}: {str(e)}", extra={
                'traceback': traceback.format_exc(),
                'function': func.__name__
            })
            return _handle_error_response('資料庫操作失敗，請稍後再試', 500)
    
    return wrapper

def handle_validation_error(func):
    """表單驗證錯誤處理裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValidationError as e:
            logger.warning(f"驗證錯誤 in {func.__name__}: {e.message}", extra={
                'error_code': e.error_code,
                'details': e.details,
                'function': func.__name__
            })
            return _handle_error_response(f'資料驗證失敗：{e.message}', 400)
        except ValueError as e:
            logger.warning(f"值錯誤 in {func.__name__}: {str(e)}")
            return _handle_error_response(f'資料格式錯誤：{str(e)}', 400)
        except Exception as e:
            logger.error(f"未預期錯誤 in {func.__name__}: {str(e)}", extra={
                'traceback': traceback.format_exc(),
                'function': func.__name__
            })
            return _handle_error_response('系統錯誤，請聯繫管理員', 500)
    
    return wrapper

def handle_business_logic_error(func):
    """業務邏輯錯誤處理裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except BusinessLogicError as e:
            logger.warning(f"業務邏輯錯誤 in {func.__name__}: {e.message}", extra={
                'error_code': e.error_code,
                'details': e.details,
                'function': func.__name__
            })
            return _handle_error_response(e.message, 422)
        except Exception as e:
            logger.error(f"未預期的業務邏輯錯誤 in {func.__name__}: {str(e)}", extra={
                'traceback': traceback.format_exc(),
                'function': func.__name__
            })
            return _handle_error_response('業務處理失敗，請稍後再試', 500)
    
    return wrapper

def handle_authentication_error(func):
    """認證錯誤處理裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except AuthenticationError as e:
            logger.warning(f"認證錯誤 in {func.__name__}: {e.message}", extra={
                'error_code': e.error_code,
                'details': e.details,
                'function': func.__name__,
                'remote_addr': request.remote_addr
            })
            return _handle_error_response(e.message, 401)
        except PermissionError as e:
            logger.warning(f"權限錯誤 in {func.__name__}: {e.message}", extra={
                'error_code': e.error_code,
                'details': e.details,
                'function': func.__name__,
                'remote_addr': request.remote_addr
            })
            return _handle_error_response(e.message, 403)
        except Exception as e:
            logger.error(f"未預期的認證錯誤 in {func.__name__}: {str(e)}", extra={
                'traceback': traceback.format_exc(),
                'function': func.__name__
            })
            return _handle_error_response('認證失敗，請重新登入', 401)
    
    return wrapper

def _handle_error_response(message, status_code):
    """統一處理錯誤響應"""
    if request.is_json or request.path.startswith('/api/'):
        return jsonify({
            'success': False,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }), status_code
    else:
        flash(message, 'error')
        return redirect(request.referrer or '/')

class ErrorHandler:
    """錯誤處理工具類"""
    
    @staticmethod
    def log_and_flash(error_msg, error_type='error', log_level='error'):
        """記錄日誌並顯示 flash 訊息"""
        if log_level == 'error':
            logger.error(error_msg)
        elif log_level == 'warning':
            logger.warning(error_msg)
        else:
            logger.info(error_msg)
        
        flash(error_msg, error_type)
    
    @staticmethod
    def handle_form_errors(form):
        """處理 WTForms 表單錯誤"""
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')
    
    @staticmethod
    def safe_int_convert(value, default=0):
        """安全的整數轉換"""
        try:
            if value is None or str(value).strip() == '':
                return default
            return int(value)
        except (ValueError, TypeError) as e:
            logger.warning(f"整數轉換失敗: {value} -> {default}, 錯誤: {str(e)}")
            return default
    
    @staticmethod
    def safe_float_convert(value, default=0.0):
        """安全的浮點數轉換"""
        try:
            if value is None or str(value).strip() == '':
                return default
            return float(value)
        except (ValueError, TypeError) as e:
            logger.warning(f"浮點數轉換失敗: {value} -> {default}, 錯誤: {str(e)}")
            return default
    
    @staticmethod
    def safe_date_convert(date_str, default=None):
        """安全的日期轉換"""
        try:
            if not date_str or str(date_str).strip() == '':
                return default
            from datetime import datetime
            return datetime.strptime(date_str, '%Y-%m-%d').date()
        except (ValueError, TypeError) as e:
            logger.warning(f"日期轉換失敗: {date_str} -> {default}, 錯誤: {str(e)}")
            return default
    
    @staticmethod
    def validate_required_fields(data, required_fields):
        """驗證必填欄位"""
        missing_fields = []
        for field in required_fields:
            if field not in data or not data[field] or str(data[field]).strip() == '':
                missing_fields.append(field)
        
        if missing_fields:
            raise ValidationError(
                f"缺少必填欄位: {', '.join(missing_fields)}",
                error_code='MISSING_REQUIRED_FIELDS',
                details={'missing_fields': missing_fields}
            )
    
    @staticmethod
    def validate_positive_number(value, field_name):
        """驗證正數"""
        try:
            num_value = float(value)
            if num_value <= 0:
                raise ValidationError(
                    f"{field_name} 必須為正數",
                    error_code='INVALID_POSITIVE_NUMBER',
                    details={'field': field_name, 'value': value}
                )
            return num_value
        except (ValueError, TypeError):
            raise ValidationError(
                f"{field_name} 必須為有效數字",
                error_code='INVALID_NUMBER_FORMAT',
                details={'field': field_name, 'value': value}
            )
    
    @staticmethod
    def log_error_with_context(error, context=None):
        """記錄帶有上下文的錯誤"""
        error_info = {
            'error_message': str(error),
            'error_type': type(error).__name__,
            'timestamp': datetime.now().isoformat(),
            'request_url': request.url if request else None,
            'request_method': request.method if request else None,
            'remote_addr': request.remote_addr if request else None,
            'user_agent': request.headers.get('User-Agent') if request else None,
            'context': context or {}
        }
        
        if hasattr(error, '__traceback__'):
            error_info['traceback'] = traceback.format_exception(
                type(error), error, error.__traceback__
            )
        
        logger.error("詳細錯誤資訊", extra=error_info)

class ErrorReporter:
    """錯誤報告工具"""
    
    @staticmethod
    def save_error_report(error, context=None):
        """保存錯誤報告到文件"""
        try:
            error_dir = 'logs/errors'
            if not os.path.exists(error_dir):
                os.makedirs(error_dir)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{error_dir}/error_{timestamp}.log"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"錯誤時間: {datetime.now().isoformat()}\n")
                f.write(f"錯誤類型: {type(error).__name__}\n")
                f.write(f"錯誤訊息: {str(error)}\n")
                f.write(f"請求URL: {request.url if request else 'N/A'}\n")
                f.write(f"請求方法: {request.method if request else 'N/A'}\n")
                f.write(f"來源IP: {request.remote_addr if request else 'N/A'}\n")
                f.write(f"上下文: {context or {}}\n")
                f.write("=" * 50 + "\n")
                f.write(traceback.format_exc())
                
        except Exception as e:
            logger.error(f"保存錯誤報告失敗: {str(e)}")

def setup_global_error_handlers(app):
    """設置全域錯誤處理器"""
    
    @app.errorhandler(DatabaseError)
    def handle_database_error(error):
        ErrorHandler.log_error_with_context(error)
        ErrorReporter.save_error_report(error)
        return _handle_error_response(error.message, 500)
    
    @app.errorhandler(ValidationError)
    def handle_validation_error(error):
        ErrorHandler.log_error_with_context(error)
        return _handle_error_response(error.message, 400)
    
    @app.errorhandler(BusinessLogicError)
    def handle_business_logic_error(error):
        ErrorHandler.log_error_with_context(error)
        return _handle_error_response(error.message, 422)
    
    @app.errorhandler(AuthenticationError)
    def handle_authentication_error(error):
        ErrorHandler.log_error_with_context(error)
        return _handle_error_response(error.message, 401)
    
    @app.errorhandler(PermissionError)
    def handle_permission_error(error):
        ErrorHandler.log_error_with_context(error)
        return _handle_error_response(error.message, 403)
    
    @app.errorhandler(404)
    def not_found_error(error):
        logger.warning(f"404 錯誤: {request.url}")
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'message': '找不到請求的資源',
                'error_code': 'NOT_FOUND'
            }), 404
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        logger.error(f"500 錯誤: {str(error)}", extra={
            'traceback': traceback.format_exc(),
            'request_url': request.url,
            'request_method': request.method
        })
        ErrorReporter.save_error_report(error)
        
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'message': '伺服器內部錯誤',
                'error_code': 'INTERNAL_SERVER_ERROR'
            }), 500
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        logger.warning(f"403 錯誤: {request.url} - IP: {request.remote_addr}")
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'message': '沒有權限訪問此資源',
                'error_code': 'FORBIDDEN'
            }), 403
        return render_template('errors/403.html'), 403