"""
錯誤監控和統計系統
"""
import logging
import json
import os
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import Dict, List, Any, Optional
from flask import request

logger = logging.getLogger(__name__)

class ErrorMonitor:
    """錯誤監控類"""
    
    def __init__(self):
        self.error_stats = defaultdict(int)
        self.error_details = []
        self.error_trends = defaultdict(list)
        self.max_details = 1000  # 最多保存1000條詳細錯誤記錄
        
    def record_error(self, error_type: str, error_message: str, context: Optional[Dict] = None):
        """記錄錯誤"""
        timestamp = datetime.now()
        
        # 統計錯誤類型
        self.error_stats[error_type] += 1
        
        # 記錄詳細錯誤信息
        error_detail = {
            'timestamp': timestamp.isoformat(),
            'error_type': error_type,
            'message': error_message,
            'context': context or {},
            'request_info': self._get_request_info() if request else {}
        }
        
        self.error_details.append(error_detail)
        
        # 保持詳細記錄數量在限制內
        if len(self.error_details) > self.max_details:
            self.error_details = self.error_details[-self.max_details:]
        
        # 記錄趨勢數據（按小時統計）
        hour_key = timestamp.strftime('%Y-%m-%d %H:00')
        self.error_trends[hour_key].append({
            'type': error_type,
            'timestamp': timestamp.isoformat()
        })
        
        # 保存到文件
        self._save_error_to_file(error_detail)
        
    def _get_request_info(self) -> Dict:
        """獲取請求信息"""
        try:
            return {
                'url': request.url,
                'method': request.method,
                'remote_addr': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', ''),
                'referrer': request.referrer or ''
            }
        except Exception:
            return {}
    
    def _save_error_to_file(self, error_detail: Dict):
        """保存錯誤到文件"""
        try:
            error_dir = 'logs/error_monitor'
            if not os.path.exists(error_dir):
                os.makedirs(error_dir)
            
            # 按日期分文件保存
            date_str = datetime.now().strftime('%Y-%m-%d')
            filename = f"{error_dir}/errors_{date_str}.jsonl"
            
            with open(filename, 'a', encoding='utf-8') as f:
                f.write(json.dumps(error_detail, ensure_ascii=False) + '\n')
                
        except Exception as e:
            logger.error(f"保存錯誤記錄失敗: {str(e)}")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """獲取錯誤摘要"""
        now = datetime.now()
        last_24h = now - timedelta(hours=24)
        last_hour = now - timedelta(hours=1)
        
        # 統計最近24小時和1小時的錯誤
        recent_24h_errors = [
            error for error in self.error_details
            if datetime.fromisoformat(error['timestamp']) > last_24h
        ]
        
        recent_1h_errors = [
            error for error in self.error_details
            if datetime.fromisoformat(error['timestamp']) > last_hour
        ]
        
        # 統計錯誤類型分布
        error_type_stats = Counter([error['error_type'] for error in recent_24h_errors])
        
        # 統計最常見的錯誤消息
        error_message_stats = Counter([error['message'] for error in recent_24h_errors])
        
        return {
            'total_errors': len(self.error_details),
            'errors_24h': len(recent_24h_errors),
            'errors_1h': len(recent_1h_errors),
            'error_rate_24h': len(recent_24h_errors) / 24 if recent_24h_errors else 0,
            'error_rate_1h': len(recent_1h_errors),
            'error_types': dict(error_type_stats.most_common(10)),
            'common_messages': dict(error_message_stats.most_common(5)),
            'recent_errors': self.error_details[-10:] if self.error_details else []
        }
    
    def get_error_trends(self, hours: int = 24) -> Dict[str, Any]:
        """獲取錯誤趨勢數據"""
        now = datetime.now()
        start_time = now - timedelta(hours=hours)
        
        # 生成時間序列
        time_series = []
        current_time = start_time.replace(minute=0, second=0, microsecond=0)
        
        while current_time <= now:
            hour_key = current_time.strftime('%Y-%m-%d %H:00')
            error_count = len(self.error_trends.get(hour_key, []))
            
            time_series.append({
                'time': hour_key,
                'count': error_count,
                'timestamp': current_time.isoformat()
            })
            
            current_time += timedelta(hours=1)
        
        return {
            'time_series': time_series,
            'total_hours': hours,
            'max_errors_per_hour': max([item['count'] for item in time_series]) if time_series else 0
        }
    
    def get_error_details_by_type(self, error_type: str, limit: int = 50) -> List[Dict]:
        """根據錯誤類型獲取詳細信息"""
        filtered_errors = [
            error for error in self.error_details
            if error['error_type'] == error_type
        ]
        
        return filtered_errors[-limit:] if filtered_errors else []
    
    def clear_old_errors(self, days: int = 7):
        """清理舊的錯誤記錄"""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        # 清理內存中的錯誤記錄
        self.error_details = [
            error for error in self.error_details
            if datetime.fromisoformat(error['timestamp']) > cutoff_time
        ]
        
        # 清理趨勢數據
        cutoff_hour = cutoff_time.strftime('%Y-%m-%d %H:00')
        keys_to_remove = [
            key for key in self.error_trends.keys()
            if key < cutoff_hour
        ]
        
        for key in keys_to_remove:
            del self.error_trends[key]
        
        logger.info(f"清理了 {days} 天前的錯誤記錄")
    
    def export_error_report(self, start_date: Optional[str] = None, end_date: Optional[str] = None) -> Dict[str, Any]:
        """導出錯誤報告"""
        if start_date:
            start_dt = datetime.fromisoformat(start_date)
        else:
            start_dt = datetime.now() - timedelta(days=7)
        
        if end_date:
            end_dt = datetime.fromisoformat(end_date)
        else:
            end_dt = datetime.now()
        
        # 篩選時間範圍內的錯誤
        filtered_errors = [
            error for error in self.error_details
            if start_dt <= datetime.fromisoformat(error['timestamp']) <= end_dt
        ]
        
        # 統計分析
        error_types = Counter([error['error_type'] for error in filtered_errors])
        error_messages = Counter([error['message'] for error in filtered_errors])
        
        # 按日統計
        daily_stats = defaultdict(int)
        for error in filtered_errors:
            date_key = datetime.fromisoformat(error['timestamp']).strftime('%Y-%m-%d')
            daily_stats[date_key] += 1
        
        return {
            'report_period': {
                'start': start_dt.isoformat(),
                'end': end_dt.isoformat()
            },
            'summary': {
                'total_errors': len(filtered_errors),
                'unique_error_types': len(error_types),
                'avg_errors_per_day': len(filtered_errors) / max(1, (end_dt - start_dt).days)
            },
            'error_types': dict(error_types.most_common()),
            'common_messages': dict(error_messages.most_common(10)),
            'daily_statistics': dict(daily_stats),
            'detailed_errors': filtered_errors
        }

# 全域錯誤監控實例
error_monitor = ErrorMonitor()

def setup_error_monitoring(app):
    """設置錯誤監控"""
    
    @app.before_request
    def before_request_monitoring():
        """請求前的監控設置"""
        pass
    
    @app.after_request
    def after_request_monitoring(response):
        """請求後的監控"""
        # 記錄HTTP錯誤
        if response.status_code >= 400:
            error_monitor.record_error(
                error_type=f'HTTP_{response.status_code}',
                error_message=f'HTTP {response.status_code} 錯誤',
                context={
                    'status_code': response.status_code,
                    'content_length': response.content_length
                }
            )
        
        return response
    
    # 定期清理舊錯誤記錄
    import threading
    import time
    
    def cleanup_old_errors():
        """定期清理舊錯誤記錄"""
        while True:
            try:
                time.sleep(3600)  # 每小時執行一次
                error_monitor.clear_old_errors(days=7)
            except Exception as e:
                logger.error(f"清理錯誤記錄失敗: {str(e)}")
    
    cleanup_thread = threading.Thread(target=cleanup_old_errors, daemon=True)
    cleanup_thread.start()
    
    logger.info("錯誤監控系統已啟動")

# 錯誤記錄裝飾器
def monitor_errors(error_type: Optional[str] = None):
    """錯誤監控裝飾器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_monitor.record_error(
                    error_type=error_type or type(e).__name__,
                    error_message=str(e),
                    context={
                        'function': func.__name__,
                        'args': str(args),
                        'kwargs': str(kwargs)
                    }
                )
                raise
        return wrapper
    return decorator