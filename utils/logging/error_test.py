"""
錯誤處理測試工具
"""
import logging
from flask import Blueprint, jsonify
from utils.logging.error_handler import (
    DatabaseError, ValidationError, BusinessLogicError, 
    AuthenticationError, PermissionError, ErrorHandler
)
from utils.logging.error_monitor import error_monitor

logger = logging.getLogger(__name__)

error_test_bp = Blueprint('error_test', __name__, url_prefix='/admin/error-test')

@error_test_bp.route('/database-error')
def test_database_error():
    """測試資料庫錯誤"""
    raise DatabaseError(
        "測試資料庫連接失敗", 
        error_code='TEST_DB_ERROR',
        details={'test_type': 'database_connection'}
    )

@error_test_bp.route('/validation-error')
def test_validation_error():
    """測試驗證錯誤"""
    raise ValidationError(
        "測試資料驗證失敗", 
        error_code='TEST_VALIDATION_ERROR',
        details={'field': 'test_field', 'value': 'invalid_value'}
    )

@error_test_bp.route('/business-logic-error')
def test_business_logic_error():
    """測試業務邏輯錯誤"""
    raise BusinessLogicError(
        "測試業務邏輯錯誤", 
        error_code='TEST_BUSINESS_ERROR',
        details={'operation': 'test_operation'}
    )

@error_test_bp.route('/authentication-error')
def test_authentication_error():
    """測試認證錯誤"""
    raise AuthenticationError(
        "測試認證失敗", 
        error_code='TEST_AUTH_ERROR',
        details={'user': 'test_user'}
    )

@error_test_bp.route('/permission-error')
def test_permission_error():
    """測試權限錯誤"""
    raise PermissionError(
        "測試權限不足", 
        error_code='TEST_PERMISSION_ERROR',
        details={'resource': 'test_resource'}
    )

@error_test_bp.route('/general-exception')
def test_general_exception():
    """測試一般異常"""
    raise Exception("測試一般異常處理")

@error_test_bp.route('/safe-conversion')
def test_safe_conversion():
    """測試安全轉換功能"""
    test_data = {
        'int_valid': '123',
        'int_invalid': 'abc',
        'int_empty': '',
        'float_valid': '123.45',
        'float_invalid': 'xyz',
        'date_valid': '2024-01-15',
        'date_invalid': 'invalid-date'
    }
    
    results = {
        'int_valid': ErrorHandler.safe_int_convert(test_data['int_valid']),
        'int_invalid': ErrorHandler.safe_int_convert(test_data['int_invalid']),
        'int_empty': ErrorHandler.safe_int_convert(test_data['int_empty']),
        'float_valid': ErrorHandler.safe_float_convert(test_data['float_valid']),
        'float_invalid': ErrorHandler.safe_float_convert(test_data['float_invalid']),
        'date_valid': ErrorHandler.safe_date_convert(test_data['date_valid']),
        'date_invalid': ErrorHandler.safe_date_convert(test_data['date_invalid'])
    }
    
    return jsonify({
        'success': True,
        'test_data': test_data,
        'results': results,
        'message': '安全轉換測試完成'
    })

@error_test_bp.route('/validation-test')
def test_validation():
    """測試驗證功能"""
    try:
        # 測試必填欄位驗證
        test_data = {
            'name': 'test',
            'email': '',  # 缺少這個欄位
            'age': '25'
        }
        
        required_fields = ['name', 'email', 'phone']
        ErrorHandler.validate_required_fields(test_data, required_fields)
        
        return jsonify({
            'success': True,
            'message': '驗證測試通過'
        })
        
    except ValidationError as e:
        return jsonify({
            'success': False,
            'error_type': 'ValidationError',
            'message': e.message,
            'error_code': e.error_code,
            'details': e.details
        })

@error_test_bp.route('/positive-number-test')
def test_positive_number():
    """測試正數驗證"""
    test_values = [
        ('100', True),
        ('-50', False),
        ('0', False),
        ('abc', False),
        ('', False)
    ]
    
    results = []
    for value, should_pass in test_values:
        try:
            result = ErrorHandler.validate_positive_number(value, '測試欄位')
            results.append({
                'value': value,
                'result': result,
                'passed': True,
                'expected': should_pass
            })
        except ValidationError as e:
            results.append({
                'value': value,
                'error': e.message,
                'passed': False,
                'expected': should_pass
            })
    
    return jsonify({
        'success': True,
        'test_results': results,
        'message': '正數驗證測試完成'
    })

@error_test_bp.route('/monitor-test')
def test_error_monitor():
    """測試錯誤監控功能"""
    # 記錄一些測試錯誤
    test_errors = [
        ('DatabaseError', '測試資料庫錯誤'),
        ('ValidationError', '測試驗證錯誤'),
        ('BusinessLogicError', '測試業務邏輯錯誤'),
        ('SystemError', '測試系統錯誤')
    ]
    
    from datetime import datetime

    for error_type, message in test_errors:
        error_monitor.record_error(
            error_type=error_type,
            error_message=message,
            context={'test': True, 'timestamp': str(datetime.now())}
        )
    
    # 獲取監控摘要
    summary = error_monitor.get_error_summary()
    
    return jsonify({
        'success': True,
        'message': '錯誤監控測試完成',
        'recorded_errors': len(test_errors),
        'monitor_summary': summary
    })

@error_test_bp.route('/stress-test')
def stress_test():
    """壓力測試 - 生成大量錯誤"""
    import random
    
    error_types = ['DatabaseError', 'ValidationError', 'BusinessLogicError', 'SystemError']
    error_messages = [
        '連接超時',
        '資料格式錯誤',
        '業務規則違反',
        '系統資源不足',
        '網路連接失敗'
    ]
    
    # 生成100個隨機錯誤
    for i in range(100):
        error_type = random.choice(error_types)
        message = random.choice(error_messages)
        
        error_monitor.record_error(
            error_type=error_type,
            error_message=f"{message} (測試 #{i+1})",
            context={
                'test': True,
                'stress_test': True,
                'iteration': i+1
            }
        )
    
    return jsonify({
        'success': True,
        'message': '壓力測試完成',
        'generated_errors': 100
    })

@error_test_bp.route('/dashboard')
def test_dashboard():
    """錯誤測試儀表板"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-Hant">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>錯誤處理測試儀表板</title>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
        <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    </head>
    <body>
        <div class="container" style="padding: 2rem;">
            <h1 class="title">錯誤處理測試儀表板</h1>
            
            <div class="columns is-multiline">
                <div class="column is-6">
                    <div class="card">
                        <div class="card-header">
                            <p class="card-header-title">異常類型測試</p>
                        </div>
                        <div class="card-content">
                            <div class="buttons">
                                <button class="button is-danger" onclick="testError('database-error')">資料庫錯誤</button>
                                <button class="button is-warning" onclick="testError('validation-error')">驗證錯誤</button>
                                <button class="button is-info" onclick="testError('business-logic-error')">業務邏輯錯誤</button>
                                <button class="button is-primary" onclick="testError('authentication-error')">認證錯誤</button>
                                <button class="button is-link" onclick="testError('permission-error')">權限錯誤</button>
                                <button class="button is-dark" onclick="testError('general-exception')">一般異常</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="column is-6">
                    <div class="card">
                        <div class="card-header">
                            <p class="card-header-title">功能測試</p>
                        </div>
                        <div class="card-content">
                            <div class="buttons">
                                <button class="button is-success" onclick="testFunction('safe-conversion')">安全轉換</button>
                                <button class="button is-success" onclick="testFunction('validation-test')">驗證功能</button>
                                <button class="button is-success" onclick="testFunction('positive-number-test')">正數驗證</button>
                                <button class="button is-success" onclick="testFunction('monitor-test')">錯誤監控</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="column is-12">
                    <div class="card">
                        <div class="card-header">
                            <p class="card-header-title">壓力測試</p>
                        </div>
                        <div class="card-content">
                            <button class="button is-warning is-large" onclick="stressTest()">
                                <span class="icon">
                                    <i class="fas fa-bolt"></i>
                                </span>
                                <span>執行壓力測試</span>
                            </button>
                            <p class="help">生成100個隨機錯誤來測試系統處理能力</p>
                        </div>
                    </div>
                </div>
                
                <div class="column is-12">
                    <div class="card">
                        <div class="card-header">
                            <p class="card-header-title">測試結果</p>
                        </div>
                        <div class="card-content">
                            <div id="testResults">
                                <p class="has-text-grey">點擊上方按鈕開始測試...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="level" style="margin-top: 2rem;">
                <div class="level-left">
                    <div class="level-item">
                        <a href="/admin/errors/dashboard" class="button is-primary">
                            <span class="icon">
                                <i class="fas fa-chart-line"></i>
                            </span>
                            <span>查看錯誤監控</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <script>
            async function testError(errorType) {
                const resultsDiv = document.getElementById('testResults');
                resultsDiv.innerHTML = '<div class="notification is-info">測試中...</div>';
                
                try {
                    const response = await fetch(`/admin/error-test/${errorType}`);
                    const data = await response.text();
                    
                    resultsDiv.innerHTML = `
                        <div class="notification is-danger">
                            <strong>錯誤測試結果 (${errorType}):</strong><br>
                            狀態碼: ${response.status}<br>
                            <pre style="margin-top: 1rem;">${data}</pre>
                        </div>
                    `;
                } catch (error) {
                    resultsDiv.innerHTML = `
                        <div class="notification is-danger">
                            <strong>測試失敗:</strong><br>
                            ${error.message}
                        </div>
                    `;
                }
            }
            
            async function testFunction(functionName) {
                const resultsDiv = document.getElementById('testResults');
                resultsDiv.innerHTML = '<div class="notification is-info">測試中...</div>';
                
                try {
                    const response = await fetch(`/admin/error-test/${functionName}`);
                    const data = await response.json();
                    
                    resultsDiv.innerHTML = `
                        <div class="notification ${data.success ? 'is-success' : 'is-warning'}">
                            <strong>功能測試結果 (${functionName}):</strong><br>
                            ${data.message}<br>
                            <pre style="margin-top: 1rem;">${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } catch (error) {
                    resultsDiv.innerHTML = `
                        <div class="notification is-danger">
                            <strong>測試失敗:</strong><br>
                            ${error.message}
                        </div>
                    `;
                }
            }
            
            async function stressTest() {
                const resultsDiv = document.getElementById('testResults');
                resultsDiv.innerHTML = '<div class="notification is-warning">執行壓力測試中，請稍候...</div>';
                
                try {
                    const response = await fetch('/admin/error-test/stress-test');
                    const data = await response.json();
                    
                    resultsDiv.innerHTML = `
                        <div class="notification is-success">
                            <strong>壓力測試完成:</strong><br>
                            ${data.message}<br>
                            生成錯誤數: ${data.generated_errors}
                        </div>
                    `;
                } catch (error) {
                    resultsDiv.innerHTML = `
                        <div class="notification is-danger">
                            <strong>壓力測試失敗:</strong><br>
                            ${error.message}
                        </div>
                    `;
                }
            }
        </script>
    </body>
    </html>
    '''