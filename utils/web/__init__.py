"""
Web 介面相關工具模組
提供 API 回應、視圖基類、裝飾器和除錯等 Web 開發功能
"""

# API 回應系統
from .api_response import (
    APIResponse, APIStatus, APIErrorCode, APIValidator,
    api_route, require_pagination, api_response,
    success_response, error_response, paginated_response
)

# 基礎視圖
from .base_view import BaseView

# 裝飾器
from .menu_decorator import with_menu, render_with_menu_data

# 除錯工具
from .debug_helpers import is_debug_mode, debug_log, debug_only

__all__ = [
    # API 響應系統
    'APIResponse',
    'APIStatus', 
    'APIErrorCode',
    'APIValidator',
    'api_route',
    'require_pagination',
    'api_response',
    'success_response',
    'error_response', 
    'paginated_response',
    
    # 基礎類別
    'BaseView',
    
    # 裝飾器
    'with_menu',
    'render_with_menu_data',
    
    # 除錯工具
    'is_debug_mode',
    'debug_log',
    'debug_only'
]