"""
選單裝飾器 - 消除重複的選單初始化代碼
"""
from functools import wraps
from flask import render_template, g
from data.menu_data import menu

def with_menu(selected_menu=None):
    """
    選單裝飾器 - 自動注入選單資料到模板上下文
    
    Args:
        selected_menu (str): 選中的選單項目，如果為 None 則從函數名推斷
    
    Usage:
        @with_menu('資金管理')
        def some_view():
            return render_template('template.html', other_data=data)
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 設置選單資料到 Flask 的 g 對象中，供模板使用
            g.sidebar_items = list(menu.keys())
            g.selected = selected_menu or _infer_menu_from_function_name(func.__name__)
            g.submenus = menu.get(g.selected, {})
            
            # 執行原始函數
            return func(*args, **kwargs)
        return wrapper
    return decorator

def render_with_menu_data(template_name, **context):
    """
    渲染模板並自動注入選單資料
    
    Args:
        template_name (str): 模板名稱
        **context: 模板上下文資料
        
    Returns:
        渲染後的模板
    """
    # 從 g 對象中獲取選單資料，如果沒有則使用預設值
    menu_context = {
        'sidebar_items': getattr(g, 'sidebar_items', list(menu.keys())),
        'selected': getattr(g, 'selected', '首頁'),
        'submenus': getattr(g, 'submenus', {})
    }
    
    # 合併選單資料和其他上下文
    full_context = {**menu_context, **context}
    
    return render_template(template_name, **full_context)

def _infer_menu_from_function_name(func_name):
    """從函數名推斷選單項目"""
    menu_mapping = {
        'fund_': '資金管理',
        'income_': '收支帳簿',
        'expense_': '收支帳簿',
        'employee_': '薪資報酬',
        'payroll_': '薪資報酬',
        'service_': '勞務報酬',
        'asset_': '資產管理',
        'account_': '設定',
        'department_': '設定',
        'project_': '設定',
        'company_': '薪資報酬',
        'salary_': '薪資報酬',
    }
    
    for prefix, menu_item in menu_mapping.items():
        if func_name.startswith(prefix):
            return menu_item
    
    return '首頁'  # 預設值

def get_menu_context(selected=None):
    """
    獲取選單上下文的工具函數
    
    Args:
        selected (str): 選中的選單項目
        
    Returns:
        dict: 包含選單資料的字典
    """
    return {
        'sidebar_items': list(menu.keys()),
        'selected': selected,
        'submenus': menu.get(selected, {}) if selected else {}
    }