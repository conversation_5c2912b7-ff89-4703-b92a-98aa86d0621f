"""
統一的調試輔助工具
集中管理所有 DEBUG 相關的功能
"""

import os
import logging
from flask import current_app
from typing import Any, Dict, Optional
from functools import wraps


def is_debug_mode() -> bool:
    """
    檢查是否為調試模式
    
    Returns:
        bool: 是否為調試模式
    """
    try:
        return current_app.config.get('DEBUG', False)
    except RuntimeError:
        # 在應用程式上下文之外，從環境變數獲取
        return os.environ.get('FLASK_DEBUG', 'False').lower() in ['true', '1', 'yes']


def debug_log(message: str, data: Any = None) -> None:
    """
    記錄調試訊息（僅在調試模式下）
    
    Args:
        message: 調試訊息
        data: 額外的調試資料
    """
    if is_debug_mode():
        try:
            logger = current_app.logger
            if data is not None:
                logger.debug(f"{message}: {data}")
            else:
                logger.debug(message)
        except RuntimeError:
            # 如果在應用程式上下文之外，使用標準 logging
            if data is not None:
                logging.debug(f"{message}: {data}")
            else:
                logging.debug(message)


def debug_info(message: str, data: Any = None) -> None:
    """
    記錄調試資訊（僅在調試模式下，使用 info 級別）
    
    Args:
        message: 調試訊息
        data: 額外的調試資料
    """
    if is_debug_mode():
        try:
            logger = current_app.logger
            if data is not None:
                logger.info(f"[DEBUG] {message}: {data}")
            else:
                logger.info(f"[DEBUG] {message}")
        except RuntimeError:
            if data is not None:
                logging.info(f"[DEBUG] {message}: {data}")
            else:
                logging.info(f"[DEBUG] {message}")


def debug_only(func):
    """
    裝飾器：僅在調試模式下執行函數
    
    Args:
        func: 要裝飾的函數
        
    Returns:
        裝飾後的函數
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        if is_debug_mode():
            return func(*args, **kwargs)
        return None
    return wrapper


def get_debug_config() -> Dict[str, Any]:
    """
    獲取調試相關的配置資訊
    
    Returns:
        調試配置字典
    """
    try:
        app_config = current_app.config
    except RuntimeError:
        app_config = {}
    
    return {
        'debug_mode': is_debug_mode(),
        'flask_debug': app_config.get('DEBUG', False),
        'flask_env': os.environ.get('FLASK_ENV', 'production'),
        'log_level': logging.getLevelName(logging.getLogger().level),
        'environment_debug': os.environ.get('FLASK_DEBUG', 'False')
    }


def safe_debug_operation(operation, error_message: str = "調試操作失敗"):
    """
    安全執行調試操作（僅在調試模式下，且不會影響正常功能）
    
    Args:
        operation: 要執行的調試操作
        error_message: 錯誤訊息
        
    Returns:
        操作結果或 None
    """
    if not is_debug_mode():
        return None
    
    try:
        return operation()
    except Exception as e:
        debug_log(f"{error_message}: {str(e)}")
        return None


def format_debug_data(data: Dict[str, Any], title: str = "調試資料") -> str:
    """
    格式化調試資料用於日誌輸出
    
    Args:
        data: 要格式化的資料
        title: 資料標題
        
    Returns:
        格式化後的字符串
    """
    if not is_debug_mode():
        return ""
    
    try:
        import json
        formatted_data = json.dumps(data, ensure_ascii=False, indent=2)
        return f"{title}:\n{formatted_data}"
    except Exception:
        return f"{title}: {str(data)}"


class DebugContext:
    """調試上下文管理器"""
    
    def __init__(self, operation_name: str, log_data: bool = True):
        self.operation_name = operation_name
        self.log_data = log_data
        self.start_time = None
    
    def __enter__(self):
        if is_debug_mode():
            import time
            self.start_time = time.time()
            debug_log(f"開始執行: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if is_debug_mode() and self.start_time:
            import time
            elapsed_time = time.time() - self.start_time
            if exc_type is None:
                debug_log(f"完成執行: {self.operation_name} (耗時: {elapsed_time:.3f}秒)")
            else:
                debug_log(f"執行失敗: {self.operation_name} (耗時: {elapsed_time:.3f}秒, 錯誤: {exc_val})")
    
    def log(self, message: str, data: Any = None):
        """在上下文中記錄調試訊息"""
        if is_debug_mode():
            debug_log(f"[{self.operation_name}] {message}", data)


def get_development_config() -> Optional[Dict[str, Any]]:
    """
    獲取開發環境專用配置（僅在調試模式下）
    
    Returns:
        開發配置字典或 None
    """
    if not is_debug_mode():
        return None
    
    return {
        'admin_password': os.environ.get('ADMIN_PASSWORD', '0980347570'),
        'bypass_auth': os.environ.get('DEBUG_BYPASS_AUTH', 'False').lower() == 'true',
        'show_sql_queries': os.environ.get('DEBUG_SQL_QUERIES', 'False').lower() == 'true',
        'detailed_errors': True,
        'auto_reload': True
    }


# 向後兼容的函數別名
debug_print = debug_log
is_development = is_debug_mode