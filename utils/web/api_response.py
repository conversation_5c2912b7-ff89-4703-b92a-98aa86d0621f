"""
API 響應標準化工具
統一 API 響應格式，提升前後端協作效率

標準響應格式：
{
    "success": bool,          # 操作成功標識
    "message": str,           # 提示訊息
    "data": any,             # 響應資料
    "timestamp": str,        # ISO 格式時間戳
    "error": {               # 錯誤詳情（僅錯誤時）
        "code": str,         # 錯誤代碼
        "details": dict      # 詳細資訊
    },
    "meta": {               # 元資料（分頁等）
        "pagination": {...}, # 分頁資訊
        "performance": {...} # 性能資訊
    }
}
"""

from flask import jsonify, Response, request
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
import logging
import time
from functools import wraps

logger = logging.getLogger(__name__)


class APIResponse:
    """標準化 API 響應類"""

    @staticmethod
    def success(data: Any = None, message: str = "操作成功", meta: Optional[Dict] = None) -> Response:
        """成功響應"""
        response = {
            "success": True,
            "message": message,
            "data": data,
            "timestamp": datetime.now().isoformat(),
            "error": None,
        }

        if meta:
            response["meta"] = meta

        return jsonify(response)

    @staticmethod
    def error(
        message: str = "操作失敗",
        error_code: Optional[str] = None,
        details: Optional[Dict] = None,
        status_code: int = 400,
    ) -> tuple[Response, int]:
        """錯誤響應"""
        response = {
            "success": False,
            "message": message,
            "data": None,
            "timestamp": datetime.now().isoformat(),
            "error": {"code": error_code, "details": details or {}},
        }

        return jsonify(response), status_code

    @staticmethod
    def paginated(
        data: List, page: int, per_page: int, total: int, message: str = "查詢成功"
    ) -> Response:
        """分頁響應"""
        total_pages = (total + per_page - 1) // per_page

        meta = {
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1,
            }
        }

        return APIResponse.success(data=data, message=message, meta=meta)

    @staticmethod
    def created(data: Any = None, message: str = "創建成功") -> tuple[Response, int]:
        """創建成功響應"""
        response = APIResponse.success(data=data, message=message)
        return response, 201

    @staticmethod
    def no_content(message: str = "操作成功") -> tuple[Response, int]:
        """無內容響應"""
        response = {
            "success": True,
            "message": message,
            "timestamp": datetime.now().isoformat(),
        }
        return jsonify(response), 204

    @staticmethod
    def validation_error(errors: Dict, message: str = "資料驗證失敗") -> tuple[Response, int]:
        """驗證錯誤響應"""
        return APIResponse.error(
            message=message,
            error_code="VALIDATION_ERROR",
            details={"validation_errors": errors},
            status_code=422,
        )

    @staticmethod
    def not_found(resource: str = "資源", message: Optional[str] = None) -> tuple:
        """找不到資源響應"""
        if not message:
            message = f"找不到指定的{resource}"

        return APIResponse.error(
            message=message, error_code="NOT_FOUND", status_code=404
        )

    @staticmethod
    def unauthorized(message: str = "未授權訪問") -> tuple:
        """未授權響應"""
        return APIResponse.error(
            message=message, error_code="UNAUTHORIZED", status_code=401
        )

    @staticmethod
    def forbidden(message: str = "權限不足") -> tuple:
        """禁止訪問響應"""
        return APIResponse.error(
            message=message, error_code="FORBIDDEN", status_code=403
        )


def api_response(func):
    """API 響應裝飾器 - 自動處理異常並返回標準格式"""
    from functools import wraps
    from utils.logging.error_handler import (
        ValidationError,
        DatabaseError,
        BusinessLogicError,
        AuthenticationError,
        PermissionError,
    )

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)

            # 如果函數已經返回了 Flask Response 對象，直接返回
            if hasattr(result, "status_code"):
                return result

            # 如果返回的是元組（通常是 jsonify 結果和狀態碼）
            if isinstance(result, tuple):
                return result

            # 否則包裝為成功響應
            return APIResponse.success(data=result)

        except ValidationError as e:
            logger.warning(f"API 驗證錯誤: {str(e)}")
            return APIResponse.validation_error(
                errors={e.error_code: e.message}, message=e.message
            )

        except DatabaseError as e:
            logger.error(f"API 資料庫錯誤: {str(e)}")
            return APIResponse.error(
                message="資料庫操作失敗", error_code=e.error_code, status_code=500
            )

        except BusinessLogicError as e:
            logger.warning(f"API 業務邏輯錯誤: {str(e)}")
            return APIResponse.error(
                message=e.message, error_code=e.error_code, status_code=422
            )

        except AuthenticationError as e:
            logger.warning(f"API 認證錯誤: {str(e)}")
            return APIResponse.unauthorized(message=e.message)

        except PermissionError as e:
            logger.warning(f"API 權限錯誤: {str(e)}")
            return APIResponse.forbidden(message=e.message)

        except Exception as e:
            logger.error(f"API 未預期錯誤: {str(e)}")
            return APIResponse.error(
                message="系統內部錯誤", error_code="INTERNAL_ERROR", status_code=500
            )

    return wrapper


class APIValidator:
    """API 參數驗證器"""

    @staticmethod
    def validate_pagination(page: Any, per_page: Any, max_per_page: int = 100) -> tuple:
        """驗證分頁參數"""
        from utils.logging.error_handler import ValidationError

        try:
            page = int(page) if page else 1
            per_page = int(per_page) if per_page else 20
        except (ValueError, TypeError):
            raise ValidationError("分頁參數必須為整數")

        if page < 1:
            raise ValidationError("頁碼必須大於0")

        if per_page < 1:
            raise ValidationError("每頁數量必須大於0")

        if per_page > max_per_page:
            raise ValidationError(f"每頁數量不能超過{max_per_page}")

        return page, per_page

    @staticmethod
    def validate_date_range(start_date: str, end_date: str) -> tuple:
        """驗證日期範圍"""
        from datetime import datetime
        from utils.logging.error_handler import ValidationError

        try:
            start = (
                datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
            )
            end = datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else None
        except ValueError:
            raise ValidationError("日期格式錯誤，請使用 YYYY-MM-DD 格式")

        if start and end and start > end:
            raise ValidationError("開始日期不能晚於結束日期")

        return start, end

    @staticmethod
    def validate_required_fields(data: Dict, required_fields: List[str]) -> None:
        """驗證必填欄位"""
        from utils.logging.error_handler import ValidationError

        missing_fields = []

        for field in required_fields:
            if (
                field not in data
                or data[field] is None
                or str(data[field]).strip() == ""
            ):
                missing_fields.append(field)

        if missing_fields:
            raise ValidationError(
                f"缺少必填欄位: {', '.join(missing_fields)}",
                error_code="MISSING_REQUIRED_FIELDS",
            )


# ============================================================================
# 標準化 API 狀態碼和錯誤代碼常數
# ============================================================================

class APIStatus:
    """標準化 API 狀態碼"""
    
    # 成功狀態
    OK = 200
    CREATED = 201
    ACCEPTED = 202
    NO_CONTENT = 204
    
    # 客戶端錯誤
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    METHOD_NOT_ALLOWED = 405
    CONFLICT = 409
    UNPROCESSABLE_ENTITY = 422
    TOO_MANY_REQUESTS = 429
    
    # 服務器錯誤
    INTERNAL_ERROR = 500
    NOT_IMPLEMENTED = 501
    SERVICE_UNAVAILABLE = 503


class APIErrorCode:
    """標準化錯誤代碼"""
    
    # 通用錯誤
    VALIDATION_ERROR = "VALIDATION_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    NOT_FOUND = "NOT_FOUND"
    CONFLICT = "CONFLICT"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    
    # 業務錯誤
    INSUFFICIENT_FUNDS = "INSUFFICIENT_FUNDS"
    DUPLICATE_ENTRY = "DUPLICATE_ENTRY"
    INVALID_OPERATION = "INVALID_OPERATION"
    DATA_INTEGRITY_ERROR = "DATA_INTEGRITY_ERROR"
    
    # 系統錯誤
    DATABASE_ERROR = "DATABASE_ERROR"
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR"
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"


# ============================================================================
# API 響應中間件和裝飾器
# ============================================================================

def api_route(methods=['GET'], require_json=False, rate_limit=None):
    """
    API 路由裝飾器，提供標準化功能
    
    Args:
        methods: 允許的 HTTP 方法
        require_json: 是否要求 JSON Content-Type
        rate_limit: 速率限制（每分鐘請求數）
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # 檢查 HTTP 方法
            if request.method not in methods:
                return APIResponse.error(
                    message="不支援的 HTTP 方法",
                    error_code=APIErrorCode.INVALID_OPERATION,
                    status_code=APIStatus.METHOD_NOT_ALLOWED
                )
            
            # 檢查 Content-Type（僅 POST/PUT/PATCH）
            if (require_json and 
                request.method in ['POST', 'PUT', 'PATCH'] and 
                not request.is_json):
                return APIResponse.error(
                    message="Content-Type 必須為 application/json",
                    error_code=APIErrorCode.VALIDATION_ERROR,
                    status_code=APIStatus.BAD_REQUEST
                )
            
            try:
                # 執行原函數
                result = func(*args, **kwargs)
                
                # 如果返回的是 APIResponse，添加性能資訊
                if isinstance(result, Response):
                    response_time = round((time.time() - start_time) * 1000, 2)
                    logger.debug(f"API {request.endpoint} 響應時間: {response_time}ms")
                
                return result
                
            except Exception as e:
                return api_exception_handler(e)
        
        return wrapper
    return decorator


def require_pagination(max_per_page=100):
    """分頁參數裝飾器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                page, per_page = APIValidator.validate_pagination(
                    request.args.get('page'),
                    request.args.get('per_page'),
                    max_per_page
                )
                kwargs['page'] = page
                kwargs['per_page'] = per_page
                return func(*args, **kwargs)
            except ValidationError as e:
                return APIResponse.validation_error({'pagination': str(e)})
        return wrapper
    return decorator


# ============================================================================
# 便捷函數
# ============================================================================

def success_response(data=None, message="操作成功", meta=None):
    """快捷成功響應函數"""
    return APIResponse.success(data=data, message=message, meta=meta)

def error_response(message="操作失敗", error_code=None, status_code=400):
    """快捷錯誤響應函數"""
    return APIResponse.error(message=message, error_code=error_code, status_code=status_code)

def paginated_response(data, page, per_page, total, message="查詢成功"):
    """快捷分頁響應函數"""
    return APIResponse.paginated(data=data, page=page, per_page=per_page, total=total, message=message)
