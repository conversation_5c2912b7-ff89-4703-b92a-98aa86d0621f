"""
進階快取策略
實現智能快取預熱、分層快取和依賴管理
"""

import time
import threading
import logging
from typing import Dict, Any, Set, List, Optional, Callable
from functools import wraps
from collections import defaultdict
import weakref
import gc
from .cache_manager import cache_manager, cache_result

logger = logging.getLogger(__name__)

class CacheDependencyManager:
    """
    快取依賴管理器
    管理快取之間的依賴關係，實現智能失效
    """
    
    def __init__(self):
        self._dependencies: Dict[str, Set[str]] = defaultdict(set)  # key -> dependent keys
        self._reverse_deps: Dict[str, Set[str]] = defaultdict(set)  # key -> keys it depends on
        self._lock = threading.RLock()
    
    def add_dependency(self, cache_key: str, depends_on: List[str]):
        """
        添加快取依賴關係
        
        Args:
            cache_key: 快取鍵
            depends_on: 依賴的快取鍵列表
        """
        with self._lock:
            for dep_key in depends_on:
                self._dependencies[dep_key].add(cache_key)
                self._reverse_deps[cache_key].add(dep_key)
                
            logger.debug(f"Added dependency: {cache_key} depends on {depends_on}")
    
    def invalidate_cascade(self, cache_key: str):
        """
        級聯失效快取
        
        Args:
            cache_key: 要失效的快取鍵
        """
        with self._lock:
            to_invalidate = {cache_key}
            processed = set()
            
            while to_invalidate:
                current_key = to_invalidate.pop()
                if current_key in processed:
                    continue
                    
                processed.add(current_key)
                
                # 失效當前快取
                cache_manager.invalidate(current_key)
                
                # 添加依賴此快取的其他快取
                dependents = self._dependencies.get(current_key, set())
                to_invalidate.update(dependents)
                
            logger.info(f"Cascade invalidated {len(processed)} cache entries starting from {cache_key}")
    
    def get_dependency_tree(self, cache_key: str) -> Dict[str, Any]:
        """
        獲取依賴樹
        
        Args:
            cache_key: 快取鍵
            
        Returns:
            依賴樹結構
        """
        with self._lock:
            return {
                'key': cache_key,
                'depends_on': list(self._reverse_deps.get(cache_key, set())),
                'dependents': list(self._dependencies.get(cache_key, set()))
            }

class TieredCache:
    """
    分層快取系統
    L1: 記憶體快取（最快）
    L2: 本地檔案快取（中等）
    L3: 資料庫快取（最慢）
    """
    
    def __init__(self, 
                 l1_size: int = 1000, 
                 l2_size: int = 5000,
                 l3_enabled: bool = False):
        self.l1_cache = {}  # 記憶體快取
        self.l2_cache = {}  # 檔案快取（模擬）
        self.l3_enabled = l3_enabled
        
        self.l1_max_size = l1_size
        self.l2_max_size = l2_size
        
        self.stats = {
            'l1_hits': 0,
            'l2_hits': 0,
            'l3_hits': 0,
            'misses': 0,
            'promotions': 0,
            'evictions': 0
        }
        
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """
        分層獲取快取數據
        
        Args:
            key: 快取鍵
            
        Returns:
            快取值或 None
        """
        with self._lock:
            # L1 快取檢查
            if key in self.l1_cache:
                entry = self.l1_cache[key]
                if entry['expires_at'] > time.time():
                    self.stats['l1_hits'] += 1
                    return entry['value']
                else:
                    del self.l1_cache[key]
            
            # L2 快取檢查
            if key in self.l2_cache:
                entry = self.l2_cache[key]
                if entry['expires_at'] > time.time():
                    self.stats['l2_hits'] += 1
                    # 提升到 L1
                    self._promote_to_l1(key, entry)
                    return entry['value']
                else:
                    del self.l2_cache[key]
            
            # L3 快取檢查（資料庫）
            if self.l3_enabled:
                value = self._check_l3_cache(key)
                if value is not None:
                    self.stats['l3_hits'] += 1
                    # 提升到 L2
                    self._promote_to_l2(key, value, 3600)  # 1小時TTL
                    return value
            
            self.stats['misses'] += 1
            return None
    
    def set(self, key: str, value: Any, ttl: int = 300, tier: int = 1):
        """
        設置快取到指定層級
        
        Args:
            key: 快取鍵
            value: 快取值
            ttl: 存活時間
            tier: 快取層級 (1, 2, 3)
        """
        with self._lock:
            entry = {
                'value': value,
                'expires_at': time.time() + ttl,
                'created_at': time.time(),
                'access_count': 0
            }
            
            if tier == 1:
                self._set_l1(key, entry)
            elif tier == 2:
                self._set_l2(key, entry)
            elif tier == 3 and self.l3_enabled:
                self._set_l3(key, entry)
    
    def _promote_to_l1(self, key: str, entry: Dict[str, Any]):
        """提升到 L1 快取"""
        self._set_l1(key, entry)
        self.stats['promotions'] += 1
        logger.debug(f"Promoted {key} to L1 cache")
    
    def _promote_to_l2(self, key: str, value: Any, ttl: int):
        """提升到 L2 快取"""
        entry = {
            'value': value,
            'expires_at': time.time() + ttl,
            'created_at': time.time(),
            'access_count': 1
        }
        self._set_l2(key, entry)
        self.stats['promotions'] += 1
        logger.debug(f"Promoted {key} to L2 cache")
    
    def _set_l1(self, key: str, entry: Dict[str, Any]):
        """設置 L1 快取"""
        if len(self.l1_cache) >= self.l1_max_size:
            self._evict_lru(self.l1_cache)
        self.l1_cache[key] = entry
    
    def _set_l2(self, key: str, entry: Dict[str, Any]):
        """設置 L2 快取"""
        if len(self.l2_cache) >= self.l2_max_size:
            self._evict_lru(self.l2_cache)
        self.l2_cache[key] = entry
    
    def _set_l3(self, key: str, entry: Dict[str, Any]):
        """設置 L3 快取（資料庫）"""
        # 這裡可以實現資料庫快取邏輯
        pass
    
    def _check_l3_cache(self, key: str) -> Optional[Any]:
        """檢查 L3 快取（資料庫）"""
        # 這裡可以實現從資料庫檢查快取的邏輯
        return None
    
    def _evict_lru(self, cache_dict: Dict[str, Dict[str, Any]]):
        """最近最少使用淘汰策略"""
        if not cache_dict:
            return
            
        # 找到最舊的條目
        oldest_key = min(cache_dict.keys(), 
                        key=lambda k: cache_dict[k]['created_at'])
        del cache_dict[oldest_key]
        self.stats['evictions'] += 1
        logger.debug(f"Evicted {oldest_key} from cache")
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取快取統計"""
        with self._lock:
            total_requests = sum([
                self.stats['l1_hits'],
                self.stats['l2_hits'],
                self.stats['l3_hits'],
                self.stats['misses']
            ])
            
            if total_requests > 0:
                hit_rate = ((self.stats['l1_hits'] + self.stats['l2_hits'] + self.stats['l3_hits']) 
                           / total_requests * 100)
            else:
                hit_rate = 0
            
            return {
                **self.stats,
                'total_requests': total_requests,
                'hit_rate': round(hit_rate, 2),
                'l1_size': len(self.l1_cache),
                'l2_size': len(self.l2_cache)
            }

class SmartCacheWarmer:
    """
    智能快取預熱器
    根據使用模式和時間預熱快取
    """
    
    def __init__(self, tiered_cache: TieredCache):
        self.cache = tiered_cache
        self.warm_up_tasks = []
        self.usage_patterns = defaultdict(list)  # key -> [access_times]
        self.is_warming = False
        self._lock = threading.RLock()
    
    def register_warm_up_task(self, task_name: str, task_func: Callable, 
                             schedule: str = 'startup', priority: int = 1):
        """
        註冊預熱任務
        
        Args:
            task_name: 任務名稱
            task_func: 預熱函數
            schedule: 排程類型 ('startup', 'hourly', 'daily')
            priority: 優先級 (1-10，數字越小優先級越高)
        """
        with self._lock:
            self.warm_up_tasks.append({
                'name': task_name,
                'function': task_func,
                'schedule': schedule,
                'priority': priority,
                'last_run': 0,
                'success_count': 0,
                'error_count': 0
            })
            
            # 按優先級排序
            self.warm_up_tasks.sort(key=lambda x: x['priority'])
            logger.info(f"Registered warm-up task: {task_name}")
    
    def record_access(self, cache_key: str):
        """
        記錄快取訪問模式
        
        Args:
            cache_key: 快取鍵
        """
        with self._lock:
            current_time = time.time()
            self.usage_patterns[cache_key].append(current_time)
            
            # 只保留最近24小時的記錄
            cutoff_time = current_time - 86400  # 24小時
            self.usage_patterns[cache_key] = [
                t for t in self.usage_patterns[cache_key] if t > cutoff_time
            ]
    
    def start_background_warming(self):
        """啟動背景預熱任務"""
        if self.is_warming:
            return
            
        self.is_warming = True
        
        def warming_worker():
            while self.is_warming:
                try:
                    self._run_scheduled_tasks()
                    time.sleep(3600)  # 每小時檢查一次
                except Exception as e:
                    logger.error(f"Error in cache warming worker: {str(e)}")
                    time.sleep(300)  # 出錯時等待5分鐘再重試
        
        warming_thread = threading.Thread(target=warming_worker, daemon=True)
        warming_thread.start()
        logger.info("Cache warming worker started")
    
    def warm_up_on_startup(self):
        """啟動時預熱"""
        startup_tasks = [task for task in self.warm_up_tasks 
                        if task['schedule'] == 'startup']
        
        logger.info(f"Running {len(startup_tasks)} startup warm-up tasks")
        
        for task in startup_tasks:
            try:
                start_time = time.time()
                task['function']()
                duration = time.time() - start_time
                
                task['last_run'] = start_time
                task['success_count'] += 1
                
                logger.info(f"Warm-up task '{task['name']}' completed in {duration:.2f}s")
                
            except Exception as e:
                task['error_count'] += 1
                logger.error(f"Warm-up task '{task['name']}' failed: {str(e)}")
    
    def _run_scheduled_tasks(self):
        """執行排程的預熱任務"""
        current_time = time.time()
        
        for task in self.warm_up_tasks:
            if task['schedule'] == 'startup':
                continue
                
            should_run = False
            
            if task['schedule'] == 'hourly':
                should_run = (current_time - task['last_run']) > 3600
            elif task['schedule'] == 'daily':
                should_run = (current_time - task['last_run']) > 86400
            
            if should_run:
                try:
                    task['function']()
                    task['last_run'] = current_time
                    task['success_count'] += 1
                    logger.info(f"Scheduled warm-up task '{task['name']}' completed")
                except Exception as e:
                    task['error_count'] += 1
                    logger.error(f"Scheduled warm-up task '{task['name']}' failed: {str(e)}")
    
    def get_popular_keys(self, limit: int = 10) -> List[str]:
        """
        獲取熱門快取鍵
        
        Args:
            limit: 返回數量限制
            
        Returns:
            按訪問頻率排序的快取鍵列表
        """
        with self._lock:
            # 按最近24小時的訪問次數排序
            key_counts = {
                key: len(accesses) 
                for key, accesses in self.usage_patterns.items()
            }
            
            sorted_keys = sorted(key_counts.items(), 
                               key=lambda x: x[1], reverse=True)
            
            return [key for key, count in sorted_keys[:limit]]

# 全局實例
dependency_manager = CacheDependencyManager()
tiered_cache = TieredCache()
cache_warmer = SmartCacheWarmer(tiered_cache)

def smart_cache(ttl: int = 300, depends_on: List[str] = None, tier: int = 1):
    """
    智能快取裝飾器
    
    Args:
        ttl: 存活時間
        depends_on: 依賴的快取鍵列表
        tier: 快取層級
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成快取鍵
            import hashlib
            import json
            
            cache_key_data = f"{func.__name__}|{args}|{json.dumps(kwargs, sort_keys=True)}"
            cache_key = hashlib.md5(cache_key_data.encode()).hexdigest()
            
            # 記錄訪問模式
            cache_warmer.record_access(cache_key)
            
            # 嘗試從分層快取獲取
            cached_value = tiered_cache.get(cache_key)
            if cached_value is not None:
                return cached_value
            
            # 執行函數
            result = func(*args, **kwargs)
            
            # 設置快取
            tiered_cache.set(cache_key, result, ttl, tier)
            
            # 添加依賴關係
            if depends_on:
                dependency_manager.add_dependency(cache_key, depends_on)
            
            return result
        
        return wrapper
    return decorator

def invalidate_cache_group(group_key: str):
    """
    使快取組失效
    
    Args:
        group_key: 組快取鍵
    """
    dependency_manager.invalidate_cascade(group_key)

# 初始化預熱任務
def init_cache_warming():
    """初始化快取預熱"""
    
    # 註冊常用數據預熱任務
    def warm_up_accounts():
        """預熱帳戶數據"""
        from utils.performance.cache_manager import ReferenceDataCache
        try:
            ReferenceDataCache.get_accounts_dropdown()
            logger.info("Warmed up accounts data")
        except Exception as e:
            logger.error(f"Failed to warm up accounts: {str(e)}")
    
    def warm_up_dropdown_data():
        """預熱下拉選單數據"""
        from utils.performance.cache_manager import ReferenceDataCache
        try:
            ReferenceDataCache.get_dropdown_data()
            logger.info("Warmed up dropdown data")
        except Exception as e:
            logger.error(f"Failed to warm up dropdown data: {str(e)}")
    
    # 註冊預熱任務
    cache_warmer.register_warm_up_task(
        'accounts_data', warm_up_accounts, 'startup', 1
    )
    cache_warmer.register_warm_up_task(
        'dropdown_data', warm_up_dropdown_data, 'startup', 2
    )
    cache_warmer.register_warm_up_task(
        'accounts_hourly', warm_up_accounts, 'hourly', 1
    )
    
    # 啟動背景預熱
    cache_warmer.start_background_warming()
    
    logger.info("Cache warming system initialized")