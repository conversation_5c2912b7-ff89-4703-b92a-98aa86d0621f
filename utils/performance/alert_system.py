"""
自動化告警系統
監控系統性能並在出現問題時發送告警
"""

import logging
import threading
import time
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Dict, List, Any, Optional, Callable
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json

try:
    from config.advanced_config import get_config
    USE_ADVANCED_CONFIG = True
except ImportError:
    USE_ADVANCED_CONFIG = False

logger = logging.getLogger(__name__)

class AlertRule:
    """
    告警規則
    定義觸發告警的條件
    """
    
    def __init__(self, 
                 name: str,
                 condition: Callable[[Any], bool],
                 severity: str = 'medium',
                 cooldown_minutes: int = 15,
                 description: str = ''):
        """
        初始化告警規則
        
        Args:
            name: 規則名稱
            condition: 判斷條件函數
            severity: 嚴重程度 ('low', 'medium', 'high', 'critical')
            cooldown_minutes: 冷卻時間（分鐘）
            description: 規則描述
        """
        self.name = name
        self.condition = condition
        self.severity = severity
        self.cooldown_minutes = cooldown_minutes
        self.description = description
        self.last_triggered = 0
        self.trigger_count = 0
    
    def can_trigger(self) -> bool:
        """
        檢查是否可以觸發（考慮冷卻時間）
        
        Returns:
            是否可以觸發
        """
        current_time = time.time()
        return (current_time - self.last_triggered) > (self.cooldown_minutes * 60)
    
    def trigger(self) -> Dict[str, Any]:
        """
        觸發告警
        
        Returns:
            告警信息
        """
        self.last_triggered = time.time()
        self.trigger_count += 1
        
        return {
            'rule_name': self.name,
            'severity': self.severity,
            'description': self.description,
            'timestamp': self.last_triggered,
            'trigger_count': self.trigger_count
        }

class AlertChannel:
    """
    告警通道基類
    """
    
    def __init__(self, name: str, enabled: bool = True):
        self.name = name
        self.enabled = enabled
        self.sent_count = 0
        self.error_count = 0
    
    def send_alert(self, alert: Dict[str, Any]) -> bool:
        """
        發送告警
        
        Args:
            alert: 告警信息
            
        Returns:
            是否發送成功
        """
        if not self.enabled:
            return False
        
        try:
            success = self._do_send(alert)
            if success:
                self.sent_count += 1
            else:
                self.error_count += 1
            return success
        except Exception as e:
            logger.error(f"Failed to send alert via {self.name}: {str(e)}")
            self.error_count += 1
            return False
    
    def _do_send(self, alert: Dict[str, Any]) -> bool:
        """
        實際發送邏輯（子類實現）
        
        Args:
            alert: 告警信息
            
        Returns:
            是否發送成功
        """
        raise NotImplementedError

class LogAlertChannel(AlertChannel):
    """
    日誌告警通道
    將告警寫入日誌
    """
    
    def __init__(self):
        super().__init__('log')
    
    def _do_send(self, alert: Dict[str, Any]) -> bool:
        """寫入日誌"""
        timestamp = datetime.fromtimestamp(alert['timestamp']).isoformat()
        message = f"[ALERT] {alert['severity'].upper()}: {alert['rule_name']} - {alert['description']} ({timestamp})"
        
        if alert['severity'] in ['high', 'critical']:
            logger.error(message)
        elif alert['severity'] == 'medium':
            logger.warning(message)
        else:
            logger.info(message)
        
        return True

class EmailAlertChannel(AlertChannel):
    """
    郵件告警通道
    """
    
    def __init__(self, 
                 smtp_server: str,
                 smtp_port: int,
                 username: str,
                 password: str,
                 from_email: str,
                 to_emails: List[str]):
        """
        初始化郵件通道
        
        Args:
            smtp_server: SMTP 伺服器
            smtp_port: SMTP 端口
            username: 用戶名
            password: 密碼
            from_email: 發送者郵箱
            to_emails: 接收者郵箱列表
        """
        super().__init__('email')
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.from_email = from_email
        self.to_emails = to_emails
    
    def _do_send(self, alert: Dict[str, Any]) -> bool:
        """發送郵件"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = f"[會計系統告警] {alert['severity'].upper()}: {alert['rule_name']}"
            
            body = f"""
告警詳情：

規則名稱: {alert['rule_name']}
嚴重程度: {alert['severity']}
描述: {alert['description']}
觸發時間: {datetime.fromtimestamp(alert['timestamp']).isoformat()}
觸發次數: {alert['trigger_count']}

請及時處理。

-- 會計系統監控
"""
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email alert: {str(e)}")
            return False

class WebhookAlertChannel(AlertChannel):
    """
    Webhook 告警通道
    發送 HTTP 請求到指定 URL
    """
    
    def __init__(self, webhook_url: str, headers: Dict[str, str] = None):
        super().__init__('webhook')
        self.webhook_url = webhook_url
        self.headers = headers or {'Content-Type': 'application/json'}
    
    def _do_send(self, alert: Dict[str, Any]) -> bool:
        """發送 Webhook"""
        try:
            import requests
            
            payload = {
                'alert': alert,
                'system': 'accounting_system',
                'timestamp': alert['timestamp']
            }
            
            response = requests.post(
                self.webhook_url,
                headers=self.headers,
                json=payload,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            logger.error(f"Failed to send webhook alert: {str(e)}")
            return False

class AlertManager:
    """
    告警管理器
    管理告警規則、通道和告警歷史
    """
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.channels: Dict[str, AlertChannel] = {}
        self.alert_history = deque(maxlen=1000)  # 保留最近1000條告警
        self.rule_channel_mapping: Dict[str, List[str]] = defaultdict(list)
        
        self._monitoring = False
        self._monitor_thread = None
        self._lock = threading.RLock()
        
        # 初始化日誌通道
        self.add_channel(LogAlertChannel())
    
    def add_rule(self, rule: AlertRule, channels: List[str] = None):
        """
        添加告警規則
        
        Args:
            rule: 告警規則
            channels: 使用的告警通道列表
        """
        with self._lock:
            self.rules[rule.name] = rule
            
            if channels:
                self.rule_channel_mapping[rule.name] = channels
            else:
                self.rule_channel_mapping[rule.name] = ['log']  # 預設使用日誌通道
            
            logger.info(f"Added alert rule: {rule.name} ({rule.severity})")
    
    def add_channel(self, channel: AlertChannel):
        """
        添加告警通道
        
        Args:
            channel: 告警通道
        """
        with self._lock:
            self.channels[channel.name] = channel
            logger.info(f"Added alert channel: {channel.name}")
    
    def check_alerts(self, metrics: Dict[str, Any]):
        """
        檢查告警條件
        
        Args:
            metrics: 系統指標數據
        """
        with self._lock:
            for rule_name, rule in self.rules.items():
                try:
                    # 檢查規則條件
                    if rule.condition(metrics) and rule.can_trigger():
                        # 觸發告警
                        alert = rule.trigger()
                        self.alert_history.append(alert)
                        
                        # 發送到相應通道
                        channels = self.rule_channel_mapping.get(rule_name, ['log'])
                        for channel_name in channels:
                            if channel_name in self.channels:
                                self.channels[channel_name].send_alert(alert)
                        
                        logger.info(f"Alert triggered: {rule_name}")
                        
                except Exception as e:
                    logger.error(f"Error checking alert rule {rule_name}: {str(e)}")
    
    def get_alert_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """
        獲取告警歷史
        
        Args:
            hours: 時間範圍（小時）
            
        Returns:
            告警歷史列表
        """
        with self._lock:
            cutoff_time = time.time() - (hours * 3600)
            
            return [
                alert for alert in self.alert_history
                if alert['timestamp'] > cutoff_time
            ]
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """
        獲取告警統計
        
        Returns:
            告警統計信息
        """
        with self._lock:
            # 按嚴重程度統計
            severity_counts = defaultdict(int)
            rule_counts = defaultdict(int)
            
            for alert in self.alert_history:
                severity_counts[alert['severity']] += 1
                rule_counts[alert['rule_name']] += 1
            
            # 通道統計
            channel_stats = {}
            for name, channel in self.channels.items():
                channel_stats[name] = {
                    'enabled': channel.enabled,
                    'sent_count': channel.sent_count,
                    'error_count': channel.error_count,
                    'success_rate': (
                        channel.sent_count / (channel.sent_count + channel.error_count) * 100
                        if (channel.sent_count + channel.error_count) > 0 else 0
                    )
                }
            
            return {
                'total_alerts': len(self.alert_history),
                'active_rules': len(self.rules),
                'active_channels': len([c for c in self.channels.values() if c.enabled]),
                'by_severity': dict(severity_counts),
                'by_rule': dict(rule_counts),
                'channel_stats': channel_stats
            }
    
    def start_monitoring(self, check_interval: int = 60):
        """
        開始監控
        
        Args:
            check_interval: 檢查間隔（秒）
        """
        if self._monitoring:
            return
        
        self._monitoring = True
        
        def monitor_worker():
            while self._monitoring:
                try:
                    # 收集系統指標
                    metrics = self._collect_system_metrics()
                    
                    # 檢查告警
                    self.check_alerts(metrics)
                    
                    time.sleep(check_interval)
                    
                except Exception as e:
                    logger.error(f"Error in alert monitoring: {str(e)}")
                    time.sleep(check_interval)
        
        self._monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        self._monitor_thread.start()
        logger.info(f"Alert monitoring started (interval: {check_interval}s)")
    
    def stop_monitoring(self):
        """停止監控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Alert monitoring stopped")
    
    def _collect_system_metrics(self) -> Dict[str, Any]:
        """
        收集系統指標
        
        Returns:
            系統指標字典
        """
        metrics = {}
        
        try:
            # 記憶體使用
            from utils.performance.memory_optimizer import memory_optimizer
            metrics['memory'] = memory_optimizer.get_memory_stats()
        except ImportError:
            pass
        
        try:
            # 連接池狀態
            from utils.database.db_pool_monitor import pool_monitor
            if pool_monitor:
                metrics['connection_pool'] = pool_monitor.get_pool_metrics()
        except ImportError:
            pass
        
        try:
            # 性能指標
            from utils.performance.performance_benchmarking import benchmark_manager
            metrics['performance'] = {
                'api_summary': benchmark_manager.get_api_performance_summary(1),  # 最近1小時
                'db_summary': benchmark_manager.get_database_performance_summary(1)
            }
        except ImportError:
            pass
        
        try:
            # 快取統計
            from utils.performance.cache_manager import cache_manager
            metrics['cache'] = cache_manager.get_stats()
        except ImportError:
            pass
        
        return metrics

def create_default_alert_rules() -> List[AlertRule]:
    """
    創建預設告警規則
    
    Returns:
        預設告警規則列表
    """
    rules = []
    
    # 高記憶體使用告警
    rules.append(AlertRule(
        name='high_memory_usage',
        condition=lambda m: m.get('memory', {}).get('current_memory_mb', 0) > 500,
        severity='high',
        cooldown_minutes=10,
        description='記憶體使用超過 500MB'
    ))
    
    # 連接池使用率過高告警
    rules.append(AlertRule(
        name='high_pool_utilization',
        condition=lambda m: m.get('connection_pool', {}).get('utilization', 0) > 0.8,
        severity='medium',
        cooldown_minutes=5,
        description='資料庫連接池使用率超過 80%'
    ))
    
    # API 響應時間過慢告警
    rules.append(AlertRule(
        name='slow_api_response',
        condition=lambda m: (
            m.get('performance', {}).get('api_summary', {})
            .get('overall_stats', {}).get('avg_response_time', 0) > 2.0
        ),
        severity='medium',
        cooldown_minutes=15,
        description='API 平均響應時間超過 2 秒'
    ))
    
    # 慢查詢告警
    rules.append(AlertRule(
        name='high_slow_queries',
        condition=lambda m: (
            m.get('performance', {}).get('db_summary', {})
            .get('slow_queries_count', 0) > 5
        ),
        severity='high',
        cooldown_minutes=10,
        description='慢查詢數量超過 5 個'
    ))
    
    # 快取命中率過低告警
    rules.append(AlertRule(
        name='low_cache_hit_rate',
        condition=lambda m: m.get('cache', {}).get('hit_rate', 100) < 70,
        severity='low',
        cooldown_minutes=30,
        description='快取命中率低於 70%'
    ))
    
    # 系統記憶體使用率過高告警
    rules.append(AlertRule(
        name='high_system_memory',
        condition=lambda m: m.get('memory', {}).get('system_memory_percent', 0) > 85,
        severity='critical',
        cooldown_minutes=5,
        description='系統記憶體使用率超過 85%'
    ))
    
    return rules

# 全局告警管理器
alert_manager = AlertManager()

def init_alert_system(email_config: Dict[str, Any] = None, 
                     webhook_url: str = None):
    """
    初始化告警系統
    
    Args:
        email_config: 郵件配置
        webhook_url: Webhook URL
    """
    # 添加預設告警規則
    default_rules = create_default_alert_rules()
    for rule in default_rules:
        alert_manager.add_rule(rule)
    
    # 從新配置系統獲取配置
    if USE_ADVANCED_CONFIG and not email_config:
        try:
            config = get_config()
            if config.mail.smtp_server:
                email_config = {
                    'smtp_server': config.mail.smtp_server,
                    'smtp_port': config.mail.smtp_port,
                    'username': config.mail.username,
                    'password': config.mail.password,
                    'from_email': config.mail.from_address,
                    'to_emails': [config.mail.admin_email] if config.mail.admin_email else []
                }
        except Exception as e:
            logger.warning(f"無法從配置系統獲取郵件設定: {e}")
    
    # 配置郵件通道
    if email_config and email_config.get('smtp_server'):
        try:
            email_channel = EmailAlertChannel(
                smtp_server=email_config['smtp_server'],
                smtp_port=email_config['smtp_port'],
                username=email_config['username'],
                password=email_config['password'],
                from_email=email_config['from_email'],
                to_emails=email_config['to_emails']
            )
            alert_manager.add_channel(email_channel)
            logger.info("郵件告警通道已配置")
        except Exception as e:
            logger.error(f"配置郵件告警通道失敗: {e}")
    
    # 配置 Webhook 通道
    if webhook_url:
        webhook_channel = WebhookAlertChannel(webhook_url)
        alert_manager.add_channel(webhook_channel)
    
    # 開始監控
    alert_manager.start_monitoring()
    
    logger.info("Alert system initialized")

def get_alert_summary() -> Dict[str, Any]:
    """
    獲取告警摘要
    
    Returns:
        告警摘要
    """
    return {
        'recent_alerts': alert_manager.get_alert_history(24),
        'statistics': alert_manager.get_alert_statistics()
    }