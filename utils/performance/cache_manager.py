"""
快取管理器
提供查詢結果快取功能，特別針對參考數據
"""

import time
from functools import wraps
from typing import Any, Dict, Optional, Callable
import logging
import hashlib
import json

logger = logging.getLogger(__name__)

class CacheManager:
    """
    簡單的記憶體快取管理器
    用於快取經常訪問但很少變動的數據
    """
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """
        從快取中獲取數據
        
        Args:
            key: 快取鍵
        
        Returns:
            快取的數據，如果不存在或過期則返回 None
        """
        if key in self._cache:
            entry = self._cache[key]
            if entry['expires_at'] > time.time():
                self._stats['hits'] += 1
                logger.debug(f"Cache hit for key: {key}")
                return entry['value']
            else:
                # 過期，移除
                del self._cache[key]
                self._stats['evictions'] += 1
                logger.debug(f"Cache expired for key: {key}")
        
        self._stats['misses'] += 1
        return None
    
    def set(self, key: str, value: Any, ttl: int = 300):
        """
        設置快取
        
        Args:
            key: 快取鍵
            value: 要快取的值
            ttl: 存活時間（秒），預設 5 分鐘
        """
        self._cache[key] = {
            'value': value,
            'expires_at': time.time() + ttl,
            'created_at': time.time()
        }
        logger.debug(f"Cache set for key: {key}, TTL: {ttl}s")
    
    def invalidate(self, key: str = None):
        """
        使快取無效
        
        Args:
            key: 要無效化的鍵，如果為 None 則清除所有快取
        """
        if key:
            if key in self._cache:
                del self._cache[key]
                logger.info(f"Cache invalidated for key: {key}")
        else:
            self._cache.clear()
            logger.info("All cache cleared")
    
    def get_stats(self) -> Dict[str, int]:
        """獲取快取統計信息"""
        total = self._stats['hits'] + self._stats['misses']
        hit_rate = (self._stats['hits'] / total * 100) if total > 0 else 0
        
        return {
            **self._stats,
            'size': len(self._cache),
            'hit_rate': round(hit_rate, 2)
        }
    
    def cleanup_expired(self):
        """清理過期的快取項目"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self._cache.items()
            if entry['expires_at'] <= current_time
        ]
        
        for key in expired_keys:
            del self._cache[key]
            self._stats['evictions'] += 1
        
        if expired_keys:
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")

# 全局快取實例
cache_manager = CacheManager()

def cache_result(ttl: int = 300, key_prefix: str = None):
    """
    函數結果快取裝飾器
    
    Args:
        ttl: 快取存活時間（秒）
        key_prefix: 快取鍵前綴
    
    使用範例：
    @cache_result(ttl=600)
    def get_account_list():
        # 耗時的資料庫查詢
        return accounts
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成快取鍵
            cache_key_parts = [key_prefix or func.__name__]
            
            # 將參數轉換為快取鍵的一部分
            if args:
                cache_key_parts.append(str(args))
            if kwargs:
                cache_key_parts.append(json.dumps(kwargs, sort_keys=True))
            
            cache_key = hashlib.md5(
                '|'.join(cache_key_parts).encode()
            ).hexdigest()
            
            # 嘗試從快取獲取
            cached_value = cache_manager.get(cache_key)
            if cached_value is not None:
                return cached_value
            
            # 執行函數並快取結果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl)
            
            return result
        
        # 添加手動使快取無效的方法
        wrapper.invalidate_cache = lambda: cache_manager.invalidate(
            key_prefix or func.__name__
        )
        
        return wrapper
    return decorator

class ReferenceDataCache:
    """
    專門用於參考數據的快取類
    參考數據包括：會計科目、帳戶列表、部門列表等
    """
    
    # 預定義的快取配置
    CACHE_CONFIG = {
        'account_subjects': {'ttl': 600},  # 會計科目，10 分鐘
        'accounts_list': {'ttl': 300},     # 帳戶列表，5 分鐘
        'departments': {'ttl': 900},       # 部門列表，15 分鐘
        'tax_subjects': {'ttl': 1800},     # 稅務科目，30 分鐘
        'dropdown_data': {'ttl': 300},     # 下拉選單數據，5 分鐘
    }
    
    @classmethod
    @cache_result(ttl=600, key_prefix='account_subjects')
    def get_account_subjects(cls):
        """獲取會計科目（快取）"""
        # 暫時註解，因為 AccountService 沒有這個方法
        # from services.account_service import AccountService
        # return AccountService.get_account_subjects()
        return []
    
    @classmethod
    @cache_result(ttl=300, key_prefix='accounts_dropdown')
    def get_accounts_dropdown(cls):
        """獲取帳戶下拉選單數據（快取）"""
        from services.account_service import AccountService
        return AccountService.get_accounts_dropdown()
    
    @classmethod
    @cache_result(ttl=300, key_prefix='dropdown_data')
    def get_dropdown_data(cls):
        """獲取所有下拉選單數據（快取）"""
        from services.account_service import AccountService
        return AccountService.get_dropdown_data()
    
    @classmethod
    def invalidate_all(cls):
        """使所有參考數據快取無效"""
        cache_manager.invalidate()
        logger.info("All reference data cache invalidated")
    
    @classmethod
    def warm_cache(cls):
        """預熱快取 - 在應用啟動時調用"""
        try:
            # cls.get_account_subjects()  # 暫時註解
            cls.get_accounts_dropdown()
            cls.get_dropdown_data()
            logger.info("Reference data cache warmed up successfully")
        except Exception as e:
            logger.error(f"Failed to warm up cache: {str(e)}")

# 定期清理過期快取的任務
def setup_cache_cleanup_task():
    """設置定期清理快取的任務"""
    import threading
    
    def cleanup_task():
        while True:
            time.sleep(300)  # 每 5 分鐘執行一次
            cache_manager.cleanup_expired()
    
    cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
    cleanup_thread.start()
    logger.info("Cache cleanup task started")

# 向後相容性別名
cache_for_5min = lambda func: cache_result(ttl=300)(func)