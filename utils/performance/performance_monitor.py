"""
性能監控模組
"""
import time
import psutil
from datetime import datetime, timezone, timedelta
from collections import defaultdict, deque
import threading

class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'request_times': deque(maxlen=1000),
            'slow_requests': deque(maxlen=100),
            'error_count': defaultdict(int),
            'endpoint_stats': defaultdict(lambda: {
                'count': 0, 'total_time': 0, 'avg_time': 0, 'max_time': 0
            }),
            'system_metrics': {
                'cpu_usage': deque(maxlen=100),
                'memory_usage': deque(maxlen=100),
                'disk_usage': deque(maxlen=100)
            }
        }
        self.start_time = time.time()
        self._lock = threading.Lock()
        
        # 啟動系統監控
        self._start_system_monitoring()
    
    def _start_system_monitoring(self):
        """啟動系統資源監控"""
        def monitor_system():
            while True:
                try:
                    # 獲取當前時間（台灣時間）
                    now = datetime.now(timezone(timedelta(hours=8))).replace(microsecond=0, tzinfo=None)
                    
                    # 收集系統資源數據
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()
                    disk = psutil.disk_usage('.')
                    
                    with self._lock:
                        # 添加 CPU 使用率
                        self.metrics['system_metrics']['cpu_usage'].append({
                            'time': now,
                            'percent': cpu_percent
                        })
                        
                        # 添加記憶體使用率
                        self.metrics['system_metrics']['memory_usage'].append({
                            'time': now,
                            'percent': memory.percent,
                            'used': memory.used,
                            'total': memory.total
                        })
                        
                        # 添加磁碟使用率
                        disk_percent = (disk.used / disk.total) * 100
                        self.metrics['system_metrics']['disk_usage'].append({
                            'time': now,
                            'percent': disk_percent,
                            'used': disk.used,
                            'total': disk.total
                        })
                    
                    # 每30秒收集一次
                    time.sleep(30)
                    
                except Exception as e:
                    print(f"系統監控錯誤: {e}")
                    time.sleep(60)  # 出錯時等待更久
        
        # 在背景執行緒中運行
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
    
    def record_request(self, endpoint, method, response_time, status_code):
        """記錄請求性能"""
        now = datetime.now(timezone(timedelta(hours=8))).replace(microsecond=0, tzinfo=None)
        
        with self._lock:
            # 記錄請求時間
            self.metrics['request_times'].append({
                'time': now,
                'response_time': response_time,
                'endpoint': endpoint,
                'method': method,
                'status_code': status_code
            })
            
            # 記錄慢請求（超過1秒）
            if response_time > 1.0:
                self.metrics['slow_requests'].append({
                    'time': now,
                    'response_time': response_time,
                    'endpoint': endpoint,
                    'method': method,
                    'status_code': status_code
                })
            
            # 記錄錯誤
            if status_code >= 400:
                self.metrics['error_count'][status_code] += 1
            
            # 更新端點統計
            endpoint_key = f"{method} {endpoint}"
            stats = self.metrics['endpoint_stats'][endpoint_key]
            stats['count'] += 1
            stats['total_time'] += response_time
            stats['avg_time'] = stats['total_time'] / stats['count']
            stats['max_time'] = max(stats['max_time'], response_time)
    

    def monitor_db_pool(self):
        """監控資料庫連接池狀態"""
        try:
            from database import engine
            pool = engine.pool
            
            return {
                'pool_size': pool.size(),
                'checked_in': pool.checkedin(),
                'checked_out': pool.checkedout(),
                'overflow': pool.overflow(),
                'invalid': pool.invalid()
            }
        except Exception as e:
            return {'error': str(e)}

    def get_stats(self):
        """獲取統計數據"""
        with self._lock:
            # 計算基本統計
            total_requests = len(self.metrics['request_times'])
            slow_requests = len(self.metrics['slow_requests'])
            
            # 計算平均響應時間
            if total_requests > 0:
                avg_response_time = sum(req['response_time'] for req in self.metrics['request_times']) / total_requests
            else:
                avg_response_time = 0
            
            # 獲取最新的系統資源數據
            current_cpu = 0
            current_memory = 0
            current_disk = 0
            
            if self.metrics['system_metrics']['cpu_usage']:
                current_cpu = self.metrics['system_metrics']['cpu_usage'][-1]['percent']
            
            if self.metrics['system_metrics']['memory_usage']:
                current_memory = self.metrics['system_metrics']['memory_usage'][-1]['percent']
            
            if self.metrics['system_metrics']['disk_usage']:
                current_disk = self.metrics['system_metrics']['disk_usage'][-1]['percent']
            
            # 計算運行時間
            uptime = time.time() - self.start_time
            
            return {
                'summary': {
                    'total_requests': total_requests,
                    'slow_requests': slow_requests,
                    'avg_response_time': round(avg_response_time, 3),
                    'uptime': round(uptime, 2),
                    'current_cpu_percent': round(current_cpu, 1),
                    'current_memory_percent': round(current_memory, 1),
                    'current_disk_percent': round(current_disk, 1)
                },
                'system_metrics': {
                    'cpu_usage': list(self.metrics['system_metrics']['cpu_usage']),
                    'memory_usage': list(self.metrics['system_metrics']['memory_usage']),
                    'disk_usage': list(self.metrics['system_metrics']['disk_usage'])
                },
                'recent_requests': list(self.metrics['request_times'])[-50:],  # 最近50個請求
                'slow_requests': list(self.metrics['slow_requests']),
                'error_count': dict(self.metrics['error_count']),
                'endpoint_stats': dict(self.metrics['endpoint_stats'])
            }
    
    def get_system_info(self):
        """獲取系統基本信息"""
        try:
            return {
                'cpu_count': psutil.cpu_count(),
                'cpu_percent': psutil.cpu_percent(),
                'memory': psutil.virtual_memory()._asdict(),
                'disk': psutil.disk_usage('.')._asdict(),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()),
                'process_count': len(psutil.pids())
            }
        except Exception as e:
            return {'error': str(e)}

# 創建全局實例
performance_monitor = PerformanceMonitor()