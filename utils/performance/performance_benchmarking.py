"""
自動化性能基準測試
監控 API 響應時間、資料庫查詢性能和系統資源使用
"""

import time
import threading
import logging
import psutil
import statistics
from typing import Dict, List, Any, Optional, Callable
from functools import wraps
from collections import deque, defaultdict
from datetime import datetime, timedelta
import json
from database import engine
from utils.performance.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

class PerformanceBenchmark:
    """
    性能基準測試器
    自動收集和分析性能指標
    """
    
    def __init__(self, window_size: int = 1000):
        self.window_size = window_size
        self.metrics = {
            'api_response_times': deque(maxlen=window_size),
            'database_query_times': deque(maxlen=window_size),
            'memory_usage': deque(maxlen=window_size),
            'cpu_usage': deque(maxlen=window_size),
            'connection_pool_usage': deque(maxlen=window_size)
        }
        
        self.endpoint_metrics = defaultdict(lambda: deque(maxlen=100))
        self.query_metrics = defaultdict(lambda: deque(maxlen=100))
        
        self.benchmarks = {}
        self.alerts = []
        
        self._monitoring = False
        self._monitor_thread = None
        self._lock = threading.RLock()
    
    def record_api_response(self, endpoint: str, method: str, 
                           response_time: float, status_code: int):
        """
        記錄 API 響應性能
        
        Args:
            endpoint: API 端點
            method: HTTP 方法
            response_time: 響應時間（秒）
            status_code: 狀態碼
        """
        with self._lock:
            metric = {
                'timestamp': time.time(),
                'endpoint': endpoint,
                'method': method,
                'response_time': response_time,
                'status_code': status_code
            }
            
            self.metrics['api_response_times'].append(metric)
            
            # 按端點分類記錄
            key = f"{method} {endpoint}"
            self.endpoint_metrics[key].append(response_time)
            
            # 檢查是否需要告警
            self._check_api_alerts(endpoint, response_time)
    
    def record_database_query(self, query_type: str, execution_time: float, 
                             rows_affected: int = 0):
        """
        記錄資料庫查詢性能
        
        Args:
            query_type: 查詢類型
            execution_time: 執行時間（秒）
            rows_affected: 影響的行數
        """
        with self._lock:
            metric = {
                'timestamp': time.time(),
                'query_type': query_type,
                'execution_time': execution_time,
                'rows_affected': rows_affected
            }
            
            self.metrics['database_query_times'].append(metric)
            self.query_metrics[query_type].append(execution_time)
            
            # 檢查慢查詢
            if execution_time > 2.0:  # 超過2秒的慢查詢
                self._record_slow_query(query_type, execution_time)
    
    def record_system_metrics(self):
        """記錄系統指標"""
        with self._lock:
            current_time = time.time()
            
            # 記憶體使用
            memory = psutil.virtual_memory()
            self.metrics['memory_usage'].append({
                'timestamp': current_time,
                'percent': memory.percent,
                'used_gb': memory.used / (1024**3),
                'available_gb': memory.available / (1024**3)
            })
            
            # CPU 使用
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics['cpu_usage'].append({
                'timestamp': current_time,
                'percent': cpu_percent
            })
            
            # 連接池使用率
            if hasattr(engine, 'pool'):
                pool = engine.pool
                pool_usage = {
                    'timestamp': current_time,
                    'size': pool.size(),
                    'checked_out': pool.checkedout(),
                    'overflow': pool.overflow(),
                    'utilization': pool.checkedout() / pool.size() if pool.size() > 0 else 0
                }
                self.metrics['connection_pool_usage'].append(pool_usage)
    
    def set_benchmark(self, metric_name: str, target_value: float, 
                     operator: str = 'less_than'):
        """
        設置性能基準
        
        Args:
            metric_name: 指標名稱
            target_value: 目標值
            operator: 比較操作符 ('less_than', 'greater_than', 'equals')
        """
        self.benchmarks[metric_name] = {
            'target': target_value,
            'operator': operator,
            'created_at': time.time()
        }
        logger.info(f"Set benchmark for {metric_name}: {operator} {target_value}")
    
    def get_api_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        獲取 API 性能摘要
        
        Args:
            hours: 統計時間範圍（小時）
            
        Returns:
            性能摘要統計
        """
        with self._lock:
            cutoff_time = time.time() - (hours * 3600)
            
            # 篩選時間範圍內的數據
            recent_metrics = [
                m for m in self.metrics['api_response_times']
                if m['timestamp'] > cutoff_time
            ]
            
            if not recent_metrics:
                return {'message': 'No data available'}
            
            # 計算統計指標
            response_times = [m['response_time'] for m in recent_metrics]
            
            # 按端點統計
            endpoint_stats = defaultdict(list)
            for m in recent_metrics:
                key = f"{m['method']} {m['endpoint']}"
                endpoint_stats[key].append(m['response_time'])
            
            # 計算各端點的統計
            endpoint_summary = {}
            for endpoint, times in endpoint_stats.items():
                endpoint_summary[endpoint] = {
                    'count': len(times),
                    'avg_response_time': statistics.mean(times),
                    'p50': statistics.median(times),
                    'p95': statistics.quantiles(times, n=20)[18] if len(times) > 1 else times[0],
                    'max': max(times),
                    'min': min(times)
                }
            
            return {
                'period_hours': hours,
                'total_requests': len(recent_metrics),
                'overall_stats': {
                    'avg_response_time': statistics.mean(response_times),
                    'p50': statistics.median(response_times),
                    'p95': statistics.quantiles(response_times, n=20)[18] if len(response_times) > 1 else response_times[0],
                    'max': max(response_times),
                    'min': min(response_times)
                },
                'by_endpoint': endpoint_summary,
                'slow_requests': [
                    {
                        'endpoint': m['endpoint'],
                        'method': m['method'],
                        'response_time': m['response_time'],
                        'timestamp': datetime.fromtimestamp(m['timestamp']).isoformat()
                    }
                    for m in recent_metrics if m['response_time'] > 2.0
                ]
            }
    
    def get_database_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        獲取資料庫性能摘要
        
        Args:
            hours: 統計時間範圍（小時）
            
        Returns:
            資料庫性能摘要
        """
        with self._lock:
            cutoff_time = time.time() - (hours * 3600)
            
            recent_queries = [
                q for q in self.metrics['database_query_times']
                if q['timestamp'] > cutoff_time
            ]
            
            if not recent_queries:
                return {'message': 'No database metrics available'}
            
            # 按查詢類型統計
            query_type_stats = defaultdict(list)
            for q in recent_queries:
                query_type_stats[q['query_type']].append(q['execution_time'])
            
            query_summary = {}
            for query_type, times in query_type_stats.items():
                query_summary[query_type] = {
                    'count': len(times),
                    'avg_time': statistics.mean(times),
                    'p50': statistics.median(times),
                    'p95': statistics.quantiles(times, n=20)[18] if len(times) > 1 else times[0],
                    'max': max(times)
                }
            
            return {
                'period_hours': hours,
                'total_queries': len(recent_queries),
                'by_query_type': query_summary,
                'slow_queries': [
                    {
                        'query_type': q['query_type'],
                        'execution_time': q['execution_time'],
                        'rows_affected': q['rows_affected'],
                        'timestamp': datetime.fromtimestamp(q['timestamp']).isoformat()
                    }
                    for q in recent_queries if q['execution_time'] > 1.0
                ]
            }
    
    def get_system_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """
        獲取系統性能摘要
        
        Args:
            hours: 統計時間範圍（小時）
            
        Returns:
            系統性能摘要
        """
        with self._lock:
            cutoff_time = time.time() - (hours * 3600)
            
            # 記憶體使用統計
            recent_memory = [
                m for m in self.metrics['memory_usage']
                if m['timestamp'] > cutoff_time
            ]
            
            # CPU 使用統計
            recent_cpu = [
                c for c in self.metrics['cpu_usage']
                if c['timestamp'] > cutoff_time
            ]
            
            # 連接池使用統計
            recent_pool = [
                p for p in self.metrics['connection_pool_usage']
                if p['timestamp'] > cutoff_time
            ]
            
            summary = {'period_hours': hours}
            
            if recent_memory:
                memory_percents = [m['percent'] for m in recent_memory]
                summary['memory'] = {
                    'avg_usage_percent': statistics.mean(memory_percents),
                    'max_usage_percent': max(memory_percents),
                    'current_usage_gb': recent_memory[-1]['used_gb']
                }
            
            if recent_cpu:
                cpu_percents = [c['percent'] for c in recent_cpu]
                summary['cpu'] = {
                    'avg_usage_percent': statistics.mean(cpu_percents),
                    'max_usage_percent': max(cpu_percents)
                }
            
            if recent_pool:
                utilizations = [p['utilization'] for p in recent_pool]
                summary['connection_pool'] = {
                    'avg_utilization': statistics.mean(utilizations),
                    'max_utilization': max(utilizations),
                    'current_size': recent_pool[-1]['size'],
                    'current_checked_out': recent_pool[-1]['checked_out']
                }
            
            return summary
    
    def start_monitoring(self, interval: int = 60):
        """
        開始自動監控
        
        Args:
            interval: 監控間隔（秒）
        """
        if self._monitoring:
            return
            
        self._monitoring = True
        
        def monitor_worker():
            while self._monitoring:
                try:
                    self.record_system_metrics()
                    self._check_benchmarks()
                    time.sleep(interval)
                except Exception as e:
                    logger.error(f"Error in performance monitoring: {str(e)}")
                    time.sleep(interval)
        
        self._monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        self._monitor_thread.start()
        logger.info(f"Performance monitoring started (interval: {interval}s)")
    
    def stop_monitoring(self):
        """停止監控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Performance monitoring stopped")
    
    def _check_api_alerts(self, endpoint: str, response_time: float):
        """檢查 API 告警條件"""
        # 響應時間過慢告警
        if response_time > 3.0:
            self.alerts.append({
                'type': 'slow_api_response',
                'endpoint': endpoint,
                'response_time': response_time,
                'timestamp': time.time(),
                'severity': 'high' if response_time > 5.0 else 'medium'
            })
    
    def _record_slow_query(self, query_type: str, execution_time: float):
        """記錄慢查詢"""
        self.alerts.append({
            'type': 'slow_database_query',
            'query_type': query_type,
            'execution_time': execution_time,
            'timestamp': time.time(),
            'severity': 'high' if execution_time > 5.0 else 'medium'
        })
    
    def _check_benchmarks(self):
        """檢查性能基準"""
        for metric_name, benchmark in self.benchmarks.items():
            # 這裡可以實現基準檢查邏輯
            pass
    
    def export_metrics(self, format: str = 'json', hours: int = 24) -> str:
        """
        導出性能指標
        
        Args:
            format: 導出格式 ('json', 'csv')
            hours: 導出時間範圍
            
        Returns:
            導出的數據字符串
        """
        if format == 'json':
            return json.dumps({
                'api_performance': self.get_api_performance_summary(hours),
                'database_performance': self.get_database_performance_summary(hours),
                'system_performance': self.get_system_performance_summary(hours),
                'export_timestamp': datetime.now().isoformat()
            }, indent=2, default=str)
        else:
            # 可以實現 CSV 導出
            return "CSV export not implemented yet"

def benchmark_function(category: str = 'general'):
    """
    函數性能基準測試裝飾器
    
    Args:
        category: 分類名稱
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 記錄函數執行時間
                benchmark_manager.record_function_performance(
                    func.__name__, category, execution_time, True
                )
                
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                benchmark_manager.record_function_performance(
                    func.__name__, category, execution_time, False
                )
                raise
        return wrapper
    return decorator

def benchmark_database_query(query_type: str):
    """
    資料庫查詢基準測試裝飾器
    
    Args:
        query_type: 查詢類型
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 估算影響的行數
            rows_affected = 0
            if hasattr(result, '__len__'):
                rows_affected = len(result)
            elif hasattr(result, 'rowcount'):
                rows_affected = result.rowcount
            
            benchmark_manager.record_database_query(
                query_type, execution_time, rows_affected
            )
            
            return result
        return wrapper
    return decorator

class FunctionPerformanceTracker:
    """
    函數性能追蹤器
    """
    
    def __init__(self):
        self.function_metrics = defaultdict(lambda: {
            'call_count': 0,
            'total_time': 0,
            'success_count': 0,
            'error_count': 0,
            'avg_time': 0,
            'last_call': 0
        })
        self._lock = threading.RLock()
    
    def record_function_performance(self, function_name: str, category: str,
                                   execution_time: float, success: bool):
        """
        記錄函數性能
        
        Args:
            function_name: 函數名稱
            category: 分類
            execution_time: 執行時間
            success: 是否成功
        """
        with self._lock:
            key = f"{category}.{function_name}"
            metrics = self.function_metrics[key]
            
            metrics['call_count'] += 1
            metrics['total_time'] += execution_time
            metrics['last_call'] = time.time()
            
            if success:
                metrics['success_count'] += 1
            else:
                metrics['error_count'] += 1
            
            metrics['avg_time'] = metrics['total_time'] / metrics['call_count']
    
    def get_top_slow_functions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        獲取最慢的函數
        
        Args:
            limit: 返回數量限制
            
        Returns:
            按平均執行時間排序的函數列表
        """
        with self._lock:
            sorted_functions = sorted(
                self.function_metrics.items(),
                key=lambda x: x[1]['avg_time'],
                reverse=True
            )
            
            return [
                {
                    'function': func_name,
                    **metrics
                }
                for func_name, metrics in sorted_functions[:limit]
            ]

# 全局實例
benchmark_manager = PerformanceBenchmark()
function_tracker = FunctionPerformanceTracker()

# 將函數追蹤器綁定到基準管理器
benchmark_manager.record_function_performance = function_tracker.record_function_performance

def init_performance_benchmarking():
    """初始化性能基準測試"""
    # 設置性能基準
    benchmark_manager.set_benchmark('api_response_time', 1.0, 'less_than')
    benchmark_manager.set_benchmark('database_query_time', 0.5, 'less_than')
    benchmark_manager.set_benchmark('memory_usage_percent', 80.0, 'less_than')
    benchmark_manager.set_benchmark('cpu_usage_percent', 70.0, 'less_than')
    
    # 開始監控
    benchmark_manager.start_monitoring(interval=30)  # 每30秒收集一次系統指標
    
    logger.info("Performance benchmarking system initialized")