"""
記憶體優化工具
提供批量操作記憶體管理、垃圾回收優化和記憶體監控
"""

import gc
import psutil
import logging
import time
import threading
from typing import Dict, Any, List, Optional, Callable, Iterator
from functools import wraps
import weakref
from collections import deque

logger = logging.getLogger(__name__)

class MemoryOptimizer:
    """
    記憶體優化器
    監控和管理應用程式的記憶體使用
    """
    
    def __init__(self, 
                 memory_threshold_mb: int = 512,
                 gc_threshold_mb: int = 256,
                 monitoring_interval: int = 30):
        """
        初始化記憶體優化器
        
        Args:
            memory_threshold_mb: 記憶體使用閾值（MB）
            gc_threshold_mb: 觸發GC的記憶體增長閾值（MB）
            monitoring_interval: 監控間隔（秒）
        """
        self.memory_threshold_mb = memory_threshold_mb
        self.gc_threshold_mb = gc_threshold_mb
        self.monitoring_interval = monitoring_interval
        
        self.baseline_memory_mb = 0
        self.peak_memory_mb = 0
        self.last_gc_time = 0
        self.gc_count = 0
        
        self.memory_history = deque(maxlen=100)  # 保留最近100次的記憶體快照
        self.large_objects = weakref.WeakSet()  # 追蹤大型物件
        
        self._monitoring = False
        self._monitor_thread = None
        self._lock = threading.RLock()
        
        # 記錄基準記憶體使用
        self.baseline_memory_mb = self.get_memory_usage_mb()
    
    def get_memory_usage_mb(self) -> float:
        """
        獲取當前記憶體使用量（MB）
        
        Returns:
            記憶體使用量（MB）
        """
        process = psutil.Process()
        return process.memory_info().rss / (1024 * 1024)
    
    def get_memory_percent(self) -> float:
        """
        獲取系統記憶體使用百分比
        
        Returns:
            記憶體使用百分比
        """
        return psutil.virtual_memory().percent
    
    def check_memory_pressure(self) -> Dict[str, Any]:
        """
        檢查記憶體壓力
        
        Returns:
            記憶體壓力報告
        """
        current_memory_mb = self.get_memory_usage_mb()
        system_memory_percent = self.get_memory_percent()
        memory_growth_mb = current_memory_mb - self.baseline_memory_mb
        
        pressure_level = 'low'
        if current_memory_mb > self.memory_threshold_mb:
            pressure_level = 'high'
        elif memory_growth_mb > self.gc_threshold_mb:
            pressure_level = 'medium'
        
        return {
            'current_memory_mb': current_memory_mb,
            'baseline_memory_mb': self.baseline_memory_mb,
            'memory_growth_mb': memory_growth_mb,
            'peak_memory_mb': self.peak_memory_mb,
            'system_memory_percent': system_memory_percent,
            'pressure_level': pressure_level,
            'gc_count': self.gc_count,
            'large_objects_count': len(self.large_objects)
        }
    
    def optimize_memory(self, force: bool = False) -> Dict[str, Any]:
        """
        執行記憶體優化
        
        Args:
            force: 是否強制執行
            
        Returns:
            優化結果報告
        """
        with self._lock:
            memory_before = self.get_memory_usage_mb()
            
            # 檢查是否需要優化
            pressure = self.check_memory_pressure()
            
            if not force and pressure['pressure_level'] == 'low':
                return {
                    'optimized': False,
                    'reason': 'Memory pressure is low',
                    'current_memory_mb': memory_before
                }
            
            # 執行垃圾回收
            start_time = time.time()
            collected = gc.collect()
            gc_time = time.time() - start_time
            
            self.gc_count += 1
            self.last_gc_time = start_time
            
            memory_after = self.get_memory_usage_mb()
            memory_freed_mb = memory_before - memory_after
            
            # 記錄優化結果
            result = {
                'optimized': True,
                'memory_before_mb': memory_before,
                'memory_after_mb': memory_after,
                'memory_freed_mb': memory_freed_mb,
                'objects_collected': collected,
                'gc_time_seconds': gc_time,
                'gc_count': self.gc_count
            }
            
            logger.info(f"Memory optimization completed: freed {memory_freed_mb:.2f}MB in {gc_time:.3f}s")
            
            return result
    
    def track_large_object(self, obj: Any, name: str = None):
        """
        追蹤大型物件
        
        Args:
            obj: 要追蹤的物件
            name: 物件名稱（可選）
        """
        try:
            import sys
            size_bytes = sys.getsizeof(obj)
            
            # 只追蹤大於1MB的物件
            if size_bytes > 1024 * 1024:
                self.large_objects.add(obj)
                logger.debug(f"Tracking large object {name or type(obj).__name__}: {size_bytes / 1024 / 1024:.2f}MB")
        except Exception as e:
            logger.warning(f"Failed to track object size: {str(e)}")
    
    def start_monitoring(self):
        """開始記憶體監控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        
        def monitor_worker():
            while self._monitoring:
                try:
                    # 記錄記憶體快照
                    current_memory = self.get_memory_usage_mb()
                    self.memory_history.append({
                        'timestamp': time.time(),
                        'memory_mb': current_memory,
                        'system_percent': self.get_memory_percent()
                    })
                    
                    # 更新峰值記憶體
                    if current_memory > self.peak_memory_mb:
                        self.peak_memory_mb = current_memory
                    
                    # 檢查是否需要自動優化
                    pressure = self.check_memory_pressure()
                    if pressure['pressure_level'] == 'high':
                        logger.warning(f"High memory pressure detected: {current_memory:.2f}MB")
                        self.optimize_memory()
                    
                    time.sleep(self.monitoring_interval)
                    
                except Exception as e:
                    logger.error(f"Error in memory monitoring: {str(e)}")
                    time.sleep(self.monitoring_interval)
        
        self._monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
        self._monitor_thread.start()
        logger.info(f"Memory monitoring started (interval: {self.monitoring_interval}s)")
    
    def stop_monitoring(self):
        """停止記憶體監控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Memory monitoring stopped")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """
        獲取記憶體統計信息
        
        Returns:
            記憶體統計報告
        """
        with self._lock:
            if not self.memory_history:
                return {'message': 'No memory history available'}
            
            recent_memory = [snap['memory_mb'] for snap in self.memory_history]
            
            return {
                'current_memory_mb': self.get_memory_usage_mb(),
                'baseline_memory_mb': self.baseline_memory_mb,
                'peak_memory_mb': self.peak_memory_mb,
                'avg_memory_mb': sum(recent_memory) / len(recent_memory),
                'memory_growth_mb': self.get_memory_usage_mb() - self.baseline_memory_mb,
                'gc_count': self.gc_count,
                'last_gc_time': self.last_gc_time,
                'large_objects_count': len(self.large_objects),
                'system_memory_percent': self.get_memory_percent(),
                'history_points': len(self.memory_history)
            }

class MemoryEfficientBatchProcessor:
    """
    記憶體高效的批量處理器
    處理大型數據集時控制記憶體使用
    """
    
    def __init__(self, 
                 memory_limit_mb: int = 100,
                 batch_size: int = 1000,
                 optimizer: MemoryOptimizer = None):
        """
        初始化批量處理器
        
        Args:
            memory_limit_mb: 記憶體使用限制（MB）
            batch_size: 批次大小
            optimizer: 記憶體優化器實例
        """
        self.memory_limit_mb = memory_limit_mb
        self.initial_batch_size = batch_size
        self.current_batch_size = batch_size
        self.optimizer = optimizer or memory_optimizer
        
        self.processed_count = 0
        self.batch_count = 0
        self.memory_optimizations = 0
    
    def process_in_batches(self, 
                          items: Iterator[Any], 
                          processor_func: Callable[[List[Any]], Any],
                          progress_callback: Optional[Callable[[int, int], None]] = None) -> List[Any]:
        """
        分批處理數據，自動調整批次大小以控制記憶體使用
        
        Args:
            items: 要處理的項目迭代器
            processor_func: 處理函數，接受項目列表
            progress_callback: 進度回調函數
            
        Returns:
            處理結果列表
        """
        results = []
        current_batch = []
        start_memory = self.optimizer.get_memory_usage_mb()
        
        for item in items:
            current_batch.append(item)
            
            # 檢查是否達到批次大小
            if len(current_batch) >= self.current_batch_size:
                # 處理當前批次
                batch_result = self._process_batch(current_batch, processor_func)
                if batch_result is not None:
                    results.extend(batch_result if isinstance(batch_result, list) else [batch_result])
                
                self.processed_count += len(current_batch)
                self.batch_count += 1
                
                # 調用進度回調
                if progress_callback:
                    progress_callback(self.processed_count, self.batch_count)
                
                # 清空批次
                current_batch = []
                
                # 檢查記憶體使用並調整批次大小
                self._adjust_batch_size()
        
        # 處理剩餘項目
        if current_batch:
            batch_result = self._process_batch(current_batch, processor_func)
            if batch_result is not None:
                results.extend(batch_result if isinstance(batch_result, list) else [batch_result])
            
            self.processed_count += len(current_batch)
            self.batch_count += 1
        
        # 最終記憶體清理
        final_memory = self.optimizer.get_memory_usage_mb()
        memory_growth = final_memory - start_memory
        
        if memory_growth > self.memory_limit_mb:
            self.optimizer.optimize_memory()
            self.memory_optimizations += 1
        
        logger.info(f"Batch processing completed: {self.processed_count} items in {self.batch_count} batches")
        
        return results
    
    def _process_batch(self, batch: List[Any], processor_func: Callable) -> Any:
        """
        處理單個批次
        
        Args:
            batch: 批次數據
            processor_func: 處理函數
            
        Returns:
            處理結果
        """
        try:
            memory_before = self.optimizer.get_memory_usage_mb()
            result = processor_func(batch)
            memory_after = self.optimizer.get_memory_usage_mb()
            
            # 記錄記憶體使用
            memory_used = memory_after - memory_before
            if memory_used > 0:
                logger.debug(f"Batch processing used {memory_used:.2f}MB")
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing batch: {str(e)}")
            raise
    
    def _adjust_batch_size(self):
        """根據記憶體使用情況調整批次大小"""
        current_memory = self.optimizer.get_memory_usage_mb()
        memory_growth = current_memory - self.optimizer.baseline_memory_mb
        
        # 如果記憶體使用過高，減少批次大小
        if memory_growth > self.memory_limit_mb:
            self.current_batch_size = max(100, self.current_batch_size // 2)
            logger.debug(f"Reduced batch size to {self.current_batch_size}")
            
            # 執行記憶體優化
            self.optimizer.optimize_memory()
            self.memory_optimizations += 1
            
        # 如果記憶體使用很低，可以增加批次大小
        elif memory_growth < self.memory_limit_mb * 0.5 and self.current_batch_size < self.initial_batch_size:
            self.current_batch_size = min(self.initial_batch_size, self.current_batch_size * 2)
            logger.debug(f"Increased batch size to {self.current_batch_size}")
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """
        獲取處理統計信息
        
        Returns:
            處理統計報告
        """
        return {
            'processed_count': self.processed_count,
            'batch_count': self.batch_count,
            'current_batch_size': self.current_batch_size,
            'initial_batch_size': self.initial_batch_size,
            'memory_optimizations': self.memory_optimizations,
            'avg_items_per_batch': self.processed_count / self.batch_count if self.batch_count > 0 else 0
        }

def memory_efficient(memory_limit_mb: int = 100):
    """
    記憶體高效裝飾器
    監控函數的記憶體使用並在必要時進行優化
    
    Args:
        memory_limit_mb: 記憶體使用限制（MB）
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            memory_before = memory_optimizer.get_memory_usage_mb()
            
            try:
                result = func(*args, **kwargs)
                
                memory_after = memory_optimizer.get_memory_usage_mb()
                memory_used = memory_after - memory_before
                
                # 如果記憶體使用超過限制，進行優化
                if memory_used > memory_limit_mb:
                    logger.warning(f"Function {func.__name__} used {memory_used:.2f}MB")
                    memory_optimizer.optimize_memory()
                
                return result
                
            except Exception as e:
                # 發生異常時也要檢查記憶體
                memory_after = memory_optimizer.get_memory_usage_mb()
                memory_used = memory_after - memory_before
                
                if memory_used > memory_limit_mb:
                    memory_optimizer.optimize_memory()
                
                raise
                
        return wrapper
    return decorator

def chunk_iterator(items: List[Any], chunk_size: int) -> Iterator[List[Any]]:
    """
    將列表分割為指定大小的塊的迭代器
    
    Args:
        items: 要分割的列表
        chunk_size: 塊大小
        
    Yields:
        項目塊
    """
    for i in range(0, len(items), chunk_size):
        yield items[i:i + chunk_size]

def memory_safe_large_operation(items: List[Any], 
                              operation: Callable[[List[Any]], Any],
                              memory_limit_mb: int = 100) -> List[Any]:
    """
    記憶體安全的大型操作處理
    
    Args:
        items: 要處理的項目列表
        operation: 操作函數
        memory_limit_mb: 記憶體限制
        
    Returns:
        操作結果列表
    """
    processor = MemoryEfficientBatchProcessor(memory_limit_mb=memory_limit_mb)
    
    def progress_callback(processed: int, batches: int):
        logger.info(f"Processed {processed} items in {batches} batches")
    
    return processor.process_in_batches(
        iter(items), 
        operation, 
        progress_callback
    )

# 全局記憶體優化器實例
memory_optimizer = MemoryOptimizer()

def init_memory_optimization():
    """初始化記憶體優化系統"""
    # 設置垃圾回收閾值
    gc.set_threshold(700, 10, 10)  # 更積極的GC
    
    # 開始記憶體監控
    memory_optimizer.start_monitoring()
    
    logger.info("Memory optimization system initialized")
    logger.info(f"Baseline memory usage: {memory_optimizer.baseline_memory_mb:.2f}MB")