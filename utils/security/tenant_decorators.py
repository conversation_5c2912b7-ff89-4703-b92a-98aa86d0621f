"""
多租戶裝飾器
提供功能權限控制和數據隔離
"""
from functools import wraps
from flask import g, abort, request
from services.tenant_service import FeatureService

def require_feature(module_name: str, feature_name: str):
    """
    功能權限裝飾器
    檢查當前租戶是否有權限訪問指定功能
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 檢查功能權限
            if not FeatureService.check_feature_access(module_name, feature_name):
                # 根據情況返回不同錯誤
                if not g.get('tenant'):
                    abort(404)  # 沒有租戶時返回404
                else:
                    abort(403)  # 有租戶但無權限時返回403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_tenant():
    """
    要求必須有租戶的裝飾器
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not g.get('tenant'):
                abort(404)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_only():
    """
    僅管理員可訪問的裝飾器
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not g.get('is_admin', False):
                abort(403)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def tenant_data_filter(query, model_class):
    """
    為查詢添加租戶數據過濾
    只返回當前租戶的數據
    """
    # 管理員可以看到所有數據
    if g.get('is_admin', False):
        return query
    
    # 檢查模型是否有tenant_id字段
    if hasattr(model_class, 'tenant_id'):
        tenant_id = g.get('tenant_id')
        if tenant_id:
            return query.filter(model_class.tenant_id == tenant_id)
        else:
            # 沒有租戶時返回空結果
            return query.filter(model_class.id == -1)
    
    return query