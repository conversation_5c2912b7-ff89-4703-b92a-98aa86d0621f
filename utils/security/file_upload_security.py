"""
安全檔案上傳模組
防止各種檔案上傳攻擊，確保上傳檔案的安全性
"""
import os
import uuid
import hashlib
import mimetypes
from typing import Optional, Tuple, List, Dict
from werkzeug.datastructures import FileStorage
from werkzeug.utils import secure_filename
from flask import current_app
# 嘗試導入 python-magic，如果失敗則使用 fallback
try:
    import magic  # python-magic for file type detection
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False
    import warnings
    warnings.warn("python-magic 不可用，將使用基本的檔案類型檢測", UserWarning)
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class FileUploadSecurity:
    """安全檔案上傳管理器"""
    
    def __init__(self):
        # 允許的檔案類型 (MIME types)
        self.allowed_mime_types = {
            # 圖片類型
            'image/jpeg': ['.jpg', '.jpeg'],
            'image/png': ['.png'],
            'image/gif': ['.gif'],
            
            # 文件類型
            'application/pdf': ['.pdf'],
            'text/csv': ['.csv'],
            'text/plain': ['.txt'],
            
            # Microsoft Office
            'application/vnd.ms-excel': ['.xls'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
            'application/msword': ['.doc'],
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
        }
        
        # 檔案大小限制 (bytes)
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.max_image_size = 5 * 1024 * 1024   # 5MB for images
        
        # 危險的檔案副檔名 (絕對不允許)
        self.dangerous_extensions = {
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jse',
            '.jar', '.php', '.php3', '.php4', '.php5', '.phtml', '.asp', '.aspx',
            '.jsp', '.cgi', '.pl', '.py', '.rb', '.sh', '.bash', '.ps1', '.psm1'
        }
        
        # 檔案頭部簽名 (Magic Numbers) 用於真實類型檢測
        self.file_signatures = {
            'image/jpeg': [
                bytes([0xFF, 0xD8, 0xFF]),  # JPEG
            ],
            'image/png': [
                bytes([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]),  # PNG
            ],
            'application/pdf': [
                b'%PDF-',  # PDF
            ],
            'image/gif': [
                b'GIF87a',  # GIF87a
                b'GIF89a',  # GIF89a
            ]
        }
    
    def validate_file(self, file: FileStorage, user_id: int = None) -> Dict:
        """
        完整的檔案安全驗證
        
        Returns:
            Dict: {
                'is_valid': bool,
                'errors': List[str],
                'warnings': List[str],
                'file_info': Dict,
                'safe_filename': str
            }
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'file_info': {},
            'safe_filename': ''
        }
        
        try:
            # 1. 基本檢查
            if not file or not file.filename:
                result['errors'].append('未選擇檔案或檔案名為空')
                result['is_valid'] = False
                return result
            
            # 2. 檔案大小檢查
            file.seek(0, os.SEEK_END)
            file_size = file.tell()
            file.seek(0)  # 重置檔案指針
            
            if file_size == 0:
                result['errors'].append('檔案大小為零')
                result['is_valid'] = False
                return result
            
            if file_size > self.max_file_size:
                result['errors'].append(f'檔案過大 ({file_size} bytes)，最大允許 {self.max_file_size} bytes')
                result['is_valid'] = False
                return result
            
            # 3. 檔案名稱安全檢查
            original_filename = file.filename
            if self._is_dangerous_filename(original_filename):
                result['errors'].append('檔案名稱包含危險字符或路徑')
                result['is_valid'] = False
                return result
            
            # 4. 副檔名檢查
            file_ext = os.path.splitext(original_filename.lower())[1]
            if file_ext in self.dangerous_extensions:
                result['errors'].append(f'不允許的檔案類型: {file_ext}')
                result['is_valid'] = False
                return result
            
            # 5. 檢測真實檔案類型
            file_content = file.read(1024)  # 讀取前 1KB 用於類型檢測
            file.seek(0)  # 重置檔案指針
            
            real_mime_type = self._detect_file_type(file_content, original_filename)
            if not real_mime_type:
                result['errors'].append('無法識別檔案類型')
                result['is_valid'] = False
                return result
            
            # 6. 檢查是否為允許的類型
            if real_mime_type not in self.allowed_mime_types:
                result['errors'].append(f'不允許的檔案類型: {real_mime_type}')
                result['is_valid'] = False
                return result
            
            # 7. 檢查副檔名與實際類型是否匹配
            allowed_extensions = self.allowed_mime_types[real_mime_type]
            if file_ext not in allowed_extensions:
                result['warnings'].append(f'副檔名 {file_ext} 與實際類型 {real_mime_type} 不匹配')
            
            # 8. 特定類型的額外檢查
            if real_mime_type.startswith('image/'):
                if file_size > self.max_image_size:
                    result['errors'].append(f'圖片檔案過大，最大允許 {self.max_image_size} bytes')
                    result['is_valid'] = False
                    return result
                
                # 圖片尺寸檢查 (如果需要)
                image_info = self._validate_image_content(file)
                if image_info.get('error'):
                    result['errors'].append(image_info['error'])
                    result['is_valid'] = False
                    return result
            
            # 9. 內容安全掃描
            content_scan_result = self._scan_file_content(file_content, real_mime_type)
            if not content_scan_result['is_safe']:
                result['errors'].extend(content_scan_result['threats'])
                result['is_valid'] = False
                return result
            
            # 10. 生成安全的檔案名
            safe_filename = self._generate_safe_filename(original_filename, real_mime_type)
            result['safe_filename'] = safe_filename
            
            # 11. 收集檔案資訊
            result['file_info'] = {
                'original_name': original_filename,
                'size': file_size,
                'mime_type': real_mime_type,
                'extension': file_ext,
                'md5_hash': self._calculate_file_hash(file),
                'upload_time': datetime.now().isoformat()
            }
            
            logger.info(f"檔案驗證成功: {original_filename} -> {safe_filename}")
            
        except Exception as e:
            result['errors'].append(f'檔案驗證過程發生錯誤: {str(e)}')
            result['is_valid'] = False
            logger.error(f"檔案驗證錯誤: {str(e)}")
        
        return result
    
    def _is_dangerous_filename(self, filename: str) -> bool:
        """檢查檔案名是否包含危險字符"""
        dangerous_patterns = [
            '..', '/', '\\', '<', '>', '|', ':', '*', '?', '"',
            '\0', '\r', '\n', '\t'
        ]
        
        filename_lower = filename.lower()
        
        # 檢查危險字符
        for pattern in dangerous_patterns:
            if pattern in filename:
                return True
        
        # 檢查 Windows 保留名稱
        reserved_names = [
            'con', 'prn', 'aux', 'nul', 'com1', 'com2', 'com3', 'com4',
            'com5', 'com6', 'com7', 'com8', 'com9', 'lpt1', 'lpt2',
            'lpt3', 'lpt4', 'lpt5', 'lpt6', 'lpt7', 'lpt8', 'lpt9'
        ]
        
        base_name = os.path.splitext(filename_lower)[0]
        if base_name in reserved_names:
            return True
        
        return False
    
    def _detect_file_type(self, content: bytes, filename: str) -> Optional[str]:
        """檢測檔案的真實類型"""
        try:
            # 如果 python-magic 可用，使用它
            if MAGIC_AVAILABLE:
                mime_type = magic.from_buffer(content, mime=True)
                return mime_type
        except Exception as e:
            logger.warning(f"python-magic 檔案類型檢測失敗: {e}")
        
        # Fallback 1: 使用檔案頭部簽名
        for mime_type, signatures in self.file_signatures.items():
            for signature in signatures:
                if content.startswith(signature):
                    return mime_type
        
        # Fallback 2: 使用副檔名猜測
        guessed_type, _ = mimetypes.guess_type(filename)
        if guessed_type:
            return guessed_type
        
        # Fallback 3: 基於副檔名的簡單映射
        ext = os.path.splitext(filename.lower())[1]
        simple_mapping = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.pdf': 'application/pdf',
            '.txt': 'text/plain',
            '.csv': 'text/csv',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
        
        return simple_mapping.get(ext)
    
    def _validate_image_content(self, file: FileStorage) -> Dict:
        """驗證圖片內容（需要安裝 Pillow）"""
        try:
            from PIL import Image
            file.seek(0)
            image = Image.open(file)
            
            # 檢查圖片尺寸
            width, height = image.size
            if width > 10000 or height > 10000:
                return {'error': '圖片尺寸過大'}
            
            # 檢查是否為有效圖片
            image.verify()
            
            return {'success': True, 'width': width, 'height': height}
            
        except Exception as e:
            return {'error': f'無效的圖片檔案: {str(e)}'}
        finally:
            file.seek(0)
    
    def _scan_file_content(self, content: bytes, mime_type: str) -> Dict:
        """掃描檔案內容尋找威脅"""
        threats = []
        
        # 檢查是否包含可執行代碼
        dangerous_patterns = [
            b'<script',
            b'javascript:',
            b'<?php',
            b'<%',
            b'exec(',
            b'system(',
            b'shell_exec(',
            b'passthru(',
            b'eval(',
            b'base64_decode(',
        ]
        
        content_lower = content.lower()
        for pattern in dangerous_patterns:
            if pattern in content_lower:
                threats.append(f'檔案包含可疑代碼: {pattern.decode("utf-8", errors="ignore")}')
        
        # SVG 特殊檢查
        if mime_type == 'image/svg+xml':
            svg_threats = [
                b'<script',
                b'javascript:',
                b'onload=',
                b'onerror=',
                b'onclick=',
            ]
            
            for threat in svg_threats:
                if threat in content_lower:
                    threats.append('SVG 檔案包含危險的 JavaScript 代碼')
                    break
        
        return {
            'is_safe': len(threats) == 0,
            'threats': threats
        }
    
    def _generate_safe_filename(self, original_filename: str, mime_type: str) -> str:
        """生成安全的檔案名"""
        # 獲取副檔名
        original_ext = os.path.splitext(original_filename)[1].lower()
        
        # 根據 MIME 類型確定正確的副檔名
        correct_extensions = self.allowed_mime_types.get(mime_type, [original_ext])
        safe_ext = correct_extensions[0] if correct_extensions else '.bin'
        
        # 生成唯一的檔案名
        unique_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        return f"{timestamp}_{unique_id}{safe_ext}"
    
    def _calculate_file_hash(self, file: FileStorage) -> str:
        """計算檔案 MD5 雜湊值"""
        file.seek(0)
        hash_md5 = hashlib.md5()
        for chunk in iter(lambda: file.read(4096), b""):
            hash_md5.update(chunk)
        file.seek(0)
        return hash_md5.hexdigest()
    
    def save_file_securely(self, file: FileStorage, safe_filename: str, 
                          user_id: int = None) -> Dict:
        """安全地儲存檔案"""
        try:
            # 創建安全的上傳目錄（不在 web 可存取範圍內）
            base_upload_dir = current_app.config.get('SECURE_UPLOAD_FOLDER', 'secure_uploads')
            
            # 按日期組織目錄
            today = datetime.now().strftime('%Y/%m/%d')
            upload_dir = os.path.join(base_upload_dir, today)
            
            # 確保目錄存在
            os.makedirs(upload_dir, exist_ok=True)
            
            # 完整檔案路徑
            file_path = os.path.join(upload_dir, safe_filename)
            
            # 確保路徑是安全的（防止路徑遍歷）
            if not os.path.abspath(file_path).startswith(os.path.abspath(base_upload_dir)):
                raise ValueError("不安全的檔案路徑")
            
            # 儲存檔案
            file.save(file_path)
            
            # 設置適當的檔案權限 (只讀)
            os.chmod(file_path, 0o444)
            
            logger.info(f"檔案已安全儲存: {file_path}")
            
            return {
                'success': True,
                'file_path': file_path,
                'relative_path': os.path.relpath(file_path, base_upload_dir),
                'url_path': None  # 不提供直接存取 URL
            }
            
        except Exception as e:
            logger.error(f"檔案儲存失敗: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

# 全域實例
file_upload_security = FileUploadSecurity()

def secure_file_upload(file: FileStorage, user_id: int = None) -> Dict:
    """
    安全檔案上傳的便利函數
    
    Returns:
        Dict: 完整的上傳結果
    """
    # 驗證檔案
    validation_result = file_upload_security.validate_file(file, user_id)
    
    if not validation_result['is_valid']:
        return validation_result
    
    # 儲存檔案
    save_result = file_upload_security.save_file_securely(
        file, 
        validation_result['safe_filename'], 
        user_id
    )
    
    # 合併結果
    result = {**validation_result, **save_result}
    
    # 記錄到安全監控
    try:
        from utils.security.security_monitor import security_monitor
        security_monitor.log_sensitive_operation(
            'FILE_UPLOAD',
            f'user_{user_id}' if user_id else 'anonymous',
            {
                'original_filename': validation_result['file_info']['original_name'],
                'safe_filename': validation_result['safe_filename'],
                'file_size': validation_result['file_info']['size'],
                'mime_type': validation_result['file_info']['mime_type'],
                'success': save_result.get('success', False)
            }
        )
    except Exception as e:
        logger.warning(f"無法記錄檔案上傳到安全監控: {e}")
    
    return result