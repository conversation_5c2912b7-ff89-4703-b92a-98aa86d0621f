from functools import wraps
from flask import session, redirect, url_for, flash, abort, g
from services.auth_service import AuthService
from typing import List, Union

def login_required(f):
    """登入驗證裝飾器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 檢查會話中是否有用戶ID
        if 'user_id' not in session:
            flash('請先登入', 'warning')
            return redirect(url_for('auth.login'))
        
        # 驗證會話是否有效
        session_token = session.get('session_token')
        if session_token:
            user = AuthService.get_user_by_session(session_token)
            if user:
                g.current_user = user
                return f(*args, **kwargs)
        
        # 會話無效，清除並重新導向登入
        session.clear()
        flash('會話已過期，請重新登入', 'warning')
        return redirect(url_for('auth.login'))
    
    return decorated_function

def permission_required(permission: str):
    """權限驗證裝飾器"""
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            user_id = session.get('user_id')
            if not user_id or not AuthService.user_has_permission(user_id, permission):
                flash('您沒有權限訪問此功能', 'error')
                abort(403)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def module_required(module: str):
    """模組訪問權限裝飾器"""
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            user_id = session.get('user_id')
            if not user_id:
                flash('您沒有權限訪問此模組', 'error')
                abort(403)
            user_modules = AuthService.get_user_modules(user_id)
            if module not in user_modules:
                flash('您沒有權限訪問此模組', 'error')
                abort(403)
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def role_required(roles: Union[str, List[str]]):
    """角色驗證裝飾器"""
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            if isinstance(roles, str):
                required_roles = [roles]
            else:
                required_roles = roles
            
            user = g.current_user
            user_roles = [role.name for role in user.get_roles()]
            
            if not any(role in user_roles for role in required_roles):
                flash('您沒有足夠的權限', 'error')
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """管理員權限裝飾器"""
    @wraps(f)
    @role_required('admin')
    def decorated_function(*args, **kwargs):
        return f(*args, **kwargs)
    return decorated_function