"""
API 安全管理模組
提供統一的 API 認證、授權、驗證和保護機制
"""
import hashlib
import secrets
import time
import jwt
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple, Any
from functools import wraps
from flask import request, jsonify, current_app, g, session
from database import get_db
from model import User
import logging

logger = logging.getLogger(__name__)

class APIKeyManager:
    """API 密鑰管理器"""
    
    @staticmethod
    def generate_api_key(user_id: int, description: str = "", expires_days: int = 90) -> Dict:
        """生成 API 密鑰"""
        try:
            # 生成密鑰和密鑰 ID
            key_id = secrets.token_hex(8)  # 短 ID 用於識別
            api_key = secrets.token_hex(32)  # 64 字符的密鑰
            
            # 創建密鑰雜湊（存儲用）
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            # 設置過期時間
            expires_at = datetime.now() + timedelta(days=expires_days)
            
            # 存儲密鑰信息（這裡應該存到數據庫，目前簡化處理）
            key_info = {
                'key_id': key_id,
                'user_id': user_id,
                'key_hash': key_hash,
                'description': description,
                'created_at': datetime.now().isoformat(),
                'expires_at': expires_at.isoformat(),
                'is_active': True,
                'last_used': None,
                'request_count': 0
            }
            
            # 存儲到會話或緩存中（生產環境應存儲到數據庫）
            if 'api_keys' not in session:
                session['api_keys'] = {}
            session['api_keys'][key_id] = key_info
            
            logger.info(f"為用戶 {user_id} 生成新 API 密鑰: {key_id}")
            
            return {
                'success': True,
                'key_id': key_id,
                'api_key': f"{key_id}.{api_key}",  # 格式: key_id.actual_key
                'expires_at': expires_at.isoformat(),
                'description': description
            }
            
        except Exception as e:
            logger.error(f"生成 API 密鑰失敗: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    @staticmethod
    def validate_api_key(api_key: str) -> Optional[Dict]:
        """驗證 API 密鑰"""
        try:
            if not api_key or '.' not in api_key:
                return None
            
            # 解析密鑰格式
            key_id, actual_key = api_key.split('.', 1)
            
            # 從會話中獲取密鑰信息（生產環境應從數據庫獲取）
            api_keys = session.get('api_keys', {})
            key_info = api_keys.get(key_id)
            
            if not key_info:
                logger.warning(f"未找到 API 密鑰: {key_id}")
                return None
            
            # 檢查是否啟用
            if not key_info.get('is_active'):
                logger.warning(f"API 密鑰已停用: {key_id}")
                return None
            
            # 檢查是否過期
            expires_at = datetime.fromisoformat(key_info['expires_at'])
            if datetime.now() > expires_at:
                logger.warning(f"API 密鑰已過期: {key_id}")
                return None
            
            # 驗證密鑰雜湊
            key_hash = hashlib.sha256(actual_key.encode()).hexdigest()
            if key_hash != key_info['key_hash']:
                logger.warning(f"API 密鑰雜湊不匹配: {key_id}")
                return None
            
            # 更新使用記錄
            key_info['last_used'] = datetime.now().isoformat()
            key_info['request_count'] = key_info.get('request_count', 0) + 1
            api_keys[key_id] = key_info
            session['api_keys'] = api_keys
            
            logger.info(f"API 密鑰驗證成功: {key_id}")
            return key_info
            
        except Exception as e:
            logger.error(f"驗證 API 密鑰時發生錯誤: {str(e)}")
            return None
    
    @staticmethod
    def revoke_api_key(key_id: str, user_id: int) -> bool:
        """撤銷 API 密鑰"""
        try:
            api_keys = session.get('api_keys', {})
            key_info = api_keys.get(key_id)
            
            if not key_info:
                return False
            
            # 檢查用戶權限
            if key_info['user_id'] != user_id:
                logger.warning(f"用戶 {user_id} 嘗試撤銷不屬於自己的 API 密鑰: {key_id}")
                return False
            
            # 停用密鑰
            key_info['is_active'] = False
            key_info['revoked_at'] = datetime.now().isoformat()
            api_keys[key_id] = key_info
            session['api_keys'] = api_keys
            
            logger.info(f"API 密鑰已撤銷: {key_id}")
            return True
            
        except Exception as e:
            logger.error(f"撤銷 API 密鑰失敗: {str(e)}")
            return False

class APIValidator:
    """API 輸入驗證器"""
    
    @staticmethod
    def validate_required_fields(data: Dict, required_fields: list) -> Tuple[bool, str]:
        """驗證必要欄位"""
        if not isinstance(data, dict):
            return False, "請求數據必須是有效的 JSON 格式"
        
        missing_fields = []
        for field in required_fields:
            if field not in data or not data[field]:
                missing_fields.append(field)
        
        if missing_fields:
            return False, f"缺少必要欄位: {', '.join(missing_fields)}"
        
        return True, ""
    
    @staticmethod
    def validate_data_types(data: Dict, type_rules: Dict) -> Tuple[bool, str]:
        """驗證數據類型"""
        for field, expected_type in type_rules.items():
            if field in data:
                value = data[field]
                if not isinstance(value, expected_type):
                    return False, f"欄位 '{field}' 必須是 {expected_type.__name__} 類型"
        
        return True, ""
    
    @staticmethod
    def validate_string_length(data: Dict, length_rules: Dict) -> Tuple[bool, str]:
        """驗證字符串長度"""
        for field, (min_len, max_len) in length_rules.items():
            if field in data and isinstance(data[field], str):
                length = len(data[field])
                if length < min_len:
                    return False, f"欄位 '{field}' 長度不能少於 {min_len} 字符"
                if max_len and length > max_len:
                    return False, f"欄位 '{field}' 長度不能超過 {max_len} 字符"
        
        return True, ""
    
    @staticmethod
    def sanitize_api_input(data: Dict) -> Dict:
        """清理 API 輸入數據"""
        from utils.security.security import SecurityManager
        
        cleaned_data = {}
        for key, value in data.items():
            if isinstance(value, str):
                # 檢查 SQL 注入
                if SecurityManager.check_sql_injection(value):
                    logger.warning(f"API 輸入包含潛在 SQL 注入: {key}={value}")
                    # 拒絕包含 SQL 注入的請求
                    raise ValueError(f"輸入 '{key}' 包含非法字符")
                
                # 清理 XSS 攻擊
                cleaned_value = SecurityManager.sanitize_input(value)
                cleaned_data[key] = cleaned_value
            elif isinstance(value, dict):
                # 遞歸清理嵌套字典
                cleaned_data[key] = APIValidator.sanitize_api_input(value)
            else:
                cleaned_data[key] = value
        
        return cleaned_data

class APIRateLimit:
    """API 專用速率限制"""
    
    def __init__(self):
        self.request_counts = {}  # {api_key_or_ip: {endpoint: [timestamps]}}
        self.global_counts = {}   # {api_key_or_ip: [timestamps]}
    
    def is_allowed(self, identifier: str, endpoint: str = None, 
                   max_requests: int = 60, window_minutes: int = 1,
                   global_limit: int = 1000, global_window_minutes: int = 60) -> Tuple[bool, Dict]:
        """檢查是否允許 API 請求"""
        current_time = datetime.now()
        
        # 檢查全局限制
        if not self._check_global_limit(identifier, current_time, global_limit, global_window_minutes):
            return False, {
                'error': 'global_rate_limit_exceeded',
                'message': f'全局請求頻率超限，每 {global_window_minutes} 分鐘最多 {global_limit} 次請求'
            }
        
        # 檢查端點特定限制
        if endpoint and not self._check_endpoint_limit(identifier, endpoint, current_time, max_requests, window_minutes):
            return False, {
                'error': 'endpoint_rate_limit_exceeded',
                'message': f'端點 {endpoint} 請求頻率超限，每 {window_minutes} 分鐘最多 {max_requests} 次請求'
            }
        
        # 記錄請求
        self._record_request(identifier, endpoint, current_time)
        
        return True, {}
    
    def _check_global_limit(self, identifier: str, current_time: datetime, 
                          max_requests: int, window_minutes: int) -> bool:
        """檢查全局限制"""
        window_start = current_time - timedelta(minutes=window_minutes)
        
        if identifier not in self.global_counts:
            self.global_counts[identifier] = []
        
        # 清理舊記錄
        self.global_counts[identifier] = [
            timestamp for timestamp in self.global_counts[identifier]
            if timestamp > window_start
        ]
        
        return len(self.global_counts[identifier]) < max_requests
    
    def _check_endpoint_limit(self, identifier: str, endpoint: str, current_time: datetime,
                            max_requests: int, window_minutes: int) -> bool:
        """檢查端點特定限制"""
        window_start = current_time - timedelta(minutes=window_minutes)
        
        if identifier not in self.request_counts:
            self.request_counts[identifier] = {}
        
        if endpoint not in self.request_counts[identifier]:
            self.request_counts[identifier][endpoint] = []
        
        # 清理舊記錄
        self.request_counts[identifier][endpoint] = [
            timestamp for timestamp in self.request_counts[identifier][endpoint]
            if timestamp > window_start
        ]
        
        return len(self.request_counts[identifier][endpoint]) < max_requests
    
    def _record_request(self, identifier: str, endpoint: str, current_time: datetime):
        """記錄請求"""
        # 記錄全局請求
        if identifier not in self.global_counts:
            self.global_counts[identifier] = []
        self.global_counts[identifier].append(current_time)
        
        # 記錄端點請求
        if endpoint:
            if identifier not in self.request_counts:
                self.request_counts[identifier] = {}
            if endpoint not in self.request_counts[identifier]:
                self.request_counts[identifier][endpoint] = []
            self.request_counts[identifier][endpoint].append(current_time)
    
    def get_usage_stats(self, identifier: str) -> Dict:
        """獲取使用統計"""
        current_time = datetime.now()
        hour_ago = current_time - timedelta(hours=1)
        
        # 全局統計
        global_requests_last_hour = len([
            t for t in self.global_counts.get(identifier, [])
            if t > hour_ago
        ])
        
        # 端點統計
        endpoint_stats = {}
        if identifier in self.request_counts:
            for endpoint, timestamps in self.request_counts[identifier].items():
                endpoint_stats[endpoint] = len([t for t in timestamps if t > hour_ago])
        
        return {
            'identifier': identifier,
            'global_requests_last_hour': global_requests_last_hour,
            'endpoint_requests_last_hour': endpoint_stats,
            'total_endpoints_used': len(endpoint_stats)
        }

# 全局實例
api_key_manager = APIKeyManager()
api_validator = APIValidator()
api_rate_limit = APIRateLimit()

def require_api_auth(auth_types: list = ['api_key', 'session']):
    """API 認證裝飾器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 檢查認證
            auth_result = None
            
            # 1. API 密鑰認證
            if 'api_key' in auth_types:
                api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
                if api_key:
                    key_info = api_key_manager.validate_api_key(api_key)
                    if key_info:
                        g.api_auth = {
                            'type': 'api_key',
                            'user_id': key_info['user_id'],
                            'key_id': key_info['key_id']
                        }
                        auth_result = True
            
            # 2. 會話認證
            if not auth_result and 'session' in auth_types:
                user_id = session.get('user_id')
                if user_id:
                    g.api_auth = {
                        'type': 'session',
                        'user_id': user_id
                    }
                    auth_result = True
            
            if not auth_result:
                return jsonify({
                    'success': False,
                    'error': 'authentication_required',
                    'message': '需要有效的認證'
                }), 401
            
            return func(*args, **kwargs)
        
        return wrapper
    
    return decorator

def api_rate_limit_decorator(max_requests: int = 60, window_minutes: int = 1,
                           global_limit: int = 1000, global_window_minutes: int = 60):
    """API 速率限制裝飾器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 獲取識別符
            if hasattr(g, 'api_auth') and g.api_auth['type'] == 'api_key':
                identifier = f"api_key:{g.api_auth['key_id']}"
            else:
                identifier = f"ip:{request.remote_addr}"
            
            # 獲取端點
            endpoint = request.endpoint
            
            # 檢查速率限制
            allowed, error_info = api_rate_limit.is_allowed(
                identifier, endpoint, max_requests, window_minutes,
                global_limit, global_window_minutes
            )
            
            if not allowed:
                return jsonify({
                    'success': False,
                    **error_info
                }), 429
            
            return func(*args, **kwargs)
        
        return wrapper
    
    return decorator

def validate_api_input(required_fields: list = None, type_rules: Dict = None,
                      length_rules: Dict = None):
    """API 輸入驗證裝飾器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # 獲取請求數據
                if request.is_json:
                    data = request.get_json()
                else:
                    data = request.form.to_dict()
                
                if not data:
                    data = {}
                
                # 清理輸入
                try:
                    clean_data = api_validator.sanitize_api_input(data)
                except ValueError as e:
                    return jsonify({
                        'success': False,
                        'error': 'invalid_input',
                        'message': str(e)
                    }), 400
                
                # 驗證必要欄位
                if required_fields:
                    valid, error_msg = api_validator.validate_required_fields(clean_data, required_fields)
                    if not valid:
                        return jsonify({
                            'success': False,
                            'error': 'missing_required_fields',
                            'message': error_msg
                        }), 400
                
                # 驗證數據類型
                if type_rules:
                    valid, error_msg = api_validator.validate_data_types(clean_data, type_rules)
                    if not valid:
                        return jsonify({
                            'success': False,
                            'error': 'invalid_data_type',
                            'message': error_msg
                        }), 400
                
                # 驗證字符串長度
                if length_rules:
                    valid, error_msg = api_validator.validate_string_length(clean_data, length_rules)
                    if not valid:
                        return jsonify({
                            'success': False,
                            'error': 'invalid_length',
                            'message': error_msg
                        }), 400
                
                # 將清理後的數據存儲到 g 中供路由使用
                g.api_data = clean_data
                
                return func(*args, **kwargs)
                
            except Exception as e:
                logger.error(f"API 輸入驗證錯誤: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': 'validation_error',
                    'message': '輸入驗證失敗'
                }), 400
        
        return wrapper
    
    return decorator

def api_error_handler(func):
    """API 錯誤處理裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"API 錯誤 [{request.endpoint}]: {str(e)}")
            
            # 記錄到安全監控
            try:
                from utils.security.security_monitor import security_monitor
                user_id = getattr(g, 'api_auth', {}).get('user_id', 'anonymous')
                security_monitor.log_sensitive_operation(
                    'API_ERROR',
                    f'user_{user_id}',
                    {
                        'endpoint': request.endpoint,
                        'method': request.method,
                        'error': str(e),
                        'ip': request.remote_addr
                    }
                )
            except:
                pass  # 不讓監控錯誤影響主要功能
            
            # 根據錯誤類型返回不同響應
            if "not found" in str(e).lower():
                return jsonify({
                    'success': False,
                    'error': 'not_found',
                    'message': '請求的資源不存在'
                }), 404
            elif "permission" in str(e).lower() or "unauthorized" in str(e).lower():
                return jsonify({
                    'success': False,
                    'error': 'permission_denied',
                    'message': '沒有權限執行此操作'
                }), 403
            else:
                return jsonify({
                    'success': False,
                    'error': 'internal_error',
                    'message': '服務器內部錯誤'
                }), 500
    
    return wrapper