"""
安全監控系統
即時追蹤和分析系統中的安全事件
"""
import logging
import json
from datetime import datetime, timedelta
from collections import defaultdict, deque
from typing import Dict, List, Optional, Tuple
from flask import request, session
from functools import wraps
import hashlib

logger = logging.getLogger(__name__)

class SecurityMonitor:
    """安全監控核心類"""
    
    def __init__(self):
        # 登入嘗試追蹤
        self.login_attempts = defaultdict(lambda: deque(maxlen=100))
        self.failed_logins = defaultdict(int)
        
        # 可疑活動追蹤
        self.suspicious_activities = deque(maxlen=1000)
        
        # IP 封鎖清單
        self.blocked_ips = {}
        
        # 敏感操作日誌
        self.sensitive_operations = deque(maxlen=500)
        
        # 異常模式偵測
        self.access_patterns = defaultdict(lambda: deque(maxlen=200))
        
        # 統計數據
        self.security_stats = {
            'total_login_attempts': 0,
            'failed_login_attempts': 0,
            'blocked_ips_count': 0,
            'suspicious_activities_count': 0,
            'alerts_triggered': 0
        }
        
        # 告警閾值
        self.thresholds = {
            'max_failed_logins': 5,  # 最大失敗登入次數
            'max_requests_per_minute': 60,  # 每分鐘最大請求數
            'max_data_export': 1000,  # 單次最大數據匯出量
            'unusual_hour_start': 0,  # 異常時段開始（深夜）
            'unusual_hour_end': 6  # 異常時段結束
        }
    
    def log_login_attempt(self, username: str, ip: str, success: bool, user_agent: str = None):
        """記錄登入嘗試"""
        timestamp = datetime.now()
        
        attempt = {
            'timestamp': timestamp.isoformat(),
            'username': username,
            'ip': ip,
            'success': success,
            'user_agent': user_agent or request.headers.get('User-Agent', 'Unknown')
        }
        
        # 記錄到對應的 IP
        self.login_attempts[ip].append(attempt)
        self.security_stats['total_login_attempts'] += 1
        
        if not success:
            self.failed_logins[ip] += 1
            self.security_stats['failed_login_attempts'] += 1
            
            # 檢查是否需要封鎖 IP
            if self.failed_logins[ip] >= self.thresholds['max_failed_logins']:
                self._block_ip(ip, "多次登入失敗")
                
            # 記錄可疑活動
            self._log_suspicious_activity(
                'FAILED_LOGIN',
                f"失敗的登入嘗試: {username}",
                {'ip': ip, 'attempt_count': self.failed_logins[ip]}
            )
        else:
            # 成功登入，重置失敗計數
            self.failed_logins[ip] = 0
            
            # 檢查是否在異常時段登入
            hour = timestamp.hour
            if self.thresholds['unusual_hour_start'] <= hour < self.thresholds['unusual_hour_end']:
                self._log_suspicious_activity(
                    'UNUSUAL_TIME_LOGIN',
                    f"異常時段登入: {username} 在 {hour}:00",
                    {'ip': ip, 'username': username, 'hour': hour}
                )
        
        logger.info(f"登入嘗試 - 用戶: {username}, IP: {ip}, 成功: {success}")
    
    def log_sensitive_operation(self, operation: str, user: str, details: Dict = None):
        """記錄敏感操作"""
        timestamp = datetime.now()
        
        operation_log = {
            'timestamp': timestamp.isoformat(),
            'operation': operation,
            'user': user,
            'ip': request.remote_addr if request else 'unknown',
            'details': details or {}
        }
        
        self.sensitive_operations.append(operation_log)
        
        # 某些操作需要特別關注
        high_risk_operations = [
            'DELETE_RECORDS', 'EXPORT_ALL_DATA', 'CHANGE_PERMISSIONS',
            'MODIFY_AUDIT_LOG', 'DISABLE_SECURITY', 'ADMIN_ACCESS'
        ]
        
        if operation in high_risk_operations:
            self._trigger_alert(
                'HIGH_RISK_OPERATION',
                f"高風險操作: {operation} by {user}",
                operation_log
            )
        
        logger.warning(f"敏感操作 - {operation} 由用戶 {user} 執行")
    
    def check_access_pattern(self, user: str, resource: str) -> bool:
        """檢查訪問模式是否異常"""
        timestamp = datetime.now()
        
        # 記錄訪問
        access = {
            'timestamp': timestamp.isoformat(),
            'resource': resource
        }
        self.access_patterns[user].append(access)
        
        # 檢查短時間內的訪問頻率
        recent_accesses = [
            a for a in self.access_patterns[user]
            if datetime.fromisoformat(a['timestamp']) > timestamp - timedelta(minutes=1)
        ]
        
        if len(recent_accesses) > self.thresholds['max_requests_per_minute']:
            self._log_suspicious_activity(
                'EXCESSIVE_ACCESS',
                f"過度訪問: {user} 在1分鐘內訪問 {len(recent_accesses)} 次",
                {'user': user, 'access_count': len(recent_accesses)}
            )
            return False
        
        return True
    
    def detect_data_exfiltration(self, user: str, data_size: int, operation: str):
        """偵測數據外洩嘗試"""
        if data_size > self.thresholds['max_data_export']:
            self._log_suspicious_activity(
                'DATA_EXFILTRATION',
                f"可能的數據外洩: {user} 嘗試匯出 {data_size} 筆記錄",
                {
                    'user': user,
                    'data_size': data_size,
                    'operation': operation,
                    'ip': request.remote_addr if request else 'unknown'
                }
            )
            self._trigger_alert(
                'DATA_EXFILTRATION_ATTEMPT',
                f"警告: {user} 嘗試大量數據匯出 ({data_size} 筆)",
                {'user': user, 'size': data_size}
            )
    
    def _log_suspicious_activity(self, activity_type: str, description: str, details: Dict = None):
        """記錄可疑活動"""
        activity = {
            'timestamp': datetime.now().isoformat(),
            'type': activity_type,
            'description': description,
            'details': details or {},
            'ip': request.remote_addr if request else 'unknown'
        }
        
        self.suspicious_activities.append(activity)
        self.security_stats['suspicious_activities_count'] += 1
        
        logger.warning(f"可疑活動 - {activity_type}: {description}")
    
    def _block_ip(self, ip: str, reason: str):
        """封鎖 IP"""
        self.blocked_ips[ip] = {
            'blocked_at': datetime.now().isoformat(),
            'reason': reason,
            'unblock_at': (datetime.now() + timedelta(hours=24)).isoformat()
        }
        
        self.security_stats['blocked_ips_count'] += 1
        
        self._trigger_alert(
            'IP_BLOCKED',
            f"IP {ip} 已被封鎖: {reason}",
            {'ip': ip, 'reason': reason}
        )
        
        logger.error(f"IP 封鎖 - {ip}: {reason}")
    
    def _trigger_alert(self, alert_type: str, message: str, details: Dict = None):
        """觸發安全告警"""
        alert = {
            'timestamp': datetime.now().isoformat(),
            'type': alert_type,
            'message': message,
            'details': details or {},
            'severity': self._get_severity(alert_type)
        }
        
        self.security_stats['alerts_triggered'] += 1
        
        # 記錄到專門的安全日誌
        security_logger = logging.getLogger('security')
        security_logger.critical(f"[SECURITY ALERT] {alert_type}: {message}")
        
        # 這裡可以添加更多告警機制，如：
        # - 發送 Email 通知
        # - 發送 SMS 簡訊
        # - 推送到監控系統
        # - Webhook 通知
        
        return alert
    
    def _get_severity(self, alert_type: str) -> str:
        """獲取告警嚴重程度"""
        severity_map = {
            'DATA_EXFILTRATION_ATTEMPT': 'CRITICAL',
            'HIGH_RISK_OPERATION': 'HIGH',
            'IP_BLOCKED': 'HIGH',
            'EXCESSIVE_ACCESS': 'MEDIUM',
            'FAILED_LOGIN': 'LOW',
            'UNUSUAL_TIME_LOGIN': 'MEDIUM'
        }
        return severity_map.get(alert_type, 'LOW')
    
    def is_ip_blocked(self, ip: str) -> bool:
        """檢查 IP 是否被封鎖"""
        if ip not in self.blocked_ips:
            return False
        
        block_info = self.blocked_ips[ip]
        unblock_time = datetime.fromisoformat(block_info['unblock_at'])
        
        if datetime.now() > unblock_time:
            # 解除封鎖
            del self.blocked_ips[ip]
            return False
        
        return True
    
    def get_security_dashboard(self) -> Dict:
        """獲取安全儀表板數據"""
        return {
            'stats': self.security_stats,
            'recent_alerts': list(self.suspicious_activities)[-10:],
            'blocked_ips': self.blocked_ips,
            'failed_login_ips': dict(self.failed_logins),
            'active_sessions': self._get_active_sessions(),
            'risk_score': self._calculate_risk_score()
        }
    
    def _get_active_sessions(self) -> int:
        """獲取活躍會話數（需要與會話管理整合）"""
        # 這裡應該與實際的會話管理系統整合
        return 0
    
    def _calculate_risk_score(self) -> int:
        """計算當前風險分數 (0-100)"""
        score = 0
        
        # 基於各種因素計算風險分數
        score += min(self.security_stats['failed_login_attempts'] * 2, 20)
        score += min(self.security_stats['blocked_ips_count'] * 5, 25)
        score += min(self.security_stats['suspicious_activities_count'], 30)
        score += min(self.security_stats['alerts_triggered'] * 3, 25)
        
        return min(score, 100)

# 全域安全監控實例
security_monitor = SecurityMonitor()

def monitor_security(operation_type: str = None):
    """安全監控裝飾器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 檢查 IP 是否被封鎖
            if request and security_monitor.is_ip_blocked(request.remote_addr):
                logger.warning(f"封鎖的 IP 嘗試訪問: {request.remote_addr}")
                from flask import abort
                abort(403)  # Forbidden
            
            # 記錄操作前狀態
            start_time = datetime.now()
            user = session.get('username', 'anonymous') if session else 'anonymous'
            
            try:
                # 執行原函數
                result = func(*args, **kwargs)
                
                # 記錄成功的敏感操作
                if operation_type:
                    security_monitor.log_sensitive_operation(
                        operation_type,
                        user,
                        {
                            'function': func.__name__,
                            'duration': (datetime.now() - start_time).total_seconds()
                        }
                    )
                
                return result
                
            except Exception as e:
                # 記錄異常
                security_monitor._log_suspicious_activity(
                    'OPERATION_FAILED',
                    f"操作失敗: {func.__name__}",
                    {
                        'user': user,
                        'error': str(e),
                        'operation_type': operation_type
                    }
                )
                raise
        
        return wrapper
    return decorator

def setup_security_monitoring(app):
    """初始化安全監控"""
    
    @app.before_request
    def check_security():
        """每個請求前的安全檢查"""
        if request:
            ip = request.remote_addr
            
            # 檢查 IP 是否被封鎖
            if security_monitor.is_ip_blocked(ip):
                from flask import abort
                abort(403)
            
            # 檢查訪問頻率
            user = session.get('username', f'anonymous_{ip}') if session else f'anonymous_{ip}'
            if not security_monitor.check_access_pattern(user, request.path):
                logger.warning(f"訪問頻率過高: {user} -> {request.path}")
    
    logger.info("安全監控系統已啟動")