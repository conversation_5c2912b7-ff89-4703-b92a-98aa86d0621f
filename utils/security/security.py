"""
安全性增強工具
提升系統安全性，防範常見攻擊
"""
import hashlib
import secrets
import re
from datetime import datetime, timedelta
from typing import Optional, Dict
from flask import request, session
import logging

logger = logging.getLogger(__name__)

class SecurityManager:
    """安全管理器"""
    
    # 常見的危險 SQL 關鍵字
    SQL_INJECTION_PATTERNS = [
        r'\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b',
        r'[\'";]',
        r'--',
        r'/\*.*\*/',
        r'\bor\b.*=.*\bor\b',
        r'\band\b.*=.*\band\b'
    ]
    
    # XSS 攻擊模式
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
        r'<iframe[^>]*>.*?</iframe>',
        r'<object[^>]*>.*?</object>',
        r'<embed[^>]*>.*?</embed>'
    ]
    
    @staticmethod
    def hash_password(password: str, salt: Optional[str] = None) -> tuple:
        """安全的密碼雜湊"""
        if salt is None:
            salt = secrets.token_hex(32)
        
        # 使用 PBKDF2 進行多次雜湊
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 迭代次數
        )
        
        return password_hash.hex(), salt
    
    @staticmethod
    def verify_password(password: str, password_hash: str, salt: str) -> bool:
        """驗證密碼"""
        computed_hash, _ = SecurityManager.hash_password(password, salt)
        return secrets.compare_digest(computed_hash, password_hash)
    
    @staticmethod
    def sanitize_input(input_string: str) -> str:
        """清理用戶輸入"""
        if not isinstance(input_string, str):
            return str(input_string)
        
        # 移除潛在的 XSS 攻擊代碼
        sanitized = input_string
        for pattern in SecurityManager.XSS_PATTERNS:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
        
        # HTML 實體編碼
        sanitized = (sanitized
                    .replace('&', '&amp;')
                    .replace('<', '&lt;')
                    .replace('>', '&gt;')
                    .replace('"', '&quot;')
                    .replace("'", '&#x27;'))
        
        return sanitized.strip()
    
    @staticmethod
    def check_sql_injection(input_string: str) -> bool:
        """檢查 SQL 注入攻擊"""
        if not isinstance(input_string, str):
            return False
        
        input_lower = input_string.lower()
        for pattern in SecurityManager.SQL_INJECTION_PATTERNS:
            if re.search(pattern, input_lower, re.IGNORECASE):
                logger.warning(f"檢測到潛在 SQL 注入攻擊: {input_string}")
                return True
        
        return False
    
    @staticmethod
    def validate_taiwan_tax_id(tax_id: str) -> bool:
        """驗證台灣統一編號"""
        if not tax_id or len(tax_id) != 8:
            return False
        
        if not tax_id.isdigit():
            return False
        
        # 統一編號檢查碼演算法
        weights = [1, 2, 1, 2, 1, 2, 4, 1]
        total = 0
        
        for i, digit in enumerate(tax_id):
            product = int(digit) * weights[i]
            total += product // 10 + product % 10
        
        return total % 10 == 0
    
    @staticmethod
    def generate_csrf_token() -> str:
        """生成 CSRF 令牌"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def validate_csrf_token(token: str) -> bool:
        """驗證 CSRF 令牌"""
        session_token = session.get('csrf_token')
        if not session_token:
            return False
        
        return secrets.compare_digest(token, session_token)

class RateLimiter:
    """請求頻率限制器"""
    
    def __init__(self):
        self.requests = {}  # {ip: [timestamp, ...]}
        self.blocked_ips = {}  # {ip: block_until_timestamp}
    
    def is_allowed(self, ip: str, max_requests: int = 100, window_minutes: int = 15) -> bool:
        """檢查 IP 是否允許請求"""
        current_time = datetime.now()
        
        # 檢查是否被封鎖
        if ip in self.blocked_ips:
            if current_time < self.blocked_ips[ip]:
                return False
            else:
                # 解除封鎖
                del self.blocked_ips[ip]
        
        # 清理舊的請求記錄
        window_start = current_time - timedelta(minutes=window_minutes)
        
        if ip not in self.requests:
            self.requests[ip] = []
        
        # 移除窗口外的請求
        self.requests[ip] = [
            timestamp for timestamp in self.requests[ip]
            if timestamp > window_start
        ]
        
        # 檢查請求頻率
        if len(self.requests[ip]) >= max_requests:
            # 封鎖 IP 30 分鐘
            self.blocked_ips[ip] = current_time + timedelta(minutes=30)
            logger.warning(f"IP {ip} 因請求過於頻繁被封鎖")
            return False
        
        # 記錄當前請求
        self.requests[ip].append(current_time)
        return True
    
    def get_stats(self) -> Dict:
        """獲取頻率限制統計"""
        current_time = datetime.now()
        active_blocks = sum(1 for block_time in self.blocked_ips.values() 
                           if block_time > current_time)
        
        return {
            'total_ips_tracked': len(self.requests),
            'currently_blocked': active_blocks,
            'total_requests_last_hour': sum(
                len([t for t in timestamps if t > current_time - timedelta(hours=1)])
                for timestamps in self.requests.values()
            )
        }

class InputValidator:
    """輸入驗證器"""
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """驗證電子郵件格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """驗證台灣手機號碼"""
        # 台灣手機號碼格式：09xxxxxxxx
        pattern = r'^09\d{8}$'
        return bool(re.match(pattern, phone))
    
    @staticmethod
    def validate_amount(amount: str) -> bool:
        """驗證金額格式"""
        try:
            value = float(amount)
            return value >= 0 and value <= 999999999  # 最大9億
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_date(date_string: str) -> bool:
        """驗證日期格式 YYYY-MM-DD"""
        try:
            datetime.strptime(date_string, '%Y-%m-%d')
            return True
        except ValueError:
            return False
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理檔案名稱"""
        # 移除危險字符
        sanitized = re.sub(r'[<>:"/\\|?*]', '', filename)
        
        # 限制長度
        if len(sanitized) > 255:
            import os
            name, ext = os.path.splitext(sanitized)
            sanitized = name[:255-len(ext)] + ext
        
        return sanitized

# 全域實例
rate_limiter = RateLimiter()

def require_csrf_token(func):
    """CSRF 保護裝飾器"""
    from functools import wraps
    from flask import abort
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
            token = request.form.get('csrf_token') or request.headers.get('X-CSRF-Token')
            if not token or not SecurityManager.validate_csrf_token(token):
                logger.warning(f"CSRF 攻擊嘗試，IP: {request.remote_addr}")
                abort(403)
        
        return func(*args, **kwargs)
    
    return wrapper

def rate_limit(max_requests: int = 100, window_minutes: int = 15):
    """請求頻率限制裝飾器"""
    from functools import wraps
    from flask import abort
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            client_ip = request.remote_addr or 'unknown'
            
            if not rate_limiter.is_allowed(client_ip, max_requests, window_minutes):
                logger.warning(f"請求頻率超限，IP: {client_ip}")
                abort(429)  # Too Many Requests
            
            return func(*args, **kwargs)
        
        return wrapper
    
    return decorator