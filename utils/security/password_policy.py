"""
密碼安全策略模組
實施密碼複雜度要求、歷史記錄和安全驗證
"""
import re
import hashlib
from typing import List, Tuple, Optional
from datetime import datetime, timedelta
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey
from database import get_db
from model import Base

# 常見弱密碼清單
COMMON_WEAK_PASSWORDS = {
    'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
    'admin', 'root', '12345678', '1234567890', 'password1', 'welcome',
    '123123', 'admin123', 'user', 'guest', 'test', 'demo', '111111',
    '000000', '888888', '666666', '123321', '654321', 'qwerty123',
    'abcd1234', 'letmein', 'monkey', 'dragon', 'sunshine', 'master'
}

class PasswordHistory(Base):
    """密碼歷史記錄表"""
    __tablename__ = 'password_history'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now, index=True)

class PasswordPolicy:
    """密碼策略管理類"""
    
    def __init__(self):
        self.min_length = 8
        self.max_length = 128
        self.require_uppercase = True
        self.require_lowercase = True
        self.require_digits = True
        self.require_special = True
        self.max_history_count = 5  # 記住最近5個密碼
        self.password_expiry_days = 90
        self.min_password_age_days = 1  # 密碼最短使用期限
        
        # 特殊字符定義
        self.special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    
    def validate_password(self, password: str, username: str = "", user_id: int = None) -> Tuple[bool, List[str]]:
        """
        驗證密碼是否符合安全策略
        
        Returns:
            Tuple[bool, List[str]]: (是否有效, 錯誤訊息列表)
        """
        errors = []
        
        # 1. 長度檢查
        if len(password) < self.min_length:
            errors.append(f"密碼長度至少需要 {self.min_length} 個字符")
        elif len(password) > self.max_length:
            errors.append(f"密碼長度不能超過 {self.max_length} 個字符")
        
        # 2. 複雜度檢查
        if self.require_uppercase and not re.search(r'[A-Z]', password):
            errors.append("密碼必須包含至少一個大寫字母")
        
        if self.require_lowercase and not re.search(r'[a-z]', password):
            errors.append("密碼必須包含至少一個小寫字母")
        
        if self.require_digits and not re.search(r'\d', password):
            errors.append("密碼必須包含至少一個數字")
        
        if self.require_special and not re.search(f'[{re.escape(self.special_chars)}]', password):
            errors.append(f"密碼必須包含至少一個特殊字符 ({self.special_chars})")
        
        # 3. 不能包含用戶名
        if username and username.lower() in password.lower():
            errors.append("密碼不能包含用戶名")
        
        # 4. 常見弱密碼檢查
        if password.lower() in COMMON_WEAK_PASSWORDS:
            errors.append("該密碼過於常見，請選擇更安全的密碼")
        
        # 5. 連續字符檢查
        if self._has_sequential_chars(password):
            errors.append("密碼不能包含連續的字符（如：123、abc、qwerty）")
        
        # 6. 重複字符檢查
        if self._has_repeated_chars(password):
            errors.append("密碼不能包含過多重複字符")
        
        # 7. 歷史密碼檢查
        if user_id and self._is_password_in_history(password, user_id):
            errors.append(f"不能重複使用最近 {self.max_history_count} 次使用過的密碼")
        
        return len(errors) == 0, errors
    
    def _has_sequential_chars(self, password: str) -> bool:
        """檢查是否包含連續字符"""
        # 檢查數字連續
        for i in range(len(password) - 2):
            if password[i:i+3].isdigit():
                nums = [int(c) for c in password[i:i+3]]
                if nums[1] == nums[0] + 1 and nums[2] == nums[1] + 1:
                    return True
        
        # 檢查字母連續
        for i in range(len(password) - 2):
            if password[i:i+3].isalpha():
                chars = password[i:i+3].lower()
                if ord(chars[1]) == ord(chars[0]) + 1 and ord(chars[2]) == ord(chars[1]) + 1:
                    return True
        
        # 檢查鍵盤連續
        keyboard_sequences = [
            'qwerty', 'asdf', 'zxcv', '123456', '098765',
            'qwertyuiop', 'asdfghjkl', 'zxcvbnm'
        ]
        password_lower = password.lower()
        for seq in keyboard_sequences:
            for i in range(len(seq) - 2):
                if seq[i:i+3] in password_lower:
                    return True
        
        return False
    
    def _has_repeated_chars(self, password: str) -> bool:
        """檢查是否有過多重複字符"""
        # 檢查是否有超過3個相同字符
        for char in set(password):
            if password.count(char) > 3:
                return True
        
        # 檢查連續重複
        for i in range(len(password) - 2):
            if password[i] == password[i+1] == password[i+2]:
                return True
        
        return False
    
    def _is_password_in_history(self, password: str, user_id: int) -> bool:
        """檢查密碼是否在歷史記錄中"""
        from werkzeug.security import check_password_hash
        
        try:
            with get_db() as db:
                # 獲取用戶最近的密碼記錄
                history_records = db.query(PasswordHistory).filter(
                    PasswordHistory.user_id == user_id
                ).order_by(
                    PasswordHistory.created_at.desc()
                ).limit(self.max_history_count).all()
                
                # 檢查新密碼是否與歷史密碼匹配
                for record in history_records:
                    if check_password_hash(record.password_hash, password):
                        return True
        except Exception as e:
            # 如果檢查失敗，為了安全起見，假設不重複
            print(f"密碼歷史檢查錯誤: {e}")
        
        return False
    
    def add_password_to_history(self, user_id: int, password_hash: str):
        """將密碼添加到歷史記錄"""
        try:
            with get_db() as db:
                # 添加新記錄
                history_record = PasswordHistory(
                    user_id=user_id,
                    password_hash=password_hash
                )
                db.add(history_record)
                
                # 清理舊記錄，只保留最新的 max_history_count 個
                old_records = db.query(PasswordHistory).filter(
                    PasswordHistory.user_id == user_id
                ).order_by(
                    PasswordHistory.created_at.desc()
                ).offset(self.max_history_count).all()
                
                for record in old_records:
                    db.delete(record)
                
                db.commit()
        except Exception as e:
            print(f"添加密碼歷史記錄失敗: {e}")
    
    def is_password_expired(self, last_password_change: datetime) -> bool:
        """檢查密碼是否過期"""
        if not last_password_change:
            return True  # 如果沒有記錄，視為過期
        
        expiry_date = last_password_change + timedelta(days=self.password_expiry_days)
        return datetime.now() > expiry_date
    
    def days_until_expiry(self, last_password_change: datetime) -> int:
        """計算密碼過期前的剩餘天數"""
        if not last_password_change:
            return 0
        
        expiry_date = last_password_change + timedelta(days=self.password_expiry_days)
        remaining = expiry_date - datetime.now()
        return max(0, remaining.days)
    
    def can_change_password(self, last_password_change: datetime) -> bool:
        """檢查是否可以更改密碼（最短使用期限）"""
        if not last_password_change:
            return True
        
        min_age_date = last_password_change + timedelta(days=self.min_password_age_days)
        return datetime.now() >= min_age_date
    
    def generate_password_strength_score(self, password: str) -> Tuple[int, str]:
        """
        計算密碼強度分數
        
        Returns:
            Tuple[int, str]: (分數 0-100, 強度描述)
        """
        score = 0
        
        # 長度分數 (40分)
        if len(password) >= 12:
            score += 40
        elif len(password) >= 8:
            score += 30
        elif len(password) >= 6:
            score += 20
        
        # 複雜度分數 (60分)
        if re.search(r'[a-z]', password):
            score += 10
        if re.search(r'[A-Z]', password):
            score += 10
        if re.search(r'\d', password):
            score += 10
        if re.search(f'[{re.escape(self.special_chars)}]', password):
            score += 15
        
        # 多樣性獎勵
        unique_chars = len(set(password))
        if unique_chars >= len(password) * 0.8:
            score += 10
        elif unique_chars >= len(password) * 0.6:
            score += 5
        
        # 懲罰項
        if password.lower() in COMMON_WEAK_PASSWORDS:
            score -= 30
        if self._has_repeated_chars(password):
            score -= 10
        if self._has_sequential_chars(password):
            score -= 15
        
        score = max(0, min(100, score))
        
        # 強度描述
        if score >= 80:
            strength = "非常強"
        elif score >= 60:
            strength = "強"
        elif score >= 40:
            strength = "中等"
        elif score >= 20:
            strength = "弱"
        else:
            strength = "非常弱"
        
        return score, strength

# 全域密碼策略實例
password_policy = PasswordPolicy()

def validate_password_strength(password: str, username: str = "", user_id: int = None) -> dict:
    """
    密碼強度驗證的便利函數
    
    Returns:
        dict: 包含驗證結果、錯誤訊息和強度分數的字典
    """
    is_valid, errors = password_policy.validate_password(password, username, user_id)
    score, strength = password_policy.generate_password_strength_score(password)
    
    return {
        'is_valid': is_valid,
        'errors': errors,
        'score': score,
        'strength': strength,
        'recommendations': _generate_password_recommendations(password, errors)
    }

def _generate_password_recommendations(password: str, errors: List[str]) -> List[str]:
    """生成密碼改善建議"""
    recommendations = []
    
    if len(password) < 8:
        recommendations.append("增加密碼長度至少 8 個字符")
    
    if not re.search(r'[A-Z]', password):
        recommendations.append("添加大寫字母")
    
    if not re.search(r'[a-z]', password):
        recommendations.append("添加小寫字母")
    
    if not re.search(r'\d', password):
        recommendations.append("添加數字")
    
    if not re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password):
        recommendations.append("添加特殊字符 (!@#$%^&* 等)")
    
    if len(recommendations) == 0:
        recommendations.append("密碼強度良好")
    
    return recommendations