"""
租戶安全機制模組
提供多租戶系統的安全保護措施
"""

import functools
import logging
from flask import g, session, request, abort, jsonify
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Query
from werkzeug.exceptions import Forbidden, Unauthorized
import hashlib
import hmac
import secrets

logger = logging.getLogger(__name__)

class TenantSecurityConfig:
    """租戶安全配置"""
    
    # 會話配置
    SESSION_TIMEOUT_MINUTES = 30
    MAX_CONCURRENT_SESSIONS = 5
    
    # 速率限制
    REQUEST_RATE_LIMIT = 100  # 每分鐘最大請求數
    LOGIN_ATTEMPT_LIMIT = 5   # 登入嘗試次數限制
    LOGIN_LOCKOUT_MINUTES = 15  # 登入鎖定時間
    
    # 資料存取
    MAX_RECORDS_PER_QUERY = 1000  # 單次查詢最大記錄數
    QUERY_TIMEOUT_SECONDS = 30    # 查詢超時時間
    
    # 審計配置
    AUDIT_SENSITIVE_OPERATIONS = True  # 審計敏感操作
    LOG_DATA_ACCESS = True             # 記錄資料存取
    
    # 加密配置
    USE_ENCRYPTION = True              # 使用加密
    ENCRYPTION_KEY_ROTATION_DAYS = 90  # 密鑰輪換週期

class TenantAccessControl:
    """租戶存取控制"""
    
    @staticmethod
    def verify_tenant_access(tenant_id: int, user_id: int) -> bool:
        """驗證用戶對租戶的存取權限"""
        from model import UserTenant
        from database import get_db
        
        try:
            with get_db() as db:
                access = db.query(UserTenant).filter_by(
                    tenant_id=tenant_id,
                    user_id=user_id,
                    is_active=True
                ).first()
                
                if access:
                    # 更新最後存取時間
                    access.last_access_at = datetime.utcnow()
                    db.commit()
                    return True
                    
                return False
                
        except Exception as e:
            logger.error(f"租戶存取驗證失敗: {e}")
            return False
    
    @staticmethod
    def get_user_tenants(user_id: int) -> List[int]:
        """獲取用戶可存取的所有租戶"""
        from model import UserTenant
        from database import get_db
        
        try:
            with get_db() as db:
                tenants = db.query(UserTenant).filter_by(
                    user_id=user_id,
                    is_active=True
                ).all()
                
                return [t.tenant_id for t in tenants]
                
        except Exception as e:
            logger.error(f"獲取用戶租戶失敗: {e}")
            return []
    
    @staticmethod
    def enforce_data_isolation(query: Query, model_class: Any) -> Query:
        """強制資料隔離"""
        tenant_id = g.get('tenant_id')
        
        if not tenant_id:
            logger.warning("嘗試存取資料但無租戶上下文")
            abort(403, description="無法存取資料：缺少租戶上下文")
        
        # 檢查模型是否支援租戶隔離
        if hasattr(model_class, 'tenant_id'):
            query = query.filter(model_class.tenant_id == tenant_id)
            
            # 添加查詢限制
            query = query.limit(TenantSecurityConfig.MAX_RECORDS_PER_QUERY)
            
        return query

class TenantAuditLogger:
    """租戶審計日誌"""
    
    @staticmethod
    def log_access(operation: str, resource: str, details: Optional[Dict] = None):
        """記錄存取日誌"""
        if not TenantSecurityConfig.LOG_DATA_ACCESS:
            return
            
        try:
            from model import AuditLog
            from database import get_db
            
            with get_db() as db:
                log_entry = AuditLog(
                    tenant_id=g.get('tenant_id'),
                    user_id=g.get('user_id'),
                    operation=operation,
                    resource=resource,
                    details=details,
                    ip_address=request.remote_addr,
                    user_agent=request.user_agent.string,
                    timestamp=datetime.utcnow()
                )
                
                db.add(log_entry)
                db.commit()
                
        except Exception as e:
            logger.error(f"審計日誌記錄失敗: {e}")
    
    @staticmethod
    def log_security_event(event_type: str, severity: str, message: str):
        """記錄安全事件"""
        logger.warning(f"[SECURITY-{severity}] {event_type}: {message}")
        
        # 同時記錄到資料庫
        TenantAuditLogger.log_access(
            operation=f"SECURITY_{event_type}",
            resource="SYSTEM",
            details={
                'severity': severity,
                'message': message,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

class TenantSessionManager:
    """租戶會話管理"""
    
    @staticmethod
    def create_secure_session(user_id: int, tenant_id: int) -> str:
        """創建安全會話"""
        session_token = secrets.token_urlsafe(32)
        
        session['user_id'] = user_id
        session['tenant_id'] = tenant_id
        session['created_at'] = datetime.utcnow().isoformat()
        session['last_activity'] = datetime.utcnow().isoformat()
        session['session_token'] = session_token
        
        # 設置會話過期
        session.permanent = True
        
        TenantAuditLogger.log_access(
            operation="SESSION_CREATE",
            resource=f"tenant_{tenant_id}",
            details={'user_id': user_id}
        )
        
        return session_token
    
    @staticmethod
    def validate_session() -> bool:
        """驗證會話有效性"""
        if 'user_id' not in session or 'tenant_id' not in session:
            return False
        
        # 檢查會話超時
        if 'last_activity' in session:
            last_activity = datetime.fromisoformat(session['last_activity'])
            timeout = timedelta(minutes=TenantSecurityConfig.SESSION_TIMEOUT_MINUTES)
            
            if datetime.utcnow() - last_activity > timeout:
                TenantAuditLogger.log_security_event(
                    "SESSION_TIMEOUT",
                    "INFO",
                    f"會話超時: user_id={session.get('user_id')}"
                )
                session.clear()
                return False
        
        # 更新活動時間
        session['last_activity'] = datetime.utcnow().isoformat()
        
        # 設置全局變量
        g.user_id = session['user_id']
        g.tenant_id = session['tenant_id']
        
        return True
    
    @staticmethod
    def destroy_session():
        """銷毀會話"""
        if 'user_id' in session:
            TenantAuditLogger.log_access(
                operation="SESSION_DESTROY",
                resource=f"tenant_{session.get('tenant_id')}",
                details={'user_id': session['user_id']}
            )
        
        session.clear()

class TenantRateLimiter:
    """租戶速率限制"""
    
    # 記憶體中的速率限制緩存
    _rate_limits: Dict[str, List[datetime]] = {}
    
    @classmethod
    def check_rate_limit(cls, identifier: str, limit: int = None, window_minutes: int = 1) -> bool:
        """檢查速率限制"""
        if limit is None:
            limit = TenantSecurityConfig.REQUEST_RATE_LIMIT
        
        now = datetime.utcnow()
        window_start = now - timedelta(minutes=window_minutes)
        
        # 獲取或創建請求記錄
        if identifier not in cls._rate_limits:
            cls._rate_limits[identifier] = []
        
        # 清理過期記錄
        cls._rate_limits[identifier] = [
            timestamp for timestamp in cls._rate_limits[identifier]
            if timestamp > window_start
        ]
        
        # 檢查是否超過限制
        if len(cls._rate_limits[identifier]) >= limit:
            TenantAuditLogger.log_security_event(
                "RATE_LIMIT_EXCEEDED",
                "WARNING",
                f"速率限制超過: {identifier}"
            )
            return False
        
        # 記錄新請求
        cls._rate_limits[identifier].append(now)
        return True
    
    @classmethod
    def apply_rate_limit(cls, limit: int = None):
        """應用速率限制裝飾器"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 使用租戶ID和用戶ID作為標識符
                identifier = f"{g.get('tenant_id', 'unknown')}_{g.get('user_id', 'unknown')}"
                
                if not cls.check_rate_limit(identifier, limit):
                    abort(429, description="請求過於頻繁，請稍後再試")
                
                return func(*args, **kwargs)
            
            return wrapper
        return decorator

class TenantDataEncryption:
    """租戶資料加密"""
    
    @staticmethod
    def encrypt_sensitive_data(data: str, tenant_id: int) -> str:
        """加密敏感資料"""
        if not TenantSecurityConfig.USE_ENCRYPTION:
            return data
        
        try:
            # 這裡應該使用真正的加密庫，如cryptography
            # 簡化示例：使用HMAC進行簽名
            key = f"tenant_{tenant_id}_secret_key".encode()
            signature = hmac.new(key, data.encode(), hashlib.sha256).hexdigest()
            return f"{data}:{signature}"
            
        except Exception as e:
            logger.error(f"資料加密失敗: {e}")
            return data
    
    @staticmethod
    def decrypt_sensitive_data(encrypted_data: str, tenant_id: int) -> str:
        """解密敏感資料"""
        if not TenantSecurityConfig.USE_ENCRYPTION:
            return encrypted_data
        
        try:
            # 簡化示例：驗證HMAC簽名
            if ':' in encrypted_data:
                data, signature = encrypted_data.rsplit(':', 1)
                key = f"tenant_{tenant_id}_secret_key".encode()
                expected_signature = hmac.new(key, data.encode(), hashlib.sha256).hexdigest()
                
                if hmac.compare_digest(signature, expected_signature):
                    return data
                else:
                    logger.warning("資料簽名驗證失敗")
                    return None
            
            return encrypted_data
            
        except Exception as e:
            logger.error(f"資料解密失敗: {e}")
            return None

def secure_tenant_route(func):
    """安全租戶路由裝飾器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 驗證會話
        if not TenantSessionManager.validate_session():
            abort(401, description="未授權：無效或過期的會話")
        
        # 驗證租戶存取權限
        if not TenantAccessControl.verify_tenant_access(g.tenant_id, g.user_id):
            TenantAuditLogger.log_security_event(
                "UNAUTHORIZED_ACCESS",
                "CRITICAL",
                f"未授權存取嘗試: user_id={g.user_id}, tenant_id={g.tenant_id}"
            )
            abort(403, description="禁止：無權存取此租戶")
        
        # 應用速率限制
        identifier = f"{g.tenant_id}_{g.user_id}"
        if not TenantRateLimiter.check_rate_limit(identifier):
            abort(429, description="請求過於頻繁")
        
        # 記錄存取
        TenantAuditLogger.log_access(
            operation=f"ROUTE_ACCESS_{request.method}",
            resource=request.endpoint or request.path,
            details={
                'method': request.method,
                'path': request.path,
                'args': dict(request.args)
            }
        )
        
        return func(*args, **kwargs)
    
    return wrapper

def validate_tenant_input(data: Dict[str, Any]) -> Dict[str, Any]:
    """驗證和清理租戶輸入"""
    # 複製資料以避免修改原始資料
    cleaned_data = data.copy()
    
    # 移除潛在的危險字段
    dangerous_fields = ['tenant_id', 'user_id', 'is_admin', 'is_active']
    
    for field in dangerous_fields:
        if field in cleaned_data:
            logger.warning(f"嘗試注入危險字段: {field}")
            del cleaned_data[field]
    
    # SQL注入防護（增強版）
    for key, value in list(cleaned_data.items()):
        if isinstance(value, str):
            # 檢查並清理常見的SQL注入模式
            suspicious_patterns = [
                ('DROP', ''), ('DELETE', ''), ('UPDATE', ''), 
                ('INSERT', ''), ('--', ''), ('/*', ''), ('*/', ''),
                (';', ''), ('UNION', ''), ('SELECT', ''),
                ('FROM', ''), ('WHERE', ''), ('EXEC', ''),
                ('EXECUTE', ''), ('xp_', ''), ('sp_', '')
            ]
            
            cleaned_value = value
            for pattern, replacement in suspicious_patterns:
                if pattern in cleaned_value.upper():
                    logger.warning(f"檢測到可疑輸入模式: {pattern} in {key}")
                    # 移除危險模式（不區分大小寫）
                    import re
                    cleaned_value = re.sub(re.escape(pattern), replacement, cleaned_value, flags=re.IGNORECASE)
            
            cleaned_data[key] = cleaned_value
    
    return cleaned_data

# 導出主要功能
__all__ = [
    'TenantSecurityConfig',
    'TenantAccessControl',
    'TenantAuditLogger',
    'TenantSessionManager',
    'TenantRateLimiter',
    'TenantDataEncryption',
    'secure_tenant_route',
    'validate_tenant_input'
]