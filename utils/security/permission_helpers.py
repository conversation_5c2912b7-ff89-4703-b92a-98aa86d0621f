"""
權限管理路由輔助函數
提取重複的邏輯以減少代碼重複
"""

from flask import render_template, redirect, url_for, flash, jsonify
from database import get_db
from models.auth_models import Role, Permission
from model import User
from typing import Dict, Any, List, Optional


def convert_role_to_dict(role) -> Dict[str, Any]:
    """
    將角色模型轉換為字典
    
    Args:
        role: 角色模型實例
        
    Returns:
        角色字典資料
    """
    return {
        'id': role.id,
        'name': role.name,
        'display_name': role.display_name,
        'description': role.description,
        'is_active': role.is_active,
        'created_at': role.created_at,
        'updated_at': role.updated_at
    }


def convert_permission_to_dict(permission) -> Dict[str, Any]:
    """
    將權限模型轉換為字典
    
    Args:
        permission: 權限模型實例
        
    Returns:
        權限字典資料
    """
    return {
        'id': permission.id,
        'name': permission.name,
        'display_name': permission.display_name,
        'module': permission.module,
        'action': permission.action,
        'description': permission.description
    }


def convert_user_to_dict(user) -> Dict[str, Any]:
    """
    將用戶模型轉換為字典
    
    Args:
        user: 用戶模型實例
        
    Returns:
        用戶字典資料
    """
    return {
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'full_name': user.full_name,
        'is_active': user.is_active,
        'last_login': user.last_login,
        'created_at': user.created_at,
        'updated_at': user.updated_at
    }


def get_all_roles_data() -> List[Dict[str, Any]]:
    """
    獲取所有角色資料
    
    Returns:
        角色資料列表
    """
    with get_db() as db:
        roles = db.query(Role).filter(Role.is_active).all()
        return [convert_role_to_dict(role) for role in roles]


def get_all_permissions_data() -> List[Dict[str, Any]]:
    """
    獲取所有權限資料
    
    Returns:
        權限資料列表
    """
    with get_db() as db:
        permissions = db.query(Permission).all()
        return [convert_permission_to_dict(perm) for perm in permissions]


def get_all_users_data() -> List[Dict[str, Any]]:
    """
    獲取所有用戶資料
    
    Returns:
        用戶資料列表
    """
    with get_db() as db:
        users = db.query(User).filter(User.is_active).all()
        return [convert_user_to_dict(user) for user in users]


def get_role_by_id(role_id: int) -> Optional[Dict[str, Any]]:
    """
    根據ID獲取角色資料
    
    Args:
        role_id: 角色ID
        
    Returns:
        角色字典資料或None
    """
    with get_db() as db:
        role = db.query(Role).filter_by(id=role_id).first()
        return convert_role_to_dict(role) if role else None


def get_user_by_id(user_id: int) -> Optional[Dict[str, Any]]:
    """
    根據ID獲取用戶資料
    
    Args:
        user_id: 用戶ID
        
    Returns:
        用戶字典資料或None
    """
    with get_db() as db:
        user = db.query(User).filter_by(id=user_id).first()
        return convert_user_to_dict(user) if user else None


def flash_and_redirect(message: str, category: str, endpoint: str, **kwargs):
    """
    Flash 訊息並重導向
    
    Args:
        message: Flash 訊息
        category: 訊息類別 ('success', 'error', 'warning', 'info')
        endpoint: 重導向端點
        **kwargs: url_for 的額外參數
        
    Returns:
        重導向響應
    """
    flash(message, category)
    return redirect(url_for(endpoint, **kwargs))


def render_admin_template(template_name: str, **kwargs):
    """
    渲染管理員模板
    
    Args:
        template_name: 模板名稱
        **kwargs: 模板變數
        
    Returns:
        渲染的模板
    """
    return render_template(template_name, **kwargs)


def success_redirect(message: str, endpoint: str, **kwargs):
    """
    成功操作後的重導向
    
    Args:
        message: 成功訊息
        endpoint: 重導向端點
        **kwargs: url_for 的額外參數
        
    Returns:
        重導向響應
    """
    return flash_and_redirect(message, 'success', endpoint, **kwargs)


def error_redirect(message: str, endpoint: str, **kwargs):
    """
    錯誤操作後的重導向
    
    Args:
        message: 錯誤訊息
        endpoint: 重導向端點
        **kwargs: url_for 的額外參數
        
    Returns:
        重導向響應
    """
    return flash_and_redirect(message, 'error', endpoint, **kwargs)


def validate_required_fields(form_data: Dict[str, str], required_fields: List[str]) -> List[str]:
    """
    驗證必需欄位
    
    Args:
        form_data: 表單資料
        required_fields: 必需欄位列表
        
    Returns:
        錯誤訊息列表
    """
    errors = []
    for field in required_fields:
        if not form_data.get(field, '').strip():
            errors.append(f'{field} 為必填項')
    return errors


def create_api_response(success: bool = True, data: Any = None, message: str = '') -> Dict[str, Any]:
    """
    創建標準化的API響應
    
    Args:
        success: 是否成功
        data: 響應資料
        message: 響應訊息
        
    Returns:
        API響應字典
    """
    response = {'success': success}
    if data is not None:
        response['data'] = data
    if message:
        response['message'] = message
    return response


def api_success(data: Any = None, message: str = ''):
    """
    創建成功的API響應
    
    Args:
        data: 響應資料
        message: 成功訊息
        
    Returns:
        JSON響應
    """
    return jsonify(create_api_response(True, data, message))


def api_error(message: str, data: Any = None):
    """
    創建錯誤的API響應
    
    Args:
        message: 錯誤訊息
        data: 響應資料
        
    Returns:
        JSON響應
    """
    return jsonify(create_api_response(False, data, message))


def handle_form_submission(form_data: Dict[str, str], 
                         required_fields: List[str],
                         success_callback,
                         success_message: str,
                         success_endpoint: str,
                         error_template: str,
                         **template_kwargs):
    """
    處理表單提交的通用邏輯
    
    Args:
        form_data: 表單資料
        required_fields: 必需欄位
        success_callback: 成功時的回調函數
        success_message: 成功訊息
        success_endpoint: 成功時重導向的端點
        error_template: 錯誤時渲染的模板
        **template_kwargs: 模板變數
        
    Returns:
        重導向或模板響應
    """
    # 驗證必需欄位
    errors = validate_required_fields(form_data, required_fields)
    if errors:
        for error in errors:
            flash(error, 'error')
        return render_admin_template(error_template, **template_kwargs)
    
    try:
        # 執行成功回調
        success_callback(form_data)
        return success_redirect(success_message, success_endpoint)
    except Exception as e:
        return error_redirect(f'操作失敗: {str(e)}', success_endpoint)


class PermissionDataManager:
    """權限資料管理器"""
    
    @staticmethod
    def get_system_overview_data() -> Dict[str, Any]:
        """
        獲取系統概覽資料
        
        Returns:
            系統概覽資料字典
        """
        with get_db() as db:
            return {
                'roles': [convert_role_to_dict(role) for role in db.query(Role).filter(Role.is_active).all()],
                'permissions': [convert_permission_to_dict(perm) for perm in db.query(Permission).all()],
                'users': [convert_user_to_dict(user) for user in db.query(User).filter(User.is_active).all()]
            }
    
    @staticmethod
    def get_role_permissions_data(role_id: int) -> Dict[str, Any]:
        """
        獲取角色權限資料
        
        Args:
            role_id: 角色ID
            
        Returns:
            角色權限資料字典
        """
        with get_db() as db:
            role = db.query(Role).filter_by(id=role_id).first()
            if not role:
                return {}
            
            all_permissions = db.query(Permission).all()
            role_permission_ids = {p.id for p in role.permissions}
            
            permissions_data = []
            for perm in all_permissions:
                perm_data = convert_permission_to_dict(perm)
                perm_data['assigned'] = perm.id in role_permission_ids
                permissions_data.append(perm_data)
            
            return {
                'role': convert_role_to_dict(role),
                'permissions': permissions_data
            }
    
    @staticmethod
    def get_user_roles_data(user_id: int) -> Dict[str, Any]:
        """
        獲取用戶角色資料
        
        Args:
            user_id: 用戶ID
            
        Returns:
            用戶角色資料字典
        """
        with get_db() as db:
            user = db.query(User).filter_by(id=user_id).first()
            if not user:
                return {}
            
            all_roles = db.query(Role).filter(Role.is_active).all()
            user_role_ids = {r.id for r in user.roles}
            
            roles_data = []
            for role in all_roles:
                role_data = convert_role_to_dict(role)
                role_data['assigned'] = role.id in user_role_ids
                roles_data.append(role_data)
            
            return {
                'user': convert_user_to_dict(user),
                'roles': roles_data
            }


def safe_file_operation(operation_callback, success_message: str, error_message: str, redirect_endpoint: str):
    """
    安全的檔案操作包裝器
    
    Args:
        operation_callback: 檔案操作回調函數
        success_message: 成功訊息
        error_message: 錯誤訊息前綴
        redirect_endpoint: 重導向端點
        
    Returns:
        重導向響應
    """
    try:
        operation_callback()
        return success_redirect(success_message, redirect_endpoint)
    except FileNotFoundError:
        return error_redirect('檔案不存在', redirect_endpoint)
    except PermissionError:
        return error_redirect('檔案權限不足', redirect_endpoint)
    except Exception as e:
        return error_redirect(f'{error_message}: {str(e)}', redirect_endpoint)