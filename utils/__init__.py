"""
工具模組統一入口
已重新組織為功能分類的子模組

使用方式:
    # 直接從子模組導入
    from utils.database import MoneyQueryHelper
    from utils.performance import CacheManager
    from utils.security.security import require_login
    from utils.business import parse_date
    from utils.web import debug_log
    
    # 或者導入整個子模組
    from utils import database, performance, security, business, web, logging
"""

# 為了向後相容性，可以保留一些常用的直接導入
# 但建議逐步遷移到新的模組結構

# 常用的資料庫工具
from .database import MoneyQueryHelper

# 常用的業務工具
from .business import parse_date, parse_date_range, get_current_user

# 常用的 Web 工具
from .web import debug_log, is_debug_mode

# 常用的格式化工具
from .format_helper import format_money, format_date, format_taiwan_date

# 資料庫維護工具
from .db_maintenance import init_database_maintenance, DatabaseMaintenanceManager

# 常用的安全工具
from .security import login_required

__version__ = "2.0.0"  # 重構版本

__all__ = [
    # 向後相容的常用導入
    'MoneyQueryHelper',
    'parse_date', 
    'parse_date_range',
    'get_current_user',
    'debug_log',
    'is_debug_mode',
    'format_money',
    'format_date',
    'format_taiwan_date',
    'init_database_maintenance',
    'DatabaseMaintenanceManager',
    'login_required'
]