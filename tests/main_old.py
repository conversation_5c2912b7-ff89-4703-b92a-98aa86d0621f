from flask import Flask, render_template, request, redirect, url_for
from menu_data import menu
from sqlalchemy.orm import sessionmaker, joinedload
from model import engine,AccountSubject,CompanyInfo  # 假設 engine 在 model.py 裡
from collections import OrderedDict

Session = sessionmaker(bind=engine)

app = Flask(__name__)

@app.route('/')
def index():
    main_menu = list(menu.keys())
    selected = request.args.get('main', main_menu[0])
    submenus = menu[selected]
    submenu_title = request.args.get('submenu')
    button_label = request.args.get('button')
    selected_buttons = None
    if submenu_title and button_label:
        # 找到該submenu下的button
        for submenu in submenus:
            if submenu['title'] == submenu_title:
                for btn in submenu['buttons']:
                    if btn['label'] == button_label:
                        selected_buttons = btn['children']
                        break
    return render_template('index.html', sidebar_items=main_menu, submenus=submenus, selected=selected, submenu_title=submenu_title, button_label=button_label, selected_buttons=selected_buttons)

@app.route('/income_record')
def income_record():
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    return render_template('income_record.html', sidebar_items=main_menu, selected=selected)

@app.route('/expense_record')
def expense_record():
    return render_template('expense_record.html')

@app.route('/income_list')
def income_list():
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    return render_template('income_list.html', sidebar_items=main_menu, selected=selected)

@app.route('/expense_list')
def expense_list():
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    return render_template('expense_list.html', sidebar_items=main_menu, selected=selected)

@app.route('/ac_delay_list')
def ac_delay_list():
    main_menu = list(menu.keys())
    selected = '收支帳簿'
    return render_template('ac_delay_list.html', sidebar_items=main_menu, selected=selected)

@app.route('/voucher_manage')
def voucher_manage():
    main_menu = list(menu.keys())
    selected = '會計科目'
    return render_template('voucher_manage.html', sidebar_items=main_menu, selected=selected)

@app.route('/subject_manage')
def subject_manage():
    main_menu = list(menu.keys())
    selected = '會計科目'
    db = Session()
    # 固定大分類順序
    category_order = [
        '資產','負債','權益', '營業收入', '營業成本', '營業費用', '營業外收益及費損', '所得稅'
    ]
    # 查詢所有科目，預先載入 children
    all_subjects = db.query(AccountSubject).options(joinedload(AccountSubject.children)).all()
    # for subj in all_subjects:
    #     print(f'id={subj.id}, code={subj.code}, children={[c.code for c in subj.children]}')
    db.close()
    # 依 top_category 分組
    category_dict = OrderedDict()
    for cat in category_order:
        category_dict[cat] = []
    for subj in all_subjects:
        cat = subj.top_category or '其他'
        if cat in category_dict:
            if subj.parent_id is None:
                category_dict[cat].append(subj)
    return render_template('subject_manage.html', sidebar_items=main_menu, selected=selected, category_dict=category_dict)

@app.route('/cost_transfer')
def cost_transfer():
    main_menu = list(menu.keys())
    selected = '會計科目'
    return render_template('cost_transfer.html', sidebar_items=main_menu, selected=selected)

@app.route('/tax_manage')
def tax_manage():
    main_menu = list(menu.keys())
    selected = '會計科目'
    return render_template('tax_manage.html', sidebar_items=main_menu, selected=selected)

@app.route('/add_prepaid_expense', methods=['GET', 'POST'])
def add_prepaid_expense():
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        # data = request.form
        # 儲存資料或其他處理
        pass
    main_menu = list(menu.keys())
    selected = '資產管理'
    return render_template('add_prepaid_expense.html', sidebar_items=main_menu, selected=selected)

@app.route('/add_amortization', methods=['GET', 'POST'])
def add_amortization():
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    main_menu = list(menu.keys())
    selected = '資產管理'
    return render_template('add_amortization.html', sidebar_items=main_menu, selected=selected)

@app.route('/add_fixed_asset', methods=['GET', 'POST'])
def add_fixed_asset():
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    main_menu = list(menu.keys())
    selected = '資產管理'
    return render_template('add_fixed_asset.html', sidebar_items=main_menu, selected=selected)

@app.route('/add_intangible_asset', methods=['GET', 'POST'])
def add_intangible_asset():
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    main_menu = list(menu.keys())
    selected = '資產管理'
    return render_template('add_intangible_asset.html', sidebar_items=main_menu, selected=selected)

@app.route('/asset_list', methods=['GET'])
def asset_list():
    main_menu = list(menu.keys())
    selected = '資產管理'
    return render_template('asset_list.html', sidebar_items=main_menu, selected=selected)

@app.route('/payroll_process', methods=['GET', 'POST'])
def payroll_process():
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    main_menu = list(menu.keys())
    selected = '薪資報酬'
    return render_template('payroll_process.html', sidebar_items=main_menu, selected=selected)

@app.route('/insurance_manage', methods=['GET', 'POST'])
def insurance_manage():
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    main_menu = list(menu.keys())
    selected = '薪資報酬'
    return render_template('insurance_manage.html', sidebar_items=main_menu, selected=selected)

@app.route('/add_employee', methods=['GET', 'POST'])
def add_employee():
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    main_menu = list(menu.keys())
    selected = '薪資報酬'
    return render_template('add_employee.html', sidebar_items=main_menu, selected=selected)

@app.route('/employee_list', methods=['GET'])
def employee_list():
    main_menu = list(menu.keys())
    selected = '薪資報酬'
    return render_template('employee_list.html', sidebar_items=main_menu, selected=selected)

@app.route('/company_setting', methods=['GET'])
def company_setting():
    main_menu = list(menu.keys())
    selected = '薪資報酬'  # 修正：公司設定在薪資報酬選單下
    return render_template('company_setting.html', sidebar_items=main_menu, selected=selected)

@app.route('/salary_setting', methods=['GET'])
def salary_setting():
    main_menu = list(menu.keys())
    selected = '薪資報酬'  # 修正：薪資設定也在薪資報酬選單下
    return render_template('salary_setting.html', sidebar_items=main_menu, selected=selected)

@app.route('/service_reward_list', methods=['GET'])
def service_reward_list():
    main_menu = list(menu.keys())
    selected = '勞務報酬'
    return render_template('service_reward_list.html', sidebar_items=main_menu, selected=selected)

@app.route('/add_service_reward', methods=['GET', 'POST'])
def add_service_reward():
    if request.method == 'POST':
        # 這裡可以處理表單送出的資料
        pass
    main_menu = list(menu.keys())
    selected = '勞務報酬'
    return render_template('add_service_reward.html', sidebar_items=main_menu, selected=selected)

@app.route('/withholding_declare', methods=['GET'])
def withholding_declare():
    main_menu = list(menu.keys())
    selected = '扣繳申報'
    return render_template('withholding_declare.html', sidebar_items=main_menu, selected=selected)

@app.route('/department_manage', methods=['GET'])
def department_manage():
    main_menu = list(menu.keys())
    selected = '設定'
    return render_template('department_manage.html', sidebar_items=main_menu, selected=selected)

@app.route('/basic_info', methods=['GET', 'POST'])
def basic_info():
    main_menu = list(menu.keys())
    selected = '設定'
    
    # 台灣稅徵機關列表
    tax_offices = [
        '財政部北區國稅局',
        '財政部北區國稅局臺北分局',
        '財政部北區國稅局板橋分局',
        '財政部北區國稅局桃園分局',
        '財政部北區國稅局新竹分局',
        '財政部北區國稅局基隆分局',
        '財政部北區國稅局宜蘭分局',
        '財政部北區國稅局花蓮分局',
        '財政部北區國稅局金門分局',
        '財政部北區國稅局馬祖分局',
        '財政部中區國稅局',
        '財政部中區國稅局臺中分局',
        '財政部中區國稅局豐原分局',
        '財政部中區國稅局大屯分局',
        '財政部中區國稅局沙鹿分局',
        '財政部中區國稅局彰化分局',
        '財政部中區國稅局員林分局',
        '財政部中區國稅局南投分局',
        '財政部中區國稅局埔里分局',
        '財政部中區國稅局竹山分局',
        '財政部中區國稅局雲林分局',
        '財政部中區國稅局虎尾分局',
        '財政部中區國稅局北港分局',
        '財政部南區國稅局',
        '財政部南區國稅局臺南分局',
        '財政部南區國稅局新化分局',
        '財政部南區國稅局新營分局',
        '財政部南區國稅局嘉義分局',
        '財政部南區國稅局民雄分局',
        '財政部南區國稅局朴子分局',
        '財政部南區國稅局屏東分局',
        '財政部南區國稅局潮州分局',
        '財政部南區國稅局東港分局',
        '財政部南區國稅局恆春分局',
        '財政部南區國稅局臺東分局',
        '財政部南區國稅局成功分局',
        '財政部南區國稅局關山分局',
        '財政部南區國稅局澎湖分局',
        '財政部南區國稅局馬公分局',
        '財政部高雄國稅局',
        '財政部高雄國稅局三民分局',
        '財政部高雄國稅局新興分局',
        '財政部高雄國稅局前鎮分局',
        '財政部高雄國稅局苓雅分局',
        '財政部高雄國稅局小港分局',
        '財政部高雄國稅局楠梓分局',
        '財政部高雄國稅局岡山分局',
        '財政部高雄國稅局鳳山分局',
        '財政部高雄國稅局大寮分局',
        '財政部高雄國稅局林園分局',
        '財政部高雄國稅局旗山分局',
        '財政部高雄國稅局美濃分局',
        '財政部高雄國稅局路竹分局',
        '財政部高雄國稅局湖內分局',
        '財政部高雄國稅局永安分局',
        '財政部高雄國稅局彌陀分局',
        '財政部高雄國稅局梓官分局',
        '財政部高雄國稅局橋頭分局',
        '財政部高雄國稅局燕巢分局',
        '財政部高雄國稅局田寮分局',
        '財政部高雄國稅局阿蓮分局',
        '財政部高雄國稅局茄萣分局',
        '財政部高雄國稅局桃源分局',
        '財政部高雄國稅局那瑪夏分局',
        '財政部高雄國稅局茂林分局',
        '財政部高雄國稅局六龜分局',
        '財政部高雄國稅局甲仙分局',
        '財政部高雄國稅局杉林分局',
        '財政部高雄國稅局內門分局',
        '財政部高雄國稅局旗山分局',
        '財政部高雄國稅局美濃分局',
        '財政部高雄國稅局路竹分局',
        '財政部高雄國稅局湖內分局',
        '財政部高雄國稅局永安分局',
        '財政部高雄國稅局彌陀分局',
        '財政部高雄國稅局梓官分局',
        '財政部高雄國稅局橋頭分局',
        '財政部高雄國稅局燕巢分局',
        '財政部高雄國稅局田寮分局',
        '財政部高雄國稅局阿蓮分局',
        '財政部高雄國稅局茄萣分局',
        '財政部高雄國稅局桃源分局',
        '財政部高雄國稅局那瑪夏分局',
        '財政部高雄國稅局茂林分局',
        '財政部高雄國稅局六龜分局',
        '財政部高雄國稅局甲仙分局',
        '財政部高雄國稅局杉林分局',
        '財政部高雄國稅局內門分局'
    ]
    
    db = Session()
    company_info = db.query(CompanyInfo).first()
    
    if request.method == 'POST':
        # 處理表單提交
        if company_info:
            # 更新現有資料
            company_info.company_name = request.form.get('company_name', '')
            company_info.company_id = request.form.get('company_id', '')
            company_info.owner_name = request.form.get('owner_name', '')
            company_info.owner_phone = request.form.get('owner_phone', '')
            company_info.email = request.form.get('email', '')
            company_info.tax_office = request.form.get('tax_office', '')
            company_info.address = request.form.get('address', '')
            company_info.contact_name = request.form.get('contact_name', '')
            company_info.contact_phone = request.form.get('contact_phone', '')
            company_info.tax_id = request.form.get('tax_id', '')
        else:
            # 建立新資料
            company_info = CompanyInfo(
                company_name=request.form.get('company_name', ''),
                company_id=request.form.get('company_id', ''),
                owner_name=request.form.get('owner_name', ''),
                owner_phone=request.form.get('owner_phone', ''),
                email=request.form.get('email', ''),
                tax_office=request.form.get('tax_office', ''),
                address=request.form.get('address', ''),
                contact_name=request.form.get('contact_name', ''),
                contact_phone=request.form.get('contact_phone', ''),
                tax_id=request.form.get('tax_id', '')
            )
            db.add(company_info)
        
        db.commit()
        db.close()
        return redirect(url_for('basic_info'))
    
    db.close()
    return render_template('basic_info.html', company_info=company_info, tax_offices=tax_offices, sidebar_items=main_menu, selected=selected)

@app.route('/account_setting', methods=['GET'])
def account_setting():
    main_menu = list(menu.keys())
    selected = '設定'
    tab = request.args.get('tab', '現金')
    return render_template('account_setting.html', sidebar_items=main_menu, selected=selected, tab=tab)

@app.route('/opening_setting', methods=['GET'])
def opening_setting():
    main_menu = list(menu.keys())
    selected = '設定'
    return render_template('opening_setting.html', sidebar_items=main_menu, selected=selected)

@app.route('/add_subject', methods=['GET', 'POST'])
def add_subject():
    if request.method == 'POST':
        sub_name = request.form.get('sub_name')
        sub_code = request.form.get('sub_code')
        sub_note = request.form.get('sub_note')
        parent_code = request.form.get('parent_code')
        db = Session()
        parent = None
        full_code = sub_code
        if parent_code:
            parent = db.query(AccountSubject).filter_by(code=parent_code).first()
            if parent:
                full_code = f"{parent.code}{sub_code}"
                top_category = parent.top_category
            else:
                top_category = None
        else:
            top_category = None
        new_subject = AccountSubject(name=sub_name, code=full_code, note=sub_note, parent_id=parent.id if parent else None, top_category=top_category)
        db.add(new_subject)
        db.commit()
        db.close()
        return redirect(url_for('subject_manage'))
    else:
        parent_code = request.args.get('parent_code')
        parent_info = None
        next_sub_code = '001'
        main_menu = list(menu.keys())
        selected = '會計科目'
        if parent_code:
            db = Session()
            parent_info = db.query(AccountSubject).filter_by(code=parent_code).first()
            # 計算下一個可用的三碼子科目代碼
            if parent_info:
                used_codes = set()
                for child in parent_info.children:
                    try:
                        code_int = int(child.code[-3:])  # 只取最後三碼
                        used_codes.add(code_int)
                    except ValueError:
                        pass
                for i in range(1, 1000):
                    code_str = f"{i:03d}"
                    if int(code_str) not in used_codes:
                        next_sub_code = code_str
                        break
            db.close()
        return render_template('add_subject.html', parent_info=parent_info, next_sub_code=next_sub_code, sidebar_items=main_menu, selected=selected)

@app.route('/edit_subject', methods=['GET', 'POST'])
def edit_subject():
    code = request.args.get('code') if request.method == 'GET' else request.form.get('code')
    db = Session()
    subject = db.query(AccountSubject).filter_by(code=code).first()
    main_menu = list(menu.keys())
    selected = '會計科目'
    if not subject:
        db.close()
        return '找不到該科目', 404
    if request.method == 'POST':
        subject.name = request.form.get('sub_name')
        subject.note = request.form.get('sub_note')
        db.commit()
        db.close()
        return redirect(url_for('subject_manage'))
    db.close()
    return render_template('edit_subject.html', subject=subject, sidebar_items=main_menu, selected=selected)

@app.route('/delete_subject', methods=['POST'])
def delete_subject():
    code = request.form.get('code')
    db = Session()
    subject = db.query(AccountSubject).filter_by(code=code).first()
    if subject:
        db.delete(subject)
        db.commit()
        print(f"Deleted subject: {subject.name} ({subject.code})")
    db.close()
    return redirect(url_for('subject_manage'))

if __name__ == '__main__':
    app.run(debug=True) 