"""整合測試"""
from model import AccountSubject, Account


class TestAccountSubjectIntegration:
    """會計科目整合測試"""
    
    def test_complete_subject_workflow(self, client, db_session):
        """測試完整的科目管理流程"""
        # 1. 訪問科目管理頁面
        response = client.get('/accounting/subject_manage')
        assert response.status_code == 200
        
        # 2. 新增主科目
        response = client.post('/accounting/add_subject', data={
            'sub_name': '測試資產',
            'sub_code': '9',
            'sub_style': '資產',
            'sub_note': '測試用主科目',
            'parent_code': '',
            'top_category': '資產'
        }, follow_redirects=True)
        assert response.status_code == 200
        
        # 3. 新增子科目
        response = client.post('/accounting/add_subject', data={
            'sub_name': '測試流動資產',
            'sub_code': '91',
            'sub_style': '資產',
            'sub_note': '測試用子科目',
            'parent_code': '9',
            'top_category': '資產'
        }, follow_redirects=True)
        assert response.status_code == 200
        
        # 4. 編輯科目
        response = client.post('/accounting/edit_subject', data={
            'code': '91',
            'sub_name': '測試流動資產(已修改)',
            'sub_note': '已修改的測試科目'
        }, follow_redirects=True)
        assert response.status_code == 200
        
        # 5. 刪除科目
        response = client.post('/accounting/delete_subject', data={
            'code': '91'
        }, follow_redirects=True)
        assert response.status_code == 200


class TestAccountIntegration:
    """帳戶整合測試"""
    
    def test_complete_account_workflow(self, client, sample_account_subjects):
        """測試完整的帳戶管理流程"""
        # 1. 訪問帳戶設定頁面
        response = client.get('/account_setting')
        assert response.status_code == 200
        
        # 2. 新增現金帳戶
        response = client.post('/account/add/cash', data={
            'name': '測試現金',
            'init_amount': '50000',
            'subject_code': '001',
            'note': '測試現金帳戶'
        }, follow_redirects=True)
        assert response.status_code == 200
        
        # 3. 新增銀行帳戶
        response = client.post('/account/add/bank', data={
            'name': '測試銀行帳戶',
            'bank_name': '測試銀行',
            'branch': '測試分行',
            'account_number': '********90',
            'account_holder': '測試公司',
            'subject_code': '001',
            'note': '測試銀行帳戶'
        }, follow_redirects=True)
        assert response.status_code == 200


class TestCompanySettingsIntegration:
    """公司設定整合測試"""
    
    def test_company_info_workflow(self, client):
        """測試公司資訊設定流程"""
        # 1. 訪問公司設定頁面
        response = client.get('/company_setting')
        assert response.status_code == 200
        
        # 2. 更新公司資訊
        response = client.post('/company_setting', data={
            'company_name': '測試整合股份有限公司',
            'company_id': '********',
            'owner_name': '王五',
            'owner_phone': '09********',
            'email': '<EMAIL>',
            'tax_office': '台北國稅局',
            'address': '台北市大安區',
            'contact_name': '趙六',
            'contact_phone': '09********',
            'tax_id': '********'
        }, follow_redirects=True)
        assert response.status_code == 200


class TestIncomeExpenseIntegration:
    """收支整合測試"""
    
    def test_income_expense_workflow(self, client, sample_accounts, sample_account_subjects):
        """測試收支記錄流程"""
        # 1. 記錄收入
        response = client.post('/income_record', data={
            'amount': '10000',
            'description': '測試收入',
            'account_id': '1',  # 假設第一個帳戶
            'date': '2024-01-01',
            'category': '營業收入'
        }, follow_redirects=True)
        assert response.status_code == 200
        
        # 2. 記錄支出
        response = client.post('/expense_record', data={
            'amount': '5000',
            'description': '測試支出',
            'account_id': '1',
            'date': '2024-01-02',
            'category': '營業費用'
        }, follow_redirects=True)
        assert response.status_code == 200
        
        # 3. 查看收入列表
        response = client.get('/income_list')
        assert response.status_code == 200
        
        # 4. 查看支出列表
        response = client.get('/expense_list')
        assert response.status_code == 200


class TestDataConsistency:
    """資料一致性測試"""
    
    def test_account_subject_consistency(self, db_session, sample_account_subjects):
        """測試會計科目資料一致性"""
        # 檢查父子關係的一致性
        subjects = db_session.query(AccountSubject).all()
        
        for subject in subjects:
            if subject.parent_id:
                parent = db_session.query(AccountSubject).filter_by(id=subject.parent_id).first()
                assert parent is not None
                assert subject in parent.children
                # 子科目的 top_category 應該與父科目相同
                assert subject.top_category == parent.top_category
    
    def test_account_subject_code_consistency(self, db_session, sample_account_subjects):
        """測試會計科目代碼一致性"""
        subjects = db_session.query(AccountSubject).all()
        
        for subject in subjects:
            if subject.parent_id:
                parent = db_session.query(AccountSubject).filter_by(id=subject.parent_id).first()
                # 子科目代碼應該以父科目代碼開頭
                assert subject.code.startswith(parent.code)
    
    def test_account_balance_consistency(self, db_session, sample_accounts):
        """測試帳戶餘額一致性"""
        accounts = db_session.query(Account).all()
        
        for account in accounts:
            # 初始金額應該是非負數（根據業務規則）
            if account.init_amount is not None:
                assert account.init_amount >= 0
            
            # 銀行帳戶應該有必要的銀行資訊
            if account.category == '銀行帳戶':
                assert account.bank_name is not None
                assert account.account_number is not None


class TestErrorHandlingIntegration:
    """錯誤處理整合測試"""
    
    def test_invalid_subject_creation(self, client):
        """測試無效科目創建的錯誤處理"""
        # 嘗試創建無效的科目
        response = client.post('/accounting/add_subject', data={
            'sub_name': '',  # 空名稱
            'sub_code': '',  # 空代碼
            'sub_style': '無效類型'
        })
        # 應該返回錯誤或重新顯示表單
        assert response.status_code in [200, 400, 422]
    
    def test_duplicate_subject_code(self, client, sample_account_subjects):
        """測試重複科目代碼的處理"""
        # 嘗試創建重複代碼的科目
        response = client.post('/accounting/add_subject', data={
            'sub_name': '重複科目',
            'sub_code': '1',  # 已存在的代碼
            'sub_style': '資產',
            'top_category': '資產'
        })
        # 應該處理重複代碼的情況
        assert response.status_code in [200, 400, 422]
    
    def test_invalid_account_creation(self, client):
        """測試無效帳戶創建的錯誤處理"""
        response = client.post('/account/add/cash', data={
            'name': '',  # 空名稱
            'init_amount': 'invalid',  # 無效金額
            'subject_code': 'INVALID'  # 無效科目代碼
        })
        assert response.status_code in [200, 400, 422]