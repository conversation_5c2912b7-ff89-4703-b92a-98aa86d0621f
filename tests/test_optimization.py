"""
測試資料庫查詢優化效果
比較優化前後的性能差異
"""

import time
import tracemalloc
from datetime import datetime, date
from database import get_db
from services.balance_sheet_service import BalanceSheetService
from services.optimized_balance_sheet_service import OptimizedBalanceSheetService

def measure_performance(func, *args, **kwargs):
    """測量函數執行時間和記憶體使用"""
    # 開始記憶體追蹤
    tracemalloc.start()
    
    # 記錄開始時間
    start_time = time.time()
    
    # 執行函數
    result = func(*args, **kwargs)
    
    # 記錄結束時間
    end_time = time.time()
    
    # 獲取記憶體使用峰值
    current, peak = tracemalloc.get_traced_memory()
    tracemalloc.stop()
    
    return {
        'result': result,
        'execution_time': end_time - start_time,
        'peak_memory_mb': peak / 1024 / 1024
    }

def test_balance_sheet_optimization():
    """測試資產負債表查詢優化"""
    print("=" * 60)
    print("資產負債表查詢優化測試")
    print("=" * 60)
    
    # 測試日期
    test_date = date.today().strftime('%Y-%m-%d')
    
    # 測試原始版本
    print("\n1. 測試原始版本...")
    original_metrics = measure_performance(
        BalanceSheetService.generate_balance_sheet,
        test_date
    )
    print(f"   執行時間: {original_metrics['execution_time']:.3f} 秒")
    print(f"   記憶體峰值: {original_metrics['peak_memory_mb']:.2f} MB")
    
    # 測試優化版本
    print("\n2. 測試優化版本...")
    optimized_metrics = measure_performance(
        OptimizedBalanceSheetService.generate_balance_sheet,
        test_date
    )
    print(f"   執行時間: {optimized_metrics['execution_time']:.3f} 秒")
    print(f"   記憶體峰值: {optimized_metrics['peak_memory_mb']:.2f} MB")
    
    # 計算改善百分比
    time_improvement = ((original_metrics['execution_time'] - optimized_metrics['execution_time']) 
                       / original_metrics['execution_time'] * 100)
    memory_improvement = ((original_metrics['peak_memory_mb'] - optimized_metrics['peak_memory_mb']) 
                         / original_metrics['peak_memory_mb'] * 100)
    
    print("\n3. 優化效果分析:")
    print(f"   執行時間改善: {time_improvement:.1f}%")
    print(f"   記憶體使用改善: {memory_improvement:.1f}%")
    
    # 驗證結果一致性
    print("\n4. 驗證結果一致性...")
    original_data = original_metrics['result']
    optimized_data = optimized_metrics['result']
    
    # 比較總資產、總負債、總權益
    original_total_assets = original_data['assets']['total_assets']
    optimized_total_assets = optimized_data['assets']['total_assets']
    
    original_total_liabilities = original_data['liabilities']['total_liabilities']
    optimized_total_liabilities = optimized_data['liabilities']['total_liabilities']
    
    original_total_equity = original_data['equity']['total_equity']
    optimized_total_equity = optimized_data['equity']['total_equity']
    
    print(f"   總資產一致: {original_total_assets == optimized_total_assets}")
    print(f"   總負債一致: {original_total_liabilities == optimized_total_liabilities}")
    print(f"   總權益一致: {original_total_equity == optimized_total_equity}")
    
    if (original_total_assets == optimized_total_assets and 
        original_total_liabilities == optimized_total_liabilities and
        original_total_equity == optimized_total_equity):
        print("\n✅ 優化成功！結果一致且性能提升")
    else:
        print("\n⚠️ 警告：優化後結果不一致，需要檢查邏輯")
    
    return {
        'time_improvement': time_improvement,
        'memory_improvement': memory_improvement,
        'results_match': original_total_assets == optimized_total_assets
    }

def test_query_count():
    """測試查詢次數優化"""
    print("\n" + "=" * 60)
    print("資料庫查詢次數測試")
    print("=" * 60)
    
    from sqlalchemy import event
    from sqlalchemy.engine import Engine
    
    query_count = {'count': 0, 'queries': []}
    
    @event.listens_for(Engine, "before_cursor_execute")
    def count_queries(conn, cursor, statement, parameters, context, executemany):
        query_count['count'] += 1
        query_count['queries'].append(statement[:100])  # 記錄前100個字符
    
    # 測試原始版本的查詢次數
    print("\n1. 原始版本查詢次數:")
    query_count['count'] = 0
    query_count['queries'] = []
    
    test_date = date.today().strftime('%Y-%m-%d')
    BalanceSheetService.generate_balance_sheet(test_date)
    
    original_query_count = query_count['count']
    print(f"   總查詢次數: {original_query_count}")
    
    # 測試優化版本的查詢次數
    print("\n2. 優化版本查詢次數:")
    query_count['count'] = 0
    query_count['queries'] = []
    
    OptimizedBalanceSheetService.generate_balance_sheet(test_date)
    
    optimized_query_count = query_count['count']
    print(f"   總查詢次數: {optimized_query_count}")
    
    # 計算改善
    query_reduction = ((original_query_count - optimized_query_count) 
                      / original_query_count * 100)
    print(f"\n3. 查詢次數減少: {query_reduction:.1f}%")
    
    # 移除事件監聽器
    event.remove(Engine, "before_cursor_execute", count_queries)
    
    return {
        'original_queries': original_query_count,
        'optimized_queries': optimized_query_count,
        'reduction_percentage': query_reduction
    }

def main():
    """主測試函數"""
    print("\n🚀 開始資料庫優化測試\n")
    
    try:
        # 測試資產負債表優化
        balance_sheet_results = test_balance_sheet_optimization()
        
        # 測試查詢次數優化
        query_results = test_query_count()
        
        # 總結
        print("\n" + "=" * 60)
        print("優化測試總結")
        print("=" * 60)
        print(f"✅ 執行時間改善: {balance_sheet_results['time_improvement']:.1f}%")
        print(f"✅ 記憶體使用改善: {balance_sheet_results['memory_improvement']:.1f}%")
        print(f"✅ 查詢次數減少: {query_results['reduction_percentage']:.1f}%")
        print(f"✅ 結果一致性: {'通過' if balance_sheet_results['results_match'] else '失敗'}")
        
    except Exception as e:
        print(f"\n❌ 測試失敗: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()