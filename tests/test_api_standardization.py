"""
API 標準化測試範例
測試統一 API 響應格式的功能和一致性
"""

import json
import pytest
from datetime import datetime
from flask import Flask
from utils.web import APIResponse, APIStatus, APIErrorCode, api_route, require_pagination


class TestAPIResponse:
    """測試 API 響應類別"""
    
    def test_success_response(self):
        """測試成功響應格式"""
        test_data = {"user_id": 123, "name": "測試用戶"}
        response = APIResponse.success(data=test_data, message="操作成功")
        
        data = json.loads(response.data)
        
        assert data["success"] is True
        assert data["message"] == "操作成功"
        assert data["data"] == test_data
        assert "timestamp" in data
        assert data["error"] is None
        
        # 檢查時間戳格式
        timestamp = datetime.fromisoformat(data["timestamp"])
        assert isinstance(timestamp, datetime)
    
    def test_error_response(self):
        """測試錯誤響應格式"""
        response, status_code = APIResponse.error(
            message="操作失敗",
            error_code=APIErrorCode.VALIDATION_ERROR,
            details={"field": "name", "reason": "必填"},
            status_code=APIStatus.BAD_REQUEST
        )
        
        data = json.loads(response.data)
        
        assert data["success"] is False
        assert data["message"] == "操作失敗"
        assert data["data"] is None
        assert data["error"]["code"] == APIErrorCode.VALIDATION_ERROR
        assert data["error"]["details"]["field"] == "name"
        assert status_code == APIStatus.BAD_REQUEST
    
    def test_paginated_response(self):
        """測試分頁響應格式"""
        test_data = [{"id": 1}, {"id": 2}, {"id": 3}]
        response = APIResponse.paginated(
            data=test_data,
            page=2,
            per_page=3,
            total=10,
            message="查詢成功"
        )
        
        data = json.loads(response.data)
        
        assert data["success"] is True
        assert data["message"] == "查詢成功"
        assert data["data"] == test_data
        
        # 檢查分頁資訊
        pagination = data["meta"]["pagination"]
        assert pagination["page"] == 2
        assert pagination["per_page"] == 3
        assert pagination["total"] == 10
        assert pagination["total_pages"] == 4
        assert pagination["has_next"] is True
        assert pagination["has_prev"] is True
    
    def test_created_response(self):
        """測試創建成功響應"""
        test_data = {"id": 123, "name": "新用戶"}
        response, status_code = APIResponse.created(test_data, "創建成功")
        
        data = json.loads(response.data)
        
        assert data["success"] is True
        assert data["message"] == "創建成功"
        assert data["data"] == test_data
        assert status_code == 201
    
    def test_not_found_response(self):
        """測試找不到資源響應"""
        response, status_code = APIResponse.not_found("用戶", "指定的用戶不存在")
        
        data = json.loads(response.data)
        
        assert data["success"] is False
        assert data["message"] == "指定的用戶不存在"
        assert data["error"]["code"] == APIErrorCode.NOT_FOUND
        assert status_code == APIStatus.NOT_FOUND
    
    def test_validation_error_response(self):
        """測試驗證錯誤響應"""
        errors = {
            "name": "名稱不能為空",
            "email": "電子郵件格式錯誤"
        }
        response, status_code = APIResponse.validation_error(errors)
        
        data = json.loads(response.data)
        
        assert data["success"] is False
        assert data["message"] == "資料驗證失敗"
        assert data["error"]["code"] == APIErrorCode.VALIDATION_ERROR
        assert data["error"]["details"]["validation_errors"] == errors
        assert status_code == APIStatus.UNPROCESSABLE_ENTITY


class TestAPIDecorators:
    """測試 API 裝飾器"""
    
    def setup_method(self):
        """設置測試環境"""
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
    
    def test_api_route_decorator(self):
        """測試 API 路由裝飾器"""
        @self.app.route('/test')
        @api_route(methods=['GET'])
        def test_endpoint():
            return APIResponse.success({"test": True}, "測試成功")
        
        # 測試允許的方法
        response = self.client.get('/test')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["message"] == "測試成功"
        
        # 測試不允許的方法
        response = self.client.post('/test')
        assert response.status_code == 405  # Method Not Allowed
    
    def test_require_json_decorator(self):
        """測試要求 JSON 的裝飾器"""
        @self.app.route('/test', methods=['POST'])
        @api_route(methods=['POST'], require_json=True)
        def test_json_endpoint():
            return APIResponse.success({"received": True})
        
        # 測試沒有 JSON Content-Type 的請求
        response = self.client.post('/test', data="not json")
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert data["success"] is False
        assert "application/json" in data["message"]
        
        # 測試有效的 JSON 請求
        response = self.client.post('/test', 
                                  json={"test": "data"},
                                  content_type='application/json')
        assert response.status_code == 200


class TestAPIConstants:
    """測試 API 常數"""
    
    def test_api_status_codes(self):
        """測試 API 狀態碼常數"""
        assert APIStatus.OK == 200
        assert APIStatus.CREATED == 201
        assert APIStatus.BAD_REQUEST == 400
        assert APIStatus.UNAUTHORIZED == 401
        assert APIStatus.FORBIDDEN == 403
        assert APIStatus.NOT_FOUND == 404
        assert APIStatus.UNPROCESSABLE_ENTITY == 422
        assert APIStatus.INTERNAL_ERROR == 500
    
    def test_api_error_codes(self):
        """測試 API 錯誤代碼常數"""
        assert APIErrorCode.VALIDATION_ERROR == "VALIDATION_ERROR"
        assert APIErrorCode.AUTHENTICATION_ERROR == "AUTHENTICATION_ERROR"
        assert APIErrorCode.PERMISSION_DENIED == "PERMISSION_DENIED"
        assert APIErrorCode.NOT_FOUND == "NOT_FOUND"
        assert APIErrorCode.INTERNAL_ERROR == "INTERNAL_ERROR"


class TestAPIIntegration:
    """整合測試 - 模擬實際 API 端點"""
    
    def setup_method(self):
        """設置測試環境"""
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
        
        # 模擬一個完整的 API 端點
        @self.app.route('/api/users')
        @api_route(methods=['GET'])
        @require_pagination(max_per_page=50)
        def get_users(page, per_page):
            # 模擬用戶資料
            users = [{"id": i, "name": f"User{i}"} for i in range(1, 101)]
            
            # 計算分頁
            start = (page - 1) * per_page
            end = start + per_page
            page_users = users[start:end]
            
            return APIResponse.paginated(
                data=page_users,
                page=page,
                per_page=per_page,
                total=len(users),
                message="用戶列表查詢成功"
            )
        
        @self.app.route('/api/users', methods=['POST'])
        @api_route(methods=['POST'], require_json=True)
        def create_user():
            user_data = request.get_json()
            
            # 簡單驗證
            if not user_data.get('name'):
                return APIResponse.validation_error({
                    "name": "名稱不能為空"
                })
            
            # 模擬創建用戶
            new_user = {"id": 999, "name": user_data["name"]}
            
            return APIResponse.created(new_user, "用戶創建成功")
    
    def test_paginated_users_api(self):
        """測試分頁用戶 API"""
        response = self.client.get('/api/users?page=2&per_page=10')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data["success"] is True
        assert len(data["data"]) == 10
        
        pagination = data["meta"]["pagination"]
        assert pagination["page"] == 2
        assert pagination["per_page"] == 10
        assert pagination["total"] == 100
        assert pagination["has_next"] is True
        assert pagination["has_prev"] is True
    
    def test_create_user_api_success(self):
        """測試創建用戶 API - 成功案例"""
        response = self.client.post('/api/users', 
                                  json={"name": "新用戶"},
                                  content_type='application/json')
        assert response.status_code == 201
        
        data = json.loads(response.data)
        assert data["success"] is True
        assert data["message"] == "用戶創建成功"
        assert data["data"]["name"] == "新用戶"
    
    def test_create_user_api_validation_error(self):
        """測試創建用戶 API - 驗證錯誤"""
        response = self.client.post('/api/users', 
                                  json={},
                                  content_type='application/json')
        assert response.status_code == 422
        
        data = json.loads(response.data)
        assert data["success"] is False
        assert data["error"]["code"] == APIErrorCode.VALIDATION_ERROR
        assert "name" in data["error"]["details"]["validation_errors"]


if __name__ == '__main__':
    # 運行測試
    pytest.main([__file__, '-v'])