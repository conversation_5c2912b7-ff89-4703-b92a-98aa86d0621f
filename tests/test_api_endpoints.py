"""測試 API 端點"""
import pytest
from app import create_app
import tempfile
import os


@pytest.fixture
def app():
    """創建測試用的 Flask 應用"""
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False  # 測試時關閉 CSRF
    
    # 使用臨時資料庫
    db_fd, db_path = tempfile.mkstemp()
    app.config['DATABASE_URI'] = f'sqlite:///{db_path}'
    
    with app.app_context():
        # 這裡可以初始化測試資料庫
        pass
    
    yield app
    
    # 清理
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def client(app):
    """創建測試客戶端"""
    return app.test_client()


@pytest.fixture
def runner(app):
    """創建測試運行器"""
    return app.test_cli_runner()


class TestMainRoutes:
    """測試主要路由"""
    
    def test_index_page(self, client):
        """測試首頁"""
        response = client.get('/')
        assert response.status_code == 200
        assert '會計系統' in response.get_data(as_text=True) or 'accounting' in response.get_data(as_text=True).lower()
    
    def test_subject_manage_page(self, client):
        """測試科目管理頁面"""
        response = client.get('/accounting/subject_manage')
        assert response.status_code == 200
    
    def test_add_subject_get(self, client):
        """測試新增科目頁面 GET 請求"""
        response = client.get('/accounting/add_subject')
        assert response.status_code == 200
    
    def test_add_subject_post_invalid_data(self, client):
        """測試新增科目 POST 請求 - 無效資料"""
        response = client.post('/accounting/add_subject', data={
            'sub_name': '',  # 空名稱
            'sub_code': '',  # 空代碼
            'sub_style': '資產'
        })
        # 應該重新顯示表單或返回錯誤
        assert response.status_code in [200, 400, 422]
    
    def test_edit_subject_not_found(self, client):
        """測試編輯不存在的科目"""
        response = client.get('/accounting/edit_subject?code=NONEXISTENT')
        assert response.status_code == 404
        assert '找不到該科目' in response.get_data(as_text=True)


class TestAccountRoutes:
    """測試帳戶相關路由"""
    
    def test_account_list_page(self, client):
        """測試帳戶列表頁面"""
        response = client.get('/account_setting')
        assert response.status_code == 200
    
    def test_cash_account_list_page(self, client):
        """測試現金帳戶列表頁面"""
        response = client.get('/account/cash')
        assert response.status_code == 200
    
    def test_add_cash_account_page(self, client):
        """測試新增現金帳戶頁面"""
        response = client.get('/account/add/cash')
        assert response.status_code == 200
    
    def test_add_bank_account_page(self, client):
        """測試新增銀行帳戶頁面"""
        response = client.get('/account/add/bank')
        assert response.status_code == 200


class TestIncomeExpenseRoutes:
    """測試收支相關路由"""
    
    def test_income_record_page(self, client):
        """測試收入記錄頁面"""
        response = client.get('/income_record')
        assert response.status_code == 200
    
    def test_expense_record_page(self, client):
        """測試支出記錄頁面"""
        response = client.get('/expense_record')
        assert response.status_code == 200
    
    def test_income_list_page(self, client):
        """測試收入列表頁面"""
        response = client.get('/income_list')
        assert response.status_code == 200
    
    def test_expense_list_page(self, client):
        """測試支出列表頁面"""
        response = client.get('/expense_list')
        assert response.status_code == 200


class TestSettingsRoutes:
    """測試設定相關路由"""
    
    def test_company_setting_page(self, client):
        """測試公司設定頁面"""
        response = client.get('/company_setting')
        assert response.status_code == 200
    
    def test_basic_info_page(self, client):
        """測試基本資料頁面"""
        response = client.get('/basic_info')
        assert response.status_code == 200
    
    def test_department_manage_page(self, client):
        """測試部門管理頁面"""
        response = client.get('/department_manage')
        assert response.status_code == 200


class TestPayrollRoutes:
    """測試薪資相關路由"""
    
    def test_employee_list_page(self, client):
        """測試員工列表頁面"""
        response = client.get('/employee_list')
        assert response.status_code == 200
    
    def test_add_employee_page(self, client):
        """測試新增員工頁面"""
        response = client.get('/add_employee')
        assert response.status_code == 200
    
    def test_salary_setting_page(self, client):
        """測試薪資設定頁面"""
        response = client.get('/salary_setting')
        assert response.status_code == 200


class TestAssetRoutes:
    """測試資產相關路由"""
    
    def test_asset_list_page(self, client):
        """測試資產列表頁面"""
        response = client.get('/asset_list')
        assert response.status_code == 200
    
    def test_add_fixed_asset_page(self, client):
        """測試新增固定資產頁面"""
        response = client.get('/add_fixed_asset')
        assert response.status_code == 200
    
    def test_add_intangible_asset_page(self, client):
        """測試新增無形資產頁面"""
        response = client.get('/add_intangible_asset')
        assert response.status_code == 200


class TestErrorHandling:
    """測試錯誤處理"""
    
    def test_404_error(self, client):
        """測試 404 錯誤頁面"""
        response = client.get('/nonexistent_page')
        assert response.status_code == 404
    
    def test_method_not_allowed(self, client):
        """測試方法不允許錯誤"""
        # 對只接受 POST 的路由發送 GET 請求
        response = client.get('/accounting/delete_subject')
        assert response.status_code == 405


class TestFormSubmission:
    """測試表單提交"""
    
    def test_valid_form_submission(self, client):
        """測試有效的表單提交"""
        # 這裡需要根據實際的表單結構來調整
        response = client.post('/accounting/add_subject', data={
            'sub_name': '測試科目',
            'sub_code': '9999',
            'sub_style': '資產',
            'sub_note': '測試用科目',
            'parent_code': '',
            'top_category': '資產'
        }, follow_redirects=True)
        
        # 成功提交後應該重定向
        assert response.status_code == 200
    
    def test_csrf_protection(self, client):
        """測試 CSRF 保護（如果啟用的話）"""
        # 這個測試需要根據是否啟用 CSRF 來調整
        pass


class TestAPIResponses:
    """測試 API 響應格式"""
    
    def test_json_response_format(self, client):
        """測試 JSON 響應格式（如果有 API 端點的話）"""
        # 如果有 JSON API 端點，可以測試響應格式
        pass
    
    def test_response_headers(self, client):
        """測試響應標頭"""
        response = client.get('/')
        assert 'text/html' in response.content_type