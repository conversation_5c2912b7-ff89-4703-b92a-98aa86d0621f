#!/usr/bin/env python3
"""
測試科目管理頁面的資料顯示
"""

from database import get_db
from model import AccountSubject
from sqlalchemy.orm import joinedload
from collections import OrderedDict

def test_subject_manage():
    """測試科目管理頁面的資料"""
    
    print("🧪 測試科目管理頁面資料...")
    
    # 固定大分類順序
    category_order = [
        '資產', '負債', '權益', '營業收入', '營業成本',
        '營業費用', '營業外收益及費損', '所得稅'
    ]
    
    try:
        with get_db() as db:
            # 查詢所有科目，預先載入 children
            all_subjects = db.query(AccountSubject).options(
                joinedload(AccountSubject.children)
            ).all()
            
            print(f"📊 資料庫中總共有 {len(all_subjects)} 個科目")
            
            # 依 top_category 分組
            category_dict = OrderedDict()
            for cat in category_order:
                category_dict[cat] = []
                
            for subj in all_subjects:
                cat = subj.top_category or '其他'
                if cat in category_dict and subj.parent_id is None:
                    category_dict[cat].append(subj)
            
            print("\n📋 科目管理頁面顯示統計:")
            total_displayed = 0
            for cat, subjects in category_dict.items():
                count = len(subjects)
                total_displayed += count
                print(f"  {cat}: {count} 個主科目")
                
                # 顯示前3個科目作為範例
                if count > 0:
                    print("    範例科目:")
                    for i, subj in enumerate(subjects[:3]):
                        print(f"      - {subj.code} {subj.name}")
                    if count > 3:
                        print(f"      ... 還有 {count - 3} 個科目")
                print()
            
            print(f"✅ 總共顯示 {total_displayed} 個主科目")
            
            # 檢查是否有科目沒有被顯示
            main_subjects = [s for s in all_subjects if s.parent_id is None]
            not_displayed = len(main_subjects) - total_displayed
            if not_displayed > 0:
                print(f"⚠️  有 {not_displayed} 個主科目沒有被顯示")
                
                # 找出沒有被顯示的科目
                displayed_ids = set()
                for subjects in category_dict.values():
                    for subj in subjects:
                        displayed_ids.add(subj.id)
                
                print("未顯示的科目:")
                for subj in main_subjects:
                    if subj.id not in displayed_ids:
                        print(f"  - {subj.code} {subj.name} (分類: {subj.top_category})")
            else:
                print("✅ 所有主科目都有正確顯示")
                
    except Exception as e:
        print(f"❌ 測試失敗: {e}")

if __name__ == "__main__":
    test_subject_manage()
