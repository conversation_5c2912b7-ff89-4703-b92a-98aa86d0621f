"""性能測試"""
import pytest
import time
import tempfile
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from model import Base, AccountSubject, Account
from app import create_app
from concurrent.futures import ThreadPoolExecutor, as_completed


@pytest.fixture
def performance_db():
    """創建性能測試用的資料庫"""
    db_fd, db_path = tempfile.mkstemp()
    test_engine = create_engine(f'sqlite:///{db_path}')
    Base.metadata.create_all(test_engine)
    
    TestSession = sessionmaker(bind=test_engine)
    session = TestSession()
    
    yield session, test_engine
    
    # 清理
    session.close()
    os.close(db_fd)
    os.unlink(db_path)


@pytest.fixture
def performance_app():
    """創建性能測試用的應用"""
    app = create_app()
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    return app


class TestDatabasePerformance:
    """資料庫性能測試"""
    
    def test_bulk_account_subject_creation(self, performance_db):
        """測試大量會計科目創建性能"""
        session, engine = performance_db
        
        # 測試創建 1000 個會計科目
        start_time = time.time()
        
        subjects = []
        for i in range(1000):
            subject = AccountSubject(
                name=f'測試科目{i:04d}',
                code=f'{i:04d}',
                style='資產',
                top_category='資產',
                note=f'性能測試科目 {i}'
            )
            subjects.append(subject)
        
        # 批量插入
        session.add_all(subjects)
        session.commit()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n創建 1000 個會計科目耗時: {duration:.3f} 秒")
        
        # 性能要求：應該在 5 秒內完成
        assert duration < 5.0, f"創建 1000 個科目耗時過長: {duration:.3f} 秒"
        
        # 驗證資料正確性
        count = session.query(AccountSubject).count()
        assert count == 1000
    
    def test_bulk_account_creation(self, performance_db):
        """測試大量帳戶創建性能"""
        session, engine = performance_db
        
        start_time = time.time()
        
        accounts = []
        for i in range(500):
            # 交替創建現金和銀行帳戶
            if i % 2 == 0:
                account = Account(
                    name=f'現金帳戶{i:03d}',
                    category='現金',
                    init_amount=10000 + i,
                    subject_code=f'{i:03d}',
                    note=f'性能測試現金帳戶 {i}'
                )
            else:
                account = Account(
                    name=f'銀行帳戶{i:03d}',
                    category='銀行帳戶',
                    bank_name='測試銀行',
                    branch='測試分行',
                    account_number=f'12345{i:07d}',
                    account_holder='測試公司',
                    init_amount=100000 + i,
                    subject_code=f'{i:03d}',
                    note=f'性能測試銀行帳戶 {i}'
                )
            accounts.append(account)
        
        session.add_all(accounts)
        session.commit()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n創建 500 個帳戶耗時: {duration:.3f} 秒")
        
        # 性能要求：應該在 3 秒內完成
        assert duration < 3.0, f"創建 500 個帳戶耗時過長: {duration:.3f} 秒"
        
        # 驗證資料正確性
        count = session.query(Account).count()
        assert count == 500
    
    def test_complex_query_performance(self, performance_db):
        """測試複雜查詢性能"""
        session, engine = performance_db
        
        # 先創建測試資料
        self._create_test_data(session)
        
        start_time = time.time()
        
        # 執行複雜查詢：查詢所有資產類科目及其子科目
        results = session.query(AccountSubject).filter(
            AccountSubject.top_category == '資產'
        ).all()
        
        # 對每個父科目，查詢其子科目
        for parent in results:
            if parent.parent_id is None:  # 只處理父科目
                # children = session.query(AccountSubject).filter(
                #     AccountSubject.parent_id == parent.id
                # ).all()  # 暫時註解，未使用
                pass
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n複雜查詢耗時: {duration:.3f} 秒")
        
        # 性能要求：應該在 1 秒內完成
        assert duration < 1.0, f"複雜查詢耗時過長: {duration:.3f} 秒"
    
    def test_pagination_performance(self, performance_db):
        """測試分頁查詢性能"""
        session, engine = performance_db
        
        # 創建大量測試資料
        subjects = []
        for i in range(2000):
            subject = AccountSubject(
                name=f'分頁測試科目{i:04d}',
                code=f'P{i:04d}',
                style='資產',
                top_category='資產'
            )
            subjects.append(subject)
        
        session.add_all(subjects)
        session.commit()
        
        # 測試分頁查詢性能
        page_size = 50
        start_time = time.time()
        
        for page in range(10):  # 測試前 10 頁
            offset = page * page_size
            results = session.query(AccountSubject).offset(offset).limit(page_size).all()
            assert len(results) == page_size
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n分頁查詢 10 頁耗時: {duration:.3f} 秒")
        
        # 性能要求：應該在 2 秒內完成
        assert duration < 2.0, f"分頁查詢耗時過長: {duration:.3f} 秒"
    
    def _create_test_data(self, session):
        """創建測試資料"""
        # 創建主分類
        categories = ['資產', '負債', '權益', '收入', '費用']
        parent_subjects = []
        
        for i, category in enumerate(categories):
            parent = AccountSubject(
                name=category,
                code=str(i + 1),
                style=category,
                top_category=category
            )
            parent_subjects.append(parent)
        
        session.add_all(parent_subjects)
        session.commit()
        
        # 為每個主分類創建子科目
        child_subjects = []
        for parent in parent_subjects:
            for j in range(20):  # 每個主分類 20 個子科目
                child = AccountSubject(
                    name=f'{parent.name}子科目{j:02d}',
                    code=f'{parent.code}{j:02d}',
                    style=parent.style,
                    top_category=parent.top_category,
                    parent_id=parent.id
                )
                child_subjects.append(child)
        
        session.add_all(child_subjects)
        session.commit()


class TestWebPerformance:
    """Web 應用性能測試"""
    
    def test_page_load_performance(self, performance_app):
        """測試頁面載入性能"""
        client = performance_app.test_client()
        
        # 測試主要頁面的載入時間
        pages = [
            '/',
            '/accounting/subject_manage',
            '/account_setting',
            '/income_record',
            '/expense_record',
            '/company_setting'
        ]
        
        for page in pages:
            start_time = time.time()
            response = client.get(page)
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"\n頁面 {page} 載入耗時: {duration:.3f} 秒")
            
            # 性能要求：每個頁面應該在 1 秒內載入
            assert response.status_code in [200, 302], f"頁面 {page} 載入失敗"
            assert duration < 1.0, f"頁面 {page} 載入耗時過長: {duration:.3f} 秒"
    
    def test_form_submission_performance(self, performance_app):
        """測試表單提交性能"""
        client = performance_app.test_client()
        
        # 測試科目創建表單提交
        start_time = time.time()
        
        for i in range(10):
            response = client.post('/accounting/add_subject', data={
                'sub_name': f'性能測試科目{i:02d}',
                'sub_code': f'PERF{i:02d}',
                'sub_style': '資產',
                'sub_note': f'性能測試用科目 {i}',
                'top_category': '資產'
            }, follow_redirects=True)
            
            assert response.status_code == 200
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n提交 10 個表單耗時: {duration:.3f} 秒")
        
        # 性能要求：應該在 5 秒內完成
        assert duration < 5.0, f"表單提交耗時過長: {duration:.3f} 秒"


class TestConcurrencyPerformance:
    """並發性能測試"""
    
    def test_concurrent_database_access(self, performance_db):
        """測試並發資料庫存取"""
        session, engine = performance_db
        
        def create_subjects(thread_id, num_subjects=50):
            """在單獨線程中創建科目"""
            TestSession = sessionmaker(bind=engine)
            thread_session = TestSession()
            
            try:
                subjects = []
                for i in range(num_subjects):
                    subject = AccountSubject(
                        name=f'線程{thread_id}_科目{i:02d}',
                        code=f'T{thread_id:02d}{i:02d}',
                        style='資產',
                        top_category='資產'
                    )
                    subjects.append(subject)
                
                thread_session.add_all(subjects)
                thread_session.commit()
                return len(subjects)
            finally:
                thread_session.close()
        
        # 使用 5 個線程並發創建科目
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_subjects, i) for i in range(5)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n5 個線程並發創建 250 個科目耗時: {duration:.3f} 秒")
        
        # 驗證結果
        total_created = sum(results)
        assert total_created == 250
        
        # 驗證資料庫中的資料
        count = session.query(AccountSubject).count()
        assert count == 250
        
        # 性能要求：應該在 10 秒內完成
        assert duration < 10.0, f"並發操作耗時過長: {duration:.3f} 秒"
    
    def test_concurrent_web_requests(self, performance_app):
        """測試並發 Web 請求"""
        
        def make_request(client, path):
            """發送單個請求"""
            start_time = time.time()
            response = client.get(path)
            end_time = time.time()
            return response.status_code, end_time - start_time
        
        # 創建多個客戶端模擬並發請求
        clients = [performance_app.test_client() for _ in range(10)]
        paths = ['/', '/income_record', '/expense_record'] * 10  # 30 個請求
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(make_request, clients[i % 10], paths[i])
                for i in range(30)
            ]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print(f"\n30 個並發請求耗時: {total_duration:.3f} 秒")
        
        # 驗證所有請求都成功
        successful_requests = sum(1 for status, _ in results if status in [200, 302])
        assert successful_requests == 30
        
        # 計算平均響應時間
        avg_response_time = sum(duration for _, duration in results) / len(results)
        print(f"平均響應時間: {avg_response_time:.3f} 秒")
        
        # 性能要求
        assert total_duration < 15.0, f"並發請求總耗時過長: {total_duration:.3f} 秒"
        assert avg_response_time < 1.0, f"平均響應時間過長: {avg_response_time:.3f} 秒"


class TestMemoryPerformance:
    """記憶體性能測試"""
    
    def test_memory_usage_with_large_dataset(self, performance_db):
        """測試大資料集的記憶體使用"""
        import psutil
        import os
        
        session, engine = performance_db
        
        # 記錄初始記憶體使用
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 創建大量資料
        large_dataset = []
        for i in range(5000):
            subject = AccountSubject(
                name=f'記憶體測試科目{i:04d}',
                code=f'MEM{i:04d}',
                style='資產',
                top_category='資產',
                note=f'這是一個用於測試記憶體使用的科目，編號 {i}，包含一些額外的文字來增加記憶體使用量。'
            )
            large_dataset.append(subject)
        
        # 批量插入
        session.add_all(large_dataset)
        session.commit()
        
        # 查詢所有資料
        all_subjects = session.query(AccountSubject).all()
        
        # 記錄峰值記憶體使用
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        print(f"\n初始記憶體: {initial_memory:.2f} MB")
        print(f"峰值記憶體: {peak_memory:.2f} MB")
        print(f"記憶體增加: {memory_increase:.2f} MB")
        
        # 性能要求：記憶體增加不應超過 100 MB
        assert memory_increase < 100, f"記憶體使用過多: {memory_increase:.2f} MB"
        
        # 驗證資料正確性
        assert len(all_subjects) == 5000
        
        # 清理資料以釋放記憶體
        del all_subjects
        del large_dataset


class TestStressTest:
    """壓力測試"""
    
    def test_high_load_simulation(self, performance_app):
        """模擬高負載情況"""
        client = performance_app.test_client()
        
        # 模擬 100 個用戶同時訪問系統
        def simulate_user_session():
            """模擬用戶會話"""
            actions = [
                ('GET', '/'),
                ('GET', '/accounting/subject_manage'),
                ('GET', '/account_setting'),
                ('GET', '/income_record'),
                ('POST', '/accounting/add_subject', {
                    'sub_name': '壓力測試科目',
                    'sub_code': f'STRESS{time.time_ns() % 10000:04d}',
                    'sub_style': '資產',
                    'top_category': '資產'
                })
            ]
            
            session_start = time.time()
            for method, path, *data in actions:
                if method == 'GET':
                    response = client.get(path)
                else:
                    response = client.post(path, data=data[0] if data else {})
                
                # 確保請求成功
                assert response.status_code in [200, 302, 400, 422]
            
            return time.time() - session_start
        
        # 執行壓力測試
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(simulate_user_session) for _ in range(100)]
            session_times = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print(f"\n100 個用戶會話總耗時: {total_duration:.3f} 秒")
        print(f"平均會話時間: {sum(session_times) / len(session_times):.3f} 秒")
        print(f"最長會話時間: {max(session_times):.3f} 秒")
        print(f"最短會話時間: {min(session_times):.3f} 秒")
        
        # 性能要求
        assert total_duration < 60.0, f"壓力測試總耗時過長: {total_duration:.3f} 秒"
        assert max(session_times) < 10.0, f"最長會話時間過長: {max(session_times):.3f} 秒"