from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, create_engine, DateTime, Date
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.orm import sessionmaker
import os 
import sys
from datetime import datetime, timezone, timedelta

# 定義台灣時間函數
def get_taiwan_time():
    """獲取台灣時間（精確到秒，不含時區顯示）"""
    now = datetime.now(timezone(timedelta(hours=8)))
    # 去掉微秒和時區信息，只保留到秒
    return now.replace(microsecond=0, tzinfo=None)

Base = declarative_base()

class User(Base):
    """使用者資料表"""
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True)
    username = Column(String(64), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=False)

class AccountSubject(Base):
    """科目資料表"""
    __tablename__ = 'account_subject'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)                          #科目名稱
    code = Column(String(20), nullable=False, unique=True, index=True)  #科目代碼
    parent_id = Column(Integer, ForeignKey('account_subject.id'), index=True)  # 主分類id
    is_expandable = Column(Boolean, default=True, index=True)           #是否可抵扣
    note = Column(Text)                                                 #備註
    top_category = Column(String(50), index=True)                       # 最上層分類（如資產、負債...）
    
    parent = relationship('AccountSubject', remote_side=[id], backref='children')

class CompanyInfo(Base):
    """用戶的公司資料表"""
    __tablename__ = 'company_info'
    id = Column(Integer, primary_key=True)
    company_name = Column(String(200), nullable=False)                  #公司名稱
    company_id = Column(String(20))                                     #公司統編
    owner_name = Column(String(100), nullable=False)                    #負責人姓名
    owner_phone = Column(String(20), nullable=False)                    #負責人聯絡電話
    email = Column(String(200), nullable=False)
    tax_office = Column(String(200))                                    #稅徵機關名稱
    address = Column(String(500))                                       #營業地址
    contact_name = Column(String(100))                                  #扣繳申報聯絡人姓名
    contact_phone = Column(String(20))                                  #扣繳申報聯絡人電話
    tax_id = Column(String(20))                                         #稅籍編號
    
class Account(Base):
    """帳戶資料表"""
    __tablename__ = 'account'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True)              #帳戶名稱
    category = Column(String(20), nullable=False, index=True)           #帳戶類別（現金/銀行帳戶）
    note = Column(Text)                                                 #備註
    # 銀行帳戶專屬欄位
    bank_name = Column(String(100))                                     #銀行代號
    branch = Column(String(100))                                        #分行代號
    account_number = Column(String(50), index=True)                     #帳號
    account_holder = Column(String(100))                                #戶名
    # 新增欄位
    init_amount = Column(Integer)                                       #期初金額，只允許整數
    subject_code = Column(String(20), ForeignKey('account_subject.code'), index=True)  # 添加外鍵關係和索引
    is_default = Column(Boolean, default=False, index=True)             #是否預設帳戶
    cover_image = Column(String(200))                                   #存摺封面檔名或路徑
    
    # 添加關聯
    subject = relationship('AccountSubject', backref='accounts')

class PaymentIdentity(Base):
    """收支對象資料表"""
    __tablename__ = 'payment_identity'
    id = Column(Integer, primary_key=True)
    type = Column(String(20), index=True)                               #客戶類型(供應商，客戶，其他)
    name = Column(String(100), index=True)                              #公司名稱
    tax_id = Column(String(20), index=True)                             #公司統編
    bank_code = Column(String(20))                                      #銀行代碼
    bank_account = Column(String(50))                                   #銀行帳號
    contact = Column(String(50))                                        #聯絡人姓名
    mobile = Column(String(50))                                         #手機
    line = Column(String(100))                                          #Line ID
    note = Column(String(200))                                          #備註

class Department(Base):
    """部門資料表"""
    __tablename__ = 'department'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True)              #部門名稱
    parent_id = Column(Integer, ForeignKey('department.id'), index=True)#上層部門 id
    note = Column(String(200))                                          #備註

    parent = relationship('Department', remote_side=[id], backref='children')

class Project(Base):
    """專案資料表"""
    __tablename__ = 'project'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True)              #專案名稱
    code = Column(String(20), unique=True, index=True)                  #專案代碼
    description = Column(Text)                                          #專案描述
    start_date = Column(DateTime)                                       #開始日期
    end_date = Column(DateTime)                                         #結束日期
    status = Column(String(20), default='進行中', index=True)           #專案狀態（進行中、已完成、暫停）
    budget = Column(Integer)                                            #預算金額
    department_id = Column(Integer, ForeignKey('department.id'), index=True)  #負責部門
    manager = Column(String(100))                                       #專案負責人
    note = Column(String(200))                                          #備註
    created_at = Column(DateTime, default=get_taiwan_time)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)

    # 關聯到部門
    department = relationship('Department', backref='projects')

class BankHeadOffice(Base):
    """銀行總行資料表"""
    __tablename__ = 'bank_head_offices'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(10), unique=True, nullable=False, comment='銀行代碼')
    name = Column(String(100), nullable=False, comment='銀行名稱')
    created_at = Column(DateTime, default=get_taiwan_time)
    #updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 關聯到分行
    branches = relationship("BankBranch", back_populates="head_office")

class BankBranch(Base):
    """銀行分行資料表"""
    __tablename__ = 'bank_branches'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False, comment='分行代碼')
    name = Column(String(100), nullable=False, comment='分行名稱')
    head_office_code = Column(String(10), ForeignKey('bank_head_offices.code'), nullable=False, comment='總行代碼')
    created_at = Column(DateTime, default=get_taiwan_time)
    #updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 關聯到總行
    head_office = relationship("BankHeadOffice", back_populates="branches")


class Money(Base):
    """收支紀錄資料表"""
    __tablename__ = 'money'
    id = Column(Integer, primary_key=True)
    money_type = Column(String(20), comment='收支類型', index=True)
    a_time = Column(Date, comment='記帳時間', index=True)
    name = Column(String(100), comment='名稱')
    total = Column(Integer, comment='總計(含稅)')
    extra_fee = Column(Integer, comment='手續費')
    subject_code = Column(String(20), ForeignKey('account_subject.code'), comment='科目代碼', index=True)
    account_id = Column(Integer, ForeignKey('account.id'), comment='帳戶', index=True)
    payment_identity_id = Column(Integer, ForeignKey('payment_identity.id'), comment='收支對象', index=True)
    is_paper = Column(Boolean, default=False, comment='是否非發票')
    number = Column(String(20), unique=True, comment='發票號碼')
    tax_type = Column(String(20), comment='稅別')
    buyer_tax_id = Column(String(20), comment='買方統編')
    seller_tax_id = Column(String(20), comment='賣方統編')
    date = Column(String(20), comment='發票日期')
    is_paid = Column(Boolean, default=False, comment='是否已收款', index=True)
    should_paid_date = Column(DateTime, comment='應收款日期', index=True)
    paid_date = Column(DateTime, comment='實收付日期', index=True)
    note = Column(Text, comment='備註')
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)
    # 新增欄位
    department_id = Column(Integer, ForeignKey('department.id'), comment='部門別', index=True)
    project_id = Column(Integer, ForeignKey('project.id'), comment='專案別', index=True)
    tags = Column(String(200), comment='標籤')
    image_path = Column(String(300), comment='圖片位置')
    # 關聯
    subject = relationship('AccountSubject', backref='money_records')
    account = relationship('Account', backref='money_records')
    payment_identity = relationship('PaymentIdentity', backref='money_records')
    department = relationship('Department', backref='money_records')
    project = relationship('Project', backref='money_records')

class ShareAccount(Base):
    """分享帳簿紀錄表"""
    __tablename__ = 'share_account'
    id = Column(Integer, primary_key=True)
    date = Column(String(20), nullable=False)         # 產生日期
    type = Column(String(20), nullable=False)         # 產生類別
    range = Column(String(50))                        # 日期起訖
    user = Column(String(100), nullable=False)        # 產生人員
    query_type = Column(String(20), nullable=False)   # 查詢類別（記帳日期/發票日期）
    start_date = Column(String(20))                   # 查詢起始日
    end_date = Column(String(20))                     # 查詢結束日
    expired = Column(Boolean, default=False)          # 狀態

# 獲取可執行文件的路徑（包括 exe 或 py）
executable_path = sys.executable

# 如果是打包成 exe 後的可執行文件，則獲取該文件所在目錄
if getattr(sys, 'frozen', False):
    executable_dir = os.path.dirname(executable_path)
else:
    # 如果是直接運行 .py 文件，則獲取該文件所在目錄
    executable_dir = os.path.dirname(os.path.abspath(__file__))

# 使用相對路徑構建數據庫連接URI
db_name = 'app.db'
db_path = os.path.join(executable_dir, db_name)
DATABASE_URI = f"sqlite:///{db_path}"

# 建立資料庫引擎（這裡用 SQLite 範例）
engine = create_engine(DATABASE_URI)

# 建立所有資料表
Base.metadata.create_all(engine)

# 建立 session 工廠
Session = sessionmaker(bind=engine)
session = Session()
