"""負載測試 - 使用 locust 進行更真實的負載測試"""
import time
from locust import HttpUser, task, between
from locust.env import Environment
from locust.log import setup_logging
import gevent


class AccountingSystemUser(HttpUser):
    """模擬會計系統用戶行為"""
    
    wait_time = between(1, 3)  # 用戶操作間隔 1-3 秒
    
    def on_start(self):
        """用戶開始時的初始化操作"""
        # 模擬用戶登入（如果有登入系統的話）
        pass
    
    @task(3)
    def view_dashboard(self):
        """查看主頁面 - 最常見的操作"""
        self.client.get("/")
    
    @task(2)
    def view_subject_management(self):
        """查看科目管理"""
        self.client.get("/accounting/subject_manage")
    
    @task(2)
    def view_account_settings(self):
        """查看帳戶設定"""
        self.client.get("/account_setting")
    
    @task(2)
    def view_income_record(self):
        """查看收入記錄"""
        self.client.get("/income_record")
    
    @task(2)
    def view_expense_record(self):
        """查看支出記錄"""
        self.client.get("/expense_record")
    
    @task(1)
    def view_company_settings(self):
        """查看公司設定"""
        self.client.get("/company_setting")
    
    @task(1)
    def add_subject_form(self):
        """訪問新增科目表單"""
        self.client.get("/accounting/add_subject")
    
    @task(1)
    def add_cash_account_form(self):
        """訪問新增現金帳戶表單"""
        self.client.get("/account/add/cash")
    
    @task(1)
    def submit_new_subject(self):
        """提交新增科目表單"""
        timestamp = int(time.time() * 1000) % 100000
        self.client.post("/accounting/add_subject", data={
            'sub_name': f'負載測試科目{timestamp}',
            'sub_code': f'LOAD{timestamp}',
            'sub_style': '資產',
            'sub_note': '負載測試用科目',
            'top_category': '資產'
        })


class HeavyUser(HttpUser):
    """模擬重度用戶 - 執行更多複雜操作"""
    
    wait_time = between(0.5, 1.5)  # 更頻繁的操作
    
    @task(5)
    def rapid_page_browsing(self):
        """快速瀏覽多個頁面"""
        pages = [
            "/",
            "/accounting/subject_manage",
            "/account_setting",
            "/income_record",
            "/expense_record"
        ]
        
        for page in pages:
            self.client.get(page)
            time.sleep(0.1)  # 短暫停頓
    
    @task(2)
    def bulk_form_submission(self):
        """批量表單提交"""
        timestamp = int(time.time() * 1000) % 100000
        
        # 連續提交多個表單
        for i in range(3):
            self.client.post("/accounting/add_subject", data={
                'sub_name': f'批量測試科目{timestamp}_{i}',
                'sub_code': f'BULK{timestamp}{i}',
                'sub_style': '資產',
                'top_category': '資產'
            })


def run_load_test():
    """運行負載測試"""
    setup_logging("INFO", None)
    
    # 設置測試環境
    env = Environment(user_classes=[AccountingSystemUser, HeavyUser])
    
    # 開始測試
    env.create_local_runner()
    
    # 啟動用戶
    env.runner.start(user_count=50, spawn_rate=5)  # 50 個用戶，每秒啟動 5 個
    
    # 運行 60 秒
    gevent.spawn_later(60, lambda: env.runner.quit())
    
    # 等待測試完成
    env.runner.greenlet.join()
    
    # 輸出結果
    print("\n=== 負載測試結果 ===")
    print(f"總請求數: {env.stats.total.num_requests}")
    print(f"失敗請求數: {env.stats.total.num_failures}")
    print(f"平均響應時間: {env.stats.total.avg_response_time:.2f} ms")
    print(f"最大響應時間: {env.stats.total.max_response_time:.2f} ms")
    print(f"每秒請求數: {env.stats.total.total_rps:.2f}")
    
    return env.stats


if __name__ == "__main__":
    # 直接運行負載測試
    run_load_test()