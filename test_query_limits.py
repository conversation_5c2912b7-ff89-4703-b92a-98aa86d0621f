#!/usr/bin/env python3
"""
測試查詢限制功能
驗證查詢限制工具是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database.query_limits import safe_all, safe_paginate, adaptive_query, QueryLimits, SafeQueryExecutor
from database import get_db
from model import Account, Money, Transaction
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_safe_all():
    """測試 safe_all 函數"""
    print("🔍 測試 safe_all 查詢限制功能...")
    
    try:
        with get_db() as db:
            # 測試不同上下文的限制
            contexts = {
                'dashboard': 50,
                'search': 200, 
                'report': 10000,
                'api': 1000,
                'general': 1000
            }
            
            for context, expected_limit in contexts.items():
                print(f"\n📊 測試 {context} 上下文 (預期限制: {expected_limit})")
                
                start_time = time.time()
                
                # 測試 Account 查詢
                accounts = safe_all(
                    db.query(Account).filter(Account.is_deleted == False),
                    context=context
                )
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                print(f"  ✅ 帳戶查詢: 返回 {len(accounts)} 筆記錄")
                print(f"  ⏱️  執行時間: {execution_time:.3f}s")
                
                # 檢查是否符合限制
                if len(accounts) <= expected_limit:
                    print(f"  ✅ 查詢限制正常 (限制: {expected_limit})")
                else:
                    print(f"  ❌ 查詢限制異常 (返回: {len(accounts)}, 限制: {expected_limit})")
                
    except Exception as e:
        print(f"  ❌ safe_all 測試失敗: {e}")

def test_safe_paginate():
    """測試 safe_paginate 分頁功能"""
    print("\n🔍 測試 safe_paginate 分頁功能...")
    
    try:
        with get_db() as db:
            # 測試分頁查詢
            page_sizes = [10, 50, 100, 250]  # 最後一個會被限制
            
            for per_page in page_sizes:
                print(f"\n📄 測試每頁 {per_page} 筆記錄")
                
                start_time = time.time()
                
                result = safe_paginate(
                    db.query(Money).filter(Money.is_deleted == False),
                    page=1,
                    per_page=per_page
                )
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                actual_per_page = result['per_page']
                items_count = len(result['items'])
                
                print(f"  ✅ 分頁結果: {items_count} 筆記錄")
                print(f"  📊 每頁限制: {actual_per_page} (請求: {per_page})")
                print(f"  ⏱️  執行時間: {execution_time:.3f}s")
                print(f"  📈 分頁信息: 頁數 {result['page']}, 總計 {result['total']}")
                
                # 檢查是否符合最大分頁限制
                max_page_size = QueryLimits.MAX_PAGE_SIZE
                if actual_per_page <= max_page_size:
                    print(f"  ✅ 分頁限制正常 (最大: {max_page_size})")
                else:
                    print(f"  ❌ 分頁限制異常 (實際: {actual_per_page}, 最大: {max_page_size})")
                
    except Exception as e:
        print(f"  ❌ safe_paginate 測試失敗: {e}")

def test_adaptive_query():
    """測試自適應查詢功能"""
    print("\n🔍 測試 adaptive_query 自適應功能...")
    
    try:
        with get_db() as db:
            # 測試自適應查詢
            max_times = [0.5, 1.0, 2.0]  # 不同的時間限制
            
            for max_time in max_times:
                print(f"\n⏱️  測試 {max_time}s 時間限制")
                
                start_time = time.time()
                
                results = adaptive_query(
                    db.query(Money).filter(Money.is_deleted == False),
                    max_time=max_time
                )
                
                end_time = time.time()
                actual_time = end_time - start_time
                
                print(f"  ✅ 自適應查詢: 返回 {len(results)} 筆記錄")
                print(f"  ⏱️  實際時間: {actual_time:.3f}s (限制: {max_time}s)")
                
                # 檢查是否在時間限制內（允許一些誤差）
                if actual_time <= max_time + 0.1:  # 允許100ms誤差
                    print(f"  ✅ 時間限制正常")
                else:
                    print(f"  ⚠️  超出時間限制 (可能是查詢較簡單)")
                
    except Exception as e:
        print(f"  ❌ adaptive_query 測試失敗: {e}")

def test_query_executor_stats():
    """測試查詢執行器統計功能"""
    print("\n🔍 測試 SafeQueryExecutor 統計功能...")
    
    try:
        executor = SafeQueryExecutor()
        
        with get_db() as db:
            # 執行多個查詢以產生統計數據
            for i in range(3):
                accounts = executor.safe_all(
                    db.query(Account).filter(Account.is_deleted == False),
                    limit=10,
                    context="dashboard"
                )
                print(f"  📊 查詢 {i+1}: 返回 {len(accounts)} 筆帳戶記錄")
            
            # 獲取統計信息
            stats = executor.get_stats()
            
            print(f"\n📈 執行器統計:")
            print(f"  🔢 總查詢數: {stats['queries']}")
            print(f"  ⚠️  限制次數: {stats['limited']}")
            print(f"  ⏰ 警告次數: {stats['warnings']}")
            
            # 重置統計
            executor.reset_stats()
            reset_stats = executor.get_stats()
            
            print(f"\n🔄 重置後統計:")
            print(f"  🔢 總查詢數: {reset_stats['queries']}")
            print(f"  ⚠️  限制次數: {reset_stats['limited']}")
            print(f"  ⏰ 警告次數: {reset_stats['warnings']}")
            
            if all(v == 0 for v in reset_stats.values()):
                print("  ✅ 統計重置正常")
            else:
                print("  ❌ 統計重置異常")
                
    except Exception as e:
        print(f"  ❌ 統計功能測試失敗: {e}")

def test_performance_impact():
    """測試查詢限制對性能的影響"""
    print("\n🔍 測試查詢限制對性能的影響...")
    
    try:
        with get_db() as db:
            # 原始查詢性能
            print("📊 原始查詢性能:")
            start_time = time.time()
            original_results = db.query(Account).filter(Account.is_deleted == False).all()
            original_time = time.time() - start_time
            
            print(f"  ✅ 原始查詢: {len(original_results)} 筆記錄, {original_time:.3f}s")
            
            # 使用安全查詢的性能
            print("\n📊 安全查詢性能:")
            start_time = time.time()
            safe_results = safe_all(
                db.query(Account).filter(Account.is_deleted == False),
                context="dashboard"
            )
            safe_time = time.time() - start_time
            
            print(f"  ✅ 安全查詢: {len(safe_results)} 筆記錄, {safe_time:.3f}s")
            
            # 計算性能差異
            overhead = safe_time - original_time
            overhead_percent = (overhead / original_time) * 100 if original_time > 0 else 0
            
            print(f"\n📈 性能分析:")
            print(f"  ⏱️  額外耗時: {overhead:.3f}s")
            print(f"  📊 性能開銷: {overhead_percent:.1f}%")
            
            if overhead_percent < 10:  # 如果開銷小於10%
                print("  ✅ 性能影響可接受")
            elif overhead_percent < 20:
                print("  ⚠️  性能影響適中")
            else:
                print("  ❌ 性能影響較大")
                
    except Exception as e:
        print(f"  ❌ 性能測試失敗: {e}")

def run_all_tests():
    """運行所有測試"""
    print("🚀 開始查詢限制功能測試...\n")
    print("=" * 60)
    
    # 運行各項測試
    test_safe_all()
    print("\n" + "=" * 60)
    
    test_safe_paginate()
    print("\n" + "=" * 60)
    
    test_adaptive_query()
    print("\n" + "=" * 60)
    
    test_query_executor_stats()
    print("\n" + "=" * 60)
    
    test_performance_impact()
    print("\n" + "=" * 60)
    
    print("\n🎉 查詢限制功能測試完成！")
    print("\n💡 測試結果說明:")
    print("✅ - 功能正常")
    print("⚠️  - 需要注意")
    print("❌ - 需要修正")
    print("\n📝 建議:")
    print("1. 定期監控查詢性能，調整限制參數")
    print("2. 根據實際使用情況優化上下文限制")
    print("3. 持續觀察慢查詢日誌")

if __name__ == "__main__":
    try:
        run_all_tests()
    except Exception as e:
        logger.error(f"測試執行失敗: {e}")
        sys.exit(1)