#!/usr/bin/env python3
"""
自動化路由測試腳本
測試所有主要路由是否正常運作
"""

import requests
import sys
import time
from typing import List, Dict, Tuple
from dataclasses import dataclass

@dataclass
class RouteTest:
    """路由測試結果"""
    url: str
    method: str
    status_code: int
    response_time: float
    error_message: str = ""

class RouteHealthChecker:
    """路由健康檢查器"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.timeout = 10
        
        # 主要路由列表
        self.routes = [
            # 主頁和基本功能
            "/",
            "/overview",
            
            # 收支管理
            "/income_expense",
            "/income_expense/monthly_income_expense",
            "/new_income_expense",
            
            # 會計功能
            "/accounting/subject_manage",
            "/accounting/edit_account_subject",
            "/voucher_manage",
            "/journal_validation",
            
            # 設定管理
            "/department_manage",
            "/project_manage", 
            "/payment_identity_list",
            "/basic_info",
            "/account_setting",
            "/opening_setting",
            
            # 薪資相關
            "/payroll_process",
            "/service_reward",
            "/withholding_declare",
            
            # 報表功能
            "/reports",
            "/reports/monthly_report",
            "/reports/balance_sheet",
            "/reports/income_statement",
            "/reports/cash_flow",
            "/reports/trial_balance",
            
            # 資產管理
            "/add_prepaid_expense",
            
            # 資金管理
            "/fund_record/create",
            "/transfer/add",
            "/bankloan/create",
            
            # API 端點
            "/api/bank_heads",
            "/api/check_invoice_number?number=TEST123",
            
            # 監控功能
            "/admin/errors/dashboard",
            "/admin/performance",
            "/admin/database/health",
            
            # 系統管理 (可能需要認證)
            "/admin",
        ]
    
    def test_route(self, route: str, method: str = "GET") -> RouteTest:
        """測試單個路由"""
        url = f"{self.base_url}{route}"
        start_time = time.time()
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url)
            elif method.upper() == "POST":
                response = self.session.post(url)
            else:
                response = self.session.request(method, url)
            
            response_time = time.time() - start_time
            
            return RouteTest(
                url=url,
                method=method,
                status_code=response.status_code,
                response_time=response_time
            )
            
        except requests.RequestException as e:
            response_time = time.time() - start_time
            return RouteTest(
                url=url,
                method=method,
                status_code=0,
                response_time=response_time,
                error_message=str(e)
            )
    
    def test_all_routes(self) -> List[RouteTest]:
        """測試所有路由"""
        results = []
        
        print(f"🧪 開始測試 {len(self.routes)} 個路由...")
        print("=" * 80)
        
        for i, route in enumerate(self.routes, 1):
            print(f"[{i:2d}/{len(self.routes):2d}] 測試: {route:<40}", end=" ")
            
            result = self.test_route(route)
            results.append(result)
            
            # 狀態顯示
            if result.status_code == 200:
                status = "✅ 正常"
                color = "\033[92m"  # 綠色
            elif result.status_code in [301, 302, 304]:
                status = "🔄 重定向"
                color = "\033[93m"  # 黃色
            elif result.status_code == 401:
                status = "🔐 需要認證"
                color = "\033[94m"  # 藍色
            elif result.status_code == 403:
                status = "🚫 權限不足"
                color = "\033[94m"  # 藍色
            elif result.status_code == 404:
                status = "❓ 找不到"
                color = "\033[91m"  # 紅色
            elif result.status_code >= 500:
                status = "❌ 伺服器錯誤"
                color = "\033[91m"  # 紅色
            elif result.status_code == 0:
                status = "💥 連接失敗"
                color = "\033[91m"  # 紅色
            else:
                status = f"⚠️ 狀態碼: {result.status_code}"
                color = "\033[93m"  # 黃色
            
            print(f"{color}{status} ({result.response_time:.3f}s)\033[0m")
            
            if result.error_message:
                print(f"     錯誤: {result.error_message}")
            
            # 避免過於頻繁的請求
            time.sleep(0.1)
        
        return results
    
    def generate_report(self, results: List[RouteTest]) -> Dict:
        """生成測試報告"""
        total = len(results)
        success = len([r for r in results if r.status_code == 200])
        redirects = len([r for r in results if r.status_code in [301, 302, 304]])
        auth_required = len([r for r in results if r.status_code in [401, 403]])
        not_found = len([r for r in results if r.status_code == 404])
        server_errors = len([r for r in results if r.status_code >= 500])
        connection_errors = len([r for r in results if r.status_code == 0])
        
        avg_response_time = sum(r.response_time for r in results) / total if total > 0 else 0
        
        report = {
            'total': total,
            'success': success,
            'redirects': redirects,
            'auth_required': auth_required,
            'not_found': not_found,
            'server_errors': server_errors,
            'connection_errors': connection_errors,
            'avg_response_time': avg_response_time,
            'success_rate': (success / total * 100) if total > 0 else 0
        }
        
        return report
    
    def print_summary(self, results: List[RouteTest]):
        """打印測試摘要"""
        report = self.generate_report(results)
        
        print("\n" + "=" * 80)
        print("📊 測試結果摘要")
        print("=" * 80)
        
        print(f"📈 總路由數量: {report['total']}")
        print(f"✅ 正常運作: {report['success']} ({report['success_rate']:.1f}%)")
        print(f"🔄 重定向: {report['redirects']}")
        print(f"🔐 需要認證: {report['auth_required']}")
        print(f"❓ 找不到: {report['not_found']}")
        print(f"❌ 伺服器錯誤: {report['server_errors']}")
        print(f"💥 連接失敗: {report['connection_errors']}")
        print(f"⏱️ 平均響應時間: {report['avg_response_time']:.3f}s")
        
        # 問題路由詳情
        problem_routes = [r for r in results if r.status_code >= 400 and r.status_code != 401 and r.status_code != 403]
        if problem_routes:
            print(f"\n🚨 發現問題的路由 ({len(problem_routes)} 個):")
            print("-" * 80)
            for route in problem_routes:
                print(f"  {route.url} -> {route.status_code}")
                if route.error_message:
                    print(f"    錯誤: {route.error_message}")
        
        # 慢速路由
        slow_routes = [r for r in results if r.response_time > 2.0]
        if slow_routes:
            print(f"\n🐌 響應較慢的路由 ({len(slow_routes)} 個):")
            print("-" * 80)
            for route in slow_routes:
                print(f"  {route.url} -> {route.response_time:.3f}s")
        
        print("\n" + "=" * 80)
    
    def save_report(self, results: List[RouteTest], filename: str = "route_test_report.json"):
        """保存測試報告"""
        import json
        from datetime import datetime
        
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'summary': self.generate_report(results),
            'results': [
                {
                    'url': r.url,
                    'method': r.method,
                    'status_code': r.status_code,
                    'response_time': r.response_time,
                    'error_message': r.error_message
                }
                for r in results
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"📄 測試報告已保存到: {filename}")

def main():
    """主函數"""
    print("🚀 印錢大師會計系統 - 路由健康檢查")
    print("=" * 80)
    
    # 檢查服務是否運行
    checker = RouteHealthChecker()
    
    try:
        response = requests.get(checker.base_url, timeout=5)
        print(f"✅ 服務正在運行: {checker.base_url}")
    except requests.RequestException:
        print(f"❌ 無法連接到服務: {checker.base_url}")
        print("請確保應用程式正在運行")
        sys.exit(1)
    
    # 執行測試
    results = checker.test_all_routes()
    
    # 顯示結果
    checker.print_summary(results)
    
    # 保存報告
    if '--save' in sys.argv:
        checker.save_report(results)
    
    # 返回退出碼
    report = checker.generate_report(results)
    if report['server_errors'] > 0 or report['connection_errors'] > 0:
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()