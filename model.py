from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, create_engine, DateTime, Date, Index, Float, CheckConstraint
from sqlalchemy.orm import declarative_base, relationship
import os 
import sys
from datetime import datetime, timezone, timedelta

# 定義台灣時間函數
def get_taiwan_time():
    """獲取台灣時間（精確到秒，不含時區顯示）"""
    now = datetime.now(timezone(timedelta(hours=8)))
    # 去掉微秒和時區信息，只保留到秒
    return now.replace(microsecond=0, tzinfo=None)

Base = declarative_base()

# ============================================================================
# Base Mixin 類別 - 提取重複欄位
# ============================================================================

class AuditMixin:
    """審計欄位 Mixin - 提供建立、更新、刪除的追蹤欄位"""
    created_at = Column(DateTime, default=get_taiwan_time, index=True, comment='建立時間')
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')
    created_by = Column(String(100), comment='建立者')
    updated_by = Column(String(100), comment='最後修改者')
    is_deleted = Column(Boolean, default=False, index=True, comment='是否已刪除')
    deleted_at = Column(DateTime, comment='刪除時間')
    deleted_by = Column(String(100), comment='刪除者')

class TenantMixin:
    """多租戶 Mixin - 提供租戶隔離功能"""
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False, index=True, comment='租戶ID')

# ============================================================================
# 核心會計模型
# ============================================================================

class Account(Base, AuditMixin, TenantMixin):
    """帳戶資料表"""
    __tablename__ = 'account'
    
    # 主鍵
    id = Column(Integer, primary_key=True)
    
    # 基本資訊
    name = Column(String(100), nullable=False, index=True, comment='帳戶名稱')
    category = Column(String(50), CheckConstraint("category IN ('現金', '銀行帳戶', '電子支付')"), nullable=False, index=True, comment='帳戶類別')
    note = Column(Text, comment='備註')
    
    # 銀行帳戶專屬欄位
    bank_name = Column(String(100), comment='銀行名稱')
    branch = Column(String(100), comment='分行名稱')
    account_number = Column(String(50), index=True, comment='帳號')
    account_holder = Column(String(100), comment='戶名')
    
    # 會計相關欄位
    init_amount = Column(Integer, default=0, comment='期初金額')
    subject_code = Column(String(50), ForeignKey('account_subject.code'), index=True, comment='會計科目代碼')
    is_default = Column(Boolean, default=False, index=True, comment='是否預設帳戶')
    cover_image = Column(String(200), comment='存摺封面檔案路徑')
    
    # 關聯關係
    subject = relationship('AccountSubject', backref='accounts')
    transactions = relationship("Transaction", back_populates="account")
    
    # 索引策略優化
    __table_args__ = (
        Index('ix_account_bank_info', 'bank_name', 'account_number'),
        Index('ix_account_category_default', 'category', 'is_default'),
        Index('ix_account_active', 'is_deleted', 'category'),
        Index('ix_account_audit', 'created_by', 'created_at'),
    )

class AccountSubject(Base):
    """會計科目資料表"""
    __tablename__ = 'account_subject'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True, comment='科目名稱')
    code = Column(String(50), nullable=False, unique=True, index=True, comment='科目代碼')
    parent_id = Column(Integer, ForeignKey('account_subject.id', ondelete='CASCADE'), index=True, comment='父科目ID')
    is_expandable = Column(Boolean, default=True, index=True, comment='是否可展開')
    note = Column(Text, comment='備註')
    top_category = Column(String(100), index=True, comment='頂層分類')
    
    # 自關聯
    parent = relationship('AccountSubject', remote_side=[id], backref='children')
    journal_entries = relationship("JournalEntry", back_populates="account_subject")
    
    # 索引策略優化
    __table_args__ = (
        Index('ix_subject_category_code', 'top_category', 'code'),
        Index('ix_subject_parent_expandable', 'parent_id', 'is_expandable'),
    )

# ============================================================================
# 銀行相關模型
# ============================================================================

class BankBranch(Base):
    """銀行分行資料表"""
    __tablename__ = 'bank_branches'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(20), unique=True, nullable=False, comment='分行代碼')
    name = Column(String(100), nullable=False, comment='分行名稱')
    head_office_code = Column(String(10), ForeignKey('bank_head_offices.code'), nullable=False, comment='總行代碼', index=True)
    created_at = Column(DateTime, default=get_taiwan_time)
    
    # 關聯到總行
    head_office = relationship("BankHeadOffice", back_populates="branches")

class BankHeadOffice(Base):
    """銀行總行資料表"""
    __tablename__ = 'bank_head_offices'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(10), unique=True, nullable=False, comment='銀行代碼')
    name = Column(String(100), nullable=False, comment='銀行名稱')
    created_at = Column(DateTime, default=get_taiwan_time)
    
    # 關聯到分行
    branches = relationship("BankBranch", back_populates="head_office")

# ============================================================================
# 組織架構模型
# ============================================================================

class CompanyInfo(Base):
    """用戶的公司資料表"""
    __tablename__ = 'company_info'
    id = Column(Integer, primary_key=True)
    company_name = Column(String(200), nullable=False, index=True)      #公司名稱
    company_id = Column(String(20), index=True)                         #公司統編
    owner_name = Column(String(100), nullable=False, index=True)        #負責人姓名
    owner_phone = Column(String(20), nullable=False)                    #負責人聯絡電話
    email = Column(String(200), nullable=False, index=True)
    tax_office = Column(String(200))                                    #稅徵機關名稱
    address = Column(String(500))                                       #營業地址
    contact_name = Column(String(100))                                  #扣繳申報聯絡人姓名
    contact_phone = Column(String(20))                                  #扣繳申報聯絡人電話
    tax_id = Column(String(20), index=True)                             #稅籍編號

class Department(Base, AuditMixin, TenantMixin):
    """部門資料表"""
    __tablename__ = 'department'
    
    # 主鍵
    id = Column(Integer, primary_key=True)
    
    # 基本資訊
    name = Column(String(100), nullable=False, index=True, comment='部門名稱')
    code = Column(String(50), unique=True, index=True, comment='部門代碼')
    parent_id = Column(Integer, ForeignKey('department.id', ondelete='CASCADE'), index=True, comment='上級部門ID')
    note = Column(Text, comment='備註')
    is_active = Column(Boolean, default=True, index=True, comment='是否啟用')

    # 自關聯
    parent = relationship('Department', remote_side=[id], backref='children')
    transactions = relationship("Transaction", back_populates="department")
    
    # 索引策略優化
    __table_args__ = (
        Index('ix_dept_parent_active', 'parent_id', 'is_active'),
        Index('ix_dept_name_code', 'name', 'code'),
    )

# ============================================================================
# 交易紀錄模型
# ============================================================================

class Money(Base, AuditMixin, TenantMixin):
    """收支紀錄資料表"""
    __tablename__ = 'money'
    id = Column(Integer, primary_key=True)
    money_type = Column(String(50), CheckConstraint("money_type IN ('收入', '支出', '轉帳')"), comment='收支類型', index=True)
    a_time = Column(Date, comment='記帳時間', index=True)
    name = Column(String(100), index=True, comment='名稱')
    total = Column(Integer, CheckConstraint('total >= 0'), comment='總計(含稅)')
    tax = Column(Integer, CheckConstraint('tax >= 0'), default=0, comment='營業稅')
    extra_fee = Column(Integer, CheckConstraint('extra_fee >= 0'), default=0, comment='手續費')
    subject_code = Column(String(50), ForeignKey('account_subject.code'), comment='科目代碼', index=True)
    account_id = Column(Integer, ForeignKey('account.id'), comment='帳戶', index=True)
    payment_identity_id = Column(Integer, ForeignKey('payment_identity.id'), comment='收支對象', index=True)
    is_paper = Column(Boolean, default=False, comment='是否非發票')
    number = Column(String(50), nullable=True, comment='發票號碼')
    tax_type = Column(String(50), comment='稅別')
    buyer_tax_id = Column(String(50), comment='買方統編')
    seller_tax_id = Column(String(50), comment='賣方統編')
    date = Column(String(50), comment='發票日期')
    is_paid = Column(Boolean, default=False, comment='是否已收款', index=True)
    should_paid_date = Column(DateTime, comment='應收款日期', index=True)
    paid_date = Column(DateTime, comment='實收付日期', index=True)
    note = Column(Text, comment='備註')

    # 會計分錄相關欄位（將被新表結構取代）
    entry_side = Column(String(10), CheckConstraint("entry_side IN ('DEBIT', 'CREDIT')"), comment='借貸方向')
    journal_reference = Column(String(50), comment='分錄參考號', index=True)

    # 時間審計
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)
    
    # 人員審計 (新增)
    created_by = Column(String(100), comment='建立者', index=True)
    updated_by = Column(String(100), comment='最後修改者')
    
    # 版本控制 (新增)
    version = Column(Integer, default=1, comment='版本號')
    
    # 軟刪除 (新增)
    is_deleted = Column(Boolean, default=False, index=True, comment='是否已刪除')
    deleted_at = Column(DateTime, comment='刪除時間')
    deleted_by = Column(String(100), comment='刪除者')
    
    # 新增欄位
    department_id = Column(Integer, ForeignKey('department.id'), comment='部門別', index=True)
    project_id = Column(Integer, ForeignKey('project.id'), comment='專案別', index=True)
    tags = Column(String(200), comment='標籤')
    image_path = Column(String(300), comment='圖片位置')
    
    # 關聯
    subject = relationship('AccountSubject', backref='money_records')
    account = relationship('Account', backref='money_records')
    payment_identity = relationship('PaymentIdentity', backref='money_records')
    department = relationship('Department', backref='money_records')
    project = relationship('Project', backref='money_records')
    
    # 複合索引 - 優化常用查詢
    __table_args__ = (
        Index('ix_money_date_type', 'a_time', 'money_type'),
        Index('ix_money_account_date', 'account_id', 'a_time'),
        Index('ix_money_subject_date', 'subject_code', 'a_time'),
        Index('ix_money_dept_project', 'department_id', 'project_id'),
        Index('ix_money_audit', 'created_by', 'created_at'),
        Index('ix_money_active', 'is_deleted', 'a_time'),
        CheckConstraint('should_paid_date IS NULL OR paid_date IS NULL OR paid_date >= should_paid_date', name='check_payment_dates'),
    )

# ============================================================================
# 業務關係人模型
# ============================================================================

class PaymentIdentityType(Base):
    """收支對象類別資料表"""
    __tablename__ = 'payment_identity_types'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False, unique=True, index=True, comment='類別名稱')
    description = Column(Text, comment='描述')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True, comment='建立時間')
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')
    created_by = Column(String(100), comment='建立者')
    updated_by = Column(String(100), comment='最後修改者')
    is_active = Column(Boolean, default=True, index=True, comment='是否啟用')
    #is_deleted = Column(Boolean, default=False, index=True, comment='是否已刪除')
    #deleted_at = Column(DateTime, comment='刪除時間')
    #deleted_by = Column(String(100), comment='刪除者')
    
    # 關聯
    payment_identities = relationship('PaymentIdentity', back_populates='identity_type')
    
    # 索引
    __table_args__ = (
        Index('ix_payment_identity_type_name', 'name'),
        Index('ix_payment_identity_type_active', 'is_active'),
    )

class PaymentIdentity(Base, AuditMixin, TenantMixin):
    """收支對象資料表"""
    __tablename__ = 'payment_identity'
    id = Column(Integer, primary_key=True)
    type = Column(String(50), index=True, comment='客戶類型')
    name = Column(String(100), nullable=False, index=True, comment='公司名稱')
    tax_id = Column(String(50), unique=True, index=True, comment='統一編號')
    bank_code = Column(String(50), comment='銀行代碼')
    bank_account = Column(String(50), comment='銀行帳號')
    contact = Column(String(100), comment='聯絡人姓名')
    mobile = Column(String(50), comment='手機')
    line = Column(String(100), comment='Line ID')
    note = Column(Text, comment='備註')
    is_active = Column(Boolean, default=True, index=True, comment='是否啟用')
    
    # 新增對象類別關聯
    type_id = Column(Integer, ForeignKey('payment_identity_types.id'), index=True, comment='對象類別ID')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True, comment='建立時間')
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')
    created_by = Column(String(100), comment='建立者')
    updated_by = Column(String(100), comment='最後修改者')
    is_deleted = Column(Boolean, default=False, index=True, comment='是否已刪除')
    deleted_at = Column(DateTime, comment='刪除時間')
    deleted_by = Column(String(100), comment='刪除者')
    
    # 關聯
    transactions = relationship("Transaction", back_populates="payment_identity")
    identity_type = relationship("PaymentIdentityType", back_populates="payment_identities")

    # 索引策略優化
    __table_args__ = (
        Index('ix_payment_type_active', 'type', 'is_active'),
        Index('ix_payment_name_search', 'name', 'tax_id'),
        Index('ix_payment_type_id', 'type_id'),
    )

class Project(Base, AuditMixin, TenantMixin):
    """專案資料表"""
    __tablename__ = 'project'
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, index=True, comment='專案名稱')
    code = Column(String(50), unique=True, nullable=False, index=True, comment='專案代碼')
    description = Column(Text, comment='專案描述')
    start_date = Column(Date, comment='開始日期')
    end_date = Column(Date, comment='結束日期')
    status = Column(String(50), CheckConstraint("status IN ('規劃中', '進行中', '暫停', '已完成', '已取消')"), default='進行中', index=True, comment='專案狀態')
    budget = Column(Integer, CheckConstraint('budget >= 0'), comment='預算金額')
    actual_cost = Column(Integer, CheckConstraint('actual_cost >= 0'), default=0, comment='實際成本')
    department_id = Column(Integer, ForeignKey('department.id', ondelete='SET NULL'), index=True, comment='負責部門')
    manager = Column(String(100), comment='專案負責人')
    note = Column(Text, comment='備註')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True, comment='建立時間')
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')
    created_by = Column(String(100), comment='建立者')
    updated_by = Column(String(100), comment='最後修改者')
    is_deleted = Column(Boolean, default=False, index=True, comment='是否已刪除')
    deleted_at = Column(DateTime, comment='刪除時間')
    deleted_by = Column(String(100), comment='刪除者')
    
    # 關聯到部門
    department = relationship('Department', backref='projects')
    transactions = relationship("Transaction", back_populates="project")
    
    # 索引策略優化
    __table_args__ = (
        CheckConstraint('end_date IS NULL OR end_date >= start_date', name='check_project_dates'),
        Index('ix_project_dept_status', 'department_id', 'status'),
        Index('ix_project_date_range', 'start_date', 'end_date'),
        Index('ix_project_manager', 'manager'),
    )

class BankLoan(Base):
    """銀行借款資料表"""
    __tablename__ = 'bank_loans'

    id = Column(Integer, primary_key=True)
    bank_name = Column(String(100), nullable=False, comment='銀行名稱')
    subject_code = Column(String(20), ForeignKey('account_subject.code'), comment='借款會計項目', index=True)
    repayment_account_id = Column(Integer, ForeignKey('account.id'), comment='還款帳戶', index=True)
    loan_start_date = Column(Date, comment='借款開始日期')
    loan_end_date = Column(Date, comment='借款結束日期')
    loan_date = Column(Date, nullable=False, comment='借款入帳日期', index=True)
    principal = Column(Integer, CheckConstraint('principal > 0'), nullable=False, comment='本金')
    fee = Column(Integer, CheckConstraint('fee >= 0'), default=0, comment='手續費')
    deposit_amount = Column(Integer, CheckConstraint('deposit_amount > 0'), comment='入帳金額')  # 本金 - 手續費
    installments = Column(Integer, CheckConstraint('installments > 0'), default=1, comment='分期數')
    fixed_repayment_day = Column(Integer, CheckConstraint('fixed_repayment_day BETWEEN 1 AND 31'), default=1, comment='固定還款月同(數)')
    status = Column(String(20), CheckConstraint("status IN ('active', 'completed', 'defaulted')"), default='active', index=True, comment='借款狀態')
    note = Column(Text, comment='備註')
    voucher_image = Column(String(255), comment='憑證圖檔路徑')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)
    created_by = Column(String(100), comment='建立者')
    updated_by = Column(String(100), comment='最後修改者')
    is_deleted = Column(Boolean, default=False, index=True, comment='是否已刪除')
    deleted_at = Column(DateTime, comment='刪除時間')
    deleted_by = Column(String(100), comment='刪除者')

    # 關聯
    subject = relationship('AccountSubject', backref='bank_loans')
    repayment_account = relationship('Account', backref='bank_loans')
    
    # 複合約束
    __table_args__ = (
        CheckConstraint('loan_end_date IS NULL OR loan_end_date >= loan_start_date', name='check_loan_dates'),
        CheckConstraint('deposit_amount = principal - fee', name='check_deposit_calculation'),
        Index('ix_bankloan_status_date', 'status', 'loan_date'),
    )

# ============================================================================
# 人事管理模型
# ============================================================================

class Employee(Base):
    """員工資料表"""
    __tablename__ = 'employees'

    id = Column(Integer, primary_key=True)

    # 基本資料
    title = Column(String(100), comment='職稱')
    emp_id = Column(String(50), unique=True, comment='員工編號', index=True)
    name = Column(String(100), nullable=False, comment='姓名', index=True)
    identity = Column(String(20), unique=True, comment='身份證號', index=True)
    onboard_date = Column(Date, comment='到職日', index=True)
    leave_date = Column(Date, comment='離職日', index=True)
    department_name = Column(String(100), comment='部門')
    address = Column(String(500), comment='通訊地址')
    phone = Column(String(20), comment='聯絡電話')
    email = Column(String(200), comment='電子信箱')

    # 薪資資訊
    salary = Column(Integer, default=0, comment='本薪')
    meal = Column(Integer, default=0, comment='伙食費')
    bank = Column(String(100), comment='薪資匯款銀行')
    bank_account = Column(String(50), comment='薪資匯款帳號')

    # 保險身份
    insurance_identity = Column(String(20), comment='保險身份')  # 負責人/勞工
    labor_insurance = Column(String(10), comment='是否投保勞保、勞退')  # yes/no
    health_insurance = Column(String(10), comment='是否投保健保')  # yes/no

    # 健康保險相關
    health_insurance_date = Column(Date, comment='健保加保日')
    health_subsidy_qualification = Column(String(20), comment='本人健保補助資格')
    health_law_effective_date = Column(Date, comment='健保級距法規生效日')
    health_level = Column(String(10), comment='健保投保級距')

    # 健保眷屬
    dependents_none = Column(Integer, default=0, comment='健保眷屬-無補助')
    dependents_1_4 = Column(Integer, default=0, comment='健保眷屬-補助1/4')
    dependents_1_2 = Column(Integer, default=0, comment='健保眷屬-補助1/2')
    dependents_local = Column(Integer, default=0, comment='健保眷屬-補助地區人口保費')
    dependents_full = Column(Integer, default=0, comment='健保眷屬-補助全額')

    # 勞工保險相關
    labor_insurance_date = Column(Date, comment='勞保加保日')
    labor_law_effective_date = Column(Date, comment='勞保級距法規生效日')
    labor_level = Column(String(10), comment='勞保投保級距')
    labor_insurance_items = Column(String(200), comment='勞保投保項目')  # 存儲多選項目，用逗號分隔

    # 職業災害保險相關
    occupational_law_effective_date = Column(Date, comment='職保級距法規生效日')
    occupational_level = Column(String(10), comment='職保投保級距')

    # 系統欄位
    created_at = Column(DateTime, default=get_taiwan_time, comment='建立時間', index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')
    is_active = Column(Boolean, default=True, comment='是否啟用', index=True)

class SalarySetting(Base):
    """薪資設定資料表"""
    __tablename__ = 'salary_settings'

    id = Column(Integer, primary_key=True)
    payday = Column(Integer, default=10, comment='發薪日（每月幾號）')
    fund_account_id = Column(Integer, ForeignKey('account.id'), comment='資金帳戶ID', index=True)
    days_type = Column(String(20), default='calendar', comment='計算基準（calendar=日曆天數，fixed=固定30天）')

    # 系統欄位
    created_at = Column(DateTime, default=get_taiwan_time, comment='建立時間')
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')

    # 關聯
    fund_account = relationship('Account', backref='salary_settings')

class ServiceReward(Base):
    """勞務報酬資料表"""
    __tablename__ = 'service_rewards'

    id = Column(Integer, primary_key=True)

    # 基本資料
    form_type = Column(String(20), nullable=False, comment='表單類型', index=True)  # company/personal
    create_date = Column(Date, nullable=False, comment='建立日期', index=True)
    name = Column(String(100), nullable=False, comment='姓名')
    email = Column(String(100), comment='電子郵件')
    phone = Column(String(20), comment='電話')

    # 公司填寫特有欄位
    id_number = Column(String(20), comment='身份證號')
    address = Column(String(200), comment='戶籍地址')

    # 身份資訊
    identity_type = Column(String(20), comment='身份類別')  # 本國人/外國人
    no_health_insurance = Column(String(10), comment='免扣健保')  # 是/否

    # 勞務資訊
    service_content = Column(String(200), nullable=False, comment='勞務內容')
    declaration_type = Column(String(10), nullable=False, comment='申報類別', index=True)  # 9A/9B/50/92
    business_category = Column(String(10), comment='執行業務類別')
    confirmation_method = Column(String(20), comment='勞報確認')  # online/paper
    service_start_date = Column(Date, comment='勞務開始日期')
    service_end_date = Column(Date, comment='勞務結束日期')

    # 付款資訊
    payment_account_id = Column(Integer, ForeignKey('account.id'), comment='付款資金帳戶', index=True)
    payment_method = Column(String(20), comment='付款方式')
    amount = Column(Integer, nullable=False, comment='金額')
    is_actual_amount = Column(Boolean, default=False, comment='是否為實際金額')

    # 所有人提供資料
    owner_bank_code = Column(String(10), comment='銀行代碼')
    owner_bank_account = Column(String(30), comment='銀行帳號')
    owner_account_name = Column(String(100), comment='帳戶名稱')

    # 檔案上傳
    owner_passbook = Column(String(200), comment='存摺影本檔案')
    owner_id_front = Column(String(200), comment='身份證正面檔案')
    owner_id_back = Column(String(200), comment='身份證背面檔案')

    # 其他資訊
    department_id = Column(Integer, ForeignKey('department.id'), comment='部門', index=True)
    project_id = Column(Integer, ForeignKey('project.id'), comment='專案', index=True)
    notice = Column(Text, comment='通知事項')
    note = Column(Text, comment='備註')

    # 狀態
    status = Column(String(20), default='draft', comment='狀態', index=True)  # draft/submitted/approved/paid

    # 系統欄位
    created_at = Column(DateTime, default=get_taiwan_time, comment='建立時間')
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')

    # 關聯
    payment_account = relationship('Account', backref='service_rewards')
    department = relationship('Department', backref='service_rewards')
    project = relationship('Project', backref='service_rewards')

    # 複合索引
    __table_args__ = (
        Index('ix_service_reward_date_type', 'create_date', 'declaration_type'),
        Index('ix_service_reward_status_date', 'status', 'create_date'),
    )

class ShareAccount(Base):
    """分享帳簿資料表"""
    __tablename__ = 'share_account'
    id = Column(Integer, primary_key=True)
    date = Column(String(20), nullable=False, index=True)               # 日期
    type = Column(String(20), nullable=False, index=True)               # 類型
    range = Column(String(50))                                          # 範圍
    user = Column(String(100), nullable=False, index=True)              # 使用者
    query_type = Column(String(20), nullable=False, index=True)         # 查詢類型
    start_date = Column(String(20), index=True)                         #開始日期
    end_date = Column(String(20), index=True)                           #結束日期
    expired = Column(Boolean, default=False, index=True)               #是否過期

class Transfer(Base):
    __tablename__ = 'transfers'
    id = Column(Integer, primary_key=True)
    out_account_id = Column(Integer, ForeignKey('account.id'), index=True)
    in_account_id = Column(Integer, ForeignKey('account.id'), index=True)
    subject_code = Column(String(50), index=True)
    amount = Column(Float, CheckConstraint('amount > 0'), nullable=False, comment='轉帳金額')
    fee = Column(Float, CheckConstraint('fee >= 0'), default=0, comment='手續費')
    status = Column(String(20), CheckConstraint("status IN ('pending', 'completed', 'cancelled')"), default='pending', index=True, comment='轉帳狀態')
    note = Column(Text)
    transfer_date = Column(Date, index=True)
    voucher = Column(String(255))  # 憑證圖檔路徑
    
    # 審計欄位 (新增)
    created_at = Column(DateTime, default=get_taiwan_time, index=True, comment='建立時間')
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')
    created_by = Column(String(100), comment='建立者')
    updated_by = Column(String(100), comment='最後修改者')
    is_deleted = Column(Boolean, default=False, index=True, comment='是否已刪除')
    deleted_at = Column(DateTime, comment='刪除時間')
    deleted_by = Column(String(100), comment='刪除者')

    out_account = relationship("Account", foreign_keys=[out_account_id])
    in_account = relationship("Account", foreign_keys=[in_account_id])
    
    # 複合索引和約束
    __table_args__ = (
        Index('ix_transfer_date_accounts', 'transfer_date', 'out_account_id', 'in_account_id'),
        Index('ix_transfer_amount_date', 'amount', 'transfer_date'),
        Index('ix_transfer_status_date', 'status', 'transfer_date'),
        CheckConstraint('out_account_id != in_account_id', name='check_different_accounts'),
    )

# ============================================================================
# 用戶權限模型
# ============================================================================

class User(Base):
    """使用者資料表"""
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True)
    username = Column(String(64), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), comment='密碼雜湊')
    full_name = Column(String(100), comment='真實姓名')
    is_active = Column(Boolean, default=True, comment='是否啟用')
    last_login = Column(DateTime, comment='最後登入時間')
    
    # 多租戶關聯
    tenant_id = Column(Integer, ForeignKey('tenants.id'), index=True, comment='租戶ID')
    is_tenant_admin = Column(Boolean, default=False, comment='是否為租戶管理員')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)
    
    # 關聯關係
    tenant = relationship("Tenant", back_populates="users")
    
    # Flask-Login需要的屬性，改為直接使用屬性而非方法
    @property
    def is_authenticated(self):
        """用戶是否已認證，供Flask-Login使用"""
        return True
    
    @property
    def is_anonymous(self):
        """用戶是否為匿名用戶，供Flask-Login使用"""
        return False
        
    # Flask-Login需要的方法
    def get_id(self):
        """返回用戶ID，供Flask-Login使用"""
        return str(self.id)
    
    def get_roles(self):
        """獲取用戶角色列表"""
        from models.auth_models import user_roles, Role
        from database import get_db
        
        # 確保有用戶ID
        if not hasattr(self, 'id') or self.id is None:
            return []
            
        with get_db() as db:
            roles = db.query(Role).join(user_roles).filter(user_roles.c.user_id == self.id).all()
            # 返回分離的角色實例，避免 DetachedInstanceError
            detached_roles = []
            for role in roles:
                detached_role = Role()
                detached_role.id = role.id
                detached_role.name = role.name
                detached_role.display_name = role.display_name
                detached_role.description = role.description
                detached_role.is_active = role.is_active
                detached_role.created_at = role.created_at
                detached_role.updated_at = role.updated_at
                detached_roles.append(detached_role)
            return detached_roles
    
    def has_role(self, role_name):
        """檢查用戶是否有特定角色"""
        roles = self.get_roles()
        return any(role.name == role_name for role in roles)


# ============================================================================
# 新的重構模型（將取代 Money 表）
# ============================================================================

class Transaction(Base):
    """交易主表 - 存儲交易的基本資訊"""
    __tablename__ = 'transactions'

    id = Column(Integer, primary_key=True)
    transaction_date = Column(Date, nullable=False, comment='交易日期', index=True)
    description = Column(Text, nullable=False, comment='交易描述')
    total_amount = Column(Integer, nullable=False, default=0, comment='總金額')
    tax_amount = Column(Integer, default=0, comment='稅額')
    extra_fee = Column(Integer, default=0, comment='手續費')
    transaction_type = Column(String(20), nullable=False, default='income', comment='交易類型：income=收入, expense=支出, transfer=轉帳', index=True)

    # 關聯資訊
    account_id = Column(Integer, ForeignKey('account.id'), comment='資金帳戶', index=True)
    payment_identity_id = Column(Integer, ForeignKey('payment_identity.id'), comment='收支對象', index=True)
    department_id = Column(Integer, ForeignKey('department.id'), comment='部門', index=True)
    project_id = Column(Integer, ForeignKey('project.id'), comment='專案', index=True)

    # 發票相關
    is_paper = Column(Boolean, default=False, comment='是否為收據類憑證')
    invoice_number = Column(String(50), comment='發票號碼', index=True)
    tax_type = Column(String(50), comment='稅別')
    buyer_tax_id = Column(String(50), comment='買方統編')
    seller_tax_id = Column(String(50), comment='賣方統編')
    invoice_date = Column(String(50), comment='發票日期')

    # 付款狀態
    is_paid = Column(Boolean, default=False, comment='是否已收付款', index=True)
    should_paid_date = Column(DateTime, comment='應收付款日期', index=True)
    paid_date = Column(DateTime, comment='實收付款日期', index=True)

    # 其他資訊
    note = Column(Text, comment='備註')
    tags = Column(Text, comment='標籤')
    image_path = Column(String(255), comment='附件路徑')

    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)
    created_by = Column(String(100), comment='建立者')
    updated_by = Column(String(100), comment='修改者')

    # 關聯
    account = relationship("Account", back_populates="transactions")
    payment_identity = relationship("PaymentIdentity", back_populates="transactions")
    department = relationship("Department", back_populates="transactions")
    project = relationship("Project", back_populates="transactions")
    journal_entries = relationship("JournalEntry", back_populates="transaction", cascade="all, delete-orphan")

    # 索引
    __table_args__ = (
        Index('ix_transaction_date_account', 'transaction_date', 'account_id'),
        Index('ix_transaction_payment_identity', 'payment_identity_id', 'transaction_date'),
        Index('ix_transaction_dept_project', 'department_id', 'project_id'),
        Index('ix_transaction_invoice', 'invoice_number'),
    )


class JournalEntry(Base):
    """會計分錄表 - 存儲每筆交易的借貸分錄"""
    __tablename__ = 'journal_entries'

    id = Column(Integer, primary_key=True)
    transaction_id = Column(Integer, ForeignKey('transactions.id'), nullable=False, comment='交易ID', index=True)
    subject_code = Column(String(50), ForeignKey('account_subject.code'), nullable=False, comment='會計科目代碼', index=True)
    debit_amount = Column(Integer, default=0, comment='借方金額')
    credit_amount = Column(Integer, default=0, comment='貸方金額')
    description = Column(Text, comment='分錄說明')
    entry_type = Column(String(20), default='primary', comment='分錄類型：primary=主要分錄, balance=平衡分錄', index=True)

    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True)

    # 檢查約束：借方和貸方金額不能同時為0，也不能同時有值
    __table_args__ = (
        CheckConstraint(
            '(debit_amount > 0 AND credit_amount = 0) OR (debit_amount = 0 AND credit_amount > 0)',
            name='check_debit_credit_exclusive'
        ),
        Index('ix_journal_transaction_subject', 'transaction_id', 'subject_code'),
        Index('ix_journal_subject_date', 'subject_code', 'created_at'),
    )

    # 關聯
    transaction = relationship("Transaction", back_populates="journal_entries")
    account_subject = relationship("AccountSubject", back_populates="journal_entries")

    def get_entry_side(self):
        """返回分錄方向"""
        debit = getattr(self, 'debit_amount', 0) or 0
        return 'DEBIT' if debit > 0 else 'CREDIT'

    def get_amount(self):
        """返回分錄金額"""
        debit = getattr(self, 'debit_amount', 0) or 0
        credit = getattr(self, 'credit_amount', 0) or 0
        return debit if debit > 0 else credit


# ============================================================================
# 資料庫設定
# ============================================================================

# 獲取可執行文件的路徑（包括 exe 或 py）
executable_path = sys.executable

# 如果是打包成 exe 後的可執行文件，則獲取該文件所在目錄
if getattr(sys, 'frozen', False):
    executable_dir = os.path.dirname(executable_path)
else:
    # 如果是直接運行 .py 文件，則獲取該文件所在目錄
    executable_dir = os.path.dirname(os.path.abspath(__file__))

# 使用相對路徑構建數據庫連接URI
db_name = 'app.db'
db_path = os.path.join(executable_dir, db_name)
DATABASE_URI = f"sqlite:///{db_path}"

# 建立資料庫引擎（這裡用 SQLite 範例）
engine = create_engine(DATABASE_URI)

# 建立所有資料表
Base.metadata.create_all(engine)

# 注意：session 管理已移至 database.py，此處不再建立 session
