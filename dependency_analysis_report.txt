================================================================================
Python 專案依賴關係分析報告
================================================================================

1. 核心檔案 (main.py 和直接依賴)
----------------------------------------
  ✓ config/config.py
  ✓ database.py
  ✓ database.py
  ✓ main.py
  ✓ routes/account.py
  ✓ routes/accounting.py
  ✓ routes/accounting_other.py
  ✓ routes/admin.py
  ✓ routes/api.py
  ✓ routes/assets.py
  ✓ routes/audit.py
  ✓ routes/auth.py
  ✓ routes/bankloan.py
  ✓ routes/database_monitor.py
  ✓ routes/debug_balance_sheet.py
  ✓ routes/error_monitor.py
  ✓ routes/fund_record.py
  ✓ routes/income_expense.py
  ✓ routes/journal_validation.py
  ✓ routes/main.py
  ✓ routes/monitoring.py
  ✓ routes/new_reports.py
  ✓ routes/payroll.py
  ✓ routes/performance_dashboard.py
  ✓ routes/performance_test.py
  ✓ routes/permission_admin.py
  ✓ routes/reports.py
  ✓ routes/service_reward.py
  ✓ routes/settings.py
  ✓ routes/share_account.py
  ✓ routes/transfer.py
  ✓ services/account_service.py
  ✓ services/account_service.py
  ✓ utils/advanced_cache.py
  ✓ utils/alert_system.py
  ✓ utils/cache_manager.py
  ✓ utils/cache_manager.py
  ✓ utils/db_maintenance.py
  ✓ utils/db_pool_monitor.py
  ✓ utils/error_handler.py
  ✓ utils/error_monitor.py
  ✓ utils/error_test.py
  ✓ utils/memory_optimizer.py
  ✓ utils/performance_benchmarking.py
  ✓ utils/performance_monitor.py
  ✓ utils/query_optimizer.py

2. ROUTES 檔案 (共 28 個)
----------------------------------------
  ✓ routes/account.py
  ✓ routes/accounting.py
  ✓ routes/accounting_other.py
  ✓ routes/admin.py
  ✓ routes/api.py
  ✓ routes/assets.py
  ✓ routes/audit.py
  ✓ routes/auth.py
  ✓ routes/bankloan.py
  ✓ routes/base_route.py
  ✓ routes/database_monitor.py
  ✓ routes/debug_balance_sheet.py
  ✓ routes/error_monitor.py
  ✓ routes/fund_record.py
  ✓ routes/income_expense.py
  ✓ routes/journal_validation.py
  ✓ routes/main.py
  ✓ routes/monitoring.py
  ✓ routes/new_reports.py
  ✓ routes/payroll.py
  ✓ routes/performance_dashboard.py
  ✓ routes/performance_test.py
  ✓ routes/permission_admin.py
  ✓ routes/reports.py
  ✓ routes/service_reward.py
  ✓ routes/settings.py
  ✓ routes/share_account.py
  ✓ routes/transfer.py

2. UTILS 檔案 (共 21 個)
----------------------------------------
  ✓ utils/advanced_cache.py
  ✓ utils/alert_system.py
  ✓ utils/audit_helper.py
  ✓ utils/auth_decorators.py
  ✓ utils/cache_manager.py
  ✓ utils/db_analyzer.py
  ✓ utils/db_maintenance.py
  ✓ utils/db_optimizer.py
  ✓ utils/db_pool_monitor.py
  ✓ utils/error_handler.py
  ✓ utils/error_monitor.py
  ✓ utils/error_test.py
  ✓ utils/format_helper.py
  ✓ utils/helpers.py
  ✓ utils/memory_optimizer.py
  ✓ utils/menu_decorator.py
  ✓ utils/performance_benchmarking.py
  ✓ utils/performance_monitor.py
  ✓ utils/query_helper.py
  ✓ utils/query_optimizer.py
  ✓ utils/report_generator.py

2. SERVICES 檔案 (共 10 個)
----------------------------------------
  ✓ services/account_service.py
  ✓ services/auth_service.py
  ✓ services/balance_sheet_service.py
  ✓ services/dashboard_service.py
  ✓ services/journal_validator.py
  ✓ services/menu_service.py
  ✓ services/money_service.py
  ✓ services/new_balance_sheet_service.py
  ✓ services/new_income_statement_service.py
  ✓ services/optimized_query_service.py

2. MODELS 檔案 (共 1 個)
----------------------------------------
  ✓ models/auth_models.py

2. CONFIG 檔案 (共 1 個)
----------------------------------------
  ✓ config/config.py

2. OTHERS 檔案 (共 8 個)
----------------------------------------
  ✓ data/bank_data.py
  ✓ data/health_insurance_levels.py
  ✓ data/labor_insurance_levels.py
  ✓ data/menu_data.py
  ✓ data/pension_levels.py
  ✓ database.py
  ✓ main.py
  ✓ model.py

3. 可能未使用的檔案 (共 84 個)
----------------------------------------
  add_tax_subjects.py/ 目錄:
    ? add_tax_subjects.py
  alembic/ 目錄:
    ? alembic/env.py
    ? alembic/versions/2401f6dab479_add_soft_delete_fields_to_.py
    ? alembic/versions/54d22b62dce6_add_tax_column_to_money_table.py
    ? alembic/versions/84cba4485354_add_payment_identity_type_model.py
    ? alembic/versions/8f3129f38236_change_a_time_from_datetime_to_date_in_.py
    ? alembic/versions/cca36410de1e_add_indexes_and_backref_relationships.py
  analyze_dependencies.py/ 目錄:
    ? analyze_dependencies.py
  check_accounts.py/ 目錄:
    ? check_accounts.py
  check_opening_records.py/ 目錄:
    ? check_opening_records.py
  clear_opening_records.py/ 目錄:
    ? clear_opening_records.py
  config/ 目錄:
    ? config/__init__.py
    ? config/logging_config.py
  debug_opening.py/ 目錄:
    ? debug_opening.py
  fix_account_subject_codes.py/ 目錄:
    ? fix_account_subject_codes.py
  fix_all_layouts.py/ 目錄:
    ? fix_all_layouts.py
  fix_sidebar_colors.py/ 目錄:
    ? fix_sidebar_colors.py
  init_auth_system.py/ 目錄:
    ? init_auth_system.py
  migrate_money_to_transaction.py/ 目錄:
    ? migrate_money_to_transaction.py
  migrations/ 目錄:
    ? migrations/add_entry_type_and_transaction_type.py
  models/ 目錄:
    ? models/bank_model.py
    ? models/new_models.py
    ? models/payment_identity_type.py
  routes/ 目錄:
    ? routes/__init__.py
    ? routes/modal_demo.py
    ? routes/new_income_expense.py
    ? routes/payment_identity_type.py
  scripts/ 目錄:
    ? scripts/add_database_indexes.py
    ? scripts/add_missing_permissions.py
    ? scripts/archive/create_employee_table.py
    ? scripts/archive/create_salary_setting_table.py
    ? scripts/archive/migrate_bank_data.py
    ? scripts/archive/update_sidebar_layout.py
    ? scripts/archive/update_subject_categories.py
    ? scripts/archive/update_subject_categories_v2.py
    ? scripts/bank_data_gen.py
    ? scripts/log_monitor.py
    ? scripts/migrate_user_table.py
    ? scripts/organize_docs.py
    ? scripts/performance_test.py
    ? scripts/run_tests.py
  services/ 目錄:
    ? services/__init__.py
    ? services/bank_service.py
    ? services/income_statement_service.py
    ? services/new_journal_validator.py
    ? services/subject_service.py
    ? services/transaction_service.py
  test_bulma_integration.py/ 目錄:
    ? test_bulma_integration.py
  test_opening.py/ 目錄:
    ? test_opening.py
  tests/ 目錄:
    ? tests/conftest.py
    ? tests/model2.py
    ? tests/test_api_endpoints.py
    ? tests/test_business_logic.py
    ? tests/test_employee_system.py
    ? tests/test_integration.py
    ? tests/test_load_testing.py
    ? tests/test_logging.py
    ? tests/test_models.py
    ? tests/test_performance.py
    ? tests/test_performance_fixed.py
    ? tests/test_quick_fixes.py
    ? tests/test_routes.py
    ? tests/test_security_recommendations.py
    ? tests/test_security_sql_injection.py
    ? tests/test_subject_manage.py
    ? tests/test_utils.py
  trash/ 目錄:
    ? trash/accounting_optimized.py
    ? trash/add_tax_subjects.py
    ? trash/check_accounts.py
    ? trash/check_opening_records.py
    ? trash/clear_opening_records.py
    ? trash/debug_opening.py
    ? trash/demo_permission_setup.py
    ? trash/fix_account_subject_codes.py
    ? trash/test_opening.py
    ? trash/update_balance_sheet_template.py
  utils/ 目錄:
    ? utils/__init__.py
    ? utils/api_response.py
    ? utils/backup_manager.py
    ? utils/base_view.py
    ? utils/batch_operations.py
    ? utils/db_session_helper.py
    ? utils/logging_middleware.py
    ? utils/security.py

4. 統計摘要
----------------------------------------
  總 Python 檔案數: 153
  已分析檔案數: 69
  實際使用檔案數: 70  # +1 for main.py
  可能未使用檔案數: 84
  使用率: 45.8%