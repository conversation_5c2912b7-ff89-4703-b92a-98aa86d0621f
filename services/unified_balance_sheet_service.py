"""
統一的資產負債表服務
整合了三個版本的優點：
1. 基礎版本的完整業務邏輯
2. 新版本的現代資料模型支援
3. 優化版本的高性能查詢
"""
from typing import Dict, List, Optional, Union
from datetime import date, datetime
from collections import defaultdict
from sqlalchemy import func, and_
from sqlalchemy.orm import Session, joinedload

from services.base import BaseService, ServiceError, ValidationError
from model import AccountSubject, Money, Account, Transaction, JournalEntry
from utils.database.query_limits import safe_all


class UnifiedBalanceSheetService(BaseService):
    """統一的資產負債表服務"""
    
    def generate_balance_sheet(self, report_date: Union[str, date] = None, 
                             use_optimized: bool = True) -> Dict:
        """
        生成資產負債表
        
        Args:
            report_date: 報表日期，預設為今天
            use_optimized: 是否使用優化的查詢方式
        
        Returns:
            資產負債表數據字典
        """
        if report_date is None:
            report_date = date.today()
        elif isinstance(report_date, str):
            report_date = datetime.strptime(report_date, '%Y-%m-%d').date()
        
        self.log_operation('generate_balance_sheet', {'report_date': str(report_date)})
        
        try:
            with self.get_db_session() as db:
                if use_optimized:
                    return self._generate_optimized_balance_sheet(db, report_date)
                else:
                    return self._generate_standard_balance_sheet(db, report_date)
        except Exception as e:
            self.logger.error(f"生成資產負債表失敗: {e}")
            raise ServiceError(f"生成資產負債表失敗: {str(e)}")
    
    def _generate_optimized_balance_sheet(self, db: Session, report_date: date) -> Dict:
        """優化版本的資產負債表生成"""
        
        # 獲取會計科目映射
        subject_map = self._get_subject_map(db)
        
        # 使用 SQL 聚合查詢計算各科目餘額
        subject_balances = self._calculate_optimized_subject_balances(db, report_date)
        
        # 整合帳戶餘額
        account_balances = self._get_account_initial_balances(db)
        subject_balances = self._integrate_account_balances(
            subject_balances, account_balances, subject_map
        )
        
        # 計算本期損益
        net_income = self._calculate_net_income_optimized(db, report_date)
        if net_income != 0:
            subject_balances['3310'] = {
                'subject_code': '3310',
                'subject_name': '本期損益',
                'total_debit': 0 if net_income > 0 else abs(net_income),
                'total_credit': net_income if net_income > 0 else 0,
                'balance': net_income,
                'category': '權益'
            }
        
        # 分類整理
        return self._organize_balance_sheet_data(subject_balances, subject_map, report_date)
    
    def _generate_standard_balance_sheet(self, db: Session, report_date: date) -> Dict:
        """標準版本的資產負債表生成（向後相容）"""
        
        subject_map = self._get_subject_map(db)
        
        # 獲取所有交易記錄
        transactions = safe_all(
            db.query(Money).options(
                joinedload(Money.account)
            ).filter(
                Money.a_time <= report_date
            ),
            context="report"
        )
        
        # 計算各科目餘額
        subject_balances = self._calculate_subject_balances_from_transactions(
            transactions, subject_map
        )
        
        # 整合帳戶餘額
        account_balances = self._get_account_initial_balances(db)
        subject_balances = self._integrate_account_balances(
            subject_balances, account_balances, subject_map
        )
        
        # 計算本期損益
        net_income = self._calculate_net_income_from_transactions(transactions)
        if net_income != 0:
            subject_balances['3310'] = {
                'subject_code': '3310',
                'subject_name': '本期損益',
                'total_debit': 0 if net_income > 0 else abs(net_income),
                'total_credit': net_income if net_income > 0 else 0,
                'balance': net_income,
                'category': '權益'
            }
        
        return self._organize_balance_sheet_data(subject_balances, subject_map, report_date)
    
    def _get_subject_map(self, db: Session) -> Dict[str, AccountSubject]:
        """獲取會計科目映射"""
        subjects = safe_all(db.query(AccountSubject), context="report")
        return {subj.code: subj for subj in subjects}
    
    def _calculate_optimized_subject_balances(self, db: Session, report_date: date) -> Dict:
        """使用優化查詢計算科目餘額"""
        
        # 使用 SQL 聚合查詢
        subject_aggregates = db.query(
            Money.subject_code,
            func.sum(Money.lend).label('total_lend'),
            func.sum(Money.borrow).label('total_borrow'),
            func.count(Money.id).label('transaction_count')
        ).filter(
            and_(
                Money.a_time <= report_date,
                Money.is_deleted == False
            )
        ).group_by(Money.subject_code).all()
        
        subject_balances = {}
        for row in subject_aggregates:
            if row.subject_code:
                total_lend = float(row.total_lend or 0)
                total_borrow = float(row.total_borrow or 0)
                balance = total_lend - total_borrow
                
                subject_balances[row.subject_code] = {
                    'subject_code': row.subject_code,
                    'total_debit': total_borrow,
                    'total_credit': total_lend,
                    'balance': balance,
                    'transaction_count': row.transaction_count
                }
        
        return subject_balances
    
    def _calculate_subject_balances_from_transactions(self, transactions: List, subject_map: Dict) -> Dict:
        """從交易記錄計算科目餘額（標準方式）"""
        
        subject_totals = defaultdict(lambda: {'debit': 0, 'credit': 0})
        
        for transaction in transactions:
            if transaction.subject_code and transaction.subject_code in subject_map:
                subject_totals[transaction.subject_code]['debit'] += float(transaction.borrow or 0)
                subject_totals[transaction.subject_code]['credit'] += float(transaction.lend or 0)
        
        subject_balances = {}
        for code, totals in subject_totals.items():
            balance = totals['credit'] - totals['debit']
            subject_balances[code] = {
                'subject_code': code,
                'total_debit': totals['debit'],
                'total_credit': totals['credit'],
                'balance': balance
            }
        
        return subject_balances
    
    def _get_account_initial_balances(self, db: Session) -> Dict:
        """獲取帳戶期初餘額"""
        accounts = safe_all(db.query(Account), context="report")
        account_balances = {}
        
        for account in accounts:
            if account.initial_balance and account.initial_balance != 0:
                account_balances[account.code] = {
                    'code': account.code,
                    'name': account.name,
                    'initial_balance': float(account.initial_balance),
                    'subject_code': account.subject_code
                }
        
        return account_balances
    
    def _integrate_account_balances(self, subject_balances: Dict, account_balances: Dict, subject_map: Dict) -> Dict:
        """整合帳戶餘額到科目餘額"""
        
        for account_code, account_data in account_balances.items():
            subject_code = account_data['subject_code']
            initial_balance = account_data['initial_balance']
            
            if subject_code and subject_code in subject_map:
                if subject_code not in subject_balances:
                    subject_balances[subject_code] = {
                        'subject_code': subject_code,
                        'total_debit': 0,
                        'total_credit': 0,
                        'balance': 0
                    }
                
                # 根據科目性質調整餘額
                subject = subject_map[subject_code]
                if subject.debit_credit == '借':
                    if initial_balance >= 0:
                        subject_balances[subject_code]['total_debit'] += initial_balance
                    else:
                        subject_balances[subject_code]['total_credit'] += abs(initial_balance)
                else:
                    if initial_balance >= 0:
                        subject_balances[subject_code]['total_credit'] += initial_balance
                    else:
                        subject_balances[subject_code]['total_debit'] += abs(initial_balance)
                
                # 重新計算餘額
                subject_balances[subject_code]['balance'] = (
                    subject_balances[subject_code]['total_credit'] - 
                    subject_balances[subject_code]['total_debit']
                )
        
        return subject_balances
    
    def _calculate_net_income_optimized(self, db: Session, report_date: date) -> float:
        """優化版本計算本期損益"""
        
        # 計算收入（4開頭科目）
        income_result = db.query(
            func.sum(Money.lend - Money.borrow).label('total_income')
        ).filter(
            and_(
                Money.a_time <= report_date,
                Money.subject_code.like('4%'),
                Money.is_deleted == False
            )
        ).scalar() or 0
        
        # 計算費用（5開頭科目）
        expense_result = db.query(
            func.sum(Money.borrow - Money.lend).label('total_expense')
        ).filter(
            and_(
                Money.a_time <= report_date,
                Money.subject_code.like('5%'),
                Money.is_deleted == False
            )
        ).scalar() or 0
        
        return float(income_result) - float(expense_result)
    
    def _calculate_net_income_from_transactions(self, transactions: List) -> float:
        """從交易記錄計算本期損益"""
        
        income_total = 0  # 收入
        expense_total = 0  # 費用
        
        for transaction in transactions:
            if transaction.subject_code:
                if transaction.subject_code.startswith('4'):  # 收入科目
                    income_total += float(transaction.lend or 0) - float(transaction.borrow or 0)
                elif transaction.subject_code.startswith('5'):  # 費用科目
                    expense_total += float(transaction.borrow or 0) - float(transaction.lend or 0)
        
        return income_total - expense_total
    
    def _organize_balance_sheet_data(self, subject_balances: Dict, subject_map: Dict, report_date: date) -> Dict:
        """整理資產負債表資料結構"""
        
        # 分類科目
        assets = defaultdict(list)        # 資產
        liabilities = defaultdict(list)   # 負債
        equity = defaultdict(list)        # 權益
        
        total_assets = 0
        total_liabilities = 0
        total_equity = 0
        
        for code, balance_data in subject_balances.items():
            if code not in subject_map:
                continue
                
            subject = subject_map[code]
            balance = balance_data['balance']
            
            # 只顯示有餘額的科目
            if balance == 0:
                continue
            
            item_data = {
                'code': code,
                'name': subject.name,
                'balance': balance,
                'total_debit': balance_data.get('total_debit', 0),
                'total_credit': balance_data.get('total_credit', 0)
            }
            
            # 根據科目代碼分類
            if code.startswith('1'):  # 資產
                category = self._get_asset_category(code)
                assets[category].append(item_data)
                total_assets += balance
                
            elif code.startswith('2'):  # 負債
                category = self._get_liability_category(code)
                liabilities[category].append(item_data)
                total_liabilities += balance
                
            elif code.startswith('3'):  # 權益
                category = self._get_equity_category(code)
                equity[category].append(item_data)
                total_equity += balance
        
        # 排序各類別內的科目
        for category in assets:
            assets[category].sort(key=lambda x: x['code'])
        for category in liabilities:
            liabilities[category].sort(key=lambda x: x['code'])
        for category in equity:
            equity[category].sort(key=lambda x: x['code'])
        
        return {
            'report_date': report_date.isoformat(),
            'assets': dict(assets),
            'liabilities': dict(liabilities),
            'equity': dict(equity),
            'totals': {
                'total_assets': total_assets,
                'total_liabilities': total_liabilities,
                'total_equity': total_equity,
                'balance_check': abs(total_assets - (total_liabilities + total_equity)) < 0.01
            },
            'metadata': {
                'generation_time': datetime.now().isoformat(),
                'total_subjects': len(subject_balances),
                'service_version': 'unified'
            }
        }
    
    def _get_asset_category(self, code: str) -> str:
        """根據科目代碼獲取資產分類"""
        if code.startswith('11'):
            return '流動資產'
        elif code.startswith('12'):
            return '非流動資產'
        elif code.startswith('13'):
            return '其他資產'
        else:
            return '資產'
    
    def _get_liability_category(self, code: str) -> str:
        """根據科目代碼獲取負債分類"""
        if code.startswith('21'):
            return '流動負債'
        elif code.startswith('22'):
            return '非流動負債'
        else:
            return '負債'
    
    def _get_equity_category(self, code: str) -> str:
        """根據科目代碼獲取權益分類"""
        if code.startswith('31'):
            return '股本'
        elif code.startswith('32'):
            return '資本公積'
        elif code.startswith('33'):
            return '保留盈餘'
        else:
            return '權益'