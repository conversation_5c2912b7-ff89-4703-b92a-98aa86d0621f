"""
統一的帳戶服務
解決了重複定義問題，整合了：
1. account_service.py 中的 AccountService
2. money_service.py 中的 AccountService
提供完整的帳戶管理功能
"""
from typing import Dict, List, Optional, Union
from datetime import date, datetime
from decimal import Decimal
from sqlalchemy import func, and_, or_
from sqlalchemy.orm import Session, joinedload

from services.base import BaseService, CRUDService, ServiceError, ValidationError
from model import Account, AccountSubject, Money
from utils.database.query_limits import safe_all, safe_paginate


class UnifiedAccountService(CRUDService[Account]):
    """統一的帳戶服務"""
    
    def __init__(self, db_session: Session = None):
        super().__init__(Account, db_session)
    
    def create_account(self, account_data: Dict) -> Account:
        """
        創建新帳戶
        
        Args:
            account_data: 帳戶資料字典
                - code: 帳戶代碼（必填）
                - name: 帳戶名稱（必填）
                - subject_code: 科目代碼（必填）
                - initial_balance: 期初餘額（選填，預設0）
                - description: 描述（選填）
        """
        # 驗證必要欄位
        self.validate_input(account_data, ['code', 'name', 'subject_code'])
        
        # 檢查帳戶代碼是否已存在
        if self._is_account_code_exists(account_data['code']):
            raise ValidationError(f"帳戶代碼 {account_data['code']} 已存在")
        
        # 驗證科目代碼是否有效
        if not self._is_valid_subject_code(account_data['subject_code']):
            raise ValidationError(f"無效的科目代碼: {account_data['subject_code']}")
        
        # 設定預設值
        account_data.setdefault('initial_balance', 0)
        account_data.setdefault('is_active', True)
        
        self.log_operation('create_account', {'code': account_data['code']})
        
        return self.create(account_data)
    
    def get_account_by_code(self, code: str) -> Optional[Account]:
        """根據帳戶代碼獲取帳戶"""
        try:
            with self.get_db_session() as db:
                return db.query(Account).filter(Account.code == code).first()
        except Exception as e:
            self.logger.error(f"獲取帳戶失敗: {e}")
            raise ServiceError(f"獲取帳戶失敗: {str(e)}")
    
    def get_accounts_by_subject(self, subject_code: str) -> List[Account]:
        """根據科目代碼獲取所有相關帳戶"""
        try:
            with self.get_db_session() as db:
                return safe_all(
                    db.query(Account).filter(
                        Account.subject_code == subject_code,
                        Account.is_active == True
                    ).order_by(Account.code),
                    context="search"
                )
        except Exception as e:
            self.logger.error(f"獲取科目帳戶失敗: {e}")
            raise ServiceError(f"獲取科目帳戶失敗: {str(e)}")
    
    def get_active_accounts(self) -> List[Account]:
        """獲取所有啟用的帳戶"""
        return self.get_all({'is_active': True})
    
    def update_account_balance(self, account_code: str, new_balance: Union[float, Decimal]) -> bool:
        """
        更新帳戶餘額
        
        Args:
            account_code: 帳戶代碼
            new_balance: 新餘額
        """
        try:
            with self.get_db_session() as db:
                account = db.query(Account).filter(Account.code == account_code).first()
                if not account:
                    return False
                
                old_balance = account.initial_balance or 0
                account.initial_balance = float(new_balance)
                db.commit()
                
                self.log_operation('update_balance', {
                    'account_code': account_code,
                    'old_balance': old_balance,
                    'new_balance': float(new_balance)
                })
                
                return True
        except Exception as e:
            self.logger.error(f"更新帳戶餘額失敗: {e}")
            raise ServiceError(f"更新帳戶餘額失敗: {str(e)}")
    
    def calculate_account_current_balance(self, account_code: str, as_of_date: date = None) -> Dict:
        """
        計算帳戶當前餘額
        
        Args:
            account_code: 帳戶代碼
            as_of_date: 截止日期，預設為今天
            
        Returns:
            包含詳細餘額資訊的字典
        """
        if as_of_date is None:
            as_of_date = date.today()
        
        try:
            with self.get_db_session() as db:
                account = db.query(Account).filter(Account.code == account_code).first()
                if not account:
                    raise ValidationError(f"帳戶代碼 {account_code} 不存在")
                
                # 獲取期初餘額
                initial_balance = float(account.initial_balance or 0)
                
                # 計算期間內的交易總額
                transactions_summary = db.query(
                    func.sum(Money.lend).label('total_credit'),
                    func.sum(Money.borrow).label('total_debit'),
                    func.count(Money.id).label('transaction_count')
                ).filter(
                    and_(
                        Money.account_code == account_code,
                        Money.a_time <= as_of_date,
                        Money.is_deleted == False
                    )
                ).first()
                
                total_credit = float(transactions_summary.total_credit or 0)
                total_debit = float(transactions_summary.total_debit or 0)
                transaction_count = transactions_summary.transaction_count or 0
                
                # 計算當前餘額
                # 根據科目性質計算餘額
                subject = self._get_account_subject(db, account.subject_code)
                if subject and subject.debit_credit == '借':  # 借方科目
                    current_balance = initial_balance + total_debit - total_credit
                else:  # 貸方科目
                    current_balance = initial_balance + total_credit - total_debit
                
                return {
                    'account_code': account_code,
                    'account_name': account.name,
                    'subject_code': account.subject_code,
                    'subject_name': subject.name if subject else None,
                    'as_of_date': as_of_date.isoformat(),
                    'initial_balance': initial_balance,
                    'total_credit': total_credit,
                    'total_debit': total_debit,
                    'current_balance': current_balance,
                    'transaction_count': transaction_count,
                    'is_debit_account': subject.debit_credit == '借' if subject else True
                }
                
        except Exception as e:
            self.logger.error(f"計算帳戶餘額失敗: {e}")
            raise ServiceError(f"計算帳戶餘額失敗: {str(e)}")
    
    def get_account_transactions(self, account_code: str, 
                               start_date: date = None,
                               end_date: date = None,
                               limit: int = None) -> List[Dict]:
        """
        獲取帳戶交易記錄
        
        Args:
            account_code: 帳戶代碼
            start_date: 開始日期
            end_date: 結束日期
            limit: 記錄數量限制
        """
        try:
            with self.get_db_session() as db:
                query = db.query(Money).filter(
                    Money.account_code == account_code,
                    Money.is_deleted == False
                )
                
                if start_date:
                    query = query.filter(Money.a_time >= start_date)
                if end_date:
                    query = query.filter(Money.a_time <= end_date)
                
                query = query.order_by(Money.a_time.desc(), Money.id.desc())
                
                if limit:
                    query = query.limit(limit)
                
                transactions = safe_all(query, context="report")
                
                # 轉換為字典格式
                result = []
                for tx in transactions:
                    result.append({
                        'id': tx.id,
                        'date': tx.a_time.isoformat() if tx.a_time else None,
                        'description': tx.summary,
                        'debit': float(tx.borrow or 0),
                        'credit': float(tx.lend or 0),
                        'subject_code': tx.subject_code,
                        'account_code': tx.account_code
                    })
                
                return result
                
        except Exception as e:
            self.logger.error(f"獲取帳戶交易記錄失敗: {e}")
            raise ServiceError(f"獲取帳戶交易記錄失敗: {str(e)}")
    
    def get_accounts_balance_summary(self, as_of_date: date = None) -> List[Dict]:
        """
        獲取所有帳戶餘額匯總
        
        Args:
            as_of_date: 截止日期，預設為今天
        """
        if as_of_date is None:
            as_of_date = date.today()
        
        try:
            with self.get_db_session() as db:
                accounts = safe_all(
                    db.query(Account).filter(Account.is_active == True),
                    context="report"
                )
                
                balance_summary = []
                for account in accounts:
                    try:
                        balance_info = self.calculate_account_current_balance(
                            account.code, as_of_date
                        )
                        balance_summary.append(balance_info)
                    except Exception as e:
                        self.logger.warning(f"計算帳戶 {account.code} 餘額失敗: {e}")
                        continue
                
                # 按科目代碼排序
                balance_summary.sort(key=lambda x: (x['subject_code'], x['account_code']))
                
                return balance_summary
                
        except Exception as e:
            self.logger.error(f"獲取帳戶餘額匯總失敗: {e}")
            raise ServiceError(f"獲取帳戶餘額匯總失敗: {str(e)}")
    
    def search_accounts(self, keyword: str) -> List[Account]:
        """
        搜尋帳戶
        
        Args:
            keyword: 搜尋關鍵字（可搜尋代碼或名稱）
        """
        try:
            with self.get_db_session() as db:
                return safe_all(
                    db.query(Account).filter(
                        and_(
                            Account.is_active == True,
                            or_(
                                Account.code.like(f'%{keyword}%'),
                                Account.name.like(f'%{keyword}%')
                            )
                        )
                    ).order_by(Account.code),
                    context="search"
                )
        except Exception as e:
            self.logger.error(f"搜尋帳戶失敗: {e}")
            raise ServiceError(f"搜尋帳戶失敗: {str(e)}")
    
    def deactivate_account(self, account_code: str) -> bool:
        """
        停用帳戶（軟刪除）
        
        Args:
            account_code: 帳戶代碼
        """
        try:
            with self.get_db_session() as db:
                account = db.query(Account).filter(Account.code == account_code).first()
                if not account:
                    return False
                
                # 檢查是否有未完成的交易
                has_transactions = db.query(Money).filter(
                    Money.account_code == account_code,
                    Money.is_deleted == False
                ).first() is not None
                
                if has_transactions:
                    raise BusinessLogicError("帳戶有交易記錄，無法停用")
                
                account.is_active = False
                db.commit()
                
                self.log_operation('deactivate_account', {'account_code': account_code})
                
                return True
        except Exception as e:
            self.logger.error(f"停用帳戶失敗: {e}")
            raise ServiceError(f"停用帳戶失敗: {str(e)}")
    
    def _is_account_code_exists(self, code: str) -> bool:
        """檢查帳戶代碼是否已存在"""
        try:
            with self.get_db_session() as db:
                return db.query(Account).filter(Account.code == code).first() is not None
        except Exception:
            return False
    
    def _is_valid_subject_code(self, subject_code: str) -> bool:
        """驗證科目代碼是否有效"""
        try:
            with self.get_db_session() as db:
                return db.query(AccountSubject).filter(
                    AccountSubject.code == subject_code
                ).first() is not None
        except Exception:
            return False
    
    def _get_account_subject(self, db: Session, subject_code: str) -> Optional[AccountSubject]:
        """獲取帳戶對應的會計科目"""
        try:
            return db.query(AccountSubject).filter(
                AccountSubject.code == subject_code
            ).first()
        except Exception:
            return None