"""
多租戶服務
處理租戶檢測、權限控制、功能限制等
"""
from flask import request, g, session
from typing import Optional
from database import get_db
from models.tenant_models import Tenant, PlanFeature, TenantPlanFeature, PlanLevel
from model import User

class TenantService:
    """多租戶服務類"""
    
    @staticmethod
    def detect_tenant_from_request() -> Optional[Tenant]:
        """
        從請求中檢測租戶
        支持以下檢測方式：
        1. 子域名 (client1.yourapp.com)
        2. 自定義域名
        3. 路徑參數
        4. 用戶會話
        """
        tenant = None
        
        # 方法1: 從子域名檢測
        host = request.host.lower()
        if host.startswith('admin.'):
            # 管理後台不屬於任何租戶
            return None
            
        # 檢查是否為子域名格式
        parts = host.split('.')
        if len(parts) >= 3 and not parts[0] in ['www', 'api']:
            subdomain = parts[0]
            tenant = TenantService.get_tenant_by_slug(subdomain)
        
        # 方法2: 從自定義域名檢測
        if not tenant:
            tenant = TenantService.get_tenant_by_domain(host)
        
        # 方法3: 從用戶會話檢測
        if not tenant and 'user_id' in session:
            tenant = TenantService.get_tenant_by_user_id(session['user_id'])
        
        return tenant
    
    @staticmethod
    def get_tenant_by_slug(slug: str) -> Optional[Tenant]:
        """根據slug獲取租戶"""
        with get_db() as db:
            return db.query(Tenant).filter(
                Tenant.slug == slug,
                Tenant.is_deleted == False
            ).first()
    
    @staticmethod
    def get_tenant_by_domain(domain: str) -> Optional[Tenant]:
        """根據自定義域名獲取租戶"""
        with get_db() as db:
            return db.query(Tenant).filter(
                Tenant.domain == domain,
                Tenant.is_deleted == False
            ).first()
    
    @staticmethod
    def get_tenant_by_user_id(user_id: int) -> Optional[Tenant]:
        """根據用戶ID獲取租戶"""
        with get_db() as db:
            user = db.query(User).filter(User.id == user_id).first()
            if user and user.tenant_id:
                return db.query(Tenant).filter(
                    Tenant.id == user.tenant_id,
                    Tenant.is_deleted == False
                ).first()
        return None
    
    @staticmethod
    def is_admin_request() -> bool:
        """檢查是否為管理後台請求"""
        host = request.host.lower()
        return host.startswith('admin.') or request.path.startswith('/admin/')
    
    @staticmethod
    def set_tenant_context(tenant: Optional[Tenant]):
        """設置租戶上下文"""
        g.tenant = tenant
        g.is_admin = TenantService.is_admin_request()
        g.tenant_id = tenant.id if tenant else None

class FeatureService:
    """功能權限服務"""
    
    @staticmethod
    def check_feature_access(module_name: str, feature_name: str, tenant: Optional[Tenant] = None) -> bool:
        """檢查功能權限"""
        # 管理員擁有所有權限
        if g.get('is_admin', False):
            return True
        
        # 沒有租戶時拒絕訪問
        if not tenant:
            tenant = g.get('tenant')
        
        if not tenant:
            return False
        
        # 檢查租戶狀態
        if tenant.status.value not in ['active', 'trial']:
            return False
        
        # 檢查方案權限
        return FeatureService._check_plan_feature(tenant.plan_level, module_name, feature_name)
    
    @staticmethod
    def _check_plan_feature(plan_level: PlanLevel, module_name: str, feature_name: str) -> bool:
        """檢查方案是否包含指定功能"""
        with get_db() as db:
            feature = db.query(PlanFeature).filter(
                PlanFeature.module_name == module_name,
                PlanFeature.feature_name == feature_name,
                PlanFeature.is_active == True
            ).first()
            
            if not feature:
                return False
            
            # 根據方案等級檢查權限
            if plan_level == PlanLevel.BASIC:
                return feature.basic_enabled
            elif plan_level == PlanLevel.STANDARD:
                return feature.standard_enabled
            elif plan_level == PlanLevel.PREMIUM:
                return feature.premium_enabled
            elif plan_level == PlanLevel.ENTERPRISE:
                return feature.enterprise_enabled
            
            return False
    
    @staticmethod
    def get_accessible_modules(tenant: Optional[Tenant] = None) -> list:
        """獲取可訪問的模組列表"""
        if g.get('is_admin', False):
            return ['all']  # 管理員可訪問所有模組
        
        if not tenant:
            tenant = g.get('tenant')
        
        if not tenant:
            return []
        
        with get_db() as db:
            features = db.query(PlanFeature).filter(
                PlanFeature.is_active == True
            ).all()
            
            accessible_modules = set()
            
            for feature in features:
                if FeatureService._check_plan_feature(tenant.plan_level, feature.module_name, feature.feature_name):
                    accessible_modules.add(feature.module_name)
            
            return list(accessible_modules)

class TenantMiddleware:
    """多租戶中間件"""
    
    @staticmethod
    def init_app(app):
        """初始化中間件"""
        app.before_request(TenantMiddleware.before_request)
        app.after_request(TenantMiddleware.after_request)
    
    @staticmethod
    def before_request():
        """請求前處理"""
        # 檢測租戶
        tenant = TenantService.detect_tenant_from_request()
        
        # 設置租戶上下文
        TenantService.set_tenant_context(tenant)
        
        # 記錄租戶資訊到日誌
        if hasattr(g, 'correlation_id'):
            from flask import current_app
            tenant_info = f"tenant={tenant.slug}" if tenant else "tenant=None"
            admin_info = "admin=True" if g.get('is_admin') else "admin=False"
            current_app.logger.info(f'[{g.correlation_id}] {tenant_info}, {admin_info}')
    
    @staticmethod
    def after_request(response):
        """請求後處理"""
        # 添加租戶標識到響應頭（僅開發環境）
        if hasattr(g, 'tenant') and g.tenant:
            response.headers['X-Tenant-ID'] = str(g.tenant.id)
        
        return response