"""
新的資產負債表服務 - 使用 Transaction 和 JournalEntry 模型
"""
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import date, datetime
from collections import defaultdict

from model import Transaction, JournalEntry, AccountSubject, Money


class NewBalanceSheetService:
    """新的資產負債表服務"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def generate_balance_sheet(self, as_of_date: date = None) -> Dict:
        """
        生成資產負債表

        Args:
            as_of_date: 截止日期，默認為今天

        Returns:
            資產負債表數據字典
        """
        if as_of_date is None:
            as_of_date = date.today()

        # 獲取資產負債表科目的餘額（1、2、3開頭）
        balance_sheet_balances = self._calculate_balance_sheet_balances(as_of_date)

        # 計算本期損益（收入減費用）
        net_income = self._calculate_net_income(as_of_date)

        # 將本期損益加入權益
        if net_income != 0:
            balance_sheet_balances['3310'] = {
                'subject_code': '3310',
                'subject_name': '本期損益',
                'total_debit': 0 if net_income > 0 else abs(net_income),
                'total_credit': net_income if net_income > 0 else 0,
                'balance': net_income,
                'top_category': '權益'
            }

        # 分類整理
        assets = self._categorize_assets(balance_sheet_balances)
        liabilities = self._categorize_liabilities(balance_sheet_balances)
        equity = self._categorize_equity(balance_sheet_balances)

        # 計算總計
        total_assets = sum(item['balance'] for category in assets.values() for item in category)
        total_liabilities = sum(item['balance'] for category in liabilities.values() for item in category)
        total_equity = sum(item['balance'] for category in equity.values() for item in category)

        return {
            'as_of_date': as_of_date,
            'assets': assets,
            'liabilities': liabilities,
            'equity': equity,
            'totals': {
                'total_assets': total_assets,
                'total_liabilities': total_liabilities,
                'total_equity': total_equity,
                'total_liabilities_equity': total_liabilities + total_equity,
                'is_balanced': abs(total_assets - (total_liabilities + total_equity)) < 1  # 允許1元誤差
            }
        }
    
    def _calculate_all_subject_balances(self, as_of_date: date) -> Dict[str, Dict]:
        """計算所有科目的餘額"""
        # 查詢截止日期前的所有分錄
        query = self.db.query(
            JournalEntry.subject_code,
            func.sum(JournalEntry.debit_amount).label('total_debit'),
            func.sum(JournalEntry.credit_amount).label('total_credit')
        ).join(Transaction).filter(
            Transaction.transaction_date <= as_of_date
        ).group_by(JournalEntry.subject_code)
        
        results = query.all()
        
        # 獲取科目資訊
        subjects = {s.code: s for s in self.db.query(AccountSubject).all()}

        # 獲取期初餘額
        opening_balances = self._get_opening_balances()

        balances = {}
        for result in results:
            subject_code = result.subject_code
            total_debit = result.total_debit or 0
            total_credit = result.total_credit or 0
            
            # 根據科目性質計算餘額
            subject = subjects.get(subject_code)
            if subject:
                # 獲取期初餘額
                opening_balance = opening_balances.get(subject_code, 0)

                # 資產、費用科目：借方餘額為正
                # 負債、權益、收入科目：貸方餘額為正
                if subject_code.startswith(('1', '5', '6')):  # 資產、費用
                    balance = opening_balance + total_debit - total_credit
                else:  # 負債、權益、收入
                    balance = opening_balance + total_credit - total_debit

                balances[subject_code] = {
                    'subject_code': subject_code,
                    'subject_name': subject.name,
                    'total_debit': total_debit,
                    'total_credit': total_credit,
                    'opening_balance': opening_balance,
                    'balance': balance,
                    'top_category': subject.top_category
                }
        
        return balances

    def _calculate_balance_sheet_balances(self, as_of_date: date) -> Dict[str, Dict]:
        """計算資產負債表科目的餘額（僅1、2、3開頭的科目）"""
        # 查詢截止日期前的所有分錄，僅包含資產負債表科目
        query = self.db.query(
            JournalEntry.subject_code,
            func.sum(JournalEntry.debit_amount).label('total_debit'),
            func.sum(JournalEntry.credit_amount).label('total_credit')
        ).join(Transaction).filter(
            Transaction.transaction_date <= as_of_date,
            (JournalEntry.subject_code.like('1%') |
             JournalEntry.subject_code.like('2%') |
             JournalEntry.subject_code.like('3%'))  # 只包含1、2、3開頭的科目
        ).group_by(JournalEntry.subject_code)

        results = query.all()

        # 獲取科目資訊
        subjects = {s.code: s for s in self.db.query(AccountSubject).all()}

        # 獲取期初餘額
        opening_balances = self._get_opening_balances()

        balances = {}
        for result in results:
            subject_code = result.subject_code
            total_debit = result.total_debit or 0
            total_credit = result.total_credit or 0

            # 根據科目性質計算餘額
            subject = subjects.get(subject_code)
            if subject:
                # 獲取期初餘額
                opening_balance = opening_balances.get(subject_code, 0)

                # 資產科目：借方餘額為正
                # 負債、權益科目：貸方餘額為正
                if subject_code.startswith('1'):  # 資產
                    balance = opening_balance + total_debit - total_credit
                else:  # 負債、權益
                    balance = opening_balance + total_credit - total_debit

                balances[subject_code] = {
                    'subject_code': subject_code,
                    'subject_name': subject.name,
                    'total_debit': total_debit,
                    'total_credit': total_credit,
                    'opening_balance': opening_balance,
                    'balance': balance,
                    'top_category': subject.top_category
                }

        # 處理只有期初餘額但沒有交易記錄的科目
        for subject_code, opening_balance in opening_balances.items():
            if subject_code not in balances and opening_balance != 0:
                # 檢查是否為資產負債表科目
                if subject_code.startswith(('1', '2', '3')):
                    subject = subjects.get(subject_code)
                    if subject:
                        balances[subject_code] = {
                            'subject_code': subject_code,
                            'subject_name': subject.name,
                            'total_debit': 0,
                            'total_credit': 0,
                            'opening_balance': opening_balance,
                            'balance': opening_balance,
                            'top_category': subject.top_category
                        }

        return balances

    def _calculate_net_income(self, as_of_date: date) -> int:
        """計算本期損益（收入 - 費用）"""
        # 計算收入總額（4開頭科目的貸方餘額）
        revenue_query = self.db.query(
            func.sum(JournalEntry.credit_amount - JournalEntry.debit_amount).label('total_revenue')
        ).join(Transaction).filter(
            Transaction.transaction_date <= as_of_date,
            JournalEntry.subject_code.like('4%')
        )

        total_revenue = revenue_query.scalar() or 0

        # 計算費用總額（5、6開頭科目的借方餘額）
        expense_query = self.db.query(
            func.sum(JournalEntry.debit_amount - JournalEntry.credit_amount).label('total_expense')
        ).join(Transaction).filter(
            Transaction.transaction_date <= as_of_date,
            (JournalEntry.subject_code.like('5%') | JournalEntry.subject_code.like('6%'))
        )

        total_expense = expense_query.scalar() or 0

        # 本期損益 = 收入 - 費用
        return total_revenue - total_expense
    
    def _categorize_assets(self, balances: Dict[str, Dict]) -> Dict[str, List]:
        """分類資產項目"""
        assets = {
            '流動資產': [],
            '非流動資產': [],
            '其他資產': []
        }
        
        for code, data in balances.items():
            if not code.startswith('1'):  # 非資產科目
                continue
                
            if data['balance'] == 0:  # 跳過零餘額
                continue
            
            # 添加百分比計算
            item = data.copy()
            
            # 根據科目代碼分類
            if code.startswith('11'):  # 流動資產
                if code.startswith('1105'):  # 現金
                    item['sub_category'] = '現金及約當現金'
                elif code.startswith('1110'):  # 銀行存款
                    item['sub_category'] = '銀行存款'
                elif code.startswith('1120'):  # 應收帳款
                    item['sub_category'] = '應收帳款'
                elif code.startswith('1130'):  # 存貨
                    item['sub_category'] = '存貨'
                else:
                    item['sub_category'] = '其他流動資產'
                assets['流動資產'].append(item)
                
            elif code.startswith('12'):  # 非流動資產
                if code.startswith('1201'):  # 不動產、廠房及設備
                    item['sub_category'] = '不動產、廠房及設備'
                elif code.startswith('1280'):  # 無形資產
                    item['sub_category'] = '無形資產'
                else:
                    item['sub_category'] = '其他非流動資產'
                assets['非流動資產'].append(item)
                
            else:
                item['sub_category'] = '其他資產'
                assets['其他資產'].append(item)
        
        # 排序
        for category in assets.values():
            category.sort(key=lambda x: x['subject_code'])
        
        return assets
    
    def _categorize_liabilities(self, balances: Dict[str, Dict]) -> Dict[str, List]:
        """分類負債項目"""
        liabilities = {
            '流動負債': [],
            '非流動負債': []
        }
        
        for code, data in balances.items():
            if not code.startswith('2'):  # 非負債科目
                continue
                
            if data['balance'] == 0:  # 跳過零餘額
                continue
            
            item = data.copy()
            
            # 根據科目代碼分類
            if code.startswith('21'):  # 流動負債
                if code.startswith('2110'):  # 短期借款
                    item['sub_category'] = '短期借款'
                elif code.startswith('2120'):  # 應付帳款
                    item['sub_category'] = '應付帳款'
                elif code.startswith('2130'):  # 應付費用
                    item['sub_category'] = '應付費用'
                elif code.startswith('2140'):  # 應付薪資
                    item['sub_category'] = '應付薪資'
                else:
                    item['sub_category'] = '其他流動負債'
                liabilities['流動負債'].append(item)
                
            elif code.startswith('22') or code.startswith('24'):  # 非流動負債
                if code.startswith('2420'):  # 長期借款
                    item['sub_category'] = '長期借款'
                else:
                    item['sub_category'] = '其他非流動負債'
                liabilities['非流動負債'].append(item)
        
        # 排序
        for category in liabilities.values():
            category.sort(key=lambda x: x['subject_code'])
        
        return liabilities
    
    def _categorize_equity(self, balances: Dict[str, Dict]) -> Dict[str, List]:
        """分類權益項目"""
        equity = {
            '股東權益': []
        }
        
        for code, data in balances.items():
            if not code.startswith('3'):  # 非權益科目
                continue
                
            if data['balance'] == 0:  # 跳過零餘額
                continue
            
            item = data.copy()
            
            # 根據科目代碼分類
            if code.startswith('3110'):  # 資本
                item['sub_category'] = '實收資本'
            elif code.startswith('3120'):  # 資本公積
                item['sub_category'] = '資本公積'
            elif code.startswith('3310'):  # 保留盈餘
                item['sub_category'] = '保留盈餘'
            else:
                item['sub_category'] = '其他權益'
            
            equity['股東權益'].append(item)
        
        # 排序
        for category in equity.values():
            category.sort(key=lambda x: x['subject_code'])
        
        return equity
    
    def calculate_percentages(self, balance_sheet_data: Dict) -> Dict:
        """計算各項目佔總資產的百分比"""
        total_assets = balance_sheet_data['totals']['total_assets']
        
        if total_assets == 0:
            return balance_sheet_data
        
        # 為資產項目添加百分比
        for category_name, items in balance_sheet_data['assets'].items():
            for item in items:
                item['percentage'] = (item['balance'] / total_assets) * 100
        
        # 為負債項目添加百分比
        for category_name, items in balance_sheet_data['liabilities'].items():
            for item in items:
                item['percentage'] = (item['balance'] / total_assets) * 100
        
        # 為權益項目添加百分比
        for category_name, items in balance_sheet_data['equity'].items():
            for item in items:
                item['percentage'] = (item['balance'] / total_assets) * 100
        
        return balance_sheet_data
    
    def get_subject_detail(self, subject_code: str, start_date: date = None, end_date: date = None) -> Dict:
        """獲取科目明細"""
        query = self.db.query(JournalEntry).filter_by(subject_code=subject_code)
        
        if start_date and end_date:
            query = query.join(Transaction).filter(
                Transaction.transaction_date.between(start_date, end_date)
            )
        
        entries = query.order_by(JournalEntry.created_at.desc()).all()
        
        total_debit = sum(entry.debit_amount or 0 for entry in entries)
        total_credit = sum(entry.credit_amount or 0 for entry in entries)
        
        return {
            'subject_code': subject_code,
            'entries': entries,
            'total_debit': total_debit,
            'total_credit': total_credit,
            'balance': total_debit - total_credit,
            'entry_count': len(entries)
        }

    def _get_opening_balances(self) -> Dict[str, int]:
        """
        獲取期初餘額（從舊系統的 Money 表）

        Returns:
            科目代碼到期初餘額的字典
        """
        # 查詢舊系統中的期初餘額記錄
        opening_entries = self.db.query(Money).filter(
            Money.name.like('%期初%') | Money.name.like('%開帳%')
        ).all()

        opening_balances = {}
        for entry in opening_entries:
            subject_code = entry.subject_code
            if subject_code:
                if subject_code not in opening_balances:
                    opening_balances[subject_code] = 0

                # 根據借貸方向計算期初餘額
                if entry.entry_side == 'DEBIT':
                    opening_balances[subject_code] += entry.total or 0
                elif entry.entry_side == 'CREDIT':
                    opening_balances[subject_code] -= entry.total or 0

        return opening_balances
