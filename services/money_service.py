"""
收支記錄業務邏輯服務
處理複雜的業務邏輯，不修改原有模型結構
"""
from sqlalchemy import func
from model import Money, Account
from database import get_db
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from utils.tenant_utils import add_tenant_filter, get_current_tenant_id

class MoneyService:
    """收支記錄業務邏輯服務"""
    
    @staticmethod
    def get_overdue_payments() -> List[Money]:
        """
        取得逾期未付款記錄 - 使用 JOIN 避免 N+1 查詢
        
        Returns:
            List[Money]: 逾期記錄列表
        """
        today = datetime.now().date()
        
        from sqlalchemy.orm import joinedload
        
        with get_db() as db:
            query = db.query(Money)\
                .options(
                    joinedload(Money.subject),
                    joinedload(Money.account),
                    joinedload(Money.payment_identity)
                )\
                .filter(
                    Money.should_paid_date < today,
                    not Money.is_paid
                )\
                .order_by(Money.should_paid_date.asc())
            
            # 添加租戶過濾
            query = add_tenant_filter(query, Money)
            return query.all()
    
    @staticmethod
    def get_upcoming_payments(days: int = 7) -> List[Money]:
        """
        取得即將到期的付款記錄
        
        Args:
            days (int): 未來天數
            
        Returns:
            List[Money]: 即將到期的記錄
        """
        today = datetime.now().date()
        future_date = today + timedelta(days=days)
        
        with get_db() as db:
            query = db.query(Money).filter(
                Money.should_paid_date >= today,
                Money.should_paid_date <= future_date,
                not Money.is_paid
            ).order_by(Money.should_paid_date.asc())
            
            # 添加租戶過濾
            query = add_tenant_filter(query, Money)
            return query.all()
    
    @staticmethod
    def mark_as_paid(money_id: int, paid_date: Optional[datetime] = None, user_id: Optional[str] = None) -> bool:
        """
        標記為已付款
        
        Args:
            money_id (int): 記錄ID
            paid_date (datetime, optional): 付款日期
            user_id (str, optional): 操作用戶
            
        Returns:
            bool: 操作是否成功
        """
        try:
            with get_db() as db:
                # 使用租戶隔離的安全查詢
                query = db.query(Money).filter(Money.id == money_id)
                query = add_tenant_filter(query, Money)
                money = query.first()
                if not money:
                    return False
                
                money.is_paid = True
                money.paid_date = paid_date or datetime.now()
                
                if user_id:
                    money.updated_by = user_id
                    money.updated_at = datetime.now()
                
                session.commit()
                return True
                
        except Exception as e:
            print(f"標記付款失敗: {e}")
            return False
    
    @staticmethod
    def get_cash_flow_summary(start_date: datetime, end_date: datetime) -> Dict:
        """
        取得現金流量摘要
        
        Args:
            start_date (datetime): 開始日期
            end_date (datetime): 結束日期
            
        Returns:
            Dict: 現金流量摘要
        """
        with get_db() as db:
            # 期間收入
            income = db.query(
                func.sum(Money.total)
            ).filter(
                Money.money_type == '收入',
                Money.a_time >= start_date.date(),
                Money.a_time <= end_date.date()
            ).scalar() or 0
            
            # 期間支出
            expense = db.query(
                func.sum(Money.total)
            ).filter(
                Money.money_type == '支出',
                Money.a_time >= start_date.date(),
                Money.a_time <= end_date.date()
            ).scalar() or 0
            
            # 已付款金額
            paid_amount = db.query(
                func.sum(Money.total)
            ).filter(
                Money.a_time >= start_date.date(),
                Money.a_time <= end_date.date(),
                Money.is_paid
            ).scalar() or 0
            
            # 未付款金額
            unpaid_amount = db.query(
                func.sum(Money.total)
            ).filter(
                Money.a_time >= start_date.date(),
                Money.a_time <= end_date.date(),
                not Money.is_paid
            ).scalar() or 0
            
            return {
                'period': {
                    'start_date': start_date.date(),
                    'end_date': end_date.date()
                },
                'income': income,
                'expense': expense,
                'net_flow': income - expense,
                'paid_amount': paid_amount,
                'unpaid_amount': unpaid_amount
            }
    
    @staticmethod
    def get_account_activity_summary(account_id: int, days: int = 30) -> Dict:
        """
        取得帳戶活動摘要
        
        Args:
            account_id (int): 帳戶ID
            days (int): 天數
            
        Returns:
            Dict: 帳戶活動摘要
        """
        cutoff_date = datetime.now().date() - timedelta(days=days)
        
        with get_db() as db:
            # 取得帳戶資訊
            account = db.query(Account).filter(Account.id == account_id).first()
            if not account:
                return {}
            
            # 期間交易統計
            transactions = db.query(Money).filter(
                Money.account_id == account_id,
                Money.a_time >= cutoff_date
            ).all()
            
            income_count = len([t for t in transactions if t.money_type == '收入'])
            expense_count = len([t for t in transactions if t.money_type == '支出'])
            income_total = sum([t.total for t in transactions if t.money_type == '收入'])
            expense_total = sum([t.total for t in transactions if t.money_type == '支出'])
            
            return {
                'account': {
                    'id': account.id,
                    'name': account.name,
                    'category': account.category
                },
                'period_days': days,
                'transaction_count': len(transactions),
                'income': {
                    'count': income_count,
                    'total': income_total
                },
                'expense': {
                    'count': expense_count,
                    'total': expense_total
                },
                'net_amount': income_total - expense_total
            }
    
    @staticmethod
    def validate_money_record(data: Dict) -> Dict:
        """
        驗證收支記錄資料
        
        Args:
            data (Dict): 記錄資料
            
        Returns:
            Dict: 驗證結果 {'valid': bool, 'errors': list}
        """
        errors = []
        
        # 檢查必要欄位
        required_fields = ['money_type', 'name', 'total', 'a_time']
        for field in required_fields:
            if not data.get(field):
                errors.append(f'缺少必要欄位: {field}')
        
        # 檢查金額
        if data.get('total') is not None:
            try:
                total = int(data['total'])
                if total < 0:
                    errors.append('金額不能為負數')
            except (ValueError, TypeError):
                errors.append('金額格式錯誤')
        
        # 檢查收支類型
        if data.get('money_type') not in ['收入', '支出', '轉帳']:
            errors.append('收支類型必須是: 收入、支出、轉帳')
        
        # 檢查日期邏輯
        if data.get('should_paid_date') and data.get('paid_date'):
            try:
                should_date = datetime.strptime(data['should_paid_date'], '%Y-%m-%d').date()
                paid_date = datetime.strptime(data['paid_date'], '%Y-%m-%d').date()
                if paid_date < should_date:
                    errors.append('付款日期不能早於應付日期')
            except ValueError:
                errors.append('日期格式錯誤')
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }

class AccountService:
    """帳戶業務邏輯服務"""
    
    @staticmethod
    def calculate_account_balance(account_id: int, as_of_date: Optional[datetime] = None) -> int:
        """
        計算帳戶餘額（截至特定日期）
        
        Args:
            account_id (int): 帳戶ID
            as_of_date (datetime, optional): 截至日期
            
        Returns:
            int: 帳戶餘額
        """
        with get_db() as db:
            query = db.query(Money).filter(
                Money.account_id == account_id
            )
            
            if as_of_date:
                query = query.filter(Money.a_time <= as_of_date.date())
            
            transactions = query.all()
            
            balance = 0
            for transaction in transactions:
                if transaction.money_type == '收入':
                    balance += transaction.total
                elif transaction.money_type == '支出':
                    balance -= transaction.total
            
            return balance
    
    @staticmethod
    def get_account_monthly_trend(account_id: int, months: int = 12) -> List[Dict]:
        """
        取得帳戶月度趨勢
        
        Args:
            account_id (int): 帳戶ID
            months (int): 月份數
            
        Returns:
            List[Dict]: 月度趨勢資料
        """
        from sqlalchemy import func, extract
        
        with get_db() as db:
            # 計算起始日期
            end_date = datetime.now().date()
            start_date = end_date.replace(day=1) - timedelta(days=months*31)
            
            # 按月統計
            results = db.query(
                extract('year', Money.a_time).label('year'),
                extract('month', Money.a_time).label('month'),
                Money.money_type,
                func.sum(Money.total).label('total')
            ).filter(
                Money.account_id == account_id,
                Money.a_time >= start_date,
                Money.a_time <= end_date
            ).group_by(
                extract('year', Money.a_time),
                extract('month', Money.a_time),
                Money.money_type
            ).order_by(
                extract('year', Money.a_time),
                extract('month', Money.a_time)
            ).all()
            
            # 整理資料
            trend_data = {}
            for result in results:
                key = f"{int(result.year)}-{int(result.month):02d}"
                if key not in trend_data:
                    trend_data[key] = {'收入': 0, '支出': 0}
                trend_data[key][result.money_type] = result.total or 0
            
            # 轉換為列表格式
            return [
                {
                    'period': period,
                    'income': data['收入'],
                    'expense': data['支出'],
                    'net': data['收入'] - data['支出']
                }
                for period, data in sorted(trend_data.items())
            ]