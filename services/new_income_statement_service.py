"""
新的損益表服務 - 使用 Transaction 和 JournalEntry 模型
"""
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import date, datetime
from collections import defaultdict

from model import Transaction, JournalEntry, AccountSubject


class NewIncomeStatementService:
    """新的損益表服務"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def generate_income_statement(self, start_date: date, end_date: date) -> Dict:
        """
        生成損益表
        
        Args:
            start_date: 開始日期
            end_date: 結束日期
            
        Returns:
            損益表數據字典
        """
        # 獲取期間內的收入和費用
        revenue_data = self._calculate_revenue(start_date, end_date)
        expense_data = self._calculate_expenses(start_date, end_date)
        
        # 計算各項總計
        total_revenue = sum(item['amount'] for item in revenue_data)
        total_operating_expenses = sum(item['amount'] for category in expense_data.values() for item in category)
        
        # 計算利潤
        gross_profit = total_revenue
        operating_profit = gross_profit - total_operating_expenses
        net_profit = operating_profit  # 簡化版，未考慮營業外收支和稅
        
        return {
            'period': {
                'start_date': start_date,
                'end_date': end_date
            },
            'revenue': revenue_data,
            'expenses': expense_data,
            'summary': {
                'total_revenue': total_revenue,
                'total_operating_expenses': total_operating_expenses,
                'gross_profit': gross_profit,
                'operating_profit': operating_profit,
                'net_profit': net_profit,
                'profit_margin': (net_profit / total_revenue * 100) if total_revenue > 0 else 0
            }
        }
    
    def _calculate_revenue(self, start_date: date, end_date: date) -> List[Dict]:
        """計算收入項目"""
        # 查詢收入科目（4開頭）的貸方金額
        query = self.db.query(
            JournalEntry.subject_code,
            func.sum(JournalEntry.credit_amount).label('total_amount')
        ).join(Transaction).filter(
            Transaction.transaction_date.between(start_date, end_date),
            JournalEntry.subject_code.like('4%'),
            JournalEntry.credit_amount > 0
        ).group_by(JournalEntry.subject_code)
        
        results = query.all()
        
        # 獲取科目資訊
        subjects = {s.code: s for s in self.db.query(AccountSubject).filter(
            AccountSubject.code.like('4%')
        ).all()}
        
        revenue_items = []
        for result in results:
            subject_code = result.subject_code
            amount = result.total_amount or 0
            
            if amount > 0:
                subject = subjects.get(subject_code)
                revenue_items.append({
                    'subject_code': subject_code,
                    'subject_name': subject.name if subject else subject_code,
                    'amount': amount,
                    'category': self._categorize_revenue(subject_code)
                })
        
        # 排序
        revenue_items.sort(key=lambda x: x['subject_code'])
        return revenue_items
    
    def _calculate_expenses(self, start_date: date, end_date: date) -> Dict[str, List]:
        """計算費用項目"""
        # 查詢費用科目（5、6開頭）的借方金額
        query = self.db.query(
            JournalEntry.subject_code,
            func.sum(JournalEntry.debit_amount).label('total_amount')
        ).join(Transaction).filter(
            Transaction.transaction_date.between(start_date, end_date),
            (JournalEntry.subject_code.like('5%') | JournalEntry.subject_code.like('6%')),
            JournalEntry.debit_amount > 0
        ).group_by(JournalEntry.subject_code)
        
        results = query.all()
        
        # 獲取科目資訊
        subjects = {s.code: s for s in self.db.query(AccountSubject).filter(
            AccountSubject.code.like('5%') | AccountSubject.code.like('6%')
        ).all()}
        
        # 按類別分組
        expenses = {
            '銷貨成本': [],
            '營業費用': [],
            '管理費用': [],
            '其他費用': []
        }
        
        for result in results:
            subject_code = result.subject_code
            amount = result.total_amount or 0
            
            if amount > 0:
                subject = subjects.get(subject_code)
                category = self._categorize_expense(subject_code)
                
                expense_item = {
                    'subject_code': subject_code,
                    'subject_name': subject.name if subject else subject_code,
                    'amount': amount
                }
                
                expenses[category].append(expense_item)
        
        # 排序每個類別
        for category in expenses.values():
            category.sort(key=lambda x: x['subject_code'])
        
        return expenses
    
    def _categorize_revenue(self, subject_code: str) -> str:
        """分類收入項目"""
        if subject_code.startswith('4100'):
            return '銷貨收入'
        elif subject_code.startswith('4200'):
            return '服務收入'
        elif subject_code.startswith('4300'):
            return '其他營業收入'
        elif subject_code.startswith('4400'):
            return '營業外收入'
        else:
            return '其他收入'
    
    def _categorize_expense(self, subject_code: str) -> str:
        """分類費用項目"""
        if subject_code.startswith('5'):
            # 5開頭為成本
            if subject_code.startswith('5100'):
                return '銷貨成本'
            else:
                return '其他費用'
        elif subject_code.startswith('6'):
            # 6開頭為費用
            if subject_code.startswith('6100'):
                return '營業費用'
            elif subject_code.startswith('6200'):
                return '管理費用'
            else:
                return '其他費用'
        else:
            return '其他費用'
    
    def calculate_monthly_comparison(self, year: int) -> Dict:
        """計算月度比較"""
        monthly_data = {}
        
        for month in range(1, 13):
            start_date = date(year, month, 1)
            if month == 12:
                end_date = date(year + 1, 1, 1)
            else:
                end_date = date(year, month + 1, 1)
            end_date = date(end_date.year, end_date.month, 1) - timedelta(days=1)
            
            monthly_statement = self.generate_income_statement(start_date, end_date)
            monthly_data[f'{year}-{month:02d}'] = {
                'revenue': monthly_statement['summary']['total_revenue'],
                'expenses': monthly_statement['summary']['total_operating_expenses'],
                'profit': monthly_statement['summary']['net_profit']
            }
        
        return monthly_data
    
    def get_expense_detail(self, subject_code: str, start_date: date, end_date: date) -> Dict:
        """獲取費用科目明細"""
        entries = self.db.query(JournalEntry).join(Transaction).filter(
            JournalEntry.subject_code == subject_code,
            Transaction.transaction_date.between(start_date, end_date),
            JournalEntry.debit_amount > 0
        ).order_by(Transaction.transaction_date.desc()).all()
        
        total_amount = sum(entry.debit_amount for entry in entries)
        
        return {
            'subject_code': subject_code,
            'entries': entries,
            'total_amount': total_amount,
            'entry_count': len(entries),
            'period': {
                'start_date': start_date,
                'end_date': end_date
            }
        }
    
    def get_revenue_detail(self, subject_code: str, start_date: date, end_date: date) -> Dict:
        """獲取收入科目明細"""
        entries = self.db.query(JournalEntry).join(Transaction).filter(
            JournalEntry.subject_code == subject_code,
            Transaction.transaction_date.between(start_date, end_date),
            JournalEntry.credit_amount > 0
        ).order_by(Transaction.transaction_date.desc()).all()
        
        total_amount = sum(entry.credit_amount for entry in entries)
        
        return {
            'subject_code': subject_code,
            'entries': entries,
            'total_amount': total_amount,
            'entry_count': len(entries),
            'period': {
                'start_date': start_date,
                'end_date': end_date
            }
        }
    
    def calculate_profit_trend(self, months: int = 12) -> List[Dict]:
        """計算利潤趨勢"""
        from datetime import timedelta
        import calendar

        today = date.today()
        trends = []

        for i in range(months):
            # 計算每月的開始和結束日期
            if i == 0:
                # 當月
                month_start = today.replace(day=1)
                month_end = today
            else:
                # 前幾個月
                year = today.year
                month = today.month - i
                if month <= 0:
                    month += 12
                    year -= 1

                month_start = date(year, month, 1)
                last_day = calendar.monthrange(year, month)[1]
                month_end = date(year, month, last_day)

            # 獲取該月的損益表
            statement = self.generate_income_statement(month_start, month_end)

            trends.append({
                'period': f'{month_start.year}-{month_start.month:02d}',
                'start_date': month_start,
                'end_date': month_end,
                'revenue': statement['summary']['total_revenue'],
                'expenses': statement['summary']['total_operating_expenses'],
                'profit': statement['summary']['net_profit'],
                'profit_margin': statement['summary']['profit_margin']
            })

        return list(reversed(trends))  # 按時間順序排列
