"""
統一的損益表服務
整合了兩個版本的優點：
1. 基礎版本的完整業務邏輯
2. 新版本的現代資料模型支援和優化查詢
"""
from typing import Dict, List, Optional, Union
from datetime import date, datetime
from collections import defaultdict
from sqlalchemy import func, and_
from sqlalchemy.orm import Session, joinedload

from services.base import BaseService, ServiceError, ValidationError
from model import AccountSubject, Money, Account, Transaction, JournalEntry


class UnifiedIncomeStatementService(BaseService):
    """統一的損益表服務"""
    
    def generate_income_statement(self, 
                                start_date: Union[str, date] = None,
                                end_date: Union[str, date] = None,
                                use_optimized: bool = True) -> Dict:
        """
        生成損益表
        
        Args:
            start_date: 開始日期
            end_date: 結束日期，預設為今天
            use_optimized: 是否使用優化的查詢方式
        
        Returns:
            損益表數據字典
        """
        if end_date is None:
            end_date = date.today()
        elif isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
        if start_date is None:
            # 預設為本年度開始
            start_date = date(end_date.year, 1, 1)
        elif isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        
        if start_date > end_date:
            raise ValidationError("開始日期不能大於結束日期")
        
        self.log_operation('generate_income_statement', {
            'start_date': str(start_date),
            'end_date': str(end_date)
        })
        
        try:
            with self.get_db_session() as db:
                if use_optimized:
                    return self._generate_optimized_income_statement(db, start_date, end_date)
                else:
                    return self._generate_standard_income_statement(db, start_date, end_date)
        except Exception as e:
            self.logger.error(f"生成損益表失敗: {e}")
            raise ServiceError(f"生成損益表失敗: {str(e)}")
    
    def _generate_optimized_income_statement(self, db: Session, start_date: date, end_date: date) -> Dict:
        """優化版本的損益表生成"""
        
        # 獲取會計科目映射
        subject_map = self._get_subject_map(db)
        
        # 使用優化查詢獲取收入和費用
        income_data = self._get_optimized_income_data(db, start_date, end_date)
        expense_data = self._get_optimized_expense_data(db, start_date, end_date)
        
        # 整理損益表資料
        return self._organize_income_statement_data(
            income_data, expense_data, subject_map, start_date, end_date
        )
    
    def _generate_standard_income_statement(self, db: Session, start_date: date, end_date: date) -> Dict:
        """標準版本的損益表生成"""
        
        subject_map = self._get_subject_map(db)
        
        # 獲取期間內的所有交易
        transactions = db.query(Money).options(
            joinedload(Money.account)
        ).filter(
            and_(
                Money.a_time >= start_date,
                Money.a_time <= end_date
            )
        ).all()
        
        # 從交易中提取收入和費用資料
        income_data, expense_data = self._extract_income_expense_from_transactions(
            transactions, subject_map
        )
        
        return self._organize_income_statement_data(
            income_data, expense_data, subject_map, start_date, end_date
        )
    
    def _get_subject_map(self, db: Session) -> Dict[str, AccountSubject]:
        """獲取會計科目映射"""
        subjects = db.query(AccountSubject).all()
        return {subj.code: subj for subj in subjects}
    
    def _get_optimized_income_data(self, db: Session, start_date: date, end_date: date) -> Dict:
        """使用優化查詢獲取收入資料"""
        
        income_aggregates = db.query(
            Money.subject_code,
            func.sum(Money.lend).label('total_credit'),
            func.sum(Money.borrow).label('total_debit'),
            func.count(Money.id).label('transaction_count')
        ).filter(
            and_(
                Money.a_time >= start_date,
                Money.a_time <= end_date,
                Money.subject_code.like('4%'),  # 收入科目
                Money.is_deleted == False
            )
        ).group_by(Money.subject_code).all()
        
        income_data = {}
        for row in income_aggregates:
            if row.subject_code:
                net_amount = float(row.total_credit or 0) - float(row.total_debit or 0)
                if net_amount != 0:  # 只包含有餘額的科目
                    income_data[row.subject_code] = {
                        'subject_code': row.subject_code,
                        'total_credit': float(row.total_credit or 0),
                        'total_debit': float(row.total_debit or 0),
                        'net_amount': net_amount,
                        'transaction_count': row.transaction_count
                    }
        
        return income_data
    
    def _get_optimized_expense_data(self, db: Session, start_date: date, end_date: date) -> Dict:
        """使用優化查詢獲取費用資料"""
        
        expense_aggregates = db.query(
            Money.subject_code,
            func.sum(Money.lend).label('total_credit'),
            func.sum(Money.borrow).label('total_debit'),
            func.count(Money.id).label('transaction_count')
        ).filter(
            and_(
                Money.a_time >= start_date,
                Money.a_time <= end_date,
                Money.subject_code.like('5%'),  # 費用科目
                Money.is_deleted == False
            )
        ).group_by(Money.subject_code).all()
        
        expense_data = {}
        for row in expense_aggregates:
            if row.subject_code:
                net_amount = float(row.total_debit or 0) - float(row.total_credit or 0)
                if net_amount != 0:  # 只包含有餘額的科目
                    expense_data[row.subject_code] = {
                        'subject_code': row.subject_code,
                        'total_credit': float(row.total_credit or 0),
                        'total_debit': float(row.total_debit or 0),
                        'net_amount': net_amount,
                        'transaction_count': row.transaction_count
                    }
        
        return expense_data
    
    def _extract_income_expense_from_transactions(self, transactions: List, subject_map: Dict) -> tuple:
        """從交易記錄中提取收入和費用資料"""
        
        income_totals = defaultdict(lambda: {'credit': 0, 'debit': 0})
        expense_totals = defaultdict(lambda: {'credit': 0, 'debit': 0})
        
        for transaction in transactions:
            if not transaction.subject_code or transaction.subject_code not in subject_map:
                continue
                
            credit = float(transaction.lend or 0)
            debit = float(transaction.borrow or 0)
            
            if transaction.subject_code.startswith('4'):  # 收入科目
                income_totals[transaction.subject_code]['credit'] += credit
                income_totals[transaction.subject_code]['debit'] += debit
                
            elif transaction.subject_code.startswith('5'):  # 費用科目
                expense_totals[transaction.subject_code]['credit'] += credit
                expense_totals[transaction.subject_code]['debit'] += debit
        
        # 轉換為統一格式
        income_data = {}
        for code, totals in income_totals.items():
            net_amount = totals['credit'] - totals['debit']
            if net_amount != 0:
                income_data[code] = {
                    'subject_code': code,
                    'total_credit': totals['credit'],
                    'total_debit': totals['debit'],
                    'net_amount': net_amount
                }
        
        expense_data = {}
        for code, totals in expense_totals.items():
            net_amount = totals['debit'] - totals['credit']
            if net_amount != 0:
                expense_data[code] = {
                    'subject_code': code,
                    'total_credit': totals['credit'],
                    'total_debit': totals['debit'],
                    'net_amount': net_amount
                }
        
        return income_data, expense_data
    
    def _organize_income_statement_data(self, income_data: Dict, expense_data: Dict, 
                                      subject_map: Dict, start_date: date, end_date: date) -> Dict:
        """整理損益表資料結構"""
        
        # 分類收入
        revenues = defaultdict(list)
        total_revenue = 0
        
        for code, data in income_data.items():
            if code in subject_map:
                subject = subject_map[code]
                category = self._get_revenue_category(code)
                
                item_data = {
                    'code': code,
                    'name': subject.name,
                    'amount': data['net_amount'],
                    'total_credit': data['total_credit'],
                    'total_debit': data['total_debit']
                }
                
                revenues[category].append(item_data)
                total_revenue += data['net_amount']
        
        # 分類費用
        expenses = defaultdict(list)
        total_expense = 0
        
        for code, data in expense_data.items():
            if code in subject_map:
                subject = subject_map[code]
                category = self._get_expense_category(code)
                
                item_data = {
                    'code': code,
                    'name': subject.name,
                    'amount': data['net_amount'],
                    'total_credit': data['total_credit'],
                    'total_debit': data['total_debit']
                }
                
                expenses[category].append(item_data)
                total_expense += data['net_amount']
        
        # 排序各類別內的科目
        for category in revenues:
            revenues[category].sort(key=lambda x: x['code'])
        for category in expenses:
            expenses[category].sort(key=lambda x: x['code'])
        
        # 計算各層級小計
        gross_profit = self._calculate_gross_profit(revenues, expenses)
        operating_income = self._calculate_operating_income(revenues, expenses)
        net_income = total_revenue - total_expense
        
        return {
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'period_description': self._get_period_description(start_date, end_date)
            },
            'revenues': dict(revenues),
            'expenses': dict(expenses),
            'summary': {
                'total_revenue': total_revenue,
                'total_expense': total_expense,
                'gross_profit': gross_profit,
                'operating_income': operating_income,
                'net_income': net_income,
                'profit_margin': (net_income / total_revenue * 100) if total_revenue != 0 else 0
            },
            'metadata': {
                'generation_time': datetime.now().isoformat(),
                'revenue_items': sum(len(items) for items in revenues.values()),
                'expense_items': sum(len(items) for items in expenses.values()),
                'service_version': 'unified'
            }
        }
    
    def _get_revenue_category(self, code: str) -> str:
        """根據科目代碼獲取收入分類"""
        if code.startswith('41'):
            return '營業收入'
        elif code.startswith('42'):
            return '其他收入'
        elif code.startswith('43'):
            return '營業外收入'
        else:
            return '收入'
    
    def _get_expense_category(self, code: str) -> str:
        """根據科目代碼獲取費用分類"""
        if code.startswith('51'):
            return '營業成本'
        elif code.startswith('52'):
            return '營業費用'
        elif code.startswith('53'):
            return '管理費用'
        elif code.startswith('54'):
            return '財務費用'
        elif code.startswith('55'):
            return '營業外費用'
        else:
            return '費用'
    
    def _calculate_gross_profit(self, revenues: Dict, expenses: Dict) -> float:
        """計算毛利潤"""
        operating_revenue = sum(
            sum(item['amount'] for item in items)
            for category, items in revenues.items()
            if category == '營業收入'
        )
        
        cost_of_goods_sold = sum(
            sum(item['amount'] for item in items)
            for category, items in expenses.items()
            if category == '營業成本'
        )
        
        return operating_revenue - cost_of_goods_sold
    
    def _calculate_operating_income(self, revenues: Dict, expenses: Dict) -> float:
        """計算營業利潤"""
        operating_revenue = sum(
            sum(item['amount'] for item in items)
            for category, items in revenues.items()
            if category in ['營業收入']
        )
        
        operating_expenses = sum(
            sum(item['amount'] for item in items)
            for category, items in expenses.items()
            if category in ['營業成本', '營業費用', '管理費用']
        )
        
        return operating_revenue - operating_expenses
    
    def _get_period_description(self, start_date: date, end_date: date) -> str:
        """獲取期間描述"""
        if start_date.year == end_date.year:
            if start_date.month == 1 and start_date.day == 1 and end_date.month == 12 and end_date.day == 31:
                return f"{start_date.year}年度"
            elif start_date.year == end_date.year and start_date.month == end_date.month:
                return f"{start_date.year}年{start_date.month}月"
            else:
                return f"{start_date.year}年{start_date.month}月{start_date.day}日至{end_date.month}月{end_date.day}日"
        else:
            return f"{start_date.year}年{start_date.month}月{start_date.day}日至{end_date.year}年{end_date.month}月{end_date.day}日"