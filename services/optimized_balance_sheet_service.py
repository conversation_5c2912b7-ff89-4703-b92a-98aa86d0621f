"""
優化版本的資產負債表服務
主要優化：
1. 使用 SQL 聚合查詢減少記憶體使用
2. 分批處理大量資料
3. 利用新增的索引提升查詢速度
"""

from database import get_db
from model import AccountSubject, Money, Account
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict
from sqlalchemy.orm import joinedload
from sqlalchemy import func, and_

class OptimizedBalanceSheetService:
    """優化版資產負債表服務類"""
    
    @staticmethod
    def generate_balance_sheet(report_date: str) -> Dict[str, Any]:
        """生成資產負債表數據 - 優化版本"""
        
        # 轉換日期格式
        if isinstance(report_date, str):
            report_date = datetime.strptime(report_date, '%Y-%m-%d').date()
        
        with get_db() as db:
            # 取得所有會計科目（這個還是需要全部載入）
            subjects = db.query(AccountSubject).all()
            subject_map = {subj.code: subj for subj in subjects}
            
            # === 主要優化 1: 使用 SQL 聚合查詢代替載入所有記錄 ===
            # 直接在資料庫層級計算各科目的借貸總額
            subject_aggregates = db.query(
                Money.subject_code,
                func.sum(Money.lend).label('total_lend'),
                func.sum(Money.borrow).label('total_borrow'),
                func.count(Money.id).label('transaction_count')
            ).filter(
                and_(
                    Money.a_time <= report_date,
                    Money.is_deleted == False  # 排除已刪除的記錄
                )
            ).group_by(Money.subject_code).all()
            
            # 將聚合結果轉換為餘額字典
            subject_balances = {}
            for row in subject_aggregates:
                if row.subject_code:
                    total_lend = row.total_lend or 0
                    total_borrow = row.total_borrow or 0
                    # 根據科目類型計算餘額
                    # 資產類：借方增加，貸方減少
                    # 負債和權益類：貸方增加，借方減少
                    subject = subject_map.get(row.subject_code)
                    if subject:
                        if subject.top_category in ['資產', '成本', '費用']:
                            balance = total_lend - total_borrow
                        else:  # 負債、權益、收入
                            balance = total_borrow - total_lend
                        subject_balances[row.subject_code] = balance
            
            # === 優化 2: 帳戶期初金額使用單一查詢 ===
            account_balances = OptimizedBalanceSheetService._get_account_initial_balances_optimized(db)
            
            # 整合帳戶餘額到科目餘額
            subject_balances = OptimizedBalanceSheetService._integrate_account_balances(
                subject_balances, account_balances, subject_map
            )
            
            # === 優化 3: 損益計算也使用聚合查詢 ===
            net_income = OptimizedBalanceSheetService._calculate_net_income_optimized(db, report_date)
            
            # 分類整理資產負債表數據
            balance_sheet_data = OptimizedBalanceSheetService._organize_balance_sheet_data(
                subject_balances, subject_map
            )
            
            # 將本期損益加入權益總計
            if net_income != 0:
                balance_sheet_data['equity']['total_equity'] += net_income
                balance_sheet_data['equity']['retained_earnings'].append({
                    'code': '3310',
                    'name': '本期損益',
                    'balance': abs(net_income),
                    'original_balance': net_income,
                    'percentage': 0
                })
                
                # 重新計算百分比
                OptimizedBalanceSheetService._calculate_percentages(balance_sheet_data)
            
            return balance_sheet_data
    
    @staticmethod
    def _get_account_initial_balances_optimized(db) -> Dict[str, int]:
        """取得帳戶期初金額 - 優化版本"""
        # 一次查詢所有需要的帳戶資料
        accounts = db.query(
            Account.subject_code,
            func.sum(Account.init_amount).label('total_init_amount')
        ).filter(
            Account.is_deleted == False
        ).group_by(Account.subject_code).all()
        
        result = {}
        for account in accounts:
            if account.subject_code and account.total_init_amount:
                result[account.subject_code] = account.total_init_amount
        
        return result
    
    @staticmethod
    def _calculate_net_income_optimized(db, report_date) -> int:
        """計算本期損益 - 優化版本"""
        # 收入類科目（4xxx）
        income_result = db.query(
            func.sum(Money.borrow - Money.lend).label('total_income')
        ).filter(
            and_(
                Money.a_time <= report_date,
                Money.subject_code.like('4%'),
                Money.is_deleted == False
            )
        ).first()
        
        # 費用類科目（5xxx, 6xxx）
        expense_result = db.query(
            func.sum(Money.lend - Money.borrow).label('total_expense')
        ).filter(
            and_(
                Money.a_time <= report_date,
                func.or_(
                    Money.subject_code.like('5%'),
                    Money.subject_code.like('6%')
                ),
                Money.is_deleted == False
            )
        ).first()
        
        total_income = income_result.total_income if income_result and income_result.total_income else 0
        total_expense = expense_result.total_expense if expense_result and expense_result.total_expense else 0
        
        return total_income - total_expense
    
    @staticmethod
    def _integrate_account_balances(subject_balances: Dict, account_balances: Dict, subject_map: Dict) -> Dict:
        """整合帳戶餘額到科目餘額"""
        # 與原版相同，因為這部分已經很優化了
        for subject_code, amount in account_balances.items():
            if subject_code in subject_balances:
                subject_balances[subject_code] += amount
            else:
                subject_balances[subject_code] = amount
        
        return subject_balances
    
    @staticmethod
    def _organize_balance_sheet_data(subject_balances: Dict, subject_map: Dict) -> Dict:
        """組織資產負債表數據結構"""
        # 這部分邏輯與原版相同，主要是資料組織
        balance_sheet_data = {
            'assets': {
                'current_assets': [],
                'non_current_assets': [],
                'total_assets': 0
            },
            'liabilities': {
                'current_liabilities': [],
                'non_current_liabilities': [],
                'total_liabilities': 0
            },
            'equity': {
                'share_capital': [],
                'capital_surplus': [],
                'retained_earnings': [],
                'total_equity': 0
            }
        }
        
        # 分類科目
        for code, balance in subject_balances.items():
            if balance == 0:
                continue
                
            subject = subject_map.get(code)
            if not subject:
                continue
            
            item = {
                'code': code,
                'name': subject.name,
                'balance': abs(balance),
                'original_balance': balance,
                'percentage': 0
            }
            
            # 根據科目代碼分類
            if code.startswith('1'):  # 資產
                if code.startswith('11') or code.startswith('12'):
                    balance_sheet_data['assets']['current_assets'].append(item)
                else:
                    balance_sheet_data['assets']['non_current_assets'].append(item)
                balance_sheet_data['assets']['total_assets'] += abs(balance)
                
            elif code.startswith('2'):  # 負債
                if code.startswith('21'):
                    balance_sheet_data['liabilities']['current_liabilities'].append(item)
                else:
                    balance_sheet_data['liabilities']['non_current_liabilities'].append(item)
                balance_sheet_data['liabilities']['total_liabilities'] += abs(balance)
                
            elif code.startswith('3'):  # 權益
                if code.startswith('31'):
                    balance_sheet_data['equity']['share_capital'].append(item)
                elif code.startswith('32'):
                    balance_sheet_data['equity']['capital_surplus'].append(item)
                else:
                    balance_sheet_data['equity']['retained_earnings'].append(item)
                balance_sheet_data['equity']['total_equity'] += abs(balance)
        
        return balance_sheet_data
    
    @staticmethod
    def _calculate_percentages(balance_sheet_data: Dict) -> None:
        """計算各項目佔總資產的百分比"""
        total_assets = balance_sheet_data['assets']['total_assets']
        
        if total_assets == 0:
            return
        
        # 計算資產項目百分比
        for item in balance_sheet_data['assets']['current_assets']:
            item['percentage'] = round((item['balance'] / total_assets) * 100, 2)
        
        for item in balance_sheet_data['assets']['non_current_assets']:
            item['percentage'] = round((item['balance'] / total_assets) * 100, 2)
        
        # 計算負債項目百分比
        for item in balance_sheet_data['liabilities']['current_liabilities']:
            item['percentage'] = round((item['balance'] / total_assets) * 100, 2)
        
        for item in balance_sheet_data['liabilities']['non_current_liabilities']:
            item['percentage'] = round((item['balance'] / total_assets) * 100, 2)
        
        # 計算權益項目百分比
        for item in balance_sheet_data['equity']['share_capital']:
            item['percentage'] = round((item['balance'] / total_assets) * 100, 2)
        
        for item in balance_sheet_data['equity']['capital_surplus']:
            item['percentage'] = round((item['balance'] / total_assets) * 100, 2)
        
        for item in balance_sheet_data['equity']['retained_earnings']:
            item['percentage'] = round((item['balance'] / total_assets) * 100, 2)