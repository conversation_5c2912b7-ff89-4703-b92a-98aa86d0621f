"""
服務層統一入口點
提供向後相容和新的統一服務介面
"""

# 基礎服務類
from .base import (
    BaseService, 
    CRUDService, 
    ServiceFactory,
    ServiceError, 
    ValidationError, 
    BusinessLogicError,
    register_service
)

# 統一服務類 (新版本)
from .unified_balance_sheet_service import UnifiedBalanceSheetService
from .unified_income_statement_service import UnifiedIncomeStatementService  
from .unified_account_service import UnifiedAccountService

# 向後相容的服務類別名 (舊版本相容)
from .auth_service import AuthService, RoleService, PermissionService
from .menu_service import MenuService
from .tenant_service import TenantService, FeatureService
from .dashboard_service import DashboardService
from .subject_service import SubjectService
from .bank_service import BankService
from .transaction_service import TransactionService
from .money_service import MoneyService
from .journal_validator import JournalValidator, JournalValidationError
from .optimized_query_service import OptimizedQueryService

# 向後相容的別名映射
# 這樣現有代碼可以繼續工作，逐步遷移到統一服務
BalanceSheetService = UnifiedBalanceSheetService
NewBalanceSheetService = UnifiedBalanceSheetService
OptimizedBalanceSheetService = UnifiedBalanceSheetService

IncomeStatementService = UnifiedIncomeStatementService
NewIncomeStatementService = UnifiedIncomeStatementService

AccountService = UnifiedAccountService

# 服務工廠的預設實例
def get_balance_sheet_service(db_session=None):
    """獲取資產負債表服務實例"""
    return ServiceFactory.get_service(UnifiedBalanceSheetService, db_session)

def get_income_statement_service(db_session=None):
    """獲取損益表服務實例"""
    return ServiceFactory.get_service(UnifiedIncomeStatementService, db_session)

def get_account_service(db_session=None):
    """獲取帳戶服務實例"""
    return ServiceFactory.get_service(UnifiedAccountService, db_session)

def get_auth_service(db_session=None):
    """獲取認證服務實例"""
    return ServiceFactory.get_service(AuthService, db_session)

def get_menu_service(db_session=None):
    """獲取選單服務實例"""
    return ServiceFactory.get_service(MenuService, db_session)

def get_dashboard_service(db_session=None):
    """獲取儀表板服務實例"""
    return ServiceFactory.get_service(DashboardService, db_session)

# 匯出所有公共介面
__all__ = [
    # 基礎類
    'BaseService',
    'CRUDService', 
    'ServiceFactory',
    'ServiceError',
    'ValidationError', 
    'BusinessLogicError',
    'register_service',
    
    # 統一服務
    'UnifiedBalanceSheetService',
    'UnifiedIncomeStatementService',
    'UnifiedAccountService',
    
    # 向後相容別名
    'BalanceSheetService',
    'NewBalanceSheetService', 
    'OptimizedBalanceSheetService',
    'IncomeStatementService',
    'NewIncomeStatementService',
    'AccountService',
    
    # 其他服務
    'AuthService',
    'RoleService', 
    'PermissionService',
    'MenuService',
    'TenantService',
    'FeatureService', 
    'DashboardService',
    'SubjectService',
    'BankService',
    'TransactionService',
    'MoneyService',
    'JournalValidator',
    'JournalValidationError',
    'OptimizedQueryService',
    
    # 工廠方法
    'get_balance_sheet_service',
    'get_income_statement_service', 
    'get_account_service',
    'get_auth_service',
    'get_menu_service',
    'get_dashboard_service',
] 