"""
會計分錄驗證服務
提供複式記帳的完整性驗證功能
"""
from typing import List, Dict, Tuple, Optional
from sqlalchemy.orm import Session
from model import Money, AccountSubject


class JournalValidationError(Exception):
    """會計分錄驗證錯誤"""
    pass


class JournalValidator:
    """會計分錄驗證器"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def validate_journal_entries(self, journal_reference: str) -> Dict[str, any]:
        """
        驗證指定分錄參考號的所有分錄
        
        Args:
            journal_reference: 分錄參考號
            
        Returns:
            驗證結果字典
            
        Raises:
            JournalValidationError: 驗證失敗時拋出
        """
        entries = self.db.query(Money).filter(
            Money.journal_reference == journal_reference
        ).all()
        
        if not entries:
            raise JournalValidationError(f"找不到分錄參考號: {journal_reference}")
        
        # 執行各項驗證
        result = {
            'journal_reference': journal_reference,
            'entry_count': len(entries),
            'debit_total': 0,
            'credit_total': 0,
            'is_balanced': False,
            'errors': [],
            'warnings': []
        }
        
        # 1. 檢查分錄數量
        if len(entries) < 2:
            result['errors'].append(f"分錄數量不足，至少需要2筆，實際: {len(entries)}")
        
        # 2. 計算借貸總額並驗證
        debit_total = 0
        credit_total = 0
        
        for entry in entries:
            # 驗證金額為正數
            if entry.total <= 0:
                result['errors'].append(f"記錄 {entry.id} 金額必須為正數，實際: {entry.total}")
            
            # 驗證借貸方向
            if entry.entry_side not in ['DEBIT', 'CREDIT']:
                result['errors'].append(f"記錄 {entry.id} 借貸方向無效: {entry.entry_side}")
            
            # 累計借貸金額
            if entry.entry_side == 'DEBIT':
                debit_total += entry.total
            elif entry.entry_side == 'CREDIT':
                credit_total += entry.total
            
            # 驗證會計科目
            if not self._validate_subject_code(entry.subject_code):
                result['warnings'].append(f"記錄 {entry.id} 會計科目可能無效: {entry.subject_code}")
        
        result['debit_total'] = debit_total
        result['credit_total'] = credit_total
        result['is_balanced'] = (debit_total == credit_total)
        
        # 3. 借貸平衡驗證
        if not result['is_balanced']:
            result['errors'].append(
                f"借貸不平衡 - 借方: {debit_total}, 貸方: {credit_total}, 差額: {debit_total - credit_total}"
            )
        
        return result
    
    def validate_before_commit(self, entries: List[Money]) -> bool:
        """
        在提交前驗證分錄
        
        Args:
            entries: 要驗證的分錄列表
            
        Returns:
            驗證是否通過
            
        Raises:
            JournalValidationError: 驗證失敗時拋出
        """
        if not entries:
            raise JournalValidationError("沒有分錄需要驗證")
        
        # 按 journal_reference 分組
        groups = {}
        for entry in entries:
            ref = entry.journal_reference
            if ref not in groups:
                groups[ref] = []
            groups[ref].append(entry)
        
        # 驗證每個分組
        for journal_ref, group_entries in groups.items():
            if len(group_entries) < 2:
                raise JournalValidationError(f"分錄 {journal_ref} 至少需要2筆記錄")
            
            debit_total = sum(int(e.total) if isinstance(e.total, str) else e.total for e in group_entries if e.entry_side == 'DEBIT')
            credit_total = sum(int(e.total) if isinstance(e.total, str) else e.total for e in group_entries if e.entry_side == 'CREDIT')
            
            if debit_total != credit_total:
                raise JournalValidationError(
                    f"分錄 {journal_ref} 借貸不平衡 - 借方: {debit_total}, 貸方: {credit_total}"
                )
        
        return True
    
    def _validate_subject_code(self, subject_code: str) -> bool:
        """驗證會計科目代碼是否存在"""
        if not subject_code:
            return False
        
        subject = self.db.query(AccountSubject).filter(
            AccountSubject.code == subject_code
        ).first()
        
        return subject is not None
    
    def get_journal_summary(self, journal_reference: str) -> Dict[str, any]:
        """
        獲取分錄摘要資訊
        
        Args:
            journal_reference: 分錄參考號
            
        Returns:
            分錄摘要字典
        """
        entries = self.db.query(Money).filter(
            Money.journal_reference == journal_reference
        ).all()
        
        if not entries:
            return None
        
        debit_entries = [e for e in entries if e.entry_side == 'DEBIT']
        credit_entries = [e for e in entries if e.entry_side == 'CREDIT']
        
        return {
            'journal_reference': journal_reference,
            'transaction_date': entries[0].a_time,
            'description': entries[0].name,
            'total_amount': sum(e.total for e in debit_entries),
            'debit_entries': [
                {
                    'subject_code': e.subject_code,
                    'subject_name': self._get_subject_name(e.subject_code),
                    'amount': e.total,
                    'description': e.name
                } for e in debit_entries
            ],
            'credit_entries': [
                {
                    'subject_code': e.subject_code,
                    'subject_name': self._get_subject_name(e.subject_code),
                    'amount': e.total,
                    'description': e.name
                } for e in credit_entries
            ]
        }
    
    def _get_subject_name(self, subject_code: str) -> str:
        """獲取會計科目名稱"""
        subject = self.db.query(AccountSubject).filter(
            AccountSubject.code == subject_code
        ).first()
        return subject.name if subject else subject_code


def validate_journal_balance(db_session: Session, journal_reference: str) -> bool:
    """
    快速驗證分錄平衡的便利函數
    
    Args:
        db_session: 資料庫會話
        journal_reference: 分錄參考號
        
    Returns:
        是否平衡
    """
    validator = JournalValidator(db_session)
    try:
        result = validator.validate_journal_entries(journal_reference)
        return result['is_balanced'] and not result['errors']
    except JournalValidationError:
        return False
