"""
帳戶相關業務邏輯服務
"""
from database import get_db
from model import Account, PaymentIdentity, Department, Project
from utils.performance.cache_manager import cache_result

# 為了向後兼容，創建別名
cache_for_5min = lambda func: cache_result(ttl=300)(func)

class AccountService:
    """帳戶服務類"""
    
    @staticmethod
    @cache_for_5min
    def get_accounts_dropdown():
        """獲取帳戶下拉選單資料"""
        with get_db() as db:
            accounts = db.query(Account).order_by(
                Account.is_default.desc(), 
                Account.name.asc()
            ).all()
            
            return [
                {
                    'id': account.id,
                    'name': account.name,
                    'display_name': f"{account.name}{' (' + account.bank_name + ')' if account.bank_name is not None and account.bank_name.strip() else ''}",
                    'category': account.category
                }
                for account in accounts
            ]
    
    @staticmethod
    @cache_for_5min
    def get_dropdown_data():
        """獲取所有下拉選單資料的統一方法"""
        with get_db() as db:
            # 一次性查詢所有需要的資料
            payment_identities = db.query(PaymentIdentity).order_by(PaymentIdentity.name.asc()).all()
            departments = db.query(Department).order_by(Department.name.asc()).all()
            projects = db.query(Project).filter_by(status='進行中').order_by(Project.name.asc()).all()
            
            # 獲取公司基本資料
            from model import CompanyInfo
            company = db.query(CompanyInfo).first()

            # 按類型分組支付對象
            identities_by_type = {}
            for identity in payment_identities:
                identity_type = identity.type
                if identity_type not in identities_by_type:
                    identities_by_type[identity_type] = []
                identities_by_type[identity_type].append({
                    'id': identity.id,
                    'name': identity.name,
                    'type': identity.type,
                    'tax_id': identity.tax_id
                })
            
            return {
                'accounts': AccountService.get_accounts_dropdown(),
                'payment_identities': [
                    {
                        'id': identity.id,
                        'name': identity.name,
                        'type': identity.type,
                        'tax_id': identity.tax_id
                    }
                    for identity in payment_identities
                ],
                'identities_by_type': identities_by_type,
                'departments': [
                    {
                        'id': department.id,
                        'name': department.name,
                        'code': department.code
                    }
                    for department in departments
                ],
                'projects': [
                    {
                        'id': project.id,
                        'name': project.name,
                        'code': project.code,
                        'status': project.status
                    }
                    for project in projects
                ],
                'company_id': company.company_id if company else None,
                'company_name': company.company_name if company else None
            }
    
    @staticmethod
    def get_account_by_id(account_id):
        """根據 ID 獲取帳戶資料"""
        with get_db() as db:
            return db.query(Account).filter_by(id=account_id).first()
    
    @staticmethod
    def get_default_account():
        """獲取預設帳戶"""
        with get_db() as db:
            return db.query(Account).filter_by(is_default=True).first()