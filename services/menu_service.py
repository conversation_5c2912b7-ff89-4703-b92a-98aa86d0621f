from typing import Dict, List, Any, Optional
from flask import session
from services.auth_service import AuthService
from data.menu_data import menu

class MenuService:
    """選單服務 - 根據用戶權限過濾選單"""
    
    # 定義模組權限映射
    MODULE_PERMISSIONS = {
        "收支帳簿": "income_expense.view",
        "資金管理": "fund_management.view", 
        "資產管理": "asset_management.view",
        "薪資報酬": "payroll.view",
        "勞務報酬": "service_reward.view",
        "我的報表": "reports.view",
        "會計科目": "accounting.view",
        "扣繳申報": "tax_declaration.view",
        "設定": "settings.view",
        "系統管理": "admin.view"
    }
    
    # 定義功能權限映射
    FUNCTION_PERMISSIONS = {
        # 收支帳簿
        "新增帳務": "income_expense.create",
        "收入紀錄列表": "income_expense.view",
        "支出紀錄列表": "income_expense.view",
        "應收應付逾期": "income_expense.view",
        "分享帳簿": "income_expense.share",
        
        # 資金管理
        "新增移轉紀錄": "fund_management.create",
        "資金移轉列表": "fund_management.view",
        "新增銀行借款": "fund_management.create",
        "銀行借款列表": "fund_management.view",
        "新增資金紀錄": "fund_management.create",
        "資金紀錄列表": "fund_management.view",
        "帳戶明細": "fund_management.view",
        
        # 資產管理
        "新增預付費用": "asset_management.create",
        "新增各項攤提": "asset_management.create",
        "新增固定資產": "asset_management.create",
        "新增 無形資產/商譽": "asset_management.create",
        "財產列表": "asset_management.view",
        
        # 薪資報酬
        "發薪作業": "payroll.create",
        "勞保/健保/退休金管理": "payroll.manage",
        "新增員工": "payroll.create",
        "員工管理列表": "payroll.view",
        "公司設定": "payroll.settings",
        "薪資設定": "payroll.settings",
        
        # 勞務報酬
        "建立勞報單": "service_reward.create",
        "勞務報酬列表": "service_reward.view",
        
        # 我的報表
        "資產負債表": "reports.view",
        "損益表": "reports.view",
        "明細分類帳": "reports.view",
        "會計科目明細": "reports.view",
        "營運分析表": "reports.view",
        "專案/部門別分析": "reports.view",
        "收支對象分析": "reports.view",
        "金流預估表": "reports.view",
        "月度報表": "reports.view",
        "年度報表": "reports.view",
        "帳戶對帳單": "reports.view",
        "現金流量": "reports.view",
        "付款狀態": "reports.view",
        "快取管理": "reports.manage",
        
        # 會計科目
        "科目管理": "accounting.manage",
        "傳票管理": "accounting.manage",
        "成本結轉": "accounting.manage",
        "進/銷項稅額管理": "accounting.manage",
        
        # 扣繳申報
        "扣繳申報作業": "tax_declaration.create",
        
        # 設定
        "部門管理": "settings.manage",
        "專案管理": "settings.manage",
        "收支對象管理": "settings.manage",
        "基本資料": "settings.view",
        "個人資料": "settings.view",
        "帳戶設定": "settings.manage",
        "開帳設定": "settings.manage",
        
        # 系統管理
        "用戶管理": "admin.user_management",
        "角色管理": "admin.user_management", 
        "權限設定": "admin.permission_management",
        "系統資訊": "admin.system_info",
        "日誌管理": "admin.log_management",
        "備份管理": "admin.backup_management"
    }
    
    @staticmethod
    def get_user_menu(user_id: Optional[int] = None) -> Dict[str, Any]:
        """獲取用戶可訪問的選單"""
        if not user_id:
            user_id = session.get('user_id')
        
        if not user_id:
            return {}
        
        # 獲取用戶權限
        user_permissions = AuthService.get_user_permissions(user_id)
        
        # 過濾選單
        filtered_menu = {}
        
        for main_menu, submenus in menu.items():
            # 檢查主選單權限
            required_permission = MenuService.MODULE_PERMISSIONS.get(main_menu)
            if required_permission and required_permission not in user_permissions:
                continue
            
            # 過濾子選單
            filtered_submenus = []
            for submenu in submenus:
                filtered_buttons = []
                
                for button in submenu['buttons']:
                    # 檢查按鈕權限
                    button_permission = MenuService.FUNCTION_PERMISSIONS.get(button['label'])
                    if button_permission and button_permission not in user_permissions:
                        continue
                    
                    # 過濾子功能
                    filtered_children = []
                    for child in button.get('children', []):
                        child_permission = MenuService.FUNCTION_PERMISSIONS.get(child)
                        if not child_permission or child_permission in user_permissions:
                            filtered_children.append(child)
                    
                    # 如果有子功能，更新子功能列表
                    if button.get('children'):
                        button = button.copy()
                        button['children'] = filtered_children
                    
                    # 如果按鈕有權限或有可訪問的子功能，則加入
                    if not button_permission or button_permission in user_permissions or filtered_children:
                        filtered_buttons.append(button)
                
                # 如果有可訪問的按鈕，則加入子選單
                if filtered_buttons:
                    filtered_submenu = submenu.copy()
                    filtered_submenu['buttons'] = filtered_buttons
                    filtered_submenus.append(filtered_submenu)
            
            # 如果有可訪問的子選單，則加入主選單
            if filtered_submenus:
                filtered_menu[main_menu] = filtered_submenus
        
        return filtered_menu
    
    @staticmethod
    def user_can_access_function(user_id: int, function_name: str) -> bool:
        """檢查用戶是否可以訪問特定功能"""
        required_permission = MenuService.FUNCTION_PERMISSIONS.get(function_name)
        if not required_permission:
            return True  # 沒有定義權限要求的功能預設允許訪問
        
        user_permissions = AuthService.get_user_permissions(user_id)
        return required_permission in user_permissions
    
    @staticmethod
    def get_available_modules() -> List[str]:
        """獲取所有可用模組列表"""
        return list(MenuService.MODULE_PERMISSIONS.keys())
    
    @staticmethod
    def get_module_functions(module_name: str) -> List[str]:
        """獲取模組下的所有功能"""
        if module_name not in menu:
            return []
        
        functions = []
        for submenu in menu[module_name]:
            for button in submenu['buttons']:
                functions.append(button['label'])
                functions.extend(button.get('children', []))
        
        return functions