"""
優化的資料庫查詢服務
解決 N+1 查詢問題，提升系統性能
"""
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy import func, and_, or_
from database import get_db
from model import Money, Account, PaymentIdentity, Department, Project, Transaction, AccountSubject
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import date, datetime

logger = logging.getLogger(__name__)

class OptimizedQueryService:
    """優化的查詢服務類"""
    
    @staticmethod
    def get_money_records_with_relations(filters: Dict = None, limit: int = 100) -> List[Dict]:
        """
        獲取收支記錄及其關聯資料（優化版）
        一次查詢獲取所有關聯資料，避免 N+1 問題
        """
        with get_db() as db:
            query = db.query(Money).options(
                joinedload(Money.account),
                joinedload(Money.payment_identity),
                joinedload(Money.department),
                joinedload(Money.project),
                joinedload(Money.subject)
            )
            
            # 應用篩選條件
            if filters:
                if 'account_id' in filters:
                    query = query.filter(Money.account_id == filters['account_id'])
                if 'money_type' in filters:
                    query = query.filter(Money.money_type == filters['money_type'])
                if 'subject_codes' in filters:
                    query = query.filter(Money.subject_code.in_(filters['subject_codes']))
                if 'date_from' in filters:
                    query = query.filter(Money.a_time >= filters['date_from'])
                if 'date_to' in filters:
                    query = query.filter(Money.a_time <= filters['date_to'])
            
            # 排序和限制
            records = query.order_by(Money.a_time.desc()).limit(limit).all()
            
            # 轉換為字典格式
            result = []
            for record in records:
                result.append({
                    'id': record.id,
                    'money_type': record.money_type,
                    'name': record.name,
                    'total': record.total,
                    'a_time': record.a_time,
                    'note': record.note,
                    'tags': record.tags,
                    'account': {
                        'id': record.account.id if record.account else None,
                        'name': record.account.name if record.account else '未指定'
                    },
                    'payment_identity': {
                        'id': record.payment_identity.id if record.payment_identity else None,
                        'name': record.payment_identity.name if record.payment_identity else '未指定'
                    },
                    'department': {
                        'id': record.department.id if record.department else None,
                        'name': record.department.name if record.department else '未指定'
                    },
                    'project': {
                        'id': record.project.id if record.project else None,
                        'name': record.project.name if record.project else '未指定'
                    }
                })
            
            return result
    
    @staticmethod
    def get_account_summary_with_balances() -> List[Dict]:
        """
        獲取帳戶摘要及餘額（優化版）
        批量計算所有帳戶餘額，避免逐個查詢
        """
        with get_db() as db:
            # 獲取所有帳戶
            accounts = db.query(Account).filter(not Account.is_deleted).all()
            
            # 批量查詢所有帳戶的交易記錄
            account_ids = [acc.id for acc in accounts]
            money_records = db.query(Money).filter(
                Money.account_id.in_(account_ids)
            ).all()
            
            # 計算每個帳戶的餘額
            account_balances = {}
            for record in money_records:
                if record.account_id not in account_balances:
                    account_balances[record.account_id] = 0
                
                amount = record.total or 0
                if record.money_type == '收入':
                    account_balances[record.account_id] += amount
                elif record.money_type == '支出':
                    account_balances[record.account_id] -= amount
            
            # 組合結果
            result = []
            for account in accounts:
                init_amount = account.init_amount or 0
                transaction_balance = account_balances.get(account.id, 0)
                current_balance = init_amount + transaction_balance
                
                result.append({
                    'id': account.id,
                    'name': account.name,
                    'category': account.category,
                    'bank_name': account.bank_name,
                    'account_number': account.account_number,
                    'init_amount': init_amount,
                    'transaction_balance': transaction_balance,
                    'current_balance': current_balance,
                    'is_default': account.is_default
                })
            
            return result
    
    @staticmethod
    def get_period_summary_optimized(
        start_date: date, 
        end_date: date,
        account_ids: List[int] = None
    ) -> Dict[str, Any]:
        """
        獲取期間摘要（優化版）
        一次查詢計算所有統計數據
        """
        with get_db() as db:
            # 構建基礎查詢
            query = db.query(
                func.sum(Money.total).label('total_amount'),
                Money.money_type,
                Money.account_id
            ).filter(
                Money.a_time >= start_date,
                Money.a_time <= end_date
            )
            
            # 可選的帳戶篩選
            if account_ids:
                query = query.filter(Money.account_id.in_(account_ids))
            
            # 分組查詢
            results = query.group_by(Money.money_type, Money.account_id).all()
            
            # 整理結果
            summary = {
                'total_income': 0,
                'total_expense': 0,
                'net_change': 0,
                'by_account': {},
                'by_type': {'收入': 0, '支出': 0}
            }
            
            for result in results:
                amount = result.total_amount or 0
                money_type = result.money_type
                account_id = result.account_id
                
                # 更新總計
                if money_type == '收入':
                    summary['total_income'] += amount
                    summary['by_type']['收入'] += amount
                elif money_type == '支出':
                    summary['total_expense'] += amount
                    summary['by_type']['支出'] += amount
                
                # 更新帳戶分類
                if account_id not in summary['by_account']:
                    summary['by_account'][account_id] = {'收入': 0, '支出': 0}
                
                summary['by_account'][account_id][money_type] = amount
            
            # 計算淨變化
            summary['net_change'] = summary['total_income'] - summary['total_expense']
            
            return summary
    
    @staticmethod
    def get_dashboard_statistics() -> Dict[str, Any]:
        """
        獲取儀表板統計資料（優化版）
        一次查詢獲取所有統計數據
        """
        from datetime import datetime
        from sqlalchemy import func, extract
        
        with get_db() as db:
            # 當前月份和年份
            current_date = datetime.now()
            current_month = current_date.month
            current_year = current_date.year
            
            # 一次性查詢所有統計數據
            stats_query = db.query(
                # 總收入
                func.sum(
                    func.case(
                        (Money.money_type == '收入', Money.total),
                        else_=0
                    )
                ).label('total_income'),
                
                # 總支出
                func.sum(
                    func.case(
                        (Money.money_type == '支出', Money.total),
                        else_=0
                    )
                ).label('total_expense'),
                
                # 本月收入
                func.sum(
                    func.case(
                        (
                            (Money.money_type == '收入') & 
                            (extract('month', Money.a_time) == current_month) &
                            (extract('year', Money.a_time) == current_year),
                            Money.total
                        ),
                        else_=0
                    )
                ).label('month_income'),
                
                # 本月支出
                func.sum(
                    func.case(
                        (
                            (Money.money_type == '支出') & 
                            (extract('month', Money.a_time) == current_month) &
                            (extract('year', Money.a_time) == current_year),
                            Money.total
                        ),
                        else_=0
                    )
                ).label('month_expense'),
                
                # 記錄總數
                func.count(Money.id).label('total_records')
            ).first()
            
            # 計算帳戶總數
            account_count = db.query(func.count(Account.id)).filter(
                not Account.is_deleted
            ).scalar()
            
            return {
                'total_income': int(stats_query.total_income or 0),
                'total_expense': int(stats_query.total_expense or 0),
                'net_income': int((stats_query.total_income or 0) - (stats_query.total_expense or 0)),
                'month_income': int(stats_query.month_income or 0),
                'month_expense': int(stats_query.month_expense or 0),
                'month_net': int((stats_query.month_income or 0) - (stats_query.month_expense or 0)),
                'total_records': int(stats_query.total_records or 0),
                'account_count': int(account_count or 0),
                'last_updated': current_date.isoformat()
            }
    
    @staticmethod
    def get_fund_records_optimized(filters: Dict = None) -> List[Dict]:
        """
        獲取資金記錄（優化版）
        為 fund_record 路由提供優化的查詢
        """
        with get_db() as db:
            query = db.query(Money).options(
                joinedload(Money.account),
                joinedload(Money.payment_identity),
                joinedload(Money.department),
                joinedload(Money.project)
            )
            
            # 應用篩選條件
            if filters:
                if 'subject_codes' in filters:
                    query = query.filter(Money.subject_code.in_(filters['subject_codes']))
                if 'account_id' in filters:
                    query = query.filter(Money.account_id == filters['account_id'])
                if 'money_type' in filters:
                    query = query.filter(Money.money_type == filters['money_type'])
            
            records = query.order_by(Money.a_time.desc()).all()
            
            # 轉換為字典格式
            result = []
            for record in records:
                result.append({
                    'id': record.id,
                    'money_type': record.money_type,
                    'amount': record.total,
                    'record_date': record.a_time.strftime('%Y-%m-%d') if record.a_time else None,
                    'account_name': record.account.name if record.account else '未指定',
                    'subject_code': record.subject_code,
                    'note': record.note,
                    'tags': record.tags
                })
            
            return result

    @staticmethod
    def get_form_data_optimized() -> Dict[str, Any]:
        """
        獲取表單所需的所有資料（優化版）
        一次性獲取所有下拉選單資料，避免多次查詢
        """
        from model import Account, Department, Project, PaymentIdentity, CompanyInfo

        with get_db() as db:
            # 一次性查詢所有需要的資料
            accounts = db.query(Account).filter(Account.is_deleted == False).all()
            departments = db.query(Department).filter(Department.is_deleted == False).all()
            projects = db.query(Project).filter(Project.is_deleted == False, Project.status.in_(['規劃中', '進行中'])).all()
            payment_identities = db.query(PaymentIdentity).filter(PaymentIdentity.is_deleted == False).all()
            company = db.query(CompanyInfo).first()

            # 轉換為字典格式
            accounts_data = [{'id': acc.id, 'name': acc.name} for acc in accounts]
            departments_data = [{'id': dept.id, 'name': dept.name} for dept in departments]
            projects_data = [{'id': proj.id, 'name': proj.name} for proj in projects]

            # 按類型分組身份資料
            identities_by_type = {}
            for identity in payment_identities:
                identity_type = identity.type or '其他'
                if identity_type not in identities_by_type:
                    identities_by_type[identity_type] = []
                identities_by_type[identity_type].append({
                    'id': identity.id,
                    'name': identity.name
                })

            return {
                'accounts': accounts_data,
                'departments': departments_data,
                'projects': projects_data,
                'identities_by_type': identities_by_type,
                'company_id': company.company_id if company else None,  # 返回統編而不是ID
                'company_name': company.company_name if company else None  # 新增公司名稱
            }