from datetime import datetime, timedelta
import json
import logging
from sqlalchemy import func, desc, and_, or_
from database import get_db
from model import Account, Money, Transaction, PaymentIdentity, JournalEntry, AccountSubject
from utils.format_helper import format_money
from utils.database.query_limits import safe_all, safe_paginate

logger = logging.getLogger(__name__)

class DashboardService:
    """儀表板服務 - 提供總覽頁面所需的數據"""
    
    @staticmethod
    def get_dashboard_data(user_id):
        """獲取儀表板數據"""
        with get_db() as db:
            # 獲取帳戶餘額
            accounts = DashboardService.get_account_balances(db)
            
            # 獲取本月收支統計
            income_expense_summary = DashboardService.get_monthly_summary(db)
            
            # 獲取最近的收支紀錄
            recent_records = DashboardService.get_recent_records(db)
            
            # 獲取待處理的應收/應付帳款
            pending_payments = DashboardService.get_pending_payments(db)
            
            # 獲取收入/支出趨勢圖數據
            chart_data = DashboardService.get_trend_chart_data(db)
            
            # 計算總餘額（使用原始數值）
            total_balance = sum(account['raw_balance'] for account in accounts)
            
            # 組合所有數據
            dashboard_data = {
                'accounts': accounts,
                'recent_records': recent_records,
                'pending_payments': pending_payments,
                'chart_labels': json.dumps(chart_data['labels']),
                'chart_income_data': json.dumps(chart_data['income_data']),
                'chart_expense_data': json.dumps(chart_data['expense_data']),
                'total_balance': format_money(total_balance),
                'total_income': format_money(income_expense_summary['income']),
                'total_expense': format_money(income_expense_summary['expense']),
                'net_income': format_money(income_expense_summary['income'] - income_expense_summary['expense'])
            }
            
            return dashboard_data
    
    @staticmethod
    def get_account_balances(db):
        """獲取帳戶餘額（只用 Transaction 表，排除開帳類型）"""
        accounts_query = safe_all(
            db.query(Account).filter(Account.is_deleted == False),
            context="dashboard"
        )
        accounts = []
        for account in accounts_query:
            transaction_income = db.query(func.sum(Transaction.total_amount)).filter(
                Transaction.account_id == account.id,
                Transaction.transaction_type == 'income',
                ~Transaction.description.like('開帳%')
            ).scalar() or 0
            transaction_expense = db.query(func.sum(Transaction.total_amount)).filter(
                Transaction.account_id == account.id,
                Transaction.transaction_type == 'expense',
                ~Transaction.description.like('開帳%')
            ).scalar() or 0
            balance = account.init_amount + transaction_income - transaction_expense
            # Debug: 針對主要銀行帳戶印出詳細計算過程
            if account.name == '主要銀行帳戶':
                # 記錄到日誌而非 print
                logger.debug(f"主要銀行帳戶餘額計算 - 期初:{account.init_amount}, 收入:{transaction_income}, 支出:{transaction_expense}, 餘額:{balance}")
            accounts.append({
                'id': account.id,
                'name': account.name,
                'category': account.category,
                'balance': format_money(balance),
                'raw_balance': balance
            })
        accounts.sort(key=lambda x: x['raw_balance'], reverse=True)
        # Debug: 輸出所有帳戶名稱與餘額
        print('--- 帳戶餘額 Debug ---')
        for acc in accounts:
            print(f"帳戶: {acc['name']}，餘額: {acc['raw_balance']}")
        print('----------------------')
        return accounts
    
    @staticmethod
    def get_monthly_summary(db):
        """獲取本月收支統計（只用 Transaction 表，排除開帳類型）"""
        today = datetime.now()
        first_day = datetime(today.year, today.month, 1).date()  # 只取 date 型態
        # 只用 Transaction 表計算本月收入
        income_query = db.query(Transaction).filter(
            Transaction.transaction_type == 'income',
            Transaction.transaction_date >= first_day,
            ~Transaction.description.like('開帳%')
        )
        transaction_income = db.query(func.sum(Transaction.total_amount)).filter(
            Transaction.transaction_type == 'income',
            Transaction.transaction_date >= first_day,
            ~Transaction.description.like('開帳%')
        ).scalar() or 0
        # Debug: 印出所有被算進本月收入的交易
        print(f'--- 本月收入明細 (查詢起始日: {first_day}) ---')
        for t in income_query:
            print(f"[Transaction] 日期:{t.transaction_date} 金額:{t.total_amount} 摘要:{t.description}")
        print('----------------------')
        # 只用 Transaction 表計算本月支出
        transaction_expense = db.query(func.sum(Transaction.total_amount)).filter(
            Transaction.transaction_type == 'expense',
            Transaction.transaction_date >= first_day,
            ~Transaction.description.like('開帳%')
        ).scalar() or 0
        # Debug: 印出所有 description 包含「光明頂」的 Transaction 資料
        guangming = safe_all(
            db.query(Transaction).filter(Transaction.description.like('%光明頂%')),
            context="search"
        )
        print('--- 所有 description 含「光明頂」的 Transaction ---')
        for t in guangming:
            print(f"[Transaction] id:{t.id} 類型:{t.transaction_type} 日期:{t.transaction_date} 金額:{t.total_amount} 摘要:{t.description}")
        print('----------------------')
        # Debug: 印出 first_day 的型態和值
        print(f'first_day: {first_day} (type: {type(first_day)})')
        # Debug: 印出光明頂那筆 transaction_date 的型態和值
        if guangming:
            t = guangming[0]
            print(f'光明頂 transaction_date: {t.transaction_date} (type: {type(t.transaction_date)})')
        return {
            'income': transaction_income,
            'expense': transaction_expense
        }
    
    @staticmethod
    def get_recent_records(db, limit=10):
        """只查 Transaction 表，獲取最近的收支紀錄，排除開帳類型"""
        records = []
        # 查詢 Transaction 表中的記錄，排除開帳
        transaction_records = db.query(Transaction, JournalEntry, AccountSubject).join(
            JournalEntry, Transaction.id == JournalEntry.transaction_id
        ).join(
            AccountSubject, JournalEntry.subject_code == AccountSubject.code
        ).filter(
            JournalEntry.entry_type == 'primary',  # 只獲取主要分錄
            ~Transaction.description.like('開帳%')
        ).order_by(desc(Transaction.transaction_date))
        
        transaction_records = safe_all(
            transaction_records,
            limit=limit,
            context="dashboard"
        )
        # 處理 Transaction 表中的記錄
        for transaction, entry, subject in transaction_records:
            try:
                transaction_type = transaction.transaction_type.capitalize()
            except AttributeError:
                transaction_type = 'Income' if entry.credit_amount > 0 else 'Expense'
            records.append({
                'id': transaction.id,
                'date': transaction.transaction_date.strftime('%Y-%m-%d') if transaction.transaction_date else '',
                'type': transaction_type,
                'name': transaction.description,
                'amount': format_money(transaction.total_amount),
                'subject': subject.name
            })
        # Debug: 印出 7 月所有 income/expense 的 transaction_type、日期、金額、描述
        from datetime import datetime
        today = datetime.now()
        first_day = datetime(today.year, today.month, 1)
        print(f'--- 7月所有 income/expense 交易明細 (查詢起始日: {first_day}) ---')
        july_tx = safe_all(
            db.query(Transaction).filter(
                Transaction.transaction_date >= first_day,
                Transaction.transaction_type.in_(['income', 'expense'])
            ),
            context="search"
        )
        for t in july_tx:
            print(f"[Transaction] 類型:{t.transaction_type} 日期:{t.transaction_date} 金額:{t.total_amount} 摘要:{t.description}")
        print('----------------------')
        # 按日期排序並限制數量
        records.sort(key=lambda x: x['date'], reverse=True)
        records = records[:limit]
        return records
    
    @staticmethod
    def get_pending_payments(db, limit=10):
        """獲取待處理的應收/應付帳款"""
        today = datetime.now()
        payments = []
        
        # 查詢舊的 Money 表中的待處理帳款
        money_query = db.query(Money, PaymentIdentity).join(
            PaymentIdentity,
            Money.payment_identity_id == PaymentIdentity.id,
            isouter=True
        ).filter(
            Money.is_paid == False,
            Money.should_paid_date != None,
            Money.is_deleted == False,
            # 只顯示主要交易記錄
            ((Money.money_type == '收入') & (Money.entry_side == 'CREDIT') & (Money.subject_code != '2290')) |
            ((Money.money_type == '支出') & (Money.entry_side == 'DEBIT') & (Money.subject_code != '1290')),
            or_(
                # 已逾期
                Money.should_paid_date <= today,
                # 即將到期（7天內）
                and_(
                    Money.should_paid_date > today,
                    Money.should_paid_date <= today + timedelta(days=7)
                )
            )
        ).order_by(Money.should_paid_date)
        
        money_query = safe_all(
            money_query,
            limit=limit,
            context="dashboard"
        )
        
        # 處理 Money 表中的記錄
        for record, identity in money_query:
            payments.append({
                'id': record.id,
                'identity': identity.name if identity else '未指定',
                'type': '應收' if record.money_type == '收入' else '應付',
                'due_date': record.should_paid_date.strftime('%Y-%m-%d') if record.should_paid_date else '',
                'amount': format_money(record.total),
                'is_overdue': record.should_paid_date <= today if record.should_paid_date else False
            })
        
        # 嘗試查詢新的 Transaction 表中的待處理帳款
        try:
            transaction_query = db.query(Transaction, PaymentIdentity).join(
                PaymentIdentity,
                Transaction.payment_identity_id == PaymentIdentity.id,
                isouter=True
            ).filter(
                Transaction.is_paid == False,
                Transaction.should_paid_date != None,
                or_(
                    # 已逾期
                    Transaction.should_paid_date <= today,
                    # 即將到期（7天內）
                    and_(
                        Transaction.should_paid_date > today,
                        Transaction.should_paid_date <= today + timedelta(days=7)
                    )
                )
            ).order_by(Transaction.should_paid_date)
            
            transaction_query = safe_all(
                transaction_query,
                limit=limit,
                context="dashboard"
            )
            
            # 處理 Transaction 表中的記錄
            for transaction, identity in transaction_query:
                try:
                    transaction_type = '應收' if transaction.transaction_type == 'income' else '應付'
                except AttributeError:
                    # 如果沒有 transaction_type 欄位，則根據其他信息判斷
                    transaction_type = '應收'  # 默認為應收
                
                payments.append({
                    'id': transaction.id,
                    'identity': identity.name if identity else '未指定',
                    'type': transaction_type,
                    'due_date': transaction.should_paid_date.strftime('%Y-%m-%d') if transaction.should_paid_date else '',
                    'amount': format_money(transaction.total_amount),
                    'is_overdue': transaction.should_paid_date <= today if transaction.should_paid_date else False
                })
        except Exception as e:
            # 如果查詢 Transaction 表出錯，忽略錯誤
            print(f"查詢 Transaction 表出錯: {str(e)}")
        
        # 按到期日排序並限制數量
        payments.sort(key=lambda x: x['due_date'])
        payments = payments[:limit]
        
        return payments
    
    @staticmethod
    def get_trend_chart_data(db, period='month'):
        """獲取收入/支出趨勢圖數據"""
        today = datetime.now()
        
        # 根據選擇的時間範圍確定日期範圍和格式
        if period == 'week':
            # 過去7天
            start_date = today - timedelta(days=6)
            date_format = '%m/%d'
            # SQLite 不支持 date() 函數，使用 strftime 代替
            group_by = func.strftime('%Y-%m-%d', Money.a_time)
            
            # 生成日期標籤
            labels = [(start_date + timedelta(days=i)).strftime(date_format) for i in range(7)]
            
        elif period == 'month':
            # 本月
            start_date = datetime(today.year, today.month, 1)
            days_in_month = (datetime(today.year, today.month + 1, 1) if today.month < 12 else datetime(today.year + 1, 1, 1)) - timedelta(days=1)
            days_in_month = days_in_month.day
            date_format = '%d'
            # SQLite 不支持 day() 函數，使用 strftime 代替
            group_by = func.strftime('%d', Money.a_time)
            
            # 生成日期標籤
            labels = [(start_date + timedelta(days=i)).strftime(date_format) for i in range(days_in_month)]
            
        elif period == 'quarter':
            # 本季度
            current_month = today.month
            quarter_start_month = ((current_month - 1) // 3) * 3 + 1
            start_date = datetime(today.year, quarter_start_month, 1)
            date_format = '%m/%d'
            # SQLite 不支持 date() 函數，使用 strftime 代替
            group_by = func.strftime('%Y-%m-%d', Money.a_time)
            
            # 生成日期標籤（簡化為每週一個數據點）
            days = (today - start_date).days + 1
            labels = []
            for i in range(0, days, 7):
                labels.append((start_date + timedelta(days=i)).strftime(date_format))
            
        else:  # year
            # 本年
            start_date = datetime(today.year, 1, 1)
            date_format = '%m月'
            # SQLite 不支持 month() 函數，使用 strftime 代替
            group_by = func.strftime('%m', Money.a_time)
            
            # 生成月份標籤
            labels = [f"{i}月" for i in range(1, 13)]
        
        # 查詢收入數據（只計算4開頭的收入科目）
        income_data = db.query(
            group_by,
            func.sum(Money.total)
        ).filter(
            Money.money_type == '收入',
            Money.entry_side == 'CREDIT',  # 只計算貸方分錄
            Money.subject_code.like('4%'),  # 只計算4開頭的收入科目
            Money.a_time >= start_date,
            Money.is_deleted == False
        ).group_by(group_by).all()
        
        # 查詢支出數據（只計算5、6開頭的費用科目）
        expense_data = db.query(
            group_by,
            func.sum(Money.total)
        ).filter(
            Money.money_type == '支出',
            Money.entry_side == 'DEBIT',  # 只計算借方分錄
            or_(
                Money.subject_code.like('5%'),  # 成本科目
                Money.subject_code.like('6%')   # 費用科目
            ),
            Money.a_time >= start_date,
            Money.is_deleted == False
        ).group_by(group_by).all()
        
        # 將查詢結果轉換為圖表數據格式
        # 對於 SQLite 的 strftime 結果，直接使用返回的字符串作為鍵
        income_dict = {date: amount for date, amount in income_data}
        expense_dict = {date: amount for date, amount in expense_data}
        
        # 根據不同的時間範圍處理數據
        income_values = []
        expense_values = []
        
        if period == 'week':
            for i in range(7):
                date_key = (start_date + timedelta(days=i)).strftime('%Y-%m-%d')
                income_values.append(income_dict.get(date_key, 0))
                expense_values.append(expense_dict.get(date_key, 0))
                
        elif period == 'month':
            for i in range(days_in_month):
                # 使用日期的天數作為鍵（與 strftime('%d') 匹配）
                day_key = f"{i+1:02d}"
                income_values.append(income_dict.get(day_key, 0))
                expense_values.append(expense_dict.get(day_key, 0))
                
        elif period == 'quarter':
            # 簡化為每週一個數據點
            for i in range(0, days, 7):
                week_start = start_date + timedelta(days=i)
                week_end = min(week_start + timedelta(days=6), today)
                
                # 計算該週的收入和支出
                week_income = 0
                week_expense = 0
                current = week_start
                while current <= week_end:
                    date_key = current.strftime('%Y-%m-%d')
                    week_income += income_dict.get(date_key, 0)
                    week_expense += expense_dict.get(date_key, 0)
                    current += timedelta(days=1)
                
                income_values.append(week_income)
                expense_values.append(week_expense)
                
        else:  # year
            for month in range(1, 13):
                # 將月份格式化為兩位數字字符串，與 strftime('%m') 的輸出匹配
                month_key = f"{month:02d}"
                income_values.append(income_dict.get(month_key, 0))
                expense_values.append(expense_dict.get(month_key, 0))
        
        return {
            'labels': labels,
            'income_data': income_values,
            'expense_data': expense_values
        }