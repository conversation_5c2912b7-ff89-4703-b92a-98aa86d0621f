"""
服務層基礎架構
提供統一的服務介面和共同功能
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic
from sqlalchemy.orm import Session
from database import get_db
from contextlib import contextmanager
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T')

class ServiceError(Exception):
    """服務層基礎錯誤"""
    def __init__(self, message: str, code: str = None, details: Dict = None):
        self.message = message
        self.code = code or 'SERVICE_ERROR'
        self.details = details or {}
        super().__init__(self.message)

class ValidationError(ServiceError):
    """驗證錯誤"""
    def __init__(self, message: str, field: str = None, details: Dict = None):
        super().__init__(message, 'VALIDATION_ERROR', details)
        self.field = field

class BusinessLogicError(ServiceError):
    """業務邏輯錯誤"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, 'BUSINESS_LOGIC_ERROR', details)

class BaseService(ABC):
    """服務層基礎類"""
    
    def __init__(self, db_session: Session = None):
        """
        初始化服務
        
        Args:
            db_session: 資料庫會話，如果為 None 將使用預設連接
        """
        self._db_session = db_session
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
    
    @contextmanager
    def get_db_session(self):
        """獲取資料庫會話的上下文管理器"""
        if self._db_session:
            # 如果已有會話，直接使用
            yield self._db_session
        else:
            # 否則創建新的會話
            with get_db() as db:
                yield db
    
    def log_operation(self, operation: str, details: Dict = None):
        """記錄操作日誌"""
        log_data = {
            'service': self.__class__.__name__,
            'operation': operation,
            **(details or {})
        }
        self.logger.info(f"執行操作: {operation}", extra=log_data)
    
    def validate_input(self, data: Dict, required_fields: List[str] = None):
        """基礎輸入驗證"""
        if not isinstance(data, dict):
            raise ValidationError("輸入資料必須是字典格式")
        
        required_fields = required_fields or []
        missing_fields = []
        
        for field in required_fields:
            if field not in data or data[field] is None:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValidationError(
                f"缺少必要欄位: {', '.join(missing_fields)}",
                details={'missing_fields': missing_fields}
            )

class CRUDService(BaseService, Generic[T]):
    """提供 CRUD 操作的基礎服務類"""
    
    def __init__(self, model_class: Type[T], db_session: Session = None):
        super().__init__(db_session)
        self.model_class = model_class
    
    def create(self, data: Dict) -> T:
        """創建新記錄"""
        try:
            with self.get_db_session() as db:
                instance = self.model_class(**data)
                db.add(instance)
                db.commit()
                db.refresh(instance)
                
                self.log_operation('create', {'model': self.model_class.__name__, 'id': getattr(instance, 'id', None)})
                return instance
        except Exception as e:
            self.logger.error(f"創建 {self.model_class.__name__} 失敗: {e}")
            raise ServiceError(f"創建記錄失敗: {str(e)}")
    
    def get_by_id(self, id: int) -> Optional[T]:
        """根據 ID 獲取記錄"""
        try:
            with self.get_db_session() as db:
                instance = db.query(self.model_class).filter(self.model_class.id == id).first()
                return instance
        except Exception as e:
            self.logger.error(f"獲取 {self.model_class.__name__} 失敗: {e}")
            raise ServiceError(f"獲取記錄失敗: {str(e)}")
    
    def get_all(self, filters: Dict = None, limit: int = None, offset: int = None) -> List[T]:
        """獲取所有記錄"""
        try:
            with self.get_db_session() as db:
                query = db.query(self.model_class)
                
                # 應用過濾條件
                if filters:
                    for field, value in filters.items():
                        if hasattr(self.model_class, field):
                            query = query.filter(getattr(self.model_class, field) == value)
                
                # 應用分頁
                if offset:
                    query = query.offset(offset)
                if limit:
                    query = query.limit(limit)
                
                return query.all()
        except Exception as e:
            self.logger.error(f"獲取 {self.model_class.__name__} 列表失敗: {e}")
            raise ServiceError(f"獲取記錄列表失敗: {str(e)}")
    
    def update(self, id: int, data: Dict) -> Optional[T]:
        """更新記錄"""
        try:
            with self.get_db_session() as db:
                instance = db.query(self.model_class).filter(self.model_class.id == id).first()
                if not instance:
                    return None
                
                for field, value in data.items():
                    if hasattr(instance, field):
                        setattr(instance, field, value)
                
                db.commit()
                db.refresh(instance)
                
                self.log_operation('update', {'model': self.model_class.__name__, 'id': id})
                return instance
        except Exception as e:
            self.logger.error(f"更新 {self.model_class.__name__} 失敗: {e}")
            raise ServiceError(f"更新記錄失敗: {str(e)}")
    
    def delete(self, id: int) -> bool:
        """刪除記錄"""
        try:
            with self.get_db_session() as db:
                instance = db.query(self.model_class).filter(self.model_class.id == id).first()
                if not instance:
                    return False
                
                db.delete(instance)
                db.commit()
                
                self.log_operation('delete', {'model': self.model_class.__name__, 'id': id})
                return True
        except Exception as e:
            self.logger.error(f"刪除 {self.model_class.__name__} 失敗: {e}")
            raise ServiceError(f"刪除記錄失敗: {str(e)}")

class ServiceFactory:
    """服務工廠類 - 管理服務實例"""
    
    _instances: Dict[str, Any] = {}
    
    @classmethod
    def get_service(cls, service_class: Type[BaseService], db_session: Session = None) -> BaseService:
        """獲取服務實例（單例模式）"""
        service_name = f"{service_class.__module__}.{service_class.__name__}"
        
        if service_name not in cls._instances:
            cls._instances[service_name] = service_class(db_session)
        
        return cls._instances[service_name]
    
    @classmethod
    def clear_cache(cls):
        """清除服務實例快取"""
        cls._instances.clear()

# 服務註冊裝飾器
def register_service(service_class: Type[BaseService]):
    """服務註冊裝飾器"""
    def decorator(cls):
        # 在此可以添加服務註冊邏輯
        return cls
    return decorator