"""
損益表服務
提供損益表數據生成和計算功能
"""
from typing import Dict, List
from datetime import datetime, date
from collections import defaultdict
from sqlalchemy.orm import sessionmaker
from model import Money, AccountSubject, engine

Session = sessionmaker(bind=engine)


class IncomeStatementService:
    """損益表服務類"""
    
    @staticmethod
    def generate_income_statement(start_date: str, end_date: str) -> Dict:
        """
        生成損益表數據
        
        Args:
            start_date (str): 開始日期 (YYYY-MM-DD)
            end_date (str): 結束日期 (YYYY-MM-DD)
            
        Returns:
            Dict: 損益表數據結構
        """
        # 轉換日期格式
        start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        with Session() as db:
            # 取得所有會計科目
            subjects = db.query(AccountSubject).all()
            subject_map = {subj.code: subj for subj in subjects}

            # 取得期間內的所有交易記錄
            transactions = db.query(Money).filter(
                Money.a_time >= start_dt,
                Money.a_time <= end_dt
            ).all()

            # 計算各科目金額
            subject_balances = IncomeStatementService._calculate_subject_balances(
                transactions, subject_map
            )

            # 組織損益表數據
            income_statement_data = IncomeStatementService._organize_income_statement_data(
                subject_balances, subject_map
            )

            return income_statement_data

    @staticmethod
    def _get_empty_income_statement() -> Dict:
        """返回空的損益表數據結構（包含示例數據）"""
        # 添加示例數據以展示功能
        sample_data = {
            'revenue': {
                'sales_revenue': [
                    {'code': '4111', 'name': '商品銷售收入', 'amount': 5000000},
                    {'code': '4112', 'name': '產品銷售收入', 'amount': 3000000}
                ],
                'sales_revenue_total': 8000000,
                'service_revenue': [
                    {'code': '4211', 'name': '技術服務收入', 'amount': 1500000},
                    {'code': '4212', 'name': '維修服務收入', 'amount': 500000}
                ],
                'service_revenue_total': 2000000,
                'other_revenue': [
                    {'code': '4911', 'name': '其他營業收入', 'amount': 200000}
                ],
                'other_revenue_total': 200000,
                'total': 10200000
            },
            'cost_of_sales': {
                'cost_items': [
                    {'code': '5111', 'name': '商品成本', 'amount': 3000000},
                    {'code': '5112', 'name': '材料成本', 'amount': 1500000},
                    {'code': '5113', 'name': '人工成本', 'amount': 800000}
                ],
                'total': 5300000
            },
            'gross_profit': 4900000,
            'operating_expenses': {
                'selling_expenses': [
                    {'code': '6111', 'name': '廣告費', 'amount': 300000},
                    {'code': '6112', 'name': '業務推廣費', 'amount': 200000}
                ],
                'selling_expenses_total': 500000,
                'admin_expenses': [
                    {'code': '6211', 'name': '管理人員薪資', 'amount': 800000},
                    {'code': '6212', 'name': '辦公費用', 'amount': 150000},
                    {'code': '6213', 'name': '租金支出', 'amount': 240000}
                ],
                'admin_expenses_total': 1190000,
                'research_expenses': [
                    {'code': '6311', 'name': '研發人員薪資', 'amount': 600000},
                    {'code': '6312', 'name': '研發材料費', 'amount': 100000}
                ],
                'research_expenses_total': 700000,
                'other_expenses': [
                    {'code': '6911', 'name': '其他營業費用', 'amount': 80000}
                ],
                'other_expenses_total': 80000,
                'total': 2470000
            },
            'operating_income': 0,
            'non_operating': {
                'income': [],
                'income_total': 0,
                'expenses': [],
                'expenses_total': 0,
                'net': 0
            },
            'income_before_tax': 0,
            'income_tax': {
                'items': [],
                'total': 0
            },
            'net_income': 0
        }

    @staticmethod
    def _calculate_subject_balances(transactions, subject_map: Dict) -> Dict:
        """計算各科目餘額"""
        balances = defaultdict(int)
        
        for transaction in transactions:
            if not transaction.subject_code:
                continue
                
            subject_code = transaction.subject_code
            amount = transaction.total or 0
            
            # 損益表只關注收入和費用科目
            if subject_code.startswith(('4', '5', '6', '7', '8')):  # 收入、成本、費用科目
                if transaction.money_type == '收入':
                    if subject_code.startswith('4'):  # 收入科目
                        balances[subject_code] += amount
                elif transaction.money_type == '支出':
                    if subject_code.startswith(('5', '6', '7', '8')):  # 成本、費用科目
                        balances[subject_code] += amount
        
        return dict(balances)
    
    @staticmethod
    def _organize_income_statement_data(subject_balances: Dict, subject_map: Dict) -> Dict:
        """組織損益表數據結構"""
        income_statement = {
            'revenue': {  # 營業收入
                'sales_revenue': [],  # 銷貨收入 (41xx)
                'sales_revenue_total': 0,
                'service_revenue': [],  # 勞務收入 (42xx)
                'service_revenue_total': 0,
                'other_revenue': [],  # 其他營業收入 (43xx-49xx)
                'other_revenue_total': 0,
                'total': 0
            },
            'cost_of_sales': {  # 營業成本
                'cost_items': [],  # 銷貨成本 (51xx)
                'total': 0
            },
            'gross_profit': 0,  # 營業毛利
            'operating_expenses': {  # 營業費用
                'selling_expenses': [],  # 推銷費用 (61xx)
                'selling_expenses_total': 0,
                'admin_expenses': [],  # 管理費用 (62xx)
                'admin_expenses_total': 0,
                'research_expenses': [],  # 研發費用 (63xx)
                'research_expenses_total': 0,
                'other_expenses': [],  # 其他營業費用 (64xx-69xx)
                'other_expenses_total': 0,
                'total': 0
            },
            'operating_income': 0,  # 營業利益
            'non_operating': {  # 營業外收支
                'income': [],  # 營業外收入 (71xx)
                'income_total': 0,
                'expenses': [],  # 營業外費用 (81xx)
                'expenses_total': 0,
                'net': 0
            },
            'income_before_tax': 0,  # 稅前淨利
            'income_tax': {  # 所得稅
                'items': [],  # 所得稅費用 (82xx)
                'total': 0
            },
            'net_income': 0  # 稅後淨利
        }
        
        # 處理所有科目
        for subject_code, subject in subject_map.items():
            balance = subject_balances.get(subject_code, 0)
            
            if balance == 0:
                continue
                
            subject_data = {
                'code': subject_code,
                'name': subject.name,
                'amount': balance
            }
            
            # 分類科目
            IncomeStatementService._classify_subject(subject_code, subject_data, income_statement)
        
        # 計算各項總計
        IncomeStatementService._calculate_totals(income_statement)
        
        # 排序科目
        IncomeStatementService._sort_income_statement(income_statement)
        
        return income_statement
    
    @staticmethod
    def _classify_subject(subject_code: str, subject_data: Dict, income_statement: Dict):
        """分類科目到對應的損益表項目"""
        if subject_code.startswith('41'):  # 銷貨收入
            income_statement['revenue']['sales_revenue'].append(subject_data)
        elif subject_code.startswith('42'):  # 勞務收入
            income_statement['revenue']['service_revenue'].append(subject_data)
        elif subject_code.startswith(('43', '44', '45', '46', '47', '48', '49')):  # 其他營業收入
            income_statement['revenue']['other_revenue'].append(subject_data)
        elif subject_code.startswith('51'):  # 銷貨成本
            income_statement['cost_of_sales']['cost_items'].append(subject_data)
        elif subject_code.startswith('61'):  # 推銷費用
            income_statement['operating_expenses']['selling_expenses'].append(subject_data)
        elif subject_code.startswith('62'):  # 管理費用
            income_statement['operating_expenses']['admin_expenses'].append(subject_data)
        elif subject_code.startswith('63'):  # 研發費用
            income_statement['operating_expenses']['research_expenses'].append(subject_data)
        elif subject_code.startswith(('64', '65', '66', '67', '68', '69')):  # 其他營業費用
            income_statement['operating_expenses']['other_expenses'].append(subject_data)
        elif subject_code.startswith('71'):  # 營業外收入
            income_statement['non_operating']['income'].append(subject_data)
        elif subject_code.startswith('81'):  # 營業外費用
            income_statement['non_operating']['expenses'].append(subject_data)
        elif subject_code.startswith('82'):  # 所得稅費用
            income_statement['income_tax']['items'].append(subject_data)
    
    @staticmethod
    def _calculate_totals(income_statement: Dict):
        """計算各項總計"""
        # 營業收入各分類小計
        sales_total = sum(item['amount'] for item in income_statement['revenue']['sales_revenue'])
        income_statement['revenue']['sales_revenue_total'] = sales_total

        service_total = sum(item['amount'] for item in income_statement['revenue']['service_revenue'])
        income_statement['revenue']['service_revenue_total'] = service_total

        other_revenue_total = sum(item['amount'] for item in income_statement['revenue']['other_revenue'])
        income_statement['revenue']['other_revenue_total'] = other_revenue_total

        # 營業收入總計
        revenue_total = sales_total + service_total + other_revenue_total
        income_statement['revenue']['total'] = revenue_total

        # 營業成本總計
        cost_total = sum(item['amount'] for item in income_statement['cost_of_sales']['cost_items'])
        income_statement['cost_of_sales']['total'] = cost_total

        # 營業毛利
        income_statement['gross_profit'] = revenue_total - cost_total

        # 營業費用各分類小計
        selling_total = sum(item['amount'] for item in income_statement['operating_expenses']['selling_expenses'])
        income_statement['operating_expenses']['selling_expenses_total'] = selling_total

        admin_total = sum(item['amount'] for item in income_statement['operating_expenses']['admin_expenses'])
        income_statement['operating_expenses']['admin_expenses_total'] = admin_total

        research_total = sum(item['amount'] for item in income_statement['operating_expenses']['research_expenses'])
        income_statement['operating_expenses']['research_expenses_total'] = research_total

        other_expenses_total = sum(item['amount'] for item in income_statement['operating_expenses']['other_expenses'])
        income_statement['operating_expenses']['other_expenses_total'] = other_expenses_total

        # 營業費用總計
        expense_total = selling_total + admin_total + research_total + other_expenses_total
        income_statement['operating_expenses']['total'] = expense_total

        # 營業利益
        income_statement['operating_income'] = income_statement['gross_profit'] - expense_total

        # 營業外收支各分類小計
        non_op_income_total = sum(item['amount'] for item in income_statement['non_operating']['income'])
        income_statement['non_operating']['income_total'] = non_op_income_total

        non_op_expense_total = sum(item['amount'] for item in income_statement['non_operating']['expenses'])
        income_statement['non_operating']['expenses_total'] = non_op_expense_total

        # 營業外收支淨額
        income_statement['non_operating']['net'] = non_op_income_total - non_op_expense_total

        # 稅前淨利
        income_statement['income_before_tax'] = income_statement['operating_income'] + income_statement['non_operating']['net']

        # 所得稅費用總計
        tax_total = sum(item['amount'] for item in income_statement['income_tax']['items'])
        income_statement['income_tax']['total'] = tax_total

        # 稅後淨利
        income_statement['net_income'] = income_statement['income_before_tax'] - tax_total

        # 計算百分比
        IncomeStatementService._calculate_percentages(income_statement)
    
    @staticmethod
    def _sort_income_statement(income_statement: Dict):
        """排序損益表項目（按科目代碼）"""
        # 排序各個分類的項目
        for category in income_statement['revenue']:
            if isinstance(income_statement['revenue'][category], list):
                income_statement['revenue'][category].sort(key=lambda x: x['code'])
        
        income_statement['cost_of_sales']['cost_items'].sort(key=lambda x: x['code'])
        
        for category in income_statement['operating_expenses']:
            if isinstance(income_statement['operating_expenses'][category], list):
                income_statement['operating_expenses'][category].sort(key=lambda x: x['code'])
        
        income_statement['non_operating']['income'].sort(key=lambda x: x['code'])
        income_statement['non_operating']['expenses'].sort(key=lambda x: x['code'])
        income_statement['income_tax']['items'].sort(key=lambda x: x['code'])

    @staticmethod
    def _calculate_percentages(income_statement: Dict):
        """計算各項目佔營收的百分比"""
        revenue_total = income_statement['revenue']['total']

        if revenue_total == 0:
            return

        # 計算營業收入各項目百分比
        for item in income_statement['revenue']['sales_revenue']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        for item in income_statement['revenue']['service_revenue']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        for item in income_statement['revenue']['other_revenue']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        # 計算營業成本百分比
        for item in income_statement['cost_of_sales']['cost_items']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        # 計算營業費用百分比
        for item in income_statement['operating_expenses']['selling_expenses']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        for item in income_statement['operating_expenses']['admin_expenses']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        for item in income_statement['operating_expenses']['research_expenses']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        for item in income_statement['operating_expenses']['other_expenses']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        # 計算營業外收支百分比
        for item in income_statement['non_operating']['income']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        for item in income_statement['non_operating']['expenses']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        # 計算所得稅費用百分比
        for item in income_statement['income_tax']['items']:
            item['percentage'] = round((item['amount'] / revenue_total) * 100, 1)

        # 計算各分類小計的百分比
        income_statement['revenue']['sales_revenue_percentage'] = round((income_statement['revenue']['sales_revenue_total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['revenue']['service_revenue_percentage'] = round((income_statement['revenue']['service_revenue_total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['revenue']['other_revenue_percentage'] = round((income_statement['revenue']['other_revenue_total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0

        income_statement['cost_of_sales']['percentage'] = round((income_statement['cost_of_sales']['total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['gross_profit_percentage'] = round((income_statement['gross_profit'] / revenue_total) * 100, 1) if revenue_total > 0 else 0

        income_statement['operating_expenses']['selling_expenses_percentage'] = round((income_statement['operating_expenses']['selling_expenses_total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['operating_expenses']['admin_expenses_percentage'] = round((income_statement['operating_expenses']['admin_expenses_total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['operating_expenses']['research_expenses_percentage'] = round((income_statement['operating_expenses']['research_expenses_total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['operating_expenses']['other_expenses_percentage'] = round((income_statement['operating_expenses']['other_expenses_total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['operating_expenses']['percentage'] = round((income_statement['operating_expenses']['total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0

        income_statement['operating_income_percentage'] = round((income_statement['operating_income'] / revenue_total) * 100, 1) if revenue_total > 0 else 0

        income_statement['non_operating']['income_percentage'] = round((income_statement['non_operating']['income_total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['non_operating']['expenses_percentage'] = round((income_statement['non_operating']['expenses_total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['non_operating']['percentage'] = round((income_statement['non_operating']['net'] / revenue_total) * 100, 1) if revenue_total > 0 else 0

        income_statement['income_before_tax_percentage'] = round((income_statement['income_before_tax'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['income_tax']['percentage'] = round((income_statement['income_tax']['total'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
        income_statement['net_income_percentage'] = round((income_statement['net_income'] / revenue_total) * 100, 1) if revenue_total > 0 else 0
