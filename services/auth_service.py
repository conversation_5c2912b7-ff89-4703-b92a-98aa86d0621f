from typing import List, Optional
from database import get_db
from models.auth_models import Role, Permission, UserSession
from model import User
from werkzeug.security import check_password_hash
import secrets
from datetime import datetime, timedelta

class AuthService:
    """認證授權服務"""
    
    @staticmethod
    def authenticate_user(username: str, password: str) -> Optional[User]:
        """用戶認證"""
        with get_db() as db:
            user = db.query(User).filter(
                User.username == username,
                User.is_active
            ).first()
            
            if user and check_password_hash(user.password_hash, password):
                # 更新最後登入時間
                user.last_login = datetime.now()
                db.commit()
                
                # 返回分離的用戶實例，避免 DetachedInstanceError
                detached_user = User()
                detached_user.id = user.id
                detached_user.username = user.username
                detached_user.email = user.email
                detached_user.full_name = user.full_name
                detached_user.is_active = user.is_active
                detached_user.last_login = user.last_login
                detached_user.tenant_id = user.tenant_id  # 包含租戶ID
                detached_user.created_at = user.created_at
                detached_user.updated_at = user.updated_at
                return detached_user
            return None
    
    @staticmethod
    def create_user_session(user_id: int) -> str:
        """建立用戶會話"""
        with get_db() as db:
            # 生成會話令牌
            session_token = secrets.token_urlsafe(32)
            expires_at = datetime.now() + timedelta(hours=24)  # 24小時過期
            
            # 建立會話記錄
            user_session = UserSession(
                user_id=user_id,
                session_token=session_token,
                expires_at=expires_at
            )
            db.add(user_session)
            db.commit()
            
            return session_token
    
    @staticmethod
    def get_user_by_session(session_token: str) -> Optional[User]:
        """根據會話令牌獲取用戶"""
        with get_db() as db:
            user_session = db.query(UserSession).filter(
                UserSession.session_token == session_token,
                UserSession.is_active,
                UserSession.expires_at > datetime.now()
            ).first()
            
            if user_session:
                # 更新最後訪問時間
                user_session.last_accessed = datetime.now()
                
                # 獲取用戶資料
                user = db.query(User).filter(User.id == user_session.user_id).first()
                db.commit()
                
                # 返回分離的用戶實例，避免 DetachedInstanceError
                if user:
                    # 創建一個新的用戶實例，包含所有必要的屬性
                    detached_user = User()
                    detached_user.id = user.id
                    detached_user.username = user.username
                    detached_user.email = user.email
                    detached_user.full_name = user.full_name
                    detached_user.is_active = user.is_active
                    detached_user.last_login = user.last_login
                    detached_user.tenant_id = user.tenant_id  # 包含租戶ID
                    detached_user.created_at = user.created_at
                    detached_user.updated_at = user.updated_at
                    return detached_user
            return None
    
    @staticmethod
    def get_user_roles(user_id: int) -> List[Role]:
        """獲取用戶角色列表（返回經處理過的分離實例）"""
        with get_db() as db:
            from models.auth_models import user_roles
            
            # 通過用戶ID獲取角色
            roles = db.query(Role).join(
                user_roles, Role.id == user_roles.c.role_id
            ).filter(
                user_roles.c.user_id == user_id
            ).all()
            
            # 返回分離的角色實例，避免 DetachedInstanceError
            detached_roles = []
            for role in roles:
                detached_role = Role()
                detached_role.id = role.id
                detached_role.name = role.name
                if hasattr(role, 'display_name'):
                    detached_role.display_name = role.display_name
                if hasattr(role, 'description'):
                    detached_role.description = role.description
                if hasattr(role, 'is_active'):
                    detached_role.is_active = role.is_active
                if hasattr(role, 'created_at'):
                    detached_role.created_at = role.created_at
                if hasattr(role, 'updated_at'):
                    detached_role.updated_at = role.updated_at
                detached_roles.append(detached_role)
            
            return detached_roles

    @staticmethod
    def get_user_role_names(user_id: int) -> List[str]:
        """獲取用戶角色名稱列表（直接返回角色名稱，避免DetachedInstanceError）"""
        with get_db() as db:
            from models.auth_models import user_roles
            
            # 直接查詢角色名稱
            role_names = db.query(Role.name).join(
                user_roles, Role.id == user_roles.c.role_id
            ).filter(
                user_roles.c.user_id == user_id
            ).all()
            
            # 提取名稱並返回列表
            return [role[0] for role in role_names]
    
    @staticmethod
    def get_user_permissions(user_id: int) -> List[str]:
        """獲取用戶權限列表"""
        with get_db() as db:
            from models.auth_models import user_roles, role_permissions
            
            # 通過用戶角色獲取權限
            permissions = db.query(Permission).join(
                role_permissions, Permission.id == role_permissions.c.permission_id
            ).join(
                user_roles, role_permissions.c.role_id == user_roles.c.role_id
            ).filter(
                user_roles.c.user_id == user_id
            ).all()
            
            return [f"{perm.module}.{perm.action}" for perm in permissions]
    
    @staticmethod
    def user_has_permission(user_id: int, permission: str) -> bool:
        """檢查用戶是否有特定權限"""
        user_permissions = AuthService.get_user_permissions(user_id)
        return permission in user_permissions
    
    @staticmethod
    def get_user_modules(user_id: int) -> List[str]:
        """獲取用戶可訪問的模組列表"""
        with get_db() as db:
            from models.auth_models import user_roles, role_permissions
            
            modules = db.query(Permission.module).join(
                role_permissions, Permission.id == role_permissions.c.permission_id
            ).join(
                user_roles, role_permissions.c.role_id == user_roles.c.role_id
            ).filter(
                user_roles.c.user_id == user_id
            ).distinct().all()
            
            return [module[0] for module in modules]
    
    @staticmethod
    def end_user_session(session_token: str) -> bool:
        """結束用戶會話"""
        with get_db() as db:
            user_session = db.query(UserSession).filter(
                UserSession.session_token == session_token,
                UserSession.is_active
            ).first()
            
            if user_session:
                user_session.is_active = False
                user_session.last_accessed = datetime.now()
                db.commit()
                return True
            return False

class RoleService:
    """角色管理服務"""
    
    @staticmethod
    def create_role(name: str, display_name: str, description: str = None) -> Role:
        """建立角色"""
        with get_db() as db:
            role = Role(
                name=name,
                display_name=display_name,
                description=description
            )
            db.add(role)
            db.commit()
            
            # 返回角色ID，避免 DetachedInstanceError
            role_id = role.id
            
        # 重新查詢角色以返回分離的實例
        with get_db() as db:
            return db.query(Role).filter(Role.id == role_id).first()
    
    @staticmethod
    def assign_permissions_to_role(role_id: int, permission_ids: List[int]):
        """為角色分配權限"""
        with get_db() as db:
            from models.auth_models import role_permissions
            from sqlalchemy import delete
            
            # 先刪除角色現有的權限
            db.execute(delete(role_permissions).where(role_permissions.c.role_id == role_id))
            
            # 添加新的權限
            for permission_id in permission_ids:
                db.execute(role_permissions.insert().values(role_id=role_id, permission_id=permission_id))
            
            db.commit()
    
    @staticmethod
    def assign_role_to_user(user_id: int, role_ids: List[int]):
        """為用戶分配角色"""
        with get_db() as db:
            from models.auth_models import user_roles
            from sqlalchemy import delete
            
            # 先刪除用戶現有的角色
            db.execute(delete(user_roles).where(user_roles.c.user_id == user_id))
            
            # 添加新的角色
            for role_id in role_ids:
                db.execute(user_roles.insert().values(user_id=user_id, role_id=role_id))
            
            db.commit()

class PermissionService:
    """權限管理服務"""
    
    @staticmethod
    def create_permission(name: str, display_name: str, module: str, action: str, description: str = None) -> Permission:
        """建立權限"""
        with get_db() as db:
            permission = Permission(
                name=name,
                display_name=display_name,
                module=module,
                action=action,
                description=description
            )
            db.add(permission)
            db.commit()
            db.refresh(permission)
            return permission
    
    @staticmethod
    def get_permissions_by_module(module: str) -> List[Permission]:
        """根據模組獲取權限"""
        with get_db() as db:
            return db.query(Permission).filter(Permission.module == module).all()