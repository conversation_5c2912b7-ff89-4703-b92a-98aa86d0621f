from database import get_db
from model import AccountSubject, Money, Account
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict
from sqlalchemy.orm import joinedload

class BalanceSheetService:
    """資產負債表服務類"""
    
    @staticmethod
    def generate_balance_sheet(report_date: str) -> Dict[str, Any]:
        """生成資產負債表數據"""
        
        # 轉換日期格式
        if isinstance(report_date, str):
            report_date = datetime.strptime(report_date, '%Y-%m-%d').date()
        
        with get_db() as db:
            # 取得所有會計科目
            subjects = db.query(AccountSubject).all()
            
            # 建立科目代碼到科目的映射
            subject_map = {subj.code: subj for subj in subjects}
            
            # 取得截至報表日期的所有交易記錄（包含帳戶關聯）
            transactions = db.query(Money).options(
                joinedload(Money.account)
            ).filter(
                Money.a_time <= report_date
            ).all()
            
            # 計算各科目餘額
            subject_balances = BalanceSheetService._calculate_subject_balances(
                transactions, subject_map
            )
            
            # 取得帳戶期初金額
            account_balances = BalanceSheetService._get_account_initial_balances(db)
            
            # 整合帳戶餘額到科目餘額
            subject_balances = BalanceSheetService._integrate_account_balances(
                subject_balances, account_balances, subject_map
            )
            
            # 計算本期損益並加入權益
            net_income = BalanceSheetService._calculate_net_income(transactions)
            if net_income != 0:
                # 直接將本期損益加入權益總計，而不是加入科目餘額
                # 因為 3310 科目可能不存在於 subject_map 中
                pass  # 我們會在後面處理
            
            # 分類整理資產負債表數據
            balance_sheet_data = BalanceSheetService._organize_balance_sheet_data(
                subject_balances, subject_map
            )
            
            # 進項稅額和銷項稅額分開處理，不計算應納稅額
            # 進項稅額在資產方，銷項稅額在負債方，各自獨立顯示
            
            # 將本期損益加入權益總計
            if net_income != 0:
                balance_sheet_data['equity']['total_equity'] += net_income
                # 添加本期損益項目到保留盈餘
                balance_sheet_data['equity']['retained_earnings'].append({
                    'code': '3310',
                    'name': '本期損益',
                    'balance': abs(net_income),
                    'original_balance': net_income,
                    'percentage': 0  # 稍後會重新計算百分比
                })
                
                # 重新計算百分比（因為總資產可能改變）
                BalanceSheetService._calculate_percentages(balance_sheet_data)
            
            return balance_sheet_data
    
    @staticmethod
    def _calculate_subject_balances(transactions: List[Money], subject_map: Dict) -> Dict[str, int]:
        """計算各科目餘額 - 基於複式記帳借貸方向"""
        balances = defaultdict(int)

        for transaction in transactions:
            if not transaction.subject_code:
                continue

            subject_code = transaction.subject_code
            amount = transaction.total or 0

            # 檢查是否有借貸方向標示
            if hasattr(transaction, 'entry_side') and transaction.entry_side:
                # 新的複式記帳邏輯：基於借貸方向
                if transaction.entry_side == 'DEBIT':
                    # 借方：資產、費用增加；負債、權益、收入減少
                    if subject_code.startswith(('1', '5', '6')):  # 資產、費用
                        balances[subject_code] += amount
                    else:  # 負債、權益、收入
                        balances[subject_code] -= amount
                elif transaction.entry_side == 'CREDIT':
                    # 貸方：負債、權益、收入增加；資產、費用減少
                    if subject_code.startswith(('1', '5', '6')):  # 資產、費用
                        balances[subject_code] -= amount
                    else:  # 負債、權益、收入
                        balances[subject_code] += amount
            else:
                # 舊的邏輯：基於交易類型（向後兼容）
                # 特殊處理開帳記錄
                is_opening_record = transaction.name and ('開帳' in transaction.name or '期初' in transaction.name)

                if transaction.money_type == '收入':
                    # 收入交易：貸方記入收入科目，借方記入資金帳戶
                    if subject_code.startswith('4'):  # 收入科目
                        balances[subject_code] += amount
                    elif subject_code.startswith('1'):  # 資產科目
                        balances[subject_code] += amount
                    elif subject_code.startswith(('2', '3')):  # 負債、權益科目
                        balances[subject_code] += amount

                    # 處理銷項稅額
                    tax_amount = transaction.tax or 0
                    if tax_amount > 0:
                        balances['2290'] += tax_amount  # 銷項稅額增加

                elif transaction.money_type == '支出':
                    if is_opening_record and subject_code.startswith('1'):
                        # 開帳記錄的資產科目：增加資產
                        balances[subject_code] += amount
                    elif subject_code.startswith('1'):  # 一般資產科目
                        balances[subject_code] -= amount  # 支出減少現金等資產
                    elif subject_code.startswith(('5', '6', '7', '8')):  # 成本、費用
                        balances[subject_code] += amount
                    else:  # 負債、權益
                        balances[subject_code] -= amount

                    # 處理進項稅額
                    tax_amount = transaction.tax or 0
                    if tax_amount > 0:
                        balances['1290'] += tax_amount  # 進項稅額增加

        return dict(balances)
    
    @staticmethod
    def _get_account_initial_balances(db) -> Dict[str, int]:
        """取得帳戶期初餘額"""
        accounts = db.query(Account).filter(
            not Account.is_deleted
        ).all()
        
        account_balances = {}
        for account in accounts:
            if account.subject_code and account.init_amount:
                if account.subject_code not in account_balances:
                    account_balances[account.subject_code] = 0
                account_balances[account.subject_code] += account.init_amount
        
        return account_balances
    
    @staticmethod
    def _integrate_account_balances(subject_balances: Dict, account_balances: Dict, subject_map: Dict) -> Dict[str, int]:
        """整合帳戶餘額到科目餘額"""
        integrated_balances = subject_balances.copy()
        
        for subject_code, balance in account_balances.items():
            if subject_code in integrated_balances:
                integrated_balances[subject_code] += balance
            else:
                integrated_balances[subject_code] = balance
        
        return integrated_balances
    
    @staticmethod
    def _calculate_net_income(transactions: List[Money]) -> int:
        """計算本期損益（收入 - 費用）"""
        revenue_total = 0
        expense_total = 0
        
        for transaction in transactions:
            if not transaction.subject_code:
                continue
                
            subject_code = transaction.subject_code
            amount = transaction.total or 0
            
            # 收入科目（4開頭）
            if subject_code.startswith('4') and transaction.money_type == '收入':
                revenue_total += amount
            
            # 費用科目（5、6、7、8開頭）
            elif subject_code.startswith(('5', '6', '7', '8')) and transaction.money_type == '支出':
                expense_total += amount
        
        # 本期損益 = 收入 - 費用
        return revenue_total - expense_total

    @staticmethod
    def _organize_balance_sheet_data(subject_balances: Dict, subject_map: Dict) -> Dict[str, Any]:
        """整理詳細的資產負債表數據結構"""

        # 初始化詳細的數據結構
        balance_sheet = {
            'assets': {
                'current_assets': {
                    'cash': [],                  # 現金
                    'petty_cash': [],            # 零用金/週轉金
                    'bank_deposits': [],         # 銀行存款
                    'notes_receivable': [],      # 應收票據
                    'accounts_receivable': [],   # 應收帳款
                    'other_receivables': [],     # 其他應收款
                    'estimated_accounts_receivable': [],  # 暫估應收帳款
                    'financial_assets_fvpl': [], # 透過損益按公允價值衡量之金融資產
                    'financial_assets_fvoci': [], # 透過其他綜合損益按公允價值衡量之金融資產
                    'financial_assets_cost': [], # 以成本衡量之金融資產
                    'merchandise_inventory': [], # 商品存貨
                    'raw_material_inventory': [], # 原料存貨
                    'finished_goods': [],        # 製成品
                    'prepaid_expenses': [],      # 預付費用
                    'prepaid_purchases': [],     # 預付貨款
                    'other_prepayments': [],     # 其他預付款
                    'temporary_payments': [],    # 暫付款
                    'prepaid_taxes': [],         # 預付稅款
                    'input_tax': [],             # 進項稅額
                    'tax_credits': [],           # 留抵稅額
                    'other_current': [],         # 其他流動資產
                    'total': 0
                },
                'non_current_assets': {
                    'property_plant_equipment': [],  # 不動產、廠房及設備
                    'intangible_assets': [],         # 無形資產
                    'investments': [],               # 投資
                    'other_non_current': [],         # 其他非流動資產
                    'total': 0
                },
                'total_assets': 0
            },
            'liabilities': {
                'current_liabilities': {
                    'bank_loans': [],                    # 銀行借款
                    'notes_payable': [],                 # 應付票據
                    'accounts_payable': [],              # 應付帳款
                    'estimated_accounts_payable': [],    # 暫估應付帳款
                    'accrued_expenses': [],              # 應付費用
                    'taxes_payable': [],                 # 應付稅捐
                    'benefits_payable': [],              # 應付股利
                    'other_payables': [],                # 其他應付款
                    'advances_from_customers': [],       # 預收貨款
                    'deferred_revenue': [],              # 預收收入/遞延收入
                    'other_advances': [],                # 其他預收款
                    'temporary_receipts': [],            # 暫收款
                    'owners_current_account': [],        # 業主(股東)往來
                    'output_tax': [],                    # 銷項稅額
                    'advances_received': [],             # 預收款項
                    'tax_payable': [],                   # 應納稅額
                    'other_current': [],                 # 其他流動負債
                    'total': 0
                },
                'non_current_liabilities': {
                    'long_term_debt': [],        # 長期借款
                    'other_non_current': [],     # 其他非流動負債
                    'total': 0
                },
                'total_liabilities': 0
            },
            'equity': {
                'capital': [],               # 股本
                'retained_earnings': [],     # 保留盈餘
                'other_equity': [],          # 其他權益
                'total_equity': 0
            },
            'report_date': None
        }
        
        # 處理所有科目，包括餘額為零的科目
        for subject_code, subject in subject_map.items():
            # 取得科目餘額，如果沒有交易記錄則為0
            balance = subject_balances.get(subject_code, 0)

            subject_data = {
                'code': subject_code,
                'name': subject.name,
                'balance': abs(balance),  # 顯示絕對值
                'original_balance': balance,
                'percentage': 0  # 百分比將在後面計算
            }

            # 詳細分類科目
            BalanceSheetService._classify_subject_detailed(subject_code, subject_data, balance_sheet)
        
        # 計算各項小計和總計
        BalanceSheetService._calculate_detailed_totals(balance_sheet)

        # 計算百分比
        BalanceSheetService._calculate_percentages(balance_sheet)

        # 排序科目（按代碼排序）
        BalanceSheetService._sort_detailed_balance_sheet(balance_sheet)

        return balance_sheet

    @staticmethod
    def _classify_subject_detailed(subject_code: str, subject_data: Dict, balance_sheet: Dict):
        """詳細分類科目到對應的子分類"""

        # 流動資產詳細分類
        if subject_code.startswith('1100'):  # 現金
            balance_sheet['assets']['current_assets']['cash'].append(subject_data)
        elif subject_code.startswith('1110'):  # 零用金/週轉金
            balance_sheet['assets']['current_assets']['petty_cash'].append(subject_data)
        elif subject_code.startswith('1120'):  # 銀行存款
            balance_sheet['assets']['current_assets']['bank_deposits'].append(subject_data)
        elif subject_code.startswith('1130'):  # 應收票據
            balance_sheet['assets']['current_assets']['notes_receivable'].append(subject_data)
        elif subject_code.startswith('1140'):  # 應收帳款
            balance_sheet['assets']['current_assets']['accounts_receivable'].append(subject_data)
        elif subject_code.startswith('1150'):  # 其他應收款
            balance_sheet['assets']['current_assets']['other_receivables'].append(subject_data)
        elif subject_code.startswith('1160'):  # 暫估應收帳款
            balance_sheet['assets']['current_assets']['estimated_accounts_receivable'].append(subject_data)
        elif subject_code.startswith('1170'):  # 透過損益按公允價值衡量之金融資產
            balance_sheet['assets']['current_assets']['financial_assets_fvpl'].append(subject_data)
        elif subject_code.startswith('1180'):  # 透過其他綜合損益按公允價值衡量之金融資產
            balance_sheet['assets']['current_assets']['financial_assets_fvoci'].append(subject_data)
        elif subject_code.startswith('1190'):  # 以成本衡量之金融資產
            balance_sheet['assets']['current_assets']['financial_assets_cost'].append(subject_data)
        elif subject_code.startswith('1200'):  # 商品存貨
            balance_sheet['assets']['current_assets']['merchandise_inventory'].append(subject_data)
        elif subject_code.startswith('1210'):  # 原料存貨
            balance_sheet['assets']['current_assets']['raw_material_inventory'].append(subject_data)
        elif subject_code.startswith('1220'):  # 製成品
            balance_sheet['assets']['current_assets']['finished_goods'].append(subject_data)
        elif subject_code.startswith('1230'):  # 預付費用
            balance_sheet['assets']['current_assets']['prepaid_expenses'].append(subject_data)
        elif subject_code.startswith('1240'):  # 預付貨款
            balance_sheet['assets']['current_assets']['prepaid_purchases'].append(subject_data)
        elif subject_code.startswith('1250'):  # 其他預付款
            balance_sheet['assets']['current_assets']['other_prepayments'].append(subject_data)
        elif subject_code.startswith('1260'):  # 暫付款
            balance_sheet['assets']['current_assets']['temporary_payments'].append(subject_data)
        elif subject_code.startswith('1270'):  # 預付稅款
            balance_sheet['assets']['current_assets']['prepaid_taxes'].append(subject_data)
        elif subject_code.startswith('1290'):  # 進項稅額
            balance_sheet['assets']['current_assets']['input_tax'].append(subject_data)
        elif subject_code.startswith('1295'):  # 留抵稅額
            balance_sheet['assets']['current_assets']['tax_credits'].append(subject_data)
        elif subject_code.startswith('11') or subject_code.startswith('12'):  # 其他流動資產
            balance_sheet['assets']['current_assets']['other_current'].append(subject_data)

        # 非流動資產詳細分類
        elif subject_code.startswith('12') or subject_code.startswith('13'):  # 不動產、廠房及設備
            balance_sheet['assets']['non_current_assets']['property_plant_equipment'].append(subject_data)
        elif subject_code.startswith('14'):  # 無形資產
            balance_sheet['assets']['non_current_assets']['intangible_assets'].append(subject_data)
        elif subject_code.startswith('15') or subject_code.startswith('16'):  # 投資
            balance_sheet['assets']['non_current_assets']['investments'].append(subject_data)
        elif subject_code.startswith(('17', '18', '19')):  # 其他非流動資產
            balance_sheet['assets']['non_current_assets']['other_non_current'].append(subject_data)

        # 流動負債詳細分類
        elif subject_code.startswith('2115'):  # 銀行借款
            balance_sheet['liabilities']['current_liabilities']['bank_loans'].append(subject_data)
        elif subject_code.startswith('2120'):  # 應付票據
            balance_sheet['liabilities']['current_liabilities']['notes_payable'].append(subject_data)
        elif subject_code.startswith('2130'):  # 應付帳款
            balance_sheet['liabilities']['current_liabilities']['accounts_payable'].append(subject_data)
        elif subject_code.startswith('2144'):  # 暫估應付帳款
            balance_sheet['liabilities']['current_liabilities']['estimated_accounts_payable'].append(subject_data)
        elif subject_code.startswith('2145'):  # 應付費用
            balance_sheet['liabilities']['current_liabilities']['accrued_expenses'].append(subject_data)
        elif subject_code.startswith('2150'):  # 應付稅捐
            balance_sheet['liabilities']['current_liabilities']['taxes_payable'].append(subject_data)
        elif subject_code.startswith('2155'):  # 應付股利
            balance_sheet['liabilities']['current_liabilities']['benefits_payable'].append(subject_data)
        elif subject_code.startswith('2160'):  # 其他應付款
            balance_sheet['liabilities']['current_liabilities']['other_payables'].append(subject_data)
        elif subject_code.startswith('2165'):  # 預收貨款
            balance_sheet['liabilities']['current_liabilities']['advances_from_customers'].append(subject_data)
        elif subject_code.startswith('2167'):  # 預收收入/遞延收入
            balance_sheet['liabilities']['current_liabilities']['deferred_revenue'].append(subject_data)
        elif subject_code.startswith('2170'):  # 其他預收款
            balance_sheet['liabilities']['current_liabilities']['other_advances'].append(subject_data)
        elif subject_code.startswith('2205'):  # 暫收款
            balance_sheet['liabilities']['current_liabilities']['temporary_receipts'].append(subject_data)
        elif subject_code.startswith('2210'):  # 業主(股東)往來
            balance_sheet['liabilities']['current_liabilities']['owners_current_account'].append(subject_data)
        elif subject_code.startswith('2290'):  # 銷項稅額
            balance_sheet['liabilities']['current_liabilities']['output_tax'].append(subject_data)
        elif subject_code.startswith('2295'):  # 應納稅額
            balance_sheet['liabilities']['current_liabilities']['tax_payable'].append(subject_data)
        elif subject_code.startswith('21') or subject_code.startswith('22'):  # 其他流動負債
            balance_sheet['liabilities']['current_liabilities']['other_current'].append(subject_data)

        # 非流動負債詳細分類
        elif subject_code.startswith('2420') or subject_code.startswith('23'):  # 長期借款
            balance_sheet['liabilities']['non_current_liabilities']['long_term_debt'].append(subject_data)
        elif subject_code.startswith(('24', '25', '26', '27', '28', '29')):  # 其他非流動負債
            balance_sheet['liabilities']['non_current_liabilities']['other_non_current'].append(subject_data)

        # 權益詳細分類
        elif subject_code.startswith('31') or subject_code.startswith('32'):  # 股本
            balance_sheet['equity']['capital'].append(subject_data)
        elif subject_code.startswith('33') or subject_code.startswith('34'):  # 保留盈餘
            balance_sheet['equity']['retained_earnings'].append(subject_data)
        elif subject_code.startswith(('35', '36', '37', '38', '39')):  # 其他權益
            balance_sheet['equity']['other_equity'].append(subject_data)

    @staticmethod
    def _calculate_detailed_totals(balance_sheet: Dict):
        """計算詳細分類的小計和總計"""
        # 計算流動資產小計
        current_assets = balance_sheet['assets']['current_assets']
        current_assets['total'] = (
            sum(item['balance'] for item in current_assets['cash']) +
            sum(item['balance'] for item in current_assets['petty_cash']) +
            sum(item['balance'] for item in current_assets['bank_deposits']) +
            sum(item['balance'] for item in current_assets['notes_receivable']) +
            sum(item['balance'] for item in current_assets['accounts_receivable']) +
            sum(item['balance'] for item in current_assets['other_receivables']) +
            sum(item['balance'] for item in current_assets['estimated_accounts_receivable']) +
            sum(item['balance'] for item in current_assets['financial_assets_fvpl']) +
            sum(item['balance'] for item in current_assets['financial_assets_fvoci']) +
            sum(item['balance'] for item in current_assets['financial_assets_cost']) +
            sum(item['balance'] for item in current_assets['merchandise_inventory']) +
            sum(item['balance'] for item in current_assets['raw_material_inventory']) +
            sum(item['balance'] for item in current_assets['finished_goods']) +
            sum(item['balance'] for item in current_assets['prepaid_expenses']) +
            sum(item['balance'] for item in current_assets['prepaid_purchases']) +
            sum(item['balance'] for item in current_assets['other_prepayments']) +
            sum(item['balance'] for item in current_assets['temporary_payments']) +
            sum(item['balance'] for item in current_assets['prepaid_taxes']) +
            sum(item['balance'] for item in current_assets['input_tax']) +
            sum(item['balance'] for item in current_assets['tax_credits']) +
            sum(item['balance'] for item in current_assets['other_current'])
        )

        # 計算非流動資產小計
        non_current_assets = balance_sheet['assets']['non_current_assets']
        non_current_assets['total'] = (
            sum(item['balance'] for item in non_current_assets['property_plant_equipment']) +
            sum(item['balance'] for item in non_current_assets['intangible_assets']) +
            sum(item['balance'] for item in non_current_assets['investments']) +
            sum(item['balance'] for item in non_current_assets['other_non_current'])
        )

        # 計算資產總計
        balance_sheet['assets']['total_assets'] = (
            current_assets['total'] + non_current_assets['total']
        )

        # 計算流動負債小計
        current_liabilities = balance_sheet['liabilities']['current_liabilities']
        current_liabilities['total'] = (
            sum(item['balance'] for item in current_liabilities['bank_loans']) +
            sum(item['balance'] for item in current_liabilities['notes_payable']) +
            sum(item['balance'] for item in current_liabilities['accounts_payable']) +
            sum(item['balance'] for item in current_liabilities['estimated_accounts_payable']) +
            sum(item['balance'] for item in current_liabilities['accrued_expenses']) +
            sum(item['balance'] for item in current_liabilities['taxes_payable']) +
            sum(item['balance'] for item in current_liabilities['benefits_payable']) +
            sum(item['balance'] for item in current_liabilities['other_payables']) +
            sum(item['balance'] for item in current_liabilities['advances_from_customers']) +
            sum(item['balance'] for item in current_liabilities['deferred_revenue']) +
            sum(item['balance'] for item in current_liabilities['other_advances']) +
            sum(item['balance'] for item in current_liabilities['temporary_receipts']) +
            sum(item['balance'] for item in current_liabilities['owners_current_account']) +
            sum(item['balance'] for item in current_liabilities['output_tax']) +
            sum(item['balance'] for item in current_liabilities['advances_received']) +
            sum(item['balance'] for item in current_liabilities['tax_payable']) +
            sum(item['balance'] for item in current_liabilities['other_current'])
        )

        # 計算非流動負債小計
        non_current_liabilities = balance_sheet['liabilities']['non_current_liabilities']
        non_current_liabilities['total'] = (
            sum(item['balance'] for item in non_current_liabilities['long_term_debt']) +
            sum(item['balance'] for item in non_current_liabilities['other_non_current'])
        )

        # 計算負債總計
        balance_sheet['liabilities']['total_liabilities'] = (
            current_liabilities['total'] + non_current_liabilities['total']
        )

        # 計算權益總計
        equity = balance_sheet['equity']
        equity['total_equity'] = (
            sum(item['balance'] for item in equity['capital']) +
            sum(item['balance'] for item in equity['retained_earnings']) +
            sum(item['balance'] for item in equity['other_equity'])
        )
        
        # 加入本期損益到權益總計
        # 注意：這裡需要從外部傳入本期損益，我們先設為0
        # 實際的本期損益會在 generate_balance_sheet 中處理

    @staticmethod
    def _calculate_percentages(balance_sheet: Dict):
        """計算各項目佔總資產的百分比"""
        total_assets = balance_sheet['assets']['total_assets']

        if total_assets == 0:
            return  # 避免除零錯誤

        # 計算流動資產各項目百分比
        current_assets = balance_sheet['assets']['current_assets']
        for category in ['cash', 'petty_cash', 'bank_deposits', 'notes_receivable', 'accounts_receivable', 
                        'other_receivables', 'estimated_accounts_receivable', 'financial_assets_fvpl', 
                        'financial_assets_fvoci', 'financial_assets_cost', 'merchandise_inventory', 
                        'raw_material_inventory', 'finished_goods', 'prepaid_expenses', 'prepaid_purchases', 
                        'other_prepayments', 'temporary_payments', 'prepaid_taxes', 'input_tax', 
                        'tax_credits', 'other_current']:
            for item in current_assets[category]:
                item['percentage'] = round((item['balance'] / total_assets) * 100, 2)

        # 計算非流動資產各項目百分比
        non_current_assets = balance_sheet['assets']['non_current_assets']
        for category in ['property_plant_equipment', 'intangible_assets', 'investments', 'other_non_current']:
            for item in non_current_assets[category]:
                item['percentage'] = round((item['balance'] / total_assets) * 100, 2)

        # 計算流動負債各項目百分比
        current_liabilities = balance_sheet['liabilities']['current_liabilities']
        for category in ['bank_loans', 'notes_payable', 'accounts_payable', 'estimated_accounts_payable', 
                        'accrued_expenses', 'taxes_payable', 'benefits_payable', 'other_payables',
                        'advances_from_customers', 'deferred_revenue', 'other_advances', 'temporary_receipts',
                        'owners_current_account', 'output_tax', 'advances_received', 'tax_payable', 'other_current']:
            for item in current_liabilities[category]:
                item['percentage'] = round((item['balance'] / total_assets) * 100, 2)

        # 計算非流動負債各項目百分比
        non_current_liabilities = balance_sheet['liabilities']['non_current_liabilities']
        for category in ['long_term_debt', 'other_non_current']:
            for item in non_current_liabilities[category]:
                item['percentage'] = round((item['balance'] / total_assets) * 100, 2)

        # 計算權益各項目百分比
        equity = balance_sheet['equity']
        for category in ['capital', 'retained_earnings', 'other_equity']:
            for item in equity[category]:
                item['percentage'] = round((item['balance'] / total_assets) * 100, 2)

    @staticmethod
    def _sort_detailed_balance_sheet(balance_sheet: Dict):
        """排序詳細分類的科目"""
        # 排序流動資產
        current_assets = balance_sheet['assets']['current_assets']
        for category in ['cash', 'petty_cash', 'bank_deposits', 'notes_receivable', 'accounts_receivable', 
                        'other_receivables', 'estimated_accounts_receivable', 'financial_assets_fvpl', 
                        'financial_assets_fvoci', 'financial_assets_cost', 'merchandise_inventory', 
                        'raw_material_inventory', 'finished_goods', 'prepaid_expenses', 'prepaid_purchases', 
                        'other_prepayments', 'temporary_payments', 'prepaid_taxes', 'input_tax', 
                        'tax_credits', 'other_current']:
            current_assets[category].sort(key=lambda x: x['code'])

        # 排序非流動資產
        non_current_assets = balance_sheet['assets']['non_current_assets']
        for category in ['property_plant_equipment', 'intangible_assets', 'investments', 'other_non_current']:
            non_current_assets[category].sort(key=lambda x: x['code'])

        # 排序流動負債
        current_liabilities = balance_sheet['liabilities']['current_liabilities']
        for category in ['bank_loans', 'notes_payable', 'accounts_payable', 'estimated_accounts_payable', 
                        'accrued_expenses', 'taxes_payable', 'benefits_payable', 'other_payables',
                        'advances_from_customers', 'deferred_revenue', 'other_advances', 'temporary_receipts',
                        'owners_current_account', 'output_tax', 'advances_received', 'tax_payable', 'other_current']:
            current_liabilities[category].sort(key=lambda x: x['code'])

        # 排序非流動負債
        non_current_liabilities = balance_sheet['liabilities']['non_current_liabilities']
        for category in ['long_term_debt', 'other_non_current']:
            non_current_liabilities[category].sort(key=lambda x: x['code'])

        # 排序權益
        equity = balance_sheet['equity']
        for category in ['capital', 'retained_earnings', 'other_equity']:
            equity[category].sort(key=lambda x: x['code'])
