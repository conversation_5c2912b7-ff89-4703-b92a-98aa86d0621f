"""
交易服務 - 使用新的 Transaction 和 JournalEntry 模型
"""
from typing import List, Dict, Optional, Tuple
from sqlalchemy.orm import Session
from datetime import datetime, date
import uuid

from model import Transaction, JournalEntry, Account, AccountSubject
from services.new_journal_validator import NewJournalValidator, JournalValidationError


class TransactionService:
    """交易服務類"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.validator = NewJournalValidator(db_session)
    
    def create_expense_transaction(self, data: Dict) -> Transaction:
        """
        創建支出交易（自動產生進項稅額分錄）
        """
        # 創建交易主記錄
        transaction = Transaction(
            transaction_date=data['transaction_date'],
            description=data['description'],
            total_amount=data['amount'],
            tax_amount=data.get('tax_amount', 0),
            extra_fee=data.get('extra_fee', 0),
            transaction_type='expense',  # 設置交易類型為支出
            account_id=data['account_id'],
            payment_identity_id=data.get('payment_identity_id'),
            department_id=data.get('department_id'),
            project_id=data.get('project_id'),
            is_paper=data.get('is_paper', False),
            invoice_number=data.get('invoice_number'),
            tax_type=data.get('tax_type'),
            buyer_tax_id=data.get('buyer_tax_id'),
            seller_tax_id=data.get('seller_tax_id'),
            invoice_date=data.get('invoice_date'),
            is_paid=data.get('is_paid', False),
            should_paid_date=data.get('should_paid_date'),
            paid_date=data.get('paid_date'),
            note=data.get('note'),
            tags=data.get('tags'),
            image_path=data.get('image_path'),
            created_by=data.get('created_by')
        )
        
        self.db.add(transaction)
        self.db.flush()  # 獲取 transaction.id
        
        entries = []
        # 借方：費用科目（未稅金額）- 主要分錄
        debit_entry = JournalEntry(
            transaction_id=transaction.id,
            subject_code=data['expense_subject_code'],
            debit_amount=data['amount'] - data.get('tax_amount', 0),
            credit_amount=0,
            description=data['description'],
            entry_type='primary'  # 標記為主要分錄
        )
        entries.append(debit_entry)
        self.db.add(debit_entry)
        # 借方：進項稅額（1290）- 平衡分錄
        tax_amount = data.get('tax_amount', 0)
        if tax_amount > 0:
            input_tax_entry = JournalEntry(
                transaction_id=transaction.id,
                subject_code='1290',
                debit_amount=tax_amount,
                credit_amount=0,
                description=f"進項稅額",
                entry_type='balance'  # 標記為平衡分錄
            )
            entries.append(input_tax_entry)
            self.db.add(input_tax_entry)
        # 貸方：銀行存款（含稅金額）
        if data.get('account_id'):
            account = self.db.query(Account).filter_by(id=data['account_id']).first()
            if account and account.subject_code:
                if account.category == '銀行帳戶':
                    full_subject_code = f'1110{str(account.subject_code).zfill(3)}'
                elif account.category == '現金':
                    full_subject_code = f'1105{str(account.subject_code).zfill(3)}'
                else:
                    full_subject_code = account.subject_code
                credit_entry = JournalEntry(
                    transaction_id=transaction.id,
                    subject_code=full_subject_code,
                    debit_amount=0,
                    credit_amount=data['amount'],
                    description=f"{data['description']} - 付款",
                    entry_type='balance'  # 標記為平衡分錄
                )
                entries.append(credit_entry)
                self.db.add(credit_entry)
        self.validator.validate_before_commit(entries)
        return transaction

    def create_income_transaction(self, data: Dict) -> Transaction:
        """
        創建收入交易（自動產生銷項稅額分錄）
        """
        transaction = Transaction(
            transaction_date=data['transaction_date'],
            description=data['description'],
            total_amount=data['amount'],
            tax_amount=data.get('tax_amount', 0),
            extra_fee=data.get('extra_fee', 0),
            transaction_type='income',  # 設置交易類型為收入
            account_id=data['account_id'],
            payment_identity_id=data.get('payment_identity_id'),
            department_id=data.get('department_id'),
            project_id=data.get('project_id'),
            is_paper=data.get('is_paper', False),
            invoice_number=data.get('invoice_number'),
            tax_type=data.get('tax_type'),
            buyer_tax_id=data.get('buyer_tax_id'),
            seller_tax_id=data.get('seller_tax_id'),
            invoice_date=data.get('invoice_date'),
            is_paid=data.get('is_paid', False),
            should_paid_date=data.get('should_paid_date'),
            paid_date=data.get('paid_date'),
            note=data.get('note'),
            tags=data.get('tags'),
            image_path=data.get('image_path'),
            created_by=data.get('created_by')
        )
        self.db.add(transaction)
        self.db.flush()
        entries = []
        # 貸方：收入科目（未稅金額）- 主要分錄
        credit_entry = JournalEntry(
            transaction_id=transaction.id,
            subject_code=data['income_subject_code'],
            debit_amount=0,
            credit_amount=data['amount'] - data.get('tax_amount', 0),
            description=data['description'],
            entry_type='primary'  # 標記為主要分錄
        )
        entries.append(credit_entry)
        self.db.add(credit_entry)
        # 貸方：銷項稅額（2290）- 平衡分錄
        tax_amount = data.get('tax_amount', 0)
        if tax_amount > 0:
            output_tax_entry = JournalEntry(
                transaction_id=transaction.id,
                subject_code='2290',
                debit_amount=0,
                credit_amount=tax_amount,
                description=f"銷項稅額",
                entry_type='balance'  # 標記為平衡分錄
            )
            entries.append(output_tax_entry)
            self.db.add(output_tax_entry)
        # 借方：現金/銀行存款（含稅金額）
        if data.get('account_id'):
            account = self.db.query(Account).filter_by(id=data['account_id']).first()
            if account and account.subject_code:
                if account.category == '銀行帳戶':
                    full_subject_code = f'1110{str(account.subject_code).zfill(3)}'
                elif account.category == '現金':
                    full_subject_code = f'1105{str(account.subject_code).zfill(3)}'
                else:
                    full_subject_code = account.subject_code
                debit_entry = JournalEntry(
                    transaction_id=transaction.id,
                    subject_code=full_subject_code,
                    debit_amount=data['amount'],
                    credit_amount=0,
                    description=f"{data['description']} - 收款",
                    entry_type='balance'  # 標記為平衡分錄
                )
                entries.append(debit_entry)
                self.db.add(debit_entry)
        self.validator.validate_before_commit(entries)
        return transaction
    
    def get_transaction_with_entries(self, transaction_id: int) -> Optional[Transaction]:
        """獲取交易及其分錄"""
        return self.db.query(Transaction).filter_by(id=transaction_id).first()
    
    def get_transactions_by_date_range(self, start_date: date, end_date: date, transaction_type: str = None) -> List[Transaction]:
        """
        按日期範圍獲取交易
        
        Args:
            start_date: 開始日期
            end_date: 結束日期
            transaction_type: 交易類型（收入/支出/轉帳）
        """
        query = self.db.query(Transaction).filter(
            Transaction.transaction_date.between(start_date, end_date)
        )
        
        if transaction_type:
            query = query.filter(Transaction.transaction_type == transaction_type)
            
        return query.order_by(Transaction.transaction_date.desc()).all()
        
    def get_primary_entries_by_transaction(self, transaction_id: int) -> List[JournalEntry]:
        """
        獲取交易的主要分錄
        
        Args:
            transaction_id: 交易ID
        """
        return self.db.query(JournalEntry).filter(
            JournalEntry.transaction_id == transaction_id,
            JournalEntry.entry_type == 'primary'
        ).all()
    
    def get_journal_entries_by_subject(self, subject_code: str, start_date: date = None, end_date: date = None, only_primary: bool = False) -> List[JournalEntry]:
        """
        按科目獲取分錄
        
        Args:
            subject_code: 科目代碼
            start_date: 開始日期
            end_date: 結束日期
            only_primary: 是否只獲取主要分錄
        """
        query = self.db.query(JournalEntry).filter_by(subject_code=subject_code)
        
        if only_primary:
            query = query.filter_by(entry_type='primary')
            
        if start_date and end_date:
            query = query.join(Transaction).filter(
                Transaction.transaction_date.between(start_date, end_date)
            )
        
        return query.order_by(JournalEntry.created_at.desc()).all()
    
    def calculate_subject_balance(self, subject_code: str, as_of_date: date = None) -> Dict[str, int]:
        """計算科目餘額"""
        query = self.db.query(JournalEntry).filter_by(subject_code=subject_code)
        
        if as_of_date:
            query = query.join(Transaction).filter(
                Transaction.transaction_date <= as_of_date
            )
        
        entries = query.all()
        
        total_debit = sum(entry.debit_amount or 0 for entry in entries)
        total_credit = sum(entry.credit_amount or 0 for entry in entries)
        
        return {
            'subject_code': subject_code,
            'total_debit': total_debit,
            'total_credit': total_credit,
            'balance': total_debit - total_credit,
            'entry_count': len(entries)
        }
    
    def delete_transaction(self, transaction_id: int) -> bool:
        """刪除交易（級聯刪除分錄）"""
        transaction = self.db.query(Transaction).filter_by(id=transaction_id).first()
        if transaction:
            self.db.delete(transaction)
            return True
        return False
