"""
新的會計分錄驗證器 - 使用 Transaction 和 JournalEntry 模型
"""
from typing import List, Dict, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func

from model import Transaction, JournalEntry, AccountSubject


class JournalValidationError(Exception):
    """分錄驗證錯誤"""
    pass


class NewJournalValidator:
    """新的會計分錄驗證器"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def validate_transaction_entries(self, transaction_id: int) -> Dict[str, any]:
        """
        驗證指定交易的所有分錄
        
        Args:
            transaction_id: 交易ID
            
        Returns:
            驗證結果字典
            
        Raises:
            JournalValidationError: 驗證失敗時拋出
        """
        # 獲取交易和分錄
        transaction = self.db.query(Transaction).filter_by(id=transaction_id).first()
        if not transaction:
            raise JournalValidationError(f"找不到交易ID: {transaction_id}")
        
        entries = self.db.query(JournalEntry).filter_by(transaction_id=transaction_id).all()
        if not entries:
            raise JournalValidationError(f"交易 {transaction_id} 沒有分錄")
        
        # 執行各項驗證
        result = {
            'transaction_id': transaction_id,
            'entry_count': len(entries),
            'debit_total': 0,
            'credit_total': 0,
            'is_balanced': False,
            'errors': [],
            'warnings': []
        }
        
        # 計算借貸總額
        for entry in entries:
            result['debit_total'] += entry.debit_amount or 0
            result['credit_total'] += entry.credit_amount or 0
        
        # 檢查借貸平衡
        balance_diff = abs(result['debit_total'] - result['credit_total'])
        result['is_balanced'] = balance_diff < 1  # 允許1元誤差
        
        if not result['is_balanced']:
            result['errors'].append(f"借貸不平衡：借方 {result['debit_total']}，貸方 {result['credit_total']}")
        
        # 檢查分錄數量
        if len(entries) < 2:
            result['errors'].append("分錄數量不足，至少需要2筆分錄")
        
        # 檢查科目代碼
        for entry in entries:
            if not self._validate_subject_code(entry.subject_code):
                result['errors'].append(f"無效的科目代碼: {entry.subject_code}")
        
        # 檢查金額
        for entry in entries:
            debit = entry.debit_amount or 0
            credit = entry.credit_amount or 0
            
            if debit == 0 and credit == 0:
                result['errors'].append(f"分錄金額不能為零: {entry.subject_code}")
            elif debit > 0 and credit > 0:
                result['errors'].append(f"分錄不能同時有借方和貸方金額: {entry.subject_code}")
        
        return result
    
    def validate_before_commit(self, entries: List[JournalEntry]) -> None:
        """
        提交前驗證分錄列表
        
        Args:
            entries: 分錄列表
            
        Raises:
            JournalValidationError: 驗證失敗時拋出
        """
        if not entries:
            raise JournalValidationError("沒有分錄需要驗證")
        
        # 按 transaction_id 分組
        groups = {}
        for entry in entries:
            tid = entry.transaction_id
            if tid not in groups:
                groups[tid] = []
            groups[tid].append(entry)
        
        # 驗證每個分組
        for transaction_id, group_entries in groups.items():
            debit_total = sum(e.debit_amount or 0 for e in group_entries)
            credit_total = sum(e.credit_amount or 0 for e in group_entries)
            
            if abs(debit_total - credit_total) >= 1:  # 允許1元誤差
                raise JournalValidationError(
                    f"交易 {transaction_id} 借貸不平衡：借方 {debit_total}，貸方 {credit_total}"
                )
            
            # 檢查分錄數量
            if len(group_entries) < 2:
                raise JournalValidationError(f"交易 {transaction_id} 分錄數量不足")
            
            # 檢查科目代碼
            for entry in group_entries:
                if not self._validate_subject_code(entry.subject_code):
                    raise JournalValidationError(f"無效的科目代碼: {entry.subject_code}")
    
    def _validate_subject_code(self, subject_code: str) -> bool:
        """驗證科目代碼是否存在"""
        subject = self.db.query(AccountSubject).filter(
            AccountSubject.code == subject_code
        ).first()
        
        return subject is not None
    
    def get_transaction_summary(self, transaction_id: int) -> Optional[Dict[str, any]]:
        """
        獲取交易摘要資訊
        
        Args:
            transaction_id: 交易ID
            
        Returns:
            交易摘要字典
        """
        transaction = self.db.query(Transaction).filter_by(id=transaction_id).first()
        if not transaction:
            return None
        
        entries = self.db.query(JournalEntry).filter_by(transaction_id=transaction_id).all()
        if not entries:
            return None
        
        debit_entries = [e for e in entries if (e.debit_amount or 0) > 0]
        credit_entries = [e for e in entries if (e.credit_amount or 0) > 0]
        
        return {
            'transaction_id': transaction_id,
            'transaction_date': transaction.transaction_date,
            'description': transaction.description,
            'total_amount': transaction.total_amount,
            'debit_entries': [
                {
                    'subject_code': e.subject_code,
                    'subject_name': self._get_subject_name(e.subject_code),
                    'amount': e.debit_amount,
                    'description': e.description
                }
                for e in debit_entries
            ],
            'credit_entries': [
                {
                    'subject_code': e.subject_code,
                    'subject_name': self._get_subject_name(e.subject_code),
                    'amount': e.credit_amount,
                    'description': e.description
                }
                for e in credit_entries
            ]
        }
    
    def _get_subject_name(self, subject_code: str) -> str:
        """獲取科目名稱"""
        subject = self.db.query(AccountSubject).filter(
            AccountSubject.code == subject_code
        ).first()
        return subject.name if subject else subject_code
    
    def validate_all_transactions(self) -> List[Dict[str, any]]:
        """
        驗證所有交易的分錄
        
        Returns:
            所有交易的驗證結果列表
        """
        transactions = self.db.query(Transaction).all()
        results = []
        
        for transaction in transactions:
            try:
                result = self.validate_transaction_entries(transaction.id)
                results.append(result)
            except JournalValidationError as e:
                results.append({
                    'transaction_id': transaction.id,
                    'entry_count': 0,
                    'debit_total': 0,
                    'credit_total': 0,
                    'is_balanced': False,
                    'errors': [str(e)],
                    'warnings': []
                })
        
        return results
    
    def get_validation_statistics(self) -> Dict[str, any]:
        """
        獲取驗證統計資訊
        
        Returns:
            統計資訊字典
        """
        results = self.validate_all_transactions()
        
        total_transactions = len(results)
        balanced_transactions = sum(1 for r in results if r['is_balanced'])
        unbalanced_transactions = total_transactions - balanced_transactions
        
        balance_rate = (balanced_transactions / total_transactions * 100) if total_transactions > 0 else 0
        
        return {
            'total_transactions': total_transactions,
            'balanced_transactions': balanced_transactions,
            'unbalanced_transactions': unbalanced_transactions,
            'balance_rate': balance_rate
        }


def validate_transaction_balance(db_session: Session, transaction_id: int) -> bool:
    """
    快速驗證交易平衡的便利函數
    
    Args:
        db_session: 資料庫會話
        transaction_id: 交易ID
        
    Returns:
        是否平衡
    """
    validator = NewJournalValidator(db_session)
    try:
        result = validator.validate_transaction_entries(transaction_id)
        return result['is_balanced'] and not result['errors']
    except JournalValidationError:
        return False
