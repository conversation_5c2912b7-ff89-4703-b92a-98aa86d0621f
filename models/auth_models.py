from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, DateTime, Table
from sqlalchemy.orm import relationship
from model import Base, get_taiwan_time

# 用戶角色關聯表（多對多）
user_roles = Table('user_roles',
    Base.metadata,
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True)
)

# 角色權限關聯表（多對多）
role_permissions = Table('role_permissions',
    Base.metadata,
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', Integer, ForeignKey('permissions.id'), primary_key=True)
)

class Role(Base):
    """角色資料表"""
    __tablename__ = 'roles'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False, comment='角色名稱')
    display_name = Column(String(100), nullable=False, comment='顯示名稱')
    description = Column(Text, comment='角色描述')
    is_active = Column(Boolean, default=True, comment='是否啟用')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)
    
    # 關聯
    permissions = relationship('Permission', secondary=role_permissions, back_populates='roles')

class Permission(Base):
    """權限資料表"""
    __tablename__ = 'permissions'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False, comment='權限名稱')
    display_name = Column(String(100), nullable=False, comment='顯示名稱')
    module = Column(String(50), nullable=False, comment='所屬模組')
    action = Column(String(50), nullable=False, comment='操作類型')  # view, create, edit, delete
    description = Column(Text, comment='權限描述')
    
    # 關聯
    roles = relationship('Role', secondary=role_permissions, back_populates='permissions')

class UserSession(Base):
    """用戶會話資料表"""
    __tablename__ = 'user_sessions'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    session_token = Column(String(255), unique=True, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time)
    last_accessed = Column(DateTime, default=get_taiwan_time)
    
    # 關聯
    user = relationship('User', backref='sessions')