"""
新的資料模型：重構後的交易和會計分錄表
"""
from sqlalchemy import Column, Integer, String, Date, DateTime, Boolean, Text, ForeignKey, CheckConstraint, Index
from sqlalchemy.orm import relationship
from model import Base, get_taiwan_time


class Transaction(Base):
    """交易主表 - 存儲交易的基本資訊"""
    __tablename__ = 'transactions'
    
    id = Column(Integer, primary_key=True)
    transaction_date = Column(Date, nullable=False, comment='交易日期', index=True)
    description = Column(Text, nullable=False, comment='交易描述')
    total_amount = Column(Integer, nullable=False, default=0, comment='總金額')
    tax_amount = Column(Integer, default=0, comment='稅額')
    extra_fee = Column(Integer, default=0, comment='手續費')
    
    # 關聯資訊
    account_id = Column(Integer, ForeignKey('account.id'), comment='資金帳戶', index=True)
    payment_identity_id = Column(Integer, ForeignKey('payment_identity.id'), comment='收支對象', index=True)
    department_id = Column(Integer, ForeignKey('department.id'), comment='部門', index=True)
    project_id = Column(Integer, ForeignKey('project.id'), comment='專案', index=True)
    
    # 發票相關
    is_paper = Column(Boolean, default=False, comment='是否為收據類憑證')
    invoice_number = Column(String(50), comment='發票號碼', index=True)
    tax_type = Column(String(50), comment='稅別')
    buyer_tax_id = Column(String(50), comment='買方統編')
    seller_tax_id = Column(String(50), comment='賣方統編')
    invoice_date = Column(String(50), comment='發票日期')
    
    # 付款狀態
    is_paid = Column(Boolean, default=False, comment='是否已收付款', index=True)
    should_paid_date = Column(DateTime, comment='應收付款日期', index=True)
    paid_date = Column(DateTime, comment='實收付款日期', index=True)
    
    # 其他資訊
    note = Column(Text, comment='備註')
    tags = Column(Text, comment='標籤')
    image_path = Column(String(255), comment='附件路徑')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)
    created_by = Column(Integer, comment='建立者')
    updated_by = Column(Integer, comment='修改者')
    
    # 關聯
    account = relationship("Account", back_populates="transactions")
    payment_identity = relationship("PaymentIdentity", back_populates="transactions")
    department = relationship("Department", back_populates="transactions")
    project = relationship("Project", back_populates="transactions")
    journal_entries = relationship("JournalEntry", back_populates="transaction", cascade="all, delete-orphan")


class JournalEntry(Base):
    """會計分錄表 - 存儲每筆交易的借貸分錄"""
    __tablename__ = 'journal_entries'
    
    id = Column(Integer, primary_key=True)
    transaction_id = Column(Integer, ForeignKey('transactions.id'), nullable=False, comment='交易ID', index=True)
    subject_code = Column(String(50), ForeignKey('account_subject.code'), nullable=False, comment='會計科目代碼', index=True)
    debit_amount = Column(Integer, default=0, comment='借方金額')
    credit_amount = Column(Integer, default=0, comment='貸方金額')
    description = Column(Text, comment='分錄說明')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    
    # 檢查約束：借方和貸方金額不能同時為0，也不能同時有值
    __table_args__ = (
        CheckConstraint(
            '(debit_amount > 0 AND credit_amount = 0) OR (debit_amount = 0 AND credit_amount > 0)',
            name='check_debit_credit_exclusive'
        ),
    )
    
    # 關聯
    transaction = relationship("Transaction", back_populates="journal_entries")
    account_subject = relationship("AccountSubject", back_populates="journal_entries")
    
    @property
    def entry_side(self):
        """返回分錄方向"""
        return 'DEBIT' if self.debit_amount > 0 else 'CREDIT'
    
    @property
    def amount(self):
        """返回分錄金額"""
        return self.debit_amount if self.debit_amount > 0 else self.credit_amount


# 更新現有模型的關聯（需要在原模型中添加）
"""
在 Account 模型中添加：
transactions = relationship("Transaction", back_populates="account")

在 PaymentIdentity 模型中添加：
transactions = relationship("Transaction", back_populates="payment_identity")

在 Department 模型中添加：
transactions = relationship("Transaction", back_populates="department")

在 Project 模型中添加：
transactions = relationship("Transaction", back_populates="project")

在 AccountSubject 模型中添加：
journal_entries = relationship("JournalEntry", back_populates="account_subject")
"""
