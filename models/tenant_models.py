"""
多租戶相關模型定義
"""
from sqlalchemy import Column, Integer, String, Boolean, Text, ForeignKey, DateTime, Date, Float, Enum
from sqlalchemy.orm import relationship
from model import Base, get_taiwan_time
import enum

class TenantStatus(enum.Enum):
    """租戶狀態枚舉"""
    TRIAL = "trial"        # 試用期
    ACTIVE = "active"      # 活躍
    SUSPENDED = "suspended"  # 暫停
    EXPIRED = "expired"    # 過期
    CANCELLED = "cancelled"  # 已取消

class PlanLevel(enum.Enum):
    """方案等級枚舉"""
    BASIC = "basic"        # 基礎版
    STANDARD = "standard"  # 標準版
    PREMIUM = "premium"    # 專業版
    ENTERPRISE = "enterprise"  # 企業版

class Tenant(Base):
    """租戶表 - 每個租戶代表一個獨立的會計系統實例"""
    __tablename__ = 'tenants'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False, comment='租戶名稱/公司名稱')
    slug = Column(String(100), unique=True, nullable=False, index=True, comment='租戶識別符(用於子域名)')
    domain = Column(String(255), index=True, comment='自定義域名')
    
    # 方案相關
    plan_level = Column(Enum(PlanLevel), default=PlanLevel.BASIC, nullable=False, index=True, comment='方案等級')
    status = Column(Enum(TenantStatus), default=TenantStatus.TRIAL, nullable=False, index=True, comment='租戶狀態')
    
    # 時間相關
    trial_start_date = Column(Date, comment='試用開始日期')
    trial_end_date = Column(Date, comment='試用結束日期')
    subscription_start_date = Column(Date, comment='訂閱開始日期')
    subscription_end_date = Column(Date, comment='訂閱結束日期')
    
    # 聯絡資訊
    contact_email = Column(String(255), nullable=False, comment='聯絡信箱')
    contact_phone = Column(String(50), comment='聯絡電話')
    contact_person = Column(String(100), comment='聯絡人')
    
    # 地址資訊
    address = Column(Text, comment='地址')
    tax_id = Column(String(20), comment='統一編號')
    
    # 設定
    max_users = Column(Integer, default=5, comment='最大用戶數')
    max_storage_mb = Column(Integer, default=1024, comment='最大儲存空間(MB)')
    custom_logo_url = Column(String(500), comment='自定義Logo URL')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True, comment='建立時間')
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time, comment='更新時間')
    created_by = Column(String(100), comment='建立者')
    updated_by = Column(String(100), comment='最後修改者')
    is_deleted = Column(Boolean, default=False, index=True, comment='是否已刪除')
    deleted_at = Column(DateTime, comment='刪除時間')
    deleted_by = Column(String(100), comment='刪除者')
    
    # 關聯關係
    users = relationship("User", back_populates="tenant")
    plan_features = relationship("TenantPlanFeature", back_populates="tenant")

class PlanFeature(Base):
    """方案功能表 - 定義各種功能模組"""
    __tablename__ = 'plan_features'
    
    id = Column(Integer, primary_key=True)
    module_name = Column(String(100), nullable=False, index=True, comment='模組名稱')
    feature_name = Column(String(100), nullable=False, comment='功能名稱')
    display_name = Column(String(200), nullable=False, comment='顯示名稱')
    description = Column(Text, comment='功能描述')
    
    # 方案可用性
    basic_enabled = Column(Boolean, default=False, comment='基礎版是否可用')
    standard_enabled = Column(Boolean, default=False, comment='標準版是否可用')
    premium_enabled = Column(Boolean, default=True, comment='專業版是否可用')
    enterprise_enabled = Column(Boolean, default=True, comment='企業版是否可用')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)
    is_active = Column(Boolean, default=True, comment='是否啟用')

class TenantPlanFeature(Base):
    """租戶方案功能關聯表 - 記錄每個租戶實際擁有的功能"""
    __tablename__ = 'tenant_plan_features'
    
    id = Column(Integer, primary_key=True)
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False, index=True)
    feature_id = Column(Integer, ForeignKey('plan_features.id'), nullable=False, index=True)
    is_enabled = Column(Boolean, default=True, comment='是否啟用')
    custom_limit = Column(Integer, comment='自定義限制')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    updated_at = Column(DateTime, default=get_taiwan_time, onupdate=get_taiwan_time)
    
    # 關聯關係
    tenant = relationship("Tenant", back_populates="plan_features")
    feature = relationship("PlanFeature")

class TenantUsageLog(Base):
    """租戶使用記錄表 - 追蹤租戶的使用情況"""
    __tablename__ = 'tenant_usage_logs'
    
    id = Column(Integer, primary_key=True)
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False, index=True)
    metric_name = Column(String(100), nullable=False, comment='指標名稱')
    metric_value = Column(Float, nullable=False, comment='指標值')
    recorded_date = Column(Date, nullable=False, index=True, comment='記錄日期')
    
    # 審計欄位
    created_at = Column(DateTime, default=get_taiwan_time, index=True)
    
    # 關聯關係
    tenant = relationship("Tenant")